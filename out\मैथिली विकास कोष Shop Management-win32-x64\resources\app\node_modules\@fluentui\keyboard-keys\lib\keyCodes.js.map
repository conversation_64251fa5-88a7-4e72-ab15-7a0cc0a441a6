{"version": 3, "sources": ["../src/keyCodes.ts"], "sourcesContent": ["export const Cancel = 3;\nexport const Help = 6;\nexport const Backspace = 8;\nexport const Tab = 9;\nexport const Clear = 12;\nexport const Enter = 13;\nexport const Shift = 16;\nexport const Control = 17;\nexport const Alt = 18;\nexport const Pause = 19;\nexport const CapsLock = 20;\nexport const Escape = 27;\nexport const Convert = 28;\nexport const NonConvert = 29;\nexport const Accept = 30;\nexport const ModeChange = 31;\nexport const Space = 32;\nexport const PageUp = 33;\nexport const PageDown = 34;\nexport const End = 35;\nexport const Home = 36;\nexport const ArrowLeft = 37;\nexport const ArrowUp = 38;\nexport const ArrowRight = 39;\nexport const ArrowDown = 40;\nexport const Select = 41;\nexport const Print = 42;\nexport const Execute = 43;\nexport const PrintScreen = 44;\nexport const Insert = 45;\nexport const Delete = 46;\nexport const Digit0 = 48;\nexport const RightParenthesis = 48;\nexport const Digit1 = 49;\nexport const ExclamationPoint = 49;\nexport const Digit2 = 50;\nexport const AtSign = 50;\nexport const Digit3 = 51;\nexport const PoundSign = 51;\nexport const Digit4 = 52;\nexport const DollarSign = 52;\nexport const Digit5 = 53;\nexport const PercentSign = 53;\nexport const Digit6 = 54;\nexport const Caret = 54;\nexport const Digit7 = 55;\nexport const Ampersand = 55;\nexport const Digit8 = 56;\nexport const MultiplicationSign = 56;\nexport const Digit9 = 57;\nexport const LeftParenthesis = 57;\nexport const a = 65;\nexport const A = 65;\nexport const b = 66;\nexport const B = 66;\nexport const c = 67;\nexport const C = 67;\nexport const d = 68;\nexport const D = 68;\nexport const e = 69;\nexport const E = 69;\nexport const f = 70;\nexport const F = 70;\nexport const g = 71;\nexport const G = 71;\nexport const h = 72;\nexport const H = 72;\nexport const i = 73;\nexport const I = 73;\nexport const j = 74;\nexport const J = 74;\nexport const k = 75;\nexport const K = 75;\nexport const l = 76;\nexport const L = 76;\nexport const m = 77;\nexport const M = 77;\nexport const n = 78;\nexport const N = 78;\nexport const o = 79;\nexport const O = 79;\nexport const p = 80;\nexport const P = 80;\nexport const q = 81;\nexport const Q = 81;\nexport const r = 82;\nexport const R = 82;\nexport const s = 83;\nexport const S = 83;\nexport const t = 84;\nexport const T = 84;\nexport const u = 85;\nexport const U = 85;\nexport const v = 86;\nexport const V = 86;\nexport const w = 87;\nexport const W = 87;\nexport const x = 88;\nexport const X = 88;\nexport const y = 89;\nexport const Y = 89;\nexport const z = 90;\nexport const Z = 90;\nexport const OS = 91;\nexport const ContextMenu = 93;\nexport const F1 = 112;\nexport const F2 = 113;\nexport const F3 = 114;\nexport const F4 = 115;\nexport const F5 = 116;\nexport const F6 = 117;\nexport const F7 = 118;\nexport const F8 = 119;\nexport const F9 = 120;\nexport const F10 = 121;\nexport const F11 = 122;\nexport const F12 = 123;\nexport const F13 = 124;\nexport const F14 = 125;\nexport const F15 = 126;\nexport const F16 = 127;\nexport const F17 = 128;\nexport const F18 = 129;\nexport const F19 = 130;\nexport const F20 = 131;\nexport const F21 = 132;\nexport const F22 = 133;\nexport const F23 = 134;\nexport const F24 = 135;\nexport const NumLock = 144;\nexport const ScrollLock = 145;\nexport const VolumeMute = 181;\nexport const VolumeDown = 182;\nexport const VolumeUp = 183;\nexport const Semicolon = 186;\nexport const EqualsSign = 187;\nexport const PlusSign = 187;\nexport const Comma = 188;\nexport const LeftAngleBracket = 188;\nexport const MinusSign = 189;\nexport const Underscore = 189;\nexport const Decimal = 190;\nexport const RightAngleBracket = 190;\nexport const ForwardSlash = 191;\nexport const QuestionMark = 191;\nexport const GraveAccent = 192;\nexport const Tilde = 192;\nexport const LeftSquareBracket = 219;\nexport const LeftCurlyBrace = 219;\nexport const BackSlash = 220;\nexport const Pipe = 220;\nexport const RightSquareBracket = 221;\nexport const RightCurlyBrace = 221;\nexport const SingleQuote = 222;\nexport const DoubleQuote = 222;\nexport const Meta = 224;\nexport const AltGraph = 225;\nexport const Attn = 246;\nexport const CrSel = 247;\nexport const ExSel = 248;\nexport const EraseEof = 249;\nexport const Play = 250;\nexport const ZoomOut = 251;\n"], "names": ["Cancel", "Help", "Backspace", "Tab", "Clear", "Enter", "Shift", "Control", "Alt", "Pause", "CapsLock", "Escape", "Convert", "NonConvert", "Accept", "ModeChange", "Space", "PageUp", "PageDown", "End", "Home", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "Select", "Print", "Execute", "PrintScreen", "Insert", "Delete", "Digit0", "RightParenthesis", "Digit1", "ExclamationPoint", "Digit2", "AtSign", "Digit3", "PoundSign", "Digit4", "DollarSign", "Digit5", "PercentSign", "Digit6", "<PERSON><PERSON>", "Digit7", "Ampersand", "Digit8", "MultiplicationSign", "Digit9", "LeftParenthesis", "a", "A", "b", "B", "c", "C", "d", "D", "e", "E", "f", "F", "g", "G", "h", "H", "i", "I", "j", "J", "k", "K", "l", "L", "m", "M", "n", "N", "o", "O", "p", "P", "q", "Q", "r", "R", "s", "S", "t", "T", "u", "U", "v", "V", "w", "W", "x", "X", "y", "Y", "z", "Z", "OS", "ContextMenu", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "F13", "F14", "F15", "F16", "F17", "F18", "F19", "F20", "F21", "F22", "F23", "F24", "NumLock", "ScrollLock", "VolumeMute", "VolumeDown", "VolumeUp", "Semicolon", "EqualsSign", "PlusSign", "Comma", "LeftAngleBracket", "MinusSign", "Underscore", "Decimal", "RightAngleBracket", "ForwardSlash", "QuestionMark", "GraveAccent", "<PERSON><PERSON>", "LeftSquareBracket", "LeftCurlyBrace", "BackSlash", "<PERSON><PERSON>", "RightSquareBracket", "RightCurlyBrace", "SingleQuote", "DoubleQuote", "Meta", "AltGraph", "Attn", "CrSel", "ExSel", "EraseEof", "Play", "ZoomOut"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,OAAO,MAAMA,SAAS,EAAE;AACxB,OAAO,MAAMC,OAAO,EAAE;AACtB,OAAO,MAAMC,YAAY,EAAE;AAC3B,OAAO,MAAMC,MAAM,EAAE;AACrB,OAAO,MAAMC,QAAQ,GAAG;AACxB,OAAO,MAAMC,QAAQ,GAAG;AACxB,OAAO,MAAMC,QAAQ,GAAG;AACxB,OAAO,MAAMC,UAAU,GAAG;AAC1B,OAAO,MAAMC,MAAM,GAAG;AACtB,OAAO,MAAMC,QAAQ,GAAG;AACxB,OAAO,MAAMC,WAAW,GAAG;AAC3B,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,UAAU,GAAG;AAC1B,OAAO,MAAMC,aAAa,GAAG;AAC7B,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,aAAa,GAAG;AAC7B,OAAO,MAAMC,QAAQ,GAAG;AACxB,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,WAAW,GAAG;AAC3B,OAAO,MAAMC,MAAM,GAAG;AACtB,OAAO,MAAMC,OAAO,GAAG;AACvB,OAAO,MAAMC,YAAY,GAAG;AAC5B,OAAO,MAAMC,UAAU,GAAG;AAC1B,OAAO,MAAMC,aAAa,GAAG;AAC7B,OAAO,MAAMC,YAAY,GAAG;AAC5B,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,QAAQ,GAAG;AACxB,OAAO,MAAMC,UAAU,GAAG;AAC1B,OAAO,MAAMC,cAAc,GAAG;AAC9B,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,mBAAmB,GAAG;AACnC,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,mBAAmB,GAAG;AACnC,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,YAAY,GAAG;AAC5B,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,aAAa,GAAG;AAC7B,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,cAAc,GAAG;AAC9B,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,QAAQ,GAAG;AACxB,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,YAAY,GAAG;AAC5B,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,qBAAqB,GAAG;AACrC,OAAO,MAAMC,SAAS,GAAG;AACzB,OAAO,MAAMC,kBAAkB,GAAG;AAClC,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,IAAI,GAAG;AACpB,OAAO,MAAMC,KAAK,GAAG;AACrB,OAAO,MAAMC,cAAc,GAAG;AAC9B,OAAO,MAAMC,KAAK,IAAI;AACtB,OAAO,MAAMC,KAAK,IAAI;AACtB,OAAO,MAAMC,KAAK,IAAI;AACtB,OAAO,MAAMC,KAAK,IAAI;AACtB,OAAO,MAAMC,KAAK,IAAI;AACtB,OAAO,MAAMC,KAAK,IAAI;AACtB,OAAO,MAAMC,KAAK,IAAI;AACtB,OAAO,MAAMC,KAAK,IAAI;AACtB,OAAO,MAAMC,KAAK,IAAI;AACtB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,MAAM,IAAI;AACvB,OAAO,MAAMC,UAAU,IAAI;AAC3B,OAAO,MAAMC,aAAa,IAAI;AAC9B,OAAO,MAAMC,aAAa,IAAI;AAC9B,OAAO,MAAMC,aAAa,IAAI;AAC9B,OAAO,MAAMC,WAAW,IAAI;AAC5B,OAAO,MAAMC,YAAY,IAAI;AAC7B,OAAO,MAAMC,aAAa,IAAI;AAC9B,OAAO,MAAMC,WAAW,IAAI;AAC5B,OAAO,MAAMC,QAAQ,IAAI;AACzB,OAAO,MAAMC,mBAAmB,IAAI;AACpC,OAAO,MAAMC,YAAY,IAAI;AAC7B,OAAO,MAAMC,aAAa,IAAI;AAC9B,OAAO,MAAMC,UAAU,IAAI;AAC3B,OAAO,MAAMC,oBAAoB,IAAI;AACrC,OAAO,MAAMC,eAAe,IAAI;AAChC,OAAO,MAAMC,eAAe,IAAI;AAChC,OAAO,MAAMC,cAAc,IAAI;AAC/B,OAAO,MAAMC,QAAQ,IAAI;AACzB,OAAO,MAAMC,oBAAoB,IAAI;AACrC,OAAO,MAAMC,iBAAiB,IAAI;AAClC,OAAO,MAAMC,YAAY,IAAI;AAC7B,OAAO,MAAMC,OAAO,IAAI;AACxB,OAAO,MAAMC,qBAAqB,IAAI;AACtC,OAAO,MAAMC,kBAAkB,IAAI;AACnC,OAAO,MAAMC,cAAc,IAAI;AAC/B,OAAO,MAAMC,cAAc,IAAI;AAC/B,OAAO,MAAMC,OAAO,IAAI;AACxB,OAAO,MAAMC,WAAW,IAAI;AAC5B,OAAO,MAAMC,OAAO,IAAI;AACxB,OAAO,MAAMC,QAAQ,IAAI;AACzB,OAAO,MAAMC,QAAQ,IAAI;AACzB,OAAO,MAAMC,WAAW,IAAI;AAC5B,OAAO,MAAMC,OAAO,IAAI;AACxB,OAAO,MAAMC,UAAU,IAAI"}