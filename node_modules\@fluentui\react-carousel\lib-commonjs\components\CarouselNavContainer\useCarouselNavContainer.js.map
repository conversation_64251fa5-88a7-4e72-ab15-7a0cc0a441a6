{"version": 3, "sources": ["../src/components/CarouselNavContainer/useCarouselNavContainer.ts"], "sourcesContent": ["import * as React from 'react';\nimport { getIntrinsicElementProps, slot } from '@fluentui/react-utilities';\nimport type { CarouselNavContainerProps, CarouselNavContainerState } from './CarouselNavContainer.types';\nimport { CarouselAutoplayButton } from '../CarouselAutoplayButton/CarouselAutoplayButton';\nimport { CarouselButton } from '../CarouselButton/CarouselButton';\nimport { Tooltip } from '@fluentui/react-tooltip';\n\n/**\n * Create the state required to render CarouselNavContainer.\n *\n * The returned state can be modified with hooks such as useCarouselNavContainerStyles_unstable,\n * before being passed to renderCarouselNavContainer_unstable.\n *\n * @param props - props from this instance of CarouselNavContainer\n * @param ref - reference to root HTMLDivElement of CarouselNavContainer\n */\nexport const useCarouselNavContainer_unstable = (\n  props: CarouselNavContainerProps,\n  ref: React.Ref<HTMLDivElement>,\n): CarouselNavContainerState => {\n  const { layout } = props;\n  const next: CarouselNavContainerState['next'] = slot.optional(props.next, {\n    defaultProps: {\n      navType: 'next',\n    },\n    elementType: CarouselButton,\n    renderByDefault: true,\n  });\n\n  const prev: CarouselNavContainerState['prev'] = slot.optional(props.prev, {\n    defaultProps: {\n      navType: 'prev',\n    },\n    elementType: CarouselButton,\n    renderByDefault: true,\n  });\n\n  const autoplay: CarouselNavContainerState['autoplay'] = slot.optional(props.autoplay, {\n    elementType: CarouselAutoplayButton,\n    renderByDefault: !!props.autoplay || !!props.autoplayTooltip,\n  });\n\n  const nextTooltip: CarouselNavContainerState['nextTooltip'] = slot.optional(props.nextTooltip, {\n    defaultProps: {},\n    elementType: Tooltip,\n    renderByDefault: false,\n  });\n\n  const prevTooltip: CarouselNavContainerState['prevTooltip'] = slot.optional(props.prevTooltip, {\n    defaultProps: {},\n    elementType: Tooltip,\n    renderByDefault: false,\n  });\n\n  const autoplayTooltip: CarouselNavContainerState['autoplayTooltip'] = slot.optional(props.autoplayTooltip, {\n    defaultProps: {},\n    elementType: Tooltip,\n    renderByDefault: false,\n  });\n\n  return {\n    layout,\n    components: {\n      root: 'div',\n      next: CarouselButton,\n      prev: CarouselButton,\n      autoplay: CarouselAutoplayButton,\n      nextTooltip: Tooltip,\n      prevTooltip: Tooltip,\n      autoplayTooltip: Tooltip,\n    },\n    root: slot.always(\n      getIntrinsicElementProps('div', {\n        ref,\n        ...props,\n      }),\n      { elementType: 'div' },\n    ),\n    next,\n    prev,\n    autoplay,\n    nextTooltip,\n    prevTooltip,\n    autoplayTooltip,\n  };\n};\n"], "names": ["useCarouselNavContainer_unstable", "props", "ref", "layout", "next", "slot", "optional", "defaultProps", "navType", "elementType", "CarouselButton", "renderByDefault", "prev", "autoplay", "CarouselAutoplayButton", "autoplayTooltip", "nextTooltip", "<PERSON><PERSON><PERSON>", "prevTooltip", "components", "root", "always", "getIntrinsicElementProps"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAgBaA;;;eAAAA;;;;iEAhBU;gCACwB;wCAER;gCACR;8BACP;AAWjB,MAAMA,mCAAmC,CAC9CC,OACAC;IAEA,MAAM,EAAEC,MAAM,EAAE,GAAGF;IACnB,MAAMG,OAA0CC,oBAAAA,CAAKC,QAAQ,CAACL,MAAMG,IAAI,EAAE;QACxEG,cAAc;YACZC,SAAS;QACX;QACAC,aAAaC,8BAAAA;QACbC,iBAAiB;IACnB;IAEA,MAAMC,OAA0CP,oBAAAA,CAAKC,QAAQ,CAACL,MAAMW,IAAI,EAAE;QACxEL,cAAc;YACZC,SAAS;QACX;QACAC,aAAaC,8BAAAA;QACbC,iBAAiB;IACnB;IAEA,MAAME,WAAkDR,oBAAAA,CAAKC,QAAQ,CAACL,MAAMY,QAAQ,EAAE;QACpFJ,aAAaK,8CAAAA;QACbH,iBAAiB,CAAC,CAACV,MAAMY,QAAQ,IAAI,CAAC,CAACZ,MAAMc,eAAe;IAC9D;IAEA,MAAMC,cAAwDX,oBAAAA,CAAKC,QAAQ,CAACL,MAAMe,WAAW,EAAE;QAC7FT,cAAc,CAAC;QACfE,aAAaQ,qBAAAA;QACbN,iBAAiB;IACnB;IAEA,MAAMO,cAAwDb,oBAAAA,CAAKC,QAAQ,CAACL,MAAMiB,WAAW,EAAE;QAC7FX,cAAc,CAAC;QACfE,aAAaQ,qBAAAA;QACbN,iBAAiB;IACnB;IAEA,MAAMI,kBAAgEV,oBAAAA,CAAKC,QAAQ,CAACL,MAAMc,eAAe,EAAE;QACzGR,cAAc,CAAC;QACfE,aAAaQ,qBAAAA;QACbN,iBAAiB;IACnB;IAEA,OAAO;QACLR;QACAgB,YAAY;YACVC,MAAM;YACNhB,MAAMM,8BAAAA;YACNE,MAAMF,8BAAAA;YACNG,UAAUC,8CAAAA;YACVE,aAAaC,qBAAAA;YACbC,aAAaD,qBAAAA;YACbF,iBAAiBE,qBAAAA;QACnB;QACAG,MAAMf,oBAAAA,CAAKgB,MAAM,CACfC,IAAAA,wCAAAA,EAAyB,OAAO;YAC9BpB;YACA,GAAGD,KAAK;QACV,IACA;YAAEQ,aAAa;QAAM;QAEvBL;QACAQ;QACAC;QACAG;QACAE;QACAH;IACF;AACF"}