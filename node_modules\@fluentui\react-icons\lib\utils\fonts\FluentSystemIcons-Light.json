{"AccessibilityCheckmark32Light": 57344, "Add32Light": 57345, "Alert32Light": 57346, "AppFolder32Light": 57347, "AppGeneric32Light": 57348, "Archive32Light": 57349, "ArchiveSettings32Light": 57350, "ArrowClockwise32Light": 57351, "ArrowDown32Light": 57352, "ArrowDownload32Light": 57353, "ArrowForward32Light": 57354, "ArrowHookDownLeft32Light": 57355, "ArrowHookDownRight32Light": 57356, "ArrowHookUpLeft32Light": 57357, "ArrowHookUpRight32Light": 57358, "ArrowRedo32Light": 57359, "ArrowReply32Light": 57360, "ArrowReplyAll32Light": 57361, "ArrowUndo32Light": 57362, "Attach32Light": 57363, "AutoFit32Light": 57364, "AutoFitWidth32Light": 57365, "Autocorrect32Light": 57366, "BreakoutRoom32Light": 57367, "Broom32Light": 57368, "Calendar3Day32Light": 57369, "CalendarClock32Light": 57370, "CalendarDataBar32Light": 57371, "CalendarDay32Light": 57372, "CalendarEdit32Light": 57373, "CalendarEmpty32Light": 57374, "CalendarLtr32Light": 57375, "CalendarMonth32Light": 57376, "CalendarMultiple32Light": 57377, "CalendarPattern32Light": 57378, "CalendarReply32Light": 57379, "CalendarSparkle32Light": 57380, "CalendarTodo32Light": 57381, "CalendarWorkWeek32Light": 57382, "Chat32Light": 57383, "Checkmark32Light": 57384, "CheckmarkCircle32Light": 57385, "Classification32Light": 57386, "ClipboardPaste32Light": 57387, "Clock32Light": 57388, "ClockAlarm32Light": 57389, "Color32Light": 57390, "ColorFill32Light": 57391, "ColorFillAccent32Light": 57392, "Comment32Light": 57393, "CommentAdd32Light": 57394, "Compose32Light": 57395, "Copy32Light": 57396, "Crop32Light": 57397, "Cursor32Light": 57398, "Cut32Light": 57399, "Delete32Light": 57400, "Dismiss32Light": 57401, "DismissCircle32Light": 57402, "Document24Light": 57403, "Document28Light": 57404, "Document32Light": 57405, "Document48Light": 57406, "DocumentLightning32Light": 57407, "DocumentSignature32Light": 57408, "DocumentSparkle24Light": 57409, "DocumentSparkle28Light": 57410, "DocumentSparkle32Light": 57411, "DocumentSparkle48Light": 57412, "DoorArrowRight32Light": 57413, "Edit32Light": 57414, "Emoji32Light": 57415, "Eye32Light": 57416, "EyeOff32Light": 57417, "Filter32Light": 57418, "Flag32Light": 57419, "FlagOff32Light": 57420, "Flash32Light": 57421, "FolderArrowRight32Light": 57422, "FolderMail32Light": 57423, "HandDraw32Light": 57424, "History32Light": 57425, "ImageAdd32Light": 57426, "ImageAltText32Light": 57427, "ImageCopy32Light": 57428, "ImageReflection32Light": 57429, "ImageShadow32Light": 57430, "ImmersiveReader32Light": 57431, "Important32Light": 57432, "Lasso32Light": 57433, "LayoutColumnTwo32Light": 57434, "LayoutColumnTwoFocusLeft32Light": 57435, "LayoutColumnTwoFocusRight32Light": 57436, "LayoutRowTwo32Light": 57437, "LayoutRowTwoFocusTop32Light": 57438, "LayoutRowTwoFocusTopSettings32Light": 57439, "LayoutRowTwoSettings32Light": 57440, "Lightbulb32Light": 57441, "Link32Light": 57442, "LockClosed32Light": 57443, "LockOpen32Light": 57444, "Mail32Light": 57445, "MailAlert32Light": 57446, "MailArrowClockwise32Light": 57447, "MailArrowDoubleBack32Light": 57448, "MailCopy32Light": 57449, "MailEdit32Light": 57450, "MailList32Light": 57451, "MailMultiple32Light": 57452, "MailRead32Light": 57453, "MailReadMultiple32Light": 57454, "MailRewind32Light": 57455, "MailSettings32Light": 57456, "MailTemplate32Light": 57457, "MailUnread32Light": 57458, "Mic32Light": 57459, "Molecule32Light": 57460, "Note32Light": 57461, "Options32Light": 57462, "PaintBrush32Light": 57463, "PanelLeftDefault32Light": 57464, "PanelLeftFocusRight32Light": 57465, "PenSparkle32Light": 57466, "People32Light": 57467, "PeopleAdd32Light": 57468, "PeopleCommunity32Light": 57469, "PeopleEdit32Light": 57470, "PeopleList32Light": 57471, "PeopleSettings32Light": 57472, "PeopleSync32Light": 57473, "Person32Light": 57474, "PersonAdd32Light": 57475, "PersonAvailable32Light": 57476, "PersonFeedback32Light": 57477, "PersonMail32Light": 57478, "PersonProhibited32Light": 57479, "PersonSuport32Light": 57480, "Phone32Light": 57481, "PictureInPicture32Light": 57482, "Pin32Light": 57483, "PinOff32Light": 57484, "Poll32Light": 57485, "Print32Light": 57486, "Question32Light": 57487, "ReadAloud32Light": 57488, "RectangleLandscape32Light": 57489, "RotateLeft32Light": 57490, "Save32Light": 57491, "SendClock32Light": 57492, "Settings32Light": 57493, "Share32Light": 57494, "ShieldError32Light": 57495, "Signature32Light": 57496, "SpeakerMute32Light": 57497, "SquareArrowForward32Light": 57498, "Stamp32Light": 57499, "StarAdd32Light": 57500, "StarArrowRight32Light": 57501, "Sticker32Light": 57502, "TabAdd32Light": 57503, "Table32Light": 57504, "TableAltText32Light": 57505, "TableCellsMerge32Light": 57506, "TableCellsSplit32Light": 57507, "TableDismiss32Light": 57508, "TableMoveAbove32Light": 57509, "TableMoveBelow32Light": 57510, "TableMoveLeft32Light": 57511, "TableMoveRight32Light": 57512, "TableSettings32Light": 57513, "TableSimple32Light": 57514, "Tag32Light": 57515, "Text32Light": 57516, "TextClearFormatting32Light": 57517, "TextCollapse32Light": 57518, "TextDensity32Light": 57519, "TextEditStyle32Light": 57520, "TextExpand32Light": 57521, "TextboxAlignTopLeft32Light": 57522, "Toolbox32Light": 57523, "Translate32Light": 57524, "Video32Light": 57525, "VideoClip32Light": 57526, "WeatherMoon32Light": 57527, "WeatherSunny32Light": 57528, "Window32Light": 57529, "WrenchScrewdriver32Light": 57530, "ZoomIn32Light": 57531, "ZoomOut32Light": 57532}