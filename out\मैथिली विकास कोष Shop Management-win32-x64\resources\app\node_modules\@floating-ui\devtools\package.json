{"name": "@floating-ui/devtools", "version": "0.2.1", "main": "./dist/floating-ui.devtools.umd.js", "module": "./dist/floating-ui.devtools.esm.js", "unpkg": "./dist/floating-ui.devtools.umd.min.js", "types": "./dist/floating-ui.devtools.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/floating-ui.devtools.d.mts", "default": "./dist/floating-ui.devtools.mjs"}, "types": "./dist/floating-ui.devtools.d.ts", "module": "./dist/floating-ui.devtools.esm.js", "default": "./dist/floating-ui.devtools.umd.js"}}, "files": ["dist/", "**/*.d.ts", "**/*.d.mts"], "peerDependencies": {"@floating-ui/dom": ">=1.5.4"}, "devDependencies": {"config": "0.0.0", "@floating-ui/dom": "1.5.4"}, "scripts": {"lint": "biome lint .", "clean": "<PERSON><PERSON><PERSON> dist out-tsc", "dev": "rollup -c -w", "build": "rollup -c", "build:api": "build-api", "publint": "publint", "typecheck": "tsc -b"}}