{"version": 3, "sources": ["../src/components/AvatarGroup/renderAvatarGroup.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport { AvatarGroupProvider } from '../../contexts/AvatarGroupContext';\nimport type { AvatarGroupState, AvatarGroupSlots, AvatarGroupContextValues } from './AvatarGroup.types';\n\n/**\n * Render the final JSX of AvatarGroup\n */\nexport const renderAvatarGroup_unstable = (state: AvatarGroupState, contextValues: AvatarGroupContextValues) => {\n  assertSlots<AvatarGroupSlots>(state);\n\n  return (\n    <AvatarGroupProvider value={contextValues.avatarGroup}>\n      <state.root />\n    </AvatarGroupProvider>\n  );\n};\n"], "names": ["renderAvatarGroup_unstable", "state", "contextValues", "assertSlots", "_jsx", "AvatarGroupProvider", "value", "avatarGroup", "root"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAUaA;;;eAAAA;;;4BATb;gCAE4B;oCACQ;AAM7B,MAAMA,6BAA6B,CAACC,OAAyBC;IAClEC,IAAAA,2BAAAA,EAA8BF;IAE9B,OAAA,WAAA,GACEG,IAAAA,eAAA,EAACC,uCAAAA,EAAAA;QAAoBC,OAAOJ,cAAcK,WAAW;kBACnD,WAAA,GAAAH,IAAAA,eAAA,EAACH,MAAMO,IAAI,EAAA,CAAA;;AAGjB"}