{"version": 3, "sources": ["../src/AriaLiveAnnouncer/AriaLiveAnnouncer.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { AriaLiveAnnouncerProps } from './AriaLiveAnnouncer.types';\nimport { renderAriaLiveAnnouncer_unstable } from './renderAriaLiveAnnouncer';\nimport { useAriaLiveAnnouncer_unstable } from './useAriaLiveAnnouncer';\nimport { useAriaLiveAnnouncerContextValues_unstable } from './useAriaLiveAnnouncerContextValues';\n\n/**\n * A sample implementation of a component that manages aria live announcements.\n */\nexport const AriaLiveAnnouncer: React.FC<AriaLiveAnnouncerProps> = props => {\n  const state = useAriaLiveAnnouncer_unstable(props);\n  const contextValues = useAriaLiveAnnouncerContextValues_unstable(state);\n\n  return renderAriaLiveAnnouncer_unstable(state, contextValues);\n};\n\nAriaLiveAnnouncer.displayName = 'AriaLiveAnnouncer';\n"], "names": ["React", "renderAriaLiveAnnouncer_unstable", "useAriaLiveAnnouncer_unstable", "useAriaLiveAnnouncerContextValues_unstable", "AriaLiveAnnouncer", "props", "state", "contextValues", "displayName"], "rangeMappings": ";;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAG/B,SAASC,gCAAgC,QAAQ,4BAA4B;AAC7E,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,SAASC,0CAA0C,QAAQ,sCAAsC;AAEjG;;CAEC,GACD,OAAO,MAAMC,oBAAsDC,CAAAA;IACjE,MAAMC,QAAQJ,8BAA8BG;IAC5C,MAAME,gBAAgBJ,2CAA2CG;IAEjE,OAAOL,iCAAiCK,OAAOC;AACjD,EAAE;AAEFH,kBAAkBI,WAAW,GAAG"}