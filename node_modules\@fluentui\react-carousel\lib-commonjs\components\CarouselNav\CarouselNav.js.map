{"version": 3, "sources": ["../src/components/CarouselNav/CarouselNav.tsx"], "sourcesContent": ["import type { ForwardRefComponent } from '@fluentui/react-utilities';\nimport * as React from 'react';\n\nimport type { CarouselNavProps } from './CarouselNav.types';\nimport { useCarouselNavContextValues_unstable } from './CarouselNavContext';\nimport { renderCarouselNav_unstable } from './renderCarouselNav';\nimport { useCarouselNav_unstable } from './useCarouselNav';\nimport { useCarouselNavStyles_unstable } from './useCarouselNavStyles.styles';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\n\n/**\n * Used to jump to a card based on index, using arrow navigation via Tabster.\n *\n * The children of this component will be wrapped in a context to\n * provide the appropriate value based on their index position.\n */\nexport const CarouselNav: ForwardRefComponent<CarouselNavProps> = React.forwardRef((props, ref) => {\n  const state = useCarouselNav_unstable(props, ref);\n  const contextValues = useCarouselNavContextValues_unstable(state);\n\n  useCarouselNavStyles_unstable(state);\n  useCustomStyleHook_unstable('useCarouselNavStyles_unstable')(state);\n\n  return renderCarouselNav_unstable(state, contextValues);\n});\n\nCarouselNav.displayName = 'CarouselNav';\n"], "names": ["CarouselNav", "React", "forwardRef", "props", "ref", "state", "useCarouselNav_unstable", "contextValues", "useCarouselNavContextValues_unstable", "useCarouselNavStyles_unstable", "useCustomStyleHook_unstable", "renderCarouselNav_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAgBaA;;;eAAAA;;;;iEAfU;oCAG8B;mCACV;gCACH;4CACM;qCACF;AAQrC,MAAMA,cAAAA,WAAAA,GAAqDC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IACzF,MAAMC,QAAQC,IAAAA,uCAAAA,EAAwBH,OAAOC;IAC7C,MAAMG,gBAAgBC,IAAAA,wDAAAA,EAAqCH;IAE3DI,IAAAA,yDAAAA,EAA8BJ;IAC9BK,IAAAA,gDAAAA,EAA4B,iCAAiCL;IAE7D,OAAOM,IAAAA,6CAAAA,EAA2BN,OAAOE;AAC3C;AAEAP,YAAYY,WAAW,GAAG"}