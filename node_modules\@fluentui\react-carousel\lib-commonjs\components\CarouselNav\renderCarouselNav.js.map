{"version": 3, "sources": ["../src/components/CarouselNav/renderCarouselNav.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\n\nimport type { CarouselNavState, CarouselNavSlots } from './CarouselNav.types';\nimport { CarouselNavContextProvider, type CarouselNavContextValues } from './CarouselNavContext';\nimport { CarouselNavIndexContextProvider } from './CarouselNavIndexContext';\n\n/**\n * Render the final JSX of CarouselNav\n */\nexport const renderCarouselNav_unstable = (state: CarouselNavState, contextValues: CarouselNavContextValues) => {\n  assertSlots<CarouselNavSlots>(state);\n\n  const { totalSlides, renderNavButton } = state;\n\n  return (\n    <state.root>\n      <CarouselNavContextProvider value={contextValues.carouselNav}>\n        {new Array(totalSlides).fill(null).map((_, index) => (\n          <CarouselNavIndexContextProvider key={index} value={index}>\n            {renderNavButton(index)}\n          </CarouselNavIndexContextProvider>\n        ))}\n      </CarouselNavContextProvider>\n    </state.root>\n  );\n};\n"], "names": ["renderCarouselNav_unstable", "state", "contextValues", "assertSlots", "totalSlides", "renderNavButton", "_jsx", "root", "CarouselNavContextProvider", "value", "carouselNav", "Array", "fill", "map", "_", "index", "CarouselNavIndexContextProvider"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAYaA;;;eAAAA;;;4BAXb;gCAE4B;oCAG8C;yCAC1B;AAKzC,MAAMA,6BAA6B,CAACC,OAAyBC;IAClEC,IAAAA,2BAAAA,EAA8BF;IAE9B,MAAM,EAAEG,WAAW,EAAEC,eAAe,EAAE,GAAGJ;IAEzC,OAAA,WAAA,GACEK,IAAAA,eAAA,EAACL,MAAMM,IAAI,EAAA;kBACT,WAAA,GAAAD,IAAAA,eAAA,EAACE,8CAAAA,EAAAA;YAA2BC,OAAOP,cAAcQ,WAAW;sBACzD,IAAIC,MAAMP,aAAaQ,IAAI,CAAC,MAAMC,GAAG,CAAC,CAACC,GAAGC,QAAAA,WAAAA,GACzCT,IAAAA,eAAA,EAACU,wDAAAA,EAAAA;oBAA4CP,OAAOM;8BACjDV,gBAAgBU;mBADmBA;;;AAOhD"}