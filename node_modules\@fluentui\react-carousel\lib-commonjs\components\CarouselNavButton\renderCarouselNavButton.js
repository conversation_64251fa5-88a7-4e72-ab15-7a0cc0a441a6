"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "renderCarouselNavButton_unstable", {
    enumerable: true,
    get: function() {
        return renderCarouselNavButton_unstable;
    }
});
const _jsxruntime = require("@fluentui/react-jsx-runtime/jsx-runtime");
const _reactutilities = require("@fluentui/react-utilities");
const renderCarouselNavButton_unstable = (state)=>{
    (0, _reactutilities.assertSlots)(state);
    // TODO Add additional slots in the appropriate place
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(state.root, {});
};
