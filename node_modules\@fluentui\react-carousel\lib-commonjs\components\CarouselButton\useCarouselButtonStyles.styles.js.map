{"version": 3, "sources": ["useCarouselButtonStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses } from '@griffel/react';\nimport { useButtonStyles_unstable } from '@fluentui/react-button';\nimport { tokens } from '@fluentui/react-theme';\nexport const carouselButtonClassNames = {\n    root: 'fui-CarouselButton',\n    icon: 'fui-CarouselButton__icon'\n};\n/**\n * Styles for the root slot\n */ const useStyles = makeStyles({\n    root: {\n        marginTop: 'auto',\n        marginBottom: 'auto',\n        color: tokens.colorNeutralForeground2,\n        backgroundColor: tokens.colorNeutralBackgroundAlpha,\n        pointerEvents: 'all',\n        ':hover': {\n            cursor: 'pointer'\n        }\n    }\n});\n/**\n * Apply styling to the CarouselButton slots based on the state\n */ export const useCarouselButtonStyles_unstable = (state)=>{\n    'use no memo';\n    const styles = useStyles();\n    state = {\n        ...state,\n        ...useButtonStyles_unstable(state)\n    };\n    state.root.className = mergeClasses(carouselButtonClassNames.root, styles.root, state.root.className);\n    if (state.icon) {\n        state.icon.className = mergeClasses(carouselButtonClassNames.icon, state.icon.className);\n    }\n    return state;\n};\n"], "names": ["carouselButtonClassNames", "useCarouselButtonStyles_unstable", "root", "icon", "useStyles", "__styles", "B6of3ja", "j<PERSON><PERSON>", "sj55zd", "De3pzq", "Bkecrkj", "eoavqd", "d", "h", "state", "styles", "useButtonStyles_unstable", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAGaA,wBAAwB;eAAxBA;;IAoBIC,gCAAgC;eAAhCA;;;uBAvBwB;6BACA;AAElC,MAAMD,2BAA2B;IACpCE,MAAM;IACNC,MAAM;AACV;AACA;;CAEA,GAAI,MAAMC,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAH,MAAA;QAAAI,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;KAAA;AAAA;AAcX,MAAMZ,mCAAoCa,CAAAA;IACjD;IACA,MAAMC,SAASX;IACfU,QAAQ;QACJ,GAAGA,KAAK;QACR,GAAGE,IAAAA,qCAAwB,EAACF,MAAK;IACrC;IACAA,MAAMZ,IAAI,CAACe,SAAS,GAAGC,IAAAA,mBAAY,EAAClB,yBAAyBE,IAAI,EAAEa,OAAOb,IAAI,EAAEY,MAAMZ,IAAI,CAACe,SAAS;IACpG,IAAIH,MAAMX,IAAI,EAAE;QACZW,MAAMX,IAAI,CAACc,SAAS,GAAGC,IAAAA,mBAAY,EAAClB,yBAAyBG,IAAI,EAAEW,MAAMX,IAAI,CAACc,SAAS;IAC3F;IACA,OAAOH;AACX"}