"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useAvatarGroupPopoverContextValues_unstable", {
    enumerable: true,
    get: function() {
        return useAvatarGroupPopoverContextValues_unstable;
    }
});
const useAvatarGroupPopoverContextValues_unstable = (state)=>{
    const avatarGroup = {
        isOverflow: true,
        size: 24
    };
    return {
        avatarGroup
    };
};
