{"version": 3, "sources": ["../src/components/CarouselSlider/CarouselSlider.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n// import { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useCarouselSlider_unstable } from './useCarouselSlider';\nimport { renderCarouselSlider_unstable } from './renderCarouselSlider';\nimport { useCarouselSliderStyles_unstable } from './useCarouselSliderStyles.styles';\nimport type { CarouselSliderProps } from './CarouselSlider.types';\nimport { useCarouselSliderContextValues_unstable } from './CarouselSliderContext';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\n\n/**\n * CarouselSlider component - The viewport window that CarouselCards are contained within.\n */\nexport const CarouselSlider: ForwardRefComponent<CarouselSliderProps> = React.forwardRef((props, ref) => {\n  const state = useCarouselSlider_unstable(props, ref);\n\n  useCarouselSliderStyles_unstable(state);\n  useCustomStyleHook_unstable('useCarouselSliderStyles_unstable')(state);\n\n  const context = useCarouselSliderContextValues_unstable(state);\n  return renderCarouselSlider_unstable(state, context);\n});\n\nCarouselSlider.displayName = 'CarouselSlider';\n"], "names": ["CarouselSlider", "React", "forwardRef", "props", "ref", "state", "useCarouselSlider_unstable", "useCarouselSliderStyles_unstable", "useCustomStyleHook_unstable", "context", "useCarouselSliderContextValues_unstable", "renderCarouselSlider_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAaaA;;;eAAAA;;;;iEAbU;mCAGoB;sCACG;+CACG;uCAEO;qCACZ;AAKrC,MAAMA,iBAAAA,WAAAA,GAA2DC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IAC/F,MAAMC,QAAQC,IAAAA,6CAAAA,EAA2BH,OAAOC;IAEhDG,IAAAA,+DAAAA,EAAiCF;IACjCG,IAAAA,gDAAAA,EAA4B,oCAAoCH;IAEhE,MAAMI,UAAUC,IAAAA,8DAAAA,EAAwCL;IACxD,OAAOM,IAAAA,mDAAAA,EAA8BN,OAAOI;AAC9C;AAEAT,eAAeY,WAAW,GAAG"}