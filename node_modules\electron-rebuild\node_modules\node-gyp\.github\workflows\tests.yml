# https://docs.github.com/en/actions/using-github-hosted-runners/about-github-hosted-runners#supported-runners-and-hardware-resources
# TODO: Line 48, enable pytest --doctest-modules

name: Tests
on:
  push:
    branches:
      - main
      - release/v*
  pull_request:
    branches: 
      - main
      - release/v*
jobs:
  Lint_Python:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - run: pip install --user ruff
    - run: ruff --output-format=github --select="E,F,PLC,PLE,UP,W,YTT" --ignore="E721,PLC1901,S101,UP031" --target-version=py38 .
  Tests:
    strategy:
      fail-fast: false
      max-parallel: 15
      matrix:
        node: [16.x, 18.x, 20.x]
        python: ["3.8", "3.11"]
        os: [macos-latest, ubuntu-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node }}
      - name: Use Python ${{ matrix.python }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python }}
        env:
          PYTHON_VERSION: ${{ matrix.python }}  # Why do this?
      - name: Install Dependencies
        run: |
          npm install --no-progress
          pip install pytest
      - name: Set Windows environment
        if: startsWith(matrix.os, 'windows')
        run: |
          echo 'GYP_MSVS_VERSION=2015' >> $Env:GITHUB_ENV
          echo 'GYP_MSVS_OVERRIDE_PATH=C:\\Dummy' >> $Env:GITHUB_ENV
      - name: Run Python tests
        run: python -m pytest
      # - name: Run doctests with pytest
      #   run: python -m pytest --doctest-modules
      - name: Environment Information
        run: npx envinfo
      - name: Run Node tests
        run: npm test
