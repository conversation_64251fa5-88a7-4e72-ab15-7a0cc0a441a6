{"version": 3, "sources": ["../src/components/AvatarGroup/renderAvatarGroup.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport { AvatarGroupProvider } from '../../contexts/AvatarGroupContext';\nimport type { AvatarGroupState, AvatarGroupSlots, AvatarGroupContextValues } from './AvatarGroup.types';\n\n/**\n * Render the final JSX of AvatarGroup\n */\nexport const renderAvatarGroup_unstable = (state: AvatarGroupState, contextValues: AvatarGroupContextValues) => {\n  assertSlots<AvatarGroupSlots>(state);\n\n  return (\n    <AvatarGroupProvider value={contextValues.avatarGroup}>\n      <state.root />\n    </AvatarGroupProvider>\n  );\n};\n"], "names": ["assertSlots", "AvatarGroupProvider", "renderAvatarGroup_unstable", "state", "contextValues", "value", "avatarGroup", "root"], "rangeMappings": ";;;;;;;;;;;", "mappings": "AAAA,0BAA0B,GAC1B,iDAAiD;AAEjD,SAASA,WAAW,QAAQ,4BAA4B;AACxD,SAASC,mBAAmB,QAAQ,oCAAoC;AAGxE;;CAEC,GACD,OAAO,MAAMC,6BAA6B,CAACC,OAAyBC;IAClEJ,YAA8BG;IAE9B,qBACE,KAACF;QAAoBI,OAAOD,cAAcE,WAAW;kBACnD,cAAA,KAACH,MAAMI,IAAI;;AAGjB,EAAE"}