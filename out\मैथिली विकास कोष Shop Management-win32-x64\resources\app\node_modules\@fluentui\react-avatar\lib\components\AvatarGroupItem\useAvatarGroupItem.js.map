{"version": 3, "sources": ["../src/components/AvatarGroupItem/useAvatarGroupItem.ts"], "sourcesContent": ["import * as React from 'react';\nimport { Avatar } from '../Avatar/Avatar';\nimport { AvatarGroupContext, useAvatarGroupContext_unstable } from '../../contexts/AvatarGroupContext';\nimport { defaultAvatarGroupSize } from '../AvatarGroup/useAvatarGroup';\nimport { slot } from '@fluentui/react-utilities';\nimport { useHasParentContext } from '@fluentui/react-context-selector';\nimport type { AvatarGroupItemProps, AvatarGroupItemState } from './AvatarGroupItem.types';\n\n/**\n * Create the state required to render AvatarGroupItem.\n *\n * The returned state can be modified with hooks such as useAvatarGroupItemStyles_unstable,\n * before being passed to renderAvatarGroupItem_unstable.\n *\n * @param props - props from this instance of AvatarGroupItem\n * @param ref - reference to root HTMLElement of AvatarGroupItem\n */\nexport const useAvatarGroupItem_unstable = (\n  props: AvatarGroupItemProps,\n  ref: React.Ref<HTMLElement>,\n): AvatarGroupItemState => {\n  const groupIsOverflow = useAvatarGroupContext_unstable(ctx => ctx.isOverflow);\n  const groupSize = useAvatarGroupContext_unstable(ctx => ctx.size);\n  const layout = useAvatarGroupContext_unstable(ctx => ctx.layout);\n  // Since the primary slot is not an intrinsic element, getPartitionedNativeProps cannot be used here.\n  const { style, className, ...avatarSlotProps } = props;\n  const size = groupSize ?? defaultAvatarGroupSize;\n  const hasAvatarGroupContext = useHasParentContext(AvatarGroupContext);\n\n  if (process.env.NODE_ENV !== 'production' && !hasAvatarGroupContext) {\n    // eslint-disable-next-line no-console\n    console.warn('AvatarGroupItem must only be used inside an AvatarGroup component.');\n  }\n\n  return {\n    isOverflowItem: groupIsOverflow,\n    layout,\n    size,\n    components: {\n      root: groupIsOverflow ? 'li' : 'div',\n      avatar: Avatar,\n      overflowLabel: 'span',\n    },\n    root: slot.always(props.root, {\n      defaultProps: {\n        style,\n        className,\n      },\n      elementType: groupIsOverflow ? 'li' : 'div',\n    }),\n    avatar: slot.always(props.avatar, {\n      defaultProps: {\n        ref,\n        size,\n        color: 'colorful',\n        ...avatarSlotProps,\n      },\n      elementType: Avatar,\n    }),\n    overflowLabel: slot.always(props.overflowLabel, {\n      defaultProps: {\n        // Avatar already has its aria-label set to the name, this will prevent the name to be read twice.\n        'aria-hidden': true,\n        children: props.name,\n      },\n      elementType: 'span',\n    }),\n  };\n};\n"], "names": ["React", "Avatar", "AvatarGroupContext", "useAvatarGroupContext_unstable", "defaultAvatarGroupSize", "slot", "useHasParentContext", "useAvatarGroupItem_unstable", "props", "ref", "groupIsOverflow", "ctx", "isOverflow", "groupSize", "size", "layout", "style", "className", "avatarSlotProps", "hasAvatarGroupContext", "process", "env", "NODE_ENV", "console", "warn", "isOverflowItem", "components", "root", "avatar", "overflowLabel", "always", "defaultProps", "elementType", "color", "children", "name"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,kBAAkB,EAAEC,8BAA8B,QAAQ,oCAAoC;AACvG,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,IAAI,QAAQ,4BAA4B;AACjD,SAASC,mBAAmB,QAAQ,mCAAmC;AAGvE;;;;;;;;CAQC,GACD,OAAO,MAAMC,8BAA8B,CACzCC,OACAC;IAEA,MAAMC,kBAAkBP,+BAA+BQ,CAAAA,MAAOA,IAAIC,UAAU;IAC5E,MAAMC,YAAYV,+BAA+BQ,CAAAA,MAAOA,IAAIG,IAAI;IAChE,MAAMC,SAASZ,+BAA+BQ,CAAAA,MAAOA,IAAII,MAAM;IAC/D,qGAAqG;IACrG,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAE,GAAGC,iBAAiB,GAAGV;IACjD,MAAMM,OAAOD,sBAAAA,uBAAAA,YAAaT;IAC1B,MAAMe,wBAAwBb,oBAAoBJ;IAElD,IAAIkB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,CAACH,uBAAuB;QACnE,sCAAsC;QACtCI,QAAQC,IAAI,CAAC;IACf;IAEA,OAAO;QACLC,gBAAgBf;QAChBK;QACAD;QACAY,YAAY;YACVC,MAAMjB,kBAAkB,OAAO;YAC/BkB,QAAQ3B;YACR4B,eAAe;QACjB;QACAF,MAAMtB,KAAKyB,MAAM,CAACtB,MAAMmB,IAAI,EAAE;YAC5BI,cAAc;gBACZf;gBACAC;YACF;YACAe,aAAatB,kBAAkB,OAAO;QACxC;QACAkB,QAAQvB,KAAKyB,MAAM,CAACtB,MAAMoB,MAAM,EAAE;YAChCG,cAAc;gBACZtB;gBACAK;gBACAmB,OAAO;gBACP,GAAGf,eAAe;YACpB;YACAc,aAAa/B;QACf;QACA4B,eAAexB,KAAKyB,MAAM,CAACtB,MAAMqB,aAAa,EAAE;YAC9CE,cAAc;gBACZ,kGAAkG;gBAClG,eAAe;gBACfG,UAAU1B,MAAM2B,IAAI;YACtB;YACAH,aAAa;QACf;IACF;AACF,EAAE"}