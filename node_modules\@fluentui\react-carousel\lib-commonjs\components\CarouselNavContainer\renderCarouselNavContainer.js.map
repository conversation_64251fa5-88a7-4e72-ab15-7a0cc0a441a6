{"version": 3, "sources": ["../src/components/CarouselNavContainer/renderCarouselNavContainer.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { CarouselNavContainerState, CarouselNavContainerSlots } from './CarouselNavContainer.types';\n\n/**\n * Render the final JSX of CarouselNavContainer\n */\nexport const renderCarouselNavContainer_unstable = (state: CarouselNavContainerState) => {\n  assertSlots<CarouselNavContainerSlots>(state);\n\n  return (\n    <state.root>\n      {!state.autoplayTooltip && state.autoplay && <state.autoplay />}\n      {state.autoplayTooltip && state.autoplay && (\n        <state.autoplayTooltip>\n          <state.autoplay />\n        </state.autoplayTooltip>\n      )}\n      {!state.prevTooltip && state.prev && <state.prev />}\n      {state.prevTooltip && state.prev && (\n        <state.prevTooltip>\n          <state.prev />\n        </state.prevTooltip>\n      )}\n      {state.root.children}\n      {!state.nextTooltip && state.next && <state.next />}\n      {state.nextTooltip && state.next && (\n        <state.nextTooltip>\n          <state.next />\n        </state.nextTooltip>\n      )}\n    </state.root>\n  );\n};\n"], "names": ["renderCarouselNavContainer_unstable", "state", "assertSlots", "_jsxs", "root", "autoplayTooltip", "autoplay", "_jsx", "prevTooltip", "prev", "children", "nextTooltip", "next"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BASaA;;;eAAAA;;;4BARb;gCAE4B;AAMrB,MAAMA,sCAAsC,CAACC;IAClDC,IAAAA,2BAAAA,EAAuCD;IAEvC,OAAA,WAAA,GACEE,IAAAA,gBAAA,EAACF,MAAMG,IAAI,EAAA;;YACR,CAACH,MAAMI,eAAe,IAAIJ,MAAMK,QAAQ,IAAA,WAAA,GAAIC,IAAAA,eAAA,EAACN,MAAMK,QAAQ,EAAA,CAAA;YAC3DL,MAAMI,eAAe,IAAIJ,MAAMK,QAAQ,IAAA,WAAA,GACtCC,IAAAA,eAAA,EAACN,MAAMI,eAAe,EAAA;0BACpB,WAAA,GAAAE,IAAAA,eAAA,EAACN,MAAMK,QAAQ,EAAA,CAAA;;YAGlB,CAACL,MAAMO,WAAW,IAAIP,MAAMQ,IAAI,IAAA,WAAA,GAAIF,IAAAA,eAAA,EAACN,MAAMQ,IAAI,EAAA,CAAA;YAC/CR,MAAMO,WAAW,IAAIP,MAAMQ,IAAI,IAAA,WAAA,GAC9BF,IAAAA,eAAA,EAACN,MAAMO,WAAW,EAAA;0BAChB,WAAA,GAAAD,IAAAA,eAAA,EAACN,MAAMQ,IAAI,EAAA,CAAA;;YAGdR,MAAMG,IAAI,CAACM,QAAQ;YACnB,CAACT,MAAMU,WAAW,IAAIV,MAAMW,IAAI,IAAA,WAAA,GAAIL,IAAAA,eAAA,EAACN,MAAMW,IAAI,EAAA,CAAA;YAC/CX,MAAMU,WAAW,IAAIV,MAAMW,IAAI,IAAA,WAAA,GAC9BL,IAAAA,eAAA,EAACN,MAAMU,WAAW,EAAA;0BAChB,WAAA,GAAAJ,IAAAA,eAAA,EAACN,MAAMW,IAAI,EAAA,CAAA;;;;AAKrB"}