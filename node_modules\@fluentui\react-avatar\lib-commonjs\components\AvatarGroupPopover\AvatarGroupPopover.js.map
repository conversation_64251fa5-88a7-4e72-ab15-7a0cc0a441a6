{"version": 3, "sources": ["../src/components/AvatarGroupPopover/AvatarGroupPopover.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAvatarGroupPopover_unstable } from './renderAvatarGroupPopover';\nimport { useAvatarGroupPopoverContextValues_unstable } from './useAvatarGroupPopoverContextValues';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAvatarGroupPopover_unstable } from './useAvatarGroupPopover';\nimport { useAvatarGroupPopoverStyles_unstable } from './useAvatarGroupPopoverStyles.styles';\nimport type { AvatarGroupPopoverProps } from './AvatarGroupPopover.types';\n\n/**\n * The AvatarGroupPopover component provides a button with a Popover containing the children provided.\n */\nexport const AvatarGroupPopover: React.FC<AvatarGroupPopoverProps> = props => {\n  const state = useAvatarGroupPopover_unstable(props);\n  const contextValues = useAvatarGroupPopoverContextValues_unstable(state);\n\n  useAvatarGroupPopoverStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAvatarGroupPopoverStyles_unstable')(state);\n\n  return renderAvatarGroupPopover_unstable(state, contextValues);\n};\n\nAvatarGroupPopover.displayName = 'AvatarGroupPopover';\n"], "names": ["AvatarGroupPopover", "props", "state", "useAvatarGroupPopover_unstable", "contextValues", "useAvatarGroupPopoverContextValues_unstable", "useAvatarGroupPopoverStyles_unstable", "useCustomStyleHook_unstable", "renderAvatarGroupPopover_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAWaA;;;eAAAA;;;;iEAXU;0CAC2B;oDACU;qCAChB;uCACG;mDACM;AAM9C,MAAMA,qBAAwDC,CAAAA;IACnE,MAAMC,QAAQC,IAAAA,qDAAAA,EAA+BF;IAC7C,MAAMG,gBAAgBC,IAAAA,+EAAAA,EAA4CH;IAElEI,IAAAA,uEAAAA,EAAqCJ;IAErCK,IAAAA,gDAAAA,EAA4B,wCAAwCL;IAEpE,OAAOM,IAAAA,2DAAAA,EAAkCN,OAAOE;AAClD;AAEAJ,mBAAmBS,WAAW,GAAG"}