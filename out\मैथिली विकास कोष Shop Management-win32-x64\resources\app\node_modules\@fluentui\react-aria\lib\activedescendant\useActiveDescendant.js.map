{"version": 3, "sources": ["../src/activedescendant/useActiveDescendant.ts"], "sourcesContent": ["import * as React from 'react';\nimport { useEventCallback, useMergedRefs } from '@fluentui/react-utilities';\nimport { useOnKeyboardNavigationChange } from '@fluentui/react-tabster';\nimport { useOptionWalker } from './useOptionWalker';\nimport type { ActiveDescendantImperativeRef, ActiveDescendantOptions, UseActiveDescendantReturn } from './types';\nimport { ACTIVEDESCENDANT_ATTRIBUTE, ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE } from './constants';\nimport { scrollIntoView } from './scrollIntoView';\n\ninterface ActiveDescendantChangeEventDetail {\n  id: string;\n  previousId: string | null;\n}\n\nexport type ActiveDescendantChangeEvent = CustomEvent<ActiveDescendantChangeEventDetail>;\n\nexport const createActiveDescendantChangeEvent = (\n  detail: ActiveDescendantChangeEventDetail,\n): ActiveDescendantChangeEvent =>\n  new CustomEvent<ActiveDescendantChangeEventDetail>('activedescendantchange', {\n    bubbles: true,\n    cancelable: false,\n    composed: true,\n    detail,\n  });\n\nexport function useActiveDescendant<TActiveParentElement extends HTMLElement, TListboxElement extends HTMLElement>(\n  options: ActiveDescendantOptions,\n): UseActiveDescendantReturn<TActiveParentElement, TListboxElement> {\n  const { imperativeRef, matchOption: matchOptionUnstable } = options;\n  const focusVisibleRef = React.useRef(false);\n  const shouldShowFocusVisibleAttrRef = React.useRef(true);\n  const activeIdRef = React.useRef<string | null>(null);\n  const lastActiveIdRef = React.useRef<string | null>(null);\n  const activeParentRef = React.useRef<TActiveParentElement>(null);\n  const attributeVisibilityRef = React.useRef(true);\n\n  const removeAttribute = React.useCallback(() => {\n    activeParentRef.current?.removeAttribute('aria-activedescendant');\n  }, []);\n\n  const setAttribute = React.useCallback((id?: string) => {\n    if (id) {\n      activeIdRef.current = id;\n    }\n    if (attributeVisibilityRef.current && activeIdRef.current) {\n      activeParentRef.current?.setAttribute('aria-activedescendant', activeIdRef.current);\n    }\n  }, []);\n\n  useOnKeyboardNavigationChange(isNavigatingWithKeyboard => {\n    focusVisibleRef.current = isNavigatingWithKeyboard;\n\n    const active = getActiveDescendant();\n    if (!active) {\n      return;\n    }\n\n    if (isNavigatingWithKeyboard && shouldShowFocusVisibleAttrRef.current) {\n      active.setAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE, '');\n    } else {\n      active.removeAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE);\n    }\n  });\n\n  const matchOption = useEventCallback(matchOptionUnstable);\n  const listboxRef = React.useRef<TListboxElement>(null);\n  const { optionWalker, listboxCallbackRef } = useOptionWalker<TListboxElement>({ matchOption });\n\n  const getActiveDescendant = React.useCallback(() => {\n    return listboxRef.current?.querySelector<HTMLElement>(`#${activeIdRef.current}`);\n  }, [listboxRef]);\n\n  const setShouldShowFocusVisibleAttribute = React.useCallback(\n    (shouldShow: boolean) => {\n      shouldShowFocusVisibleAttrRef.current = shouldShow;\n\n      const active = getActiveDescendant();\n      if (!active) {\n        return;\n      }\n\n      if (shouldShow && focusVisibleRef.current) {\n        active.setAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE, '');\n      } else {\n        active.removeAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE);\n      }\n    },\n    [getActiveDescendant],\n  );\n\n  const blurActiveDescendant = React.useCallback(() => {\n    const active = getActiveDescendant();\n    if (active) {\n      active.removeAttribute(ACTIVEDESCENDANT_ATTRIBUTE);\n      active.removeAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE);\n    }\n\n    removeAttribute();\n    lastActiveIdRef.current = activeIdRef.current;\n    activeIdRef.current = null;\n    return active?.id ?? null;\n  }, [getActiveDescendant, removeAttribute]);\n\n  const focusActiveDescendant = React.useCallback(\n    (nextActive: HTMLElement | null) => {\n      if (!nextActive) {\n        return;\n      }\n\n      const previousActiveId = blurActiveDescendant();\n\n      scrollIntoView(nextActive);\n      setAttribute(nextActive.id);\n      nextActive.setAttribute(ACTIVEDESCENDANT_ATTRIBUTE, '');\n\n      if (focusVisibleRef.current && shouldShowFocusVisibleAttrRef.current) {\n        nextActive.setAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE, '');\n      }\n\n      const event = createActiveDescendantChangeEvent({ id: nextActive.id, previousId: previousActiveId });\n      nextActive.dispatchEvent(event);\n    },\n    [blurActiveDescendant, setAttribute],\n  );\n\n  const controller: ActiveDescendantImperativeRef = React.useMemo(\n    () => ({\n      first: ({ passive } = {}) => {\n        const first = optionWalker.first();\n        if (!passive) {\n          focusActiveDescendant(first);\n        }\n\n        return first?.id;\n      },\n      last: ({ passive } = {}) => {\n        const last = optionWalker.last();\n        if (!passive) {\n          focusActiveDescendant(last);\n        }\n\n        return last?.id;\n      },\n      next: ({ passive } = {}) => {\n        const active = getActiveDescendant();\n        if (!active) {\n          return;\n        }\n\n        optionWalker.setCurrent(active);\n        const next = optionWalker.next();\n        if (!passive) {\n          focusActiveDescendant(next);\n        }\n\n        return next?.id;\n      },\n      prev: ({ passive } = {}) => {\n        const active = getActiveDescendant();\n        if (!active) {\n          return;\n        }\n\n        optionWalker.setCurrent(active);\n        const next = optionWalker.prev();\n\n        if (!passive) {\n          focusActiveDescendant(next);\n        }\n\n        return next?.id;\n      },\n      blur: () => {\n        blurActiveDescendant();\n      },\n      active: () => {\n        return getActiveDescendant()?.id;\n      },\n      focus: (id: string) => {\n        if (!listboxRef.current) {\n          return;\n        }\n\n        const target = listboxRef.current.querySelector<HTMLElement>(`#${id}`);\n        if (target) {\n          focusActiveDescendant(target);\n        }\n      },\n      focusLastActive: () => {\n        if (!listboxRef.current || !lastActiveIdRef.current) {\n          return;\n        }\n\n        const target = listboxRef.current.querySelector<HTMLElement>(`#${lastActiveIdRef.current}`);\n        if (target) {\n          focusActiveDescendant(target);\n          return true;\n        }\n      },\n      find(predicate, { passive, startFrom } = {}) {\n        const target = optionWalker.find(predicate, startFrom);\n        if (!passive) {\n          focusActiveDescendant(target);\n        }\n\n        return target?.id;\n      },\n      scrollActiveIntoView: () => {\n        if (!listboxRef.current) {\n          return;\n        }\n\n        const active = getActiveDescendant();\n        if (!active) {\n          return;\n        }\n\n        scrollIntoView(active);\n      },\n      showAttributes() {\n        attributeVisibilityRef.current = true;\n        setAttribute();\n      },\n      hideAttributes() {\n        attributeVisibilityRef.current = false;\n        removeAttribute();\n      },\n      showFocusVisibleAttributes() {\n        setShouldShowFocusVisibleAttribute(true);\n      },\n      hideFocusVisibleAttributes() {\n        setShouldShowFocusVisibleAttribute(false);\n      },\n    }),\n    [\n      optionWalker,\n      listboxRef,\n      setAttribute,\n      removeAttribute,\n      focusActiveDescendant,\n      blurActiveDescendant,\n      getActiveDescendant,\n      setShouldShowFocusVisibleAttribute,\n    ],\n  );\n\n  React.useImperativeHandle(imperativeRef, () => controller);\n\n  return { listboxRef: useMergedRefs(listboxRef, listboxCallbackRef), activeParentRef, controller };\n}\n"], "names": ["React", "useEventCallback", "useMergedRefs", "useOnKeyboardNavigationChange", "useOptionWalker", "ACTIVEDESCENDANT_ATTRIBUTE", "ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE", "scrollIntoView", "createActiveDescendantChangeEvent", "detail", "CustomEvent", "bubbles", "cancelable", "composed", "useActiveDescendant", "options", "imperativeRef", "matchOption", "matchOptionUnstable", "focusVisibleRef", "useRef", "shouldShowFocusVisibleAttrRef", "activeIdRef", "lastActiveIdRef", "activeParentRef", "attributeVisibilityRef", "removeAttribute", "useCallback", "current", "setAttribute", "id", "isNavigatingWithKeyboard", "active", "getActiveDescendant", "listboxRef", "<PERSON><PERSON><PERSON><PERSON>", "listboxCallbackRef", "querySelector", "setShouldShowFocusVisibleAttribute", "shouldShow", "blurActiveDescendant", "focusActiveDescendant", "nextActive", "previousActiveId", "event", "previousId", "dispatchEvent", "controller", "useMemo", "first", "passive", "last", "next", "setCurrent", "prev", "blur", "focus", "target", "focusLastActive", "find", "predicate", "startFrom", "scrollActiveIntoView", "showAttributes", "hideAttributes", "showFocusVisibleAttributes", "hideFocusVisibleAttributes", "useImperativeHandle"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,4BAA4B;AAC5E,SAASC,6BAA6B,QAAQ,0BAA0B;AACxE,SAASC,eAAe,QAAQ,oBAAoB;AAEpD,SAASC,0BAA0B,EAAEC,uCAAuC,QAAQ,cAAc;AAClG,SAASC,cAAc,QAAQ,mBAAmB;AASlD,OAAO,MAAMC,oCAAoC,CAC/CC,SAEA,IAAIC,YAA+C,0BAA0B;QAC3EC,SAAS;QACTC,YAAY;QACZC,UAAU;QACVJ;IACF,GAAG;AAEL,OAAO,SAASK,oBACdC,OAAgC;IAEhC,MAAM,EAAEC,aAAa,EAAEC,aAAaC,mBAAmB,EAAE,GAAGH;IAC5D,MAAMI,kBAAkBnB,MAAMoB,MAAM,CAAC;IACrC,MAAMC,gCAAgCrB,MAAMoB,MAAM,CAAC;IACnD,MAAME,cAActB,MAAMoB,MAAM,CAAgB;IAChD,MAAMG,kBAAkBvB,MAAMoB,MAAM,CAAgB;IACpD,MAAMI,kBAAkBxB,MAAMoB,MAAM,CAAuB;IAC3D,MAAMK,yBAAyBzB,MAAMoB,MAAM,CAAC;IAE5C,MAAMM,kBAAkB1B,MAAM2B,WAAW,CAAC;YACxCH;SAAAA,2BAAAA,gBAAgBI,OAAO,cAAvBJ,+CAAAA,yBAAyBE,eAAe,CAAC;IAC3C,GAAG,EAAE;IAEL,MAAMG,eAAe7B,MAAM2B,WAAW,CAAC,CAACG;QACtC,IAAIA,IAAI;YACNR,YAAYM,OAAO,GAAGE;QACxB;QACA,IAAIL,uBAAuBG,OAAO,IAAIN,YAAYM,OAAO,EAAE;gBACzDJ;aAAAA,2BAAAA,gBAAgBI,OAAO,cAAvBJ,+CAAAA,yBAAyBK,YAAY,CAAC,yBAAyBP,YAAYM,OAAO;QACpF;IACF,GAAG,EAAE;IAELzB,8BAA8B4B,CAAAA;QAC5BZ,gBAAgBS,OAAO,GAAGG;QAE1B,MAAMC,SAASC;QACf,IAAI,CAACD,QAAQ;YACX;QACF;QAEA,IAAID,4BAA4BV,8BAA8BO,OAAO,EAAE;YACrEI,OAAOH,YAAY,CAACvB,yCAAyC;QAC/D,OAAO;YACL0B,OAAON,eAAe,CAACpB;QACzB;IACF;IAEA,MAAMW,cAAchB,iBAAiBiB;IACrC,MAAMgB,aAAalC,MAAMoB,MAAM,CAAkB;IACjD,MAAM,EAAEe,YAAY,EAAEC,kBAAkB,EAAE,GAAGhC,gBAAiC;QAAEa;IAAY;IAE5F,MAAMgB,sBAAsBjC,MAAM2B,WAAW,CAAC;YACrCO;QAAP,QAAOA,sBAAAA,WAAWN,OAAO,cAAlBM,0CAAAA,oBAAoBG,aAAa,CAAc,CAAC,CAAC,EAAEf,YAAYM,OAAO,CAAC,CAAC;IACjF,GAAG;QAACM;KAAW;IAEf,MAAMI,qCAAqCtC,MAAM2B,WAAW,CAC1D,CAACY;QACClB,8BAA8BO,OAAO,GAAGW;QAExC,MAAMP,SAASC;QACf,IAAI,CAACD,QAAQ;YACX;QACF;QAEA,IAAIO,cAAcpB,gBAAgBS,OAAO,EAAE;YACzCI,OAAOH,YAAY,CAACvB,yCAAyC;QAC/D,OAAO;YACL0B,OAAON,eAAe,CAACpB;QACzB;IACF,GACA;QAAC2B;KAAoB;IAGvB,MAAMO,uBAAuBxC,MAAM2B,WAAW,CAAC;QAC7C,MAAMK,SAASC;QACf,IAAID,QAAQ;YACVA,OAAON,eAAe,CAACrB;YACvB2B,OAAON,eAAe,CAACpB;QACzB;QAEAoB;QACAH,gBAAgBK,OAAO,GAAGN,YAAYM,OAAO;QAC7CN,YAAYM,OAAO,GAAG;YACfI;QAAP,OAAOA,CAAAA,aAAAA,mBAAAA,6BAAAA,OAAQF,EAAE,cAAVE,wBAAAA,aAAc;IACvB,GAAG;QAACC;QAAqBP;KAAgB;IAEzC,MAAMe,wBAAwBzC,MAAM2B,WAAW,CAC7C,CAACe;QACC,IAAI,CAACA,YAAY;YACf;QACF;QAEA,MAAMC,mBAAmBH;QAEzBjC,eAAemC;QACfb,aAAaa,WAAWZ,EAAE;QAC1BY,WAAWb,YAAY,CAACxB,4BAA4B;QAEpD,IAAIc,gBAAgBS,OAAO,IAAIP,8BAA8BO,OAAO,EAAE;YACpEc,WAAWb,YAAY,CAACvB,yCAAyC;QACnE;QAEA,MAAMsC,QAAQpC,kCAAkC;YAAEsB,IAAIY,WAAWZ,EAAE;YAAEe,YAAYF;QAAiB;QAClGD,WAAWI,aAAa,CAACF;IAC3B,GACA;QAACJ;QAAsBX;KAAa;IAGtC,MAAMkB,aAA4C/C,MAAMgD,OAAO,CAC7D,IAAO,CAAA;YACLC,OAAO,CAAC,EAAEC,OAAO,EAAE,GAAG,CAAC,CAAC;gBACtB,MAAMD,QAAQd,aAAac,KAAK;gBAChC,IAAI,CAACC,SAAS;oBACZT,sBAAsBQ;gBACxB;gBAEA,OAAOA,kBAAAA,4BAAAA,MAAOnB,EAAE;YAClB;YACAqB,MAAM,CAAC,EAAED,OAAO,EAAE,GAAG,CAAC,CAAC;gBACrB,MAAMC,OAAOhB,aAAagB,IAAI;gBAC9B,IAAI,CAACD,SAAS;oBACZT,sBAAsBU;gBACxB;gBAEA,OAAOA,iBAAAA,2BAAAA,KAAMrB,EAAE;YACjB;YACAsB,MAAM,CAAC,EAAEF,OAAO,EAAE,GAAG,CAAC,CAAC;gBACrB,MAAMlB,SAASC;gBACf,IAAI,CAACD,QAAQ;oBACX;gBACF;gBAEAG,aAAakB,UAAU,CAACrB;gBACxB,MAAMoB,OAAOjB,aAAaiB,IAAI;gBAC9B,IAAI,CAACF,SAAS;oBACZT,sBAAsBW;gBACxB;gBAEA,OAAOA,iBAAAA,2BAAAA,KAAMtB,EAAE;YACjB;YACAwB,MAAM,CAAC,EAAEJ,OAAO,EAAE,GAAG,CAAC,CAAC;gBACrB,MAAMlB,SAASC;gBACf,IAAI,CAACD,QAAQ;oBACX;gBACF;gBAEAG,aAAakB,UAAU,CAACrB;gBACxB,MAAMoB,OAAOjB,aAAamB,IAAI;gBAE9B,IAAI,CAACJ,SAAS;oBACZT,sBAAsBW;gBACxB;gBAEA,OAAOA,iBAAAA,2BAAAA,KAAMtB,EAAE;YACjB;YACAyB,MAAM;gBACJf;YACF;YACAR,QAAQ;oBACCC;gBAAP,QAAOA,uBAAAA,mCAAAA,2CAAAA,qBAAuBH,EAAE;YAClC;YACA0B,OAAO,CAAC1B;gBACN,IAAI,CAACI,WAAWN,OAAO,EAAE;oBACvB;gBACF;gBAEA,MAAM6B,SAASvB,WAAWN,OAAO,CAACS,aAAa,CAAc,CAAC,CAAC,EAAEP,GAAG,CAAC;gBACrE,IAAI2B,QAAQ;oBACVhB,sBAAsBgB;gBACxB;YACF;YACAC,iBAAiB;gBACf,IAAI,CAACxB,WAAWN,OAAO,IAAI,CAACL,gBAAgBK,OAAO,EAAE;oBACnD;gBACF;gBAEA,MAAM6B,SAASvB,WAAWN,OAAO,CAACS,aAAa,CAAc,CAAC,CAAC,EAAEd,gBAAgBK,OAAO,CAAC,CAAC;gBAC1F,IAAI6B,QAAQ;oBACVhB,sBAAsBgB;oBACtB,OAAO;gBACT;YACF;YACAE,MAAKC,SAAS,EAAE,EAAEV,OAAO,EAAEW,SAAS,EAAE,GAAG,CAAC,CAAC;gBACzC,MAAMJ,SAAStB,aAAawB,IAAI,CAACC,WAAWC;gBAC5C,IAAI,CAACX,SAAS;oBACZT,sBAAsBgB;gBACxB;gBAEA,OAAOA,mBAAAA,6BAAAA,OAAQ3B,EAAE;YACnB;YACAgC,sBAAsB;gBACpB,IAAI,CAAC5B,WAAWN,OAAO,EAAE;oBACvB;gBACF;gBAEA,MAAMI,SAASC;gBACf,IAAI,CAACD,QAAQ;oBACX;gBACF;gBAEAzB,eAAeyB;YACjB;YACA+B;gBACEtC,uBAAuBG,OAAO,GAAG;gBACjCC;YACF;YACAmC;gBACEvC,uBAAuBG,OAAO,GAAG;gBACjCF;YACF;YACAuC;gBACE3B,mCAAmC;YACrC;YACA4B;gBACE5B,mCAAmC;YACrC;QACF,CAAA,GACA;QACEH;QACAD;QACAL;QACAH;QACAe;QACAD;QACAP;QACAK;KACD;IAGHtC,MAAMmE,mBAAmB,CAACnD,eAAe,IAAM+B;IAE/C,OAAO;QAAEb,YAAYhC,cAAcgC,YAAYE;QAAqBZ;QAAiBuB;IAAW;AAClG"}