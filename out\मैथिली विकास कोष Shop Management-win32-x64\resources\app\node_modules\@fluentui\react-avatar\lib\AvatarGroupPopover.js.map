{"version": 3, "sources": ["../src/AvatarGroupPopover.ts"], "sourcesContent": ["export type {\n  AvatarGroupPopoverProps,\n  AvatarGroupPopoverSlots,\n  AvatarGroupPopoverState,\n} from './components/AvatarGroupPopover/index';\nexport {\n  AvatarGroupPopover,\n  avatarGroupPopoverClassNames,\n  renderAvatarGroupPopover_unstable,\n  useAvatarGroupPopoverContextValues_unstable,\n  useAvatarGroupPopoverStyles_unstable,\n  useAvatarGroupPopover_unstable,\n} from './components/AvatarGroupPopover/index';\n"], "names": ["AvatarGroupPopover", "avatarGroupPopoverClassNames", "renderAvatarGroupPopover_unstable", "useAvatarGroupPopoverContextValues_unstable", "useAvatarGroupPopoverStyles_unstable", "useAvatarGroupPopover_unstable"], "rangeMappings": "", "mappings": "AAKA,SACEA,kBAAkB,EAClBC,4BAA4B,EAC5BC,iCAAiC,EACjCC,2CAA2C,EAC3CC,oCAAoC,EACpCC,8BAA8B,QACzB,wCAAwC"}