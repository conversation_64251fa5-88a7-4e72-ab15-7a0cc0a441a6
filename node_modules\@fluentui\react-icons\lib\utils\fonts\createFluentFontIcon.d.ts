import * as React from 'react';
import { FluentIconsProps } from '../FluentIconsProps.types';
export declare const enum FontFile {
    Filled = 0,
    Regular = 1,
    Resizable = 2,
    Light = 3
}
export declare type CreateFluentFontIconOptions = {
    flipInRtl?: boolean;
};
export declare function createFluentFontIcon(displayName: string, codepoint: string, font: FontFile, fontSize?: number, options?: CreateFluentFontIconOptions): React.FC<FluentIconsProps<React.HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
