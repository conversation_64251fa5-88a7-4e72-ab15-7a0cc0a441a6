{"version": 3, "sources": ["../src/components/CarouselCard/CarouselCard.types.ts"], "sourcesContent": ["import type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\n\nexport type CarouselCardSlots = {\n  root: Slot<'div'>;\n};\n\n/**\n * CarouselCard Props\n */\nexport type CarouselCardProps = ComponentProps<CarouselCardSlots> & {\n  /**\n   * Sets the card styling to be responsive based on content.\n   */\n  autoSize?: boolean;\n};\n\n/**\n * State used in rendering CarouselCard\n */\nexport type CarouselCardState = ComponentState<CarouselCardSlots> & Pick<CarouselCardProps, 'autoSize'>;\n"], "names": [], "rangeMappings": ";;", "mappings": "AAgBA;;CAEC"}