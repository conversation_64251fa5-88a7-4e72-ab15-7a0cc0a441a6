{"version": 3, "sources": ["../src/components/CarouselSlider/CarouselSliderContext.ts"], "sourcesContent": ["import * as React from 'react';\nimport { CarouselSliderContextValue, CarouselSliderState } from './CarouselSlider.types';\n\nconst carouselSliderContext = React.createContext<CarouselSliderContextValue | undefined>(undefined);\n\nexport const carouselSliderContextDefaultValue: CarouselSliderContextValue = {\n  cardFocus: false,\n};\n\nexport const useCarouselSliderContext = () =>\n  React.useContext(carouselSliderContext) ?? carouselSliderContextDefaultValue;\n\nexport const CarouselSliderContextProvider = carouselSliderContext.Provider;\n\n/**\n * Context shared between CarouselSlider and its children components\n */\nexport type CarouselSliderContextValues = {\n  carouselSlider: CarouselSliderContextValue;\n};\n\nexport function useCarouselSliderContextValues_unstable(state: CarouselSliderState): CarouselSliderContextValues {\n  const { cardFocus } = state;\n  const carouselSlider = React.useMemo(\n    () => ({\n      cardFocus,\n    }),\n    [cardFocus],\n  );\n\n  return { carouselSlider };\n}\n"], "names": ["CarouselSliderContextProvider", "carouselSliderContextDefaultValue", "useCarouselSliderContext", "useCarouselSliderContextValues_unstable", "carouselSliderContext", "React", "createContext", "undefined", "cardFocus", "useContext", "Provider", "state", "carouselSlider", "useMemo"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAYaA,6BAAAA;eAAAA;;IAPAC,iCAAAA;eAAAA;;IAIAC,wBAAAA;eAAAA;;IAYGC,uCAAAA;eAAAA;;;;iEArBO;AAGvB,MAAMC,sCAAwBC,OAAMC,aAAa,CAAyCC;AAEnF,MAAMN,oCAAgE;IAC3EO,WAAW;AACb;AAEO,MAAMN,2BAA2B;QACtCG;WAAAA,CAAAA,oBAAAA,OAAMI,UAAU,CAACL,sBAAAA,MAAAA,QAAjBC,sBAAAA,KAAAA,IAAAA,oBAA2CJ;AAAgC;AAEtE,MAAMD,gCAAgCI,sBAAsBM,QAAQ;AASpE,SAASP,wCAAwCQ,KAA0B;IAChF,MAAM,EAAEH,SAAS,EAAE,GAAGG;IACtB,MAAMC,iBAAiBP,OAAMQ,OAAO,CAClC,IAAO,CAAA;YACLL;QACF,CAAA,GACA;QAACA;KAAU;IAGb,OAAO;QAAEI;IAAe;AAC1B"}