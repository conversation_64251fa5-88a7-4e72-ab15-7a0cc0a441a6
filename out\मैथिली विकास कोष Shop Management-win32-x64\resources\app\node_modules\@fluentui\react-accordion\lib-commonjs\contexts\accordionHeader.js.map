{"version": 3, "sources": ["../src/contexts/accordionHeader.ts"], "sourcesContent": ["import * as React from 'react';\nimport type {\n  AccordionHeaderExpandIconPosition,\n  AccordionHeaderSize,\n} from '../components/AccordionHeader/AccordionHeader.types';\n\nexport type AccordionHeaderContextValue = {\n  disabled: boolean;\n  open: boolean;\n  expandIconPosition: AccordionHeaderExpandIconPosition;\n  size: AccordionHeaderSize;\n};\n\nconst AccordionHeaderContext = React.createContext<AccordionHeaderContextValue | undefined>(\n  undefined,\n) as React.Context<AccordionHeaderContextValue>;\n\nconst accordionHeaderContextDefaultValue = {\n  open: false,\n  disabled: false,\n  size: 'medium',\n  expandIconPosition: 'start',\n};\n\nexport const { Provider: AccordionHeaderProvider } = AccordionHeaderContext;\n\nexport const useAccordionHeaderContext_unstable = () =>\n  React.useContext(AccordionHeaderContext) ?? accordionHeaderContextDefaultValue;\n"], "names": ["AccordionHeaderProvider", "useAccordionHeaderContext_unstable", "AccordionHeaderContext", "React", "createContext", "undefined", "accordionHeaderContextDefaultValue", "open", "disabled", "size", "expandIconPosition", "Provider", "useContext"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAwByBA,uBAAuB;eAAvBA;;IAEZC,kCAAAA;eAAAA;;;;iEA1BU;AAavB,MAAMC,uCAAyBC,OAAMC,aAAa,CAChDC;AAGF,MAAMC,qCAAqC;IACzCC,MAAM;IACNC,UAAU;IACVC,MAAM;IACNC,oBAAoB;AACtB;AAEO,MAAM,EAAEC,UAAUX,uBAAuB,EAAE,GAAGE;AAE9C,MAAMD,qCAAqC;QAChDE;WAAAA,CAAAA,oBAAAA,OAAMS,UAAU,CAACV,uBAAAA,MAAAA,QAAjBC,sBAAAA,KAAAA,IAAAA,oBAA4CG;AAAiC"}