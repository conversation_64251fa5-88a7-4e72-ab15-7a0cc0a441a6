import type { FluentIcon } from "../utils/createFluentIcon";
export declare const WindowShield24Filled: FluentIcon;
export declare const WindowShield24Regular: FluentIcon;
export declare const WindowText16Filled: FluentIcon;
export declare const WindowText16Regular: FluentIcon;
export declare const WindowText20Filled: FluentIcon;
export declare const WindowText20Regular: FluentIcon;
export declare const WindowText24Filled: FluentIcon;
export declare const WindowText24Regular: FluentIcon;
export declare const WindowText28Filled: FluentIcon;
export declare const WindowText28Regular: FluentIcon;
export declare const WindowWrench16Filled: FluentIcon;
export declare const WindowWrench16Regular: FluentIcon;
export declare const WindowWrench20Filled: FluentIcon;
export declare const WindowWrench20Regular: FluentIcon;
export declare const WindowWrench24Filled: FluentIcon;
export declare const WindowWrench24Regular: FluentIcon;
export declare const WindowWrench28Filled: FluentIcon;
export declare const WindowWrench28Regular: FluentIcon;
export declare const WindowWrench32Filled: FluentIcon;
export declare const WindowWrench32Regular: FluentIcon;
export declare const WindowWrench48Filled: FluentIcon;
export declare const WindowWrench48Regular: FluentIcon;
export declare const Wrench16Color: FluentIcon;
export declare const Wrench16Filled: FluentIcon;
export declare const Wrench16Regular: FluentIcon;
export declare const Wrench20Color: FluentIcon;
export declare const Wrench20Filled: FluentIcon;
export declare const Wrench20Regular: FluentIcon;
export declare const Wrench24Color: FluentIcon;
export declare const Wrench24Filled: FluentIcon;
export declare const Wrench24Regular: FluentIcon;
export declare const WrenchScrewdriver20Color: FluentIcon;
export declare const WrenchScrewdriver20Filled: FluentIcon;
export declare const WrenchScrewdriver20Regular: FluentIcon;
export declare const WrenchScrewdriver24Color: FluentIcon;
export declare const WrenchScrewdriver24Filled: FluentIcon;
export declare const WrenchScrewdriver24Regular: FluentIcon;
export declare const WrenchScrewdriver32Color: FluentIcon;
export declare const WrenchScrewdriver32Filled: FluentIcon;
export declare const WrenchScrewdriver32Light: FluentIcon;
export declare const WrenchScrewdriver32Regular: FluentIcon;
export declare const WrenchSettings20Filled: FluentIcon;
export declare const WrenchSettings20Regular: FluentIcon;
export declare const WrenchSettings24Filled: FluentIcon;
export declare const WrenchSettings24Regular: FluentIcon;
export declare const XboxConsole20Filled: FluentIcon;
export declare const XboxConsole20Regular: FluentIcon;
export declare const XboxConsole24Filled: FluentIcon;
export declare const XboxConsole24Regular: FluentIcon;
export declare const XboxController16Filled: FluentIcon;
export declare const XboxController16Regular: FluentIcon;
export declare const XboxController20Filled: FluentIcon;
export declare const XboxController20Regular: FluentIcon;
export declare const XboxController24Filled: FluentIcon;
export declare const XboxController24Regular: FluentIcon;
export declare const XboxController28Filled: FluentIcon;
export declare const XboxController28Regular: FluentIcon;
export declare const XboxController32Filled: FluentIcon;
export declare const XboxController32Regular: FluentIcon;
export declare const XboxController48Filled: FluentIcon;
export declare const XboxController48Regular: FluentIcon;
export declare const XboxControllerError20Filled: FluentIcon;
export declare const XboxControllerError20Regular: FluentIcon;
export declare const XboxControllerError24Filled: FluentIcon;
export declare const XboxControllerError24Regular: FluentIcon;
export declare const XboxControllerError32Filled: FluentIcon;
export declare const XboxControllerError32Regular: FluentIcon;
export declare const XboxControllerError48Filled: FluentIcon;
export declare const XboxControllerError48Regular: FluentIcon;
export declare const Xray20Filled: FluentIcon;
export declare const Xray20Regular: FluentIcon;
export declare const Xray24Filled: FluentIcon;
export declare const Xray24Regular: FluentIcon;
export declare const ZoomFit16Filled: FluentIcon;
export declare const ZoomFit16Regular: FluentIcon;
export declare const ZoomFit20Filled: FluentIcon;
export declare const ZoomFit20Regular: FluentIcon;
export declare const ZoomFit24Filled: FluentIcon;
export declare const ZoomFit24Regular: FluentIcon;
export declare const ZoomIn16Filled: FluentIcon;
export declare const ZoomIn16Regular: FluentIcon;
export declare const ZoomIn20Filled: FluentIcon;
export declare const ZoomIn20Regular: FluentIcon;
export declare const ZoomIn24Filled: FluentIcon;
export declare const ZoomIn24Regular: FluentIcon;
export declare const ZoomIn32Filled: FluentIcon;
export declare const ZoomIn32Light: FluentIcon;
export declare const ZoomIn32Regular: FluentIcon;
export declare const ZoomOut16Filled: FluentIcon;
export declare const ZoomOut16Regular: FluentIcon;
export declare const ZoomOut20Filled: FluentIcon;
export declare const ZoomOut20Regular: FluentIcon;
export declare const ZoomOut24Filled: FluentIcon;
export declare const ZoomOut24Regular: FluentIcon;
export declare const ZoomOut32Filled: FluentIcon;
export declare const ZoomOut32Light: FluentIcon;
export declare const ZoomOut32Regular: FluentIcon;
