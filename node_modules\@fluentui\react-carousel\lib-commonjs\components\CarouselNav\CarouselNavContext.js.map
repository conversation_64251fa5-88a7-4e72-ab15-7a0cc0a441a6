{"version": 3, "sources": ["../src/components/CarouselNav/CarouselNavContext.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { CarouselNavContextValue, CarouselNavState } from './CarouselNav.types';\n\nconst carouselNavContext = React.createContext<CarouselNavContextValue | undefined>(undefined);\n\nexport const carouselNavContextDefaultValue: CarouselNavContextValue = {\n  appearance: undefined,\n};\n\nexport const useCarouselNavContext = () => React.useContext(carouselNavContext) ?? carouselNavContextDefaultValue;\n\nexport const CarouselNavContextProvider = carouselNavContext.Provider;\n\n/**\n * Context shared between CarouselNav and its children components\n */\nexport type CarouselNavContextValues = {\n  carouselNav: CarouselNavContextValue;\n};\n\nexport function useCarouselNavContextValues_unstable(state: CarouselNavState): CarouselNavContextValues {\n  const { appearance } = state;\n  const carouselNav = React.useMemo(() => ({ appearance }), [appearance]);\n\n  return { carouselNav };\n}\n"], "names": ["CarouselNavContextProvider", "carouselNavContextDefaultValue", "useCarouselNavContext", "useCarouselNavContextValues_unstable", "carouselNavContext", "React", "createContext", "undefined", "appearance", "useContext", "Provider", "state", "carouselNav", "useMemo"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAWaA,0BAAAA;eAAAA;;IANAC,8BAAAA;eAAAA;;IAIAC,qBAAAA;eAAAA;;IAWGC,oCAAAA;eAAAA;;;;iEApBO;AAGvB,MAAMC,mCAAqBC,OAAMC,aAAa,CAAsCC;AAE7E,MAAMN,iCAA0D;IACrEO,YAAYD;AACd;AAEO,MAAML,wBAAwB;QAAMG;WAAAA,CAAAA,oBAAAA,OAAMI,UAAU,CAACL,mBAAAA,MAAAA,QAAjBC,sBAAAA,KAAAA,IAAAA,oBAAwCJ;AAA6B;AAEzG,MAAMD,6BAA6BI,mBAAmBM,QAAQ;AAS9D,SAASP,qCAAqCQ,KAAuB;IAC1E,MAAM,EAAEH,UAAU,EAAE,GAAGG;IACvB,MAAMC,cAAcP,OAAMQ,OAAO,CAAC,IAAO,CAAA;YAAEL;QAAW,CAAA,GAAI;QAACA;KAAW;IAEtE,OAAO;QAAEI;IAAY;AACvB"}