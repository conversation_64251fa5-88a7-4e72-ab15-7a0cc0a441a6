{"version": 3, "sources": ["../src/AriaLiveAnnouncer/useAriaLiveAnnouncer.ts"], "sourcesContent": ["import * as React from 'react';\nimport { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nimport { useDomAnnounce_unstable } from './useDomAnnounce';\nimport { useAriaNotifyAnnounce_unstable } from './useAriaNotifyAnnounce';\n\nimport type { AriaLiveAnnouncerState, AriaLiveAnnouncerProps } from './AriaLiveAnnouncer.types';\n\nexport const useAriaLiveAnnouncer_unstable = (props: AriaLiveAnnouncerProps): AriaLiveAnnouncerState => {\n  const { targetDocument } = useFluent();\n  const domAnnounce = useDomAnnounce_unstable();\n  const ariaNotifyAnnounce = useAriaNotifyAnnounce_unstable();\n\n  const announce = React.useMemo(() => {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const supportsAriaNotify = typeof (targetDocument as any)?.ariaNotify === 'function';\n    return supportsAriaNotify ? ariaNotifyAnnounce : domAnnounce;\n  }, [targetDocument, ariaNotifyAnnounce, domAnnounce]);\n\n  return {\n    announce,\n    children: props.children,\n  };\n};\n"], "names": ["React", "useFluent_unstable", "useFluent", "useDomAnnounce_unstable", "useAriaNotifyAnnounce_unstable", "useAriaLiveAnnouncer_unstable", "props", "targetDocument", "domAnnounce", "ariaNotifyAnnounce", "announce", "useMemo", "supportsAriaNotify", "ariaNotify", "children"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,sBAAsBC,SAAS,QAAQ,kCAAkC;AAClF,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,8BAA8B,QAAQ,0BAA0B;AAIzE,OAAO,MAAMC,gCAAgC,CAACC;IAC5C,MAAM,EAAEC,cAAc,EAAE,GAAGL;IAC3B,MAAMM,cAAcL;IACpB,MAAMM,qBAAqBL;IAE3B,MAAMM,WAAWV,MAAMW,OAAO,CAAC;QAC7B,8DAA8D;QAC9D,MAAMC,qBAAqB,QAAQL,2BAAAA,qCAAD,AAACA,eAAwBM,UAAU,MAAK;QAC1E,OAAOD,qBAAqBH,qBAAqBD;IACnD,GAAG;QAACD;QAAgBE;QAAoBD;KAAY;IAEpD,OAAO;QACLE;QACAI,UAAUR,MAAMQ,QAAQ;IAC1B;AACF,EAAE"}