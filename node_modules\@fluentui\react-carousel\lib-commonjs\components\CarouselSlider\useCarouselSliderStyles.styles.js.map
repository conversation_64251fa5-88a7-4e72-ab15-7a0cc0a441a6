{"version": 3, "sources": ["useCarouselSliderStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses } from '@griffel/react';\nexport const carouselSliderClassNames = {\n    root: 'fui-CarouselSlider'\n};\n/**\n * Styles for the root slot\n */ const useStyles = makeStyles({\n    root: {\n        display: 'flex',\n        overflowAnchor: 'none'\n    }\n});\n/**\n * Apply styling to the CarouselSlider slots based on the state\n */ export const useCarouselSliderStyles_unstable = (state)=>{\n    'use no memo';\n    const styles = useStyles();\n    state.root.className = mergeClasses(carouselSliderClassNames.root, styles.root, state.root.className);\n    return state;\n};\n"], "names": ["carouselSliderClassNames", "useCarouselSliderStyles_unstable", "root", "useStyles", "__styles", "mc9l5x", "Eiaeu8", "d", "state", "styles", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IACaA,wBAAwB;eAAxBA;;IAaIC,gCAAgC;eAAhCA;;;uBAdwB;AAClC,MAAMD,2BAA2B;IACpCE,MAAM;AACV;AACA;;CAEA,GAAI,MAAMC,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAF,MAAA;QAAAG,QAAA;QAAAC,QAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;QAAA;KAAA;AAAA;AAQX,MAAMN,mCAAoCO,CAAAA;IACjD;IACA,MAAMC,SAASN;IACfK,MAAMN,IAAI,CAACQ,SAAS,GAAGC,IAAAA,mBAAY,EAACX,yBAAyBE,IAAI,EAAEO,OAAOP,IAAI,EAAEM,MAAMN,IAAI,CAACQ,SAAS;IACpG,OAAOF;AACX"}