{"version": 3, "sources": ["../src/utils/index.ts"], "sourcesContent": ["export { getInitials } from './getInitials';\nexport { partitionAvatarGroupItems } from './partitionAvatarGroupItems';\nexport type { PartitionAvatarGroupItems, PartitionAvatarGroupItemsOptions } from './partitionAvatarGroupItems';\n"], "names": ["getInitials", "partitionAvatarGroupItems"], "rangeMappings": ";", "mappings": "AAAA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,yBAAyB,QAAQ,8BAA8B"}