{"version": 3, "sources": ["../src/activedescendant/constants.ts"], "sourcesContent": ["/**\n * Applied to the element that is active descendant\n */\nexport const ACTIVEDESCENDANT_ATTRIBUTE = 'data-activedescendant';\n\n/**\n * Applied to the active descendant when the user is navigating with keyboard\n */\nexport const ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE = 'data-activedescendant-focusvisible';\n"], "names": ["ACTIVEDESCENDANT_ATTRIBUTE", "ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE"], "rangeMappings": ";;;;;", "mappings": "AAAA;;CAEC,GACD,OAAO,MAAMA,6BAA6B,wBAAwB;AAElE;;CAEC,GACD,OAAO,MAAMC,0CAA0C,qCAAqC"}