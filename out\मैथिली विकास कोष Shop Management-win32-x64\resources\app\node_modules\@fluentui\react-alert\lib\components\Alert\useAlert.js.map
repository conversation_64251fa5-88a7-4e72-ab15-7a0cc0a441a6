{"version": 3, "sources": ["useAlert.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Avatar } from '@fluentui/react-avatar';\nimport { Button } from '@fluentui/react-button';\nimport { CheckmarkCircleFilled, DismissCircleFilled, InfoFilled, WarningFilled } from '@fluentui/react-icons';\nimport { getIntrinsicElementProps, slot } from '@fluentui/react-utilities';\n\nimport type { AlertProps, AlertState } from './Alert.types';\n\n/**\n * @deprecated please use the Toast or MessageBar component\n * Create the state required to render Alert.\n *\n * The returned state can be modified with hooks such as useAlertStyles_unstable,\n * before being passed to renderAlert_unstable.\n *\n * @param props - props from this instance of Alert\n * @param ref - reference to root HTMLElement of Alert\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const useAlert_unstable = (props: AlertProps, ref: React.Ref<HTMLElement>): AlertState => {\n  const { appearance = 'primary', intent } = props;\n\n  /** Determine the role and icon to render based on the intent */\n  let defaultIcon;\n  let defaultRole = 'status';\n  switch (intent) {\n    case 'success':\n      defaultIcon = <CheckmarkCircleFilled />;\n      break;\n    case 'error':\n      defaultIcon = <DismissCircleFilled />;\n      defaultRole = 'alert';\n      break;\n    case 'warning':\n      defaultIcon = <WarningFilled />;\n      defaultRole = 'alert';\n      break;\n    case 'info':\n      defaultIcon = <InfoFilled />;\n      break;\n  }\n\n  const action = slot.optional(props.action, { defaultProps: { appearance: 'transparent' }, elementType: Button });\n  const avatar = slot.optional(props.avatar, { elementType: Avatar });\n  let icon;\n  /** Avatar prop takes precedence over the icon or intent prop */ if (!avatar) {\n    icon = slot.optional(props.icon, {\n      defaultProps: { children: defaultIcon },\n      renderByDefault: !!props.intent,\n      elementType: 'span',\n    });\n  }\n  return {\n    action,\n    appearance,\n    avatar,\n    components: { root: 'div', icon: 'span', action: Button, avatar: Avatar },\n    icon,\n    intent,\n    root: slot.always(\n      getIntrinsicElementProps('div', {\n        // FIXME:\n        // `ref` is wrongly assigned to be `HTMLElement` instead of `HTMLDivElement`\n        // but since it would be a breaking change to fix it, we are casting ref to it's proper type\n        ref: ref as React.Ref<HTMLDivElement>,\n        role: defaultRole,\n        children: props.children,\n        ...props,\n      }),\n      { elementType: 'div' },\n    ),\n  };\n};\n"], "names": ["React", "Avatar", "<PERSON><PERSON>", "CheckmarkCircleFilled", "DismissCircleFilled", "InfoFilled", "WarningFilled", "getIntrinsicElementProps", "slot", "useAlert_unstable", "props", "ref", "appearance", "intent", "defaultIcon", "defaultRole", "action", "optional", "defaultProps", "elementType", "avatar", "icon", "children", "renderByDefault", "components", "root", "always", "role"], "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAE/B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,qBAAqB,EAAEC,mBAAmB,EAAEC,UAAU,EAAEC,aAAa,QAAQ,wBAAwB;AAC9G,SAASC,wBAAwB,EAAEC,IAAI,QAAQ,4BAA4B;AAI3E;;;;;;;;;CASC,GACD,mDAAmD;AACnD,OAAO,MAAMC,oBAAoB,CAACC,OAAmBC;IACnD,MAAM,EAAEC,aAAa,SAAS,EAAEC,MAAM,EAAE,GAAGH;IAE3C,8DAA8D,GAC9D,IAAII;IACJ,IAAIC,cAAc;IAClB,OAAQF;QACN,KAAK;YACHC,4BAAc,oBAACX;YACf;QACF,KAAK;YACHW,4BAAc,oBAACV;YACfW,cAAc;YACd;QACF,KAAK;YACHD,4BAAc,oBAACR;YACfS,cAAc;YACd;QACF,KAAK;YACHD,4BAAc,oBAACT;YACf;IACJ;IAEA,MAAMW,SAASR,KAAKS,QAAQ,CAACP,MAAMM,MAAM,EAAE;QAAEE,cAAc;YAAEN,YAAY;QAAc;QAAGO,aAAajB;IAAO;IAC9G,MAAMkB,SAASZ,KAAKS,QAAQ,CAACP,MAAMU,MAAM,EAAE;QAAED,aAAalB;IAAO;IACjE,IAAIoB;IACJ,8DAA8D,GAAG,IAAI,CAACD,QAAQ;QAC5EC,OAAOb,KAAKS,QAAQ,CAACP,MAAMW,IAAI,EAAE;YAC/BH,cAAc;gBAAEI,UAAUR;YAAY;YACtCS,iBAAiB,CAAC,CAACb,MAAMG,MAAM;YAC/BM,aAAa;QACf;IACF;IACA,OAAO;QACLH;QACAJ;QACAQ;QACAI,YAAY;YAAEC,MAAM;YAAOJ,MAAM;YAAQL,QAAQd;YAAQkB,QAAQnB;QAAO;QACxEoB;QACAR;QACAY,MAAMjB,KAAKkB,MAAM,CACfnB,yBAAyB,OAAO;YAC9B,SAAS;YACT,4EAA4E;YAC5E,4FAA4F;YAC5FI,KAAKA;YACLgB,MAAMZ;YACNO,UAAUZ,MAAMY,QAAQ;YACxB,GAAGZ,KAAK;QACV,IACA;YAAES,aAAa;QAAM;IAEzB;AACF,EAAE"}