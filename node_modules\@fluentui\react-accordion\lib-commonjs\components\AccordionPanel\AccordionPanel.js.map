{"version": 3, "sources": ["../src/components/AccordionPanel/AccordionPanel.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useAccordionPanel_unstable } from './useAccordionPanel';\nimport { renderAccordionPanel_unstable } from './renderAccordionPanel';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAccordionPanelStyles_unstable } from './useAccordionPanelStyles.styles';\nimport type { AccordionPanelProps } from './AccordionPanel.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\n/**\n * Define a styled AccordionPanel, using the `useAccordionPanel_unstable` and `useAccordionPanelStyles_unstable` hooks.\n */\nexport const AccordionPanel: ForwardRefComponent<AccordionPanelProps> = React.forwardRef((props, ref) => {\n  const state = useAccordionPanel_unstable(props, ref);\n\n  useAccordionPanelStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAccordionPanelStyles_unstable')(state);\n\n  return renderAccordionPanel_unstable(state);\n});\n\nAccordionPanel.displayName = 'AccordionPanel';\n"], "names": ["AccordionPanel", "React", "forwardRef", "props", "ref", "state", "useAccordionPanel_unstable", "useAccordionPanelStyles_unstable", "useCustomStyleHook_unstable", "renderAccordionPanel_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAWaA;;;eAAAA;;;;iEAXU;mCACoB;sCACG;qCACF;+CACK;AAO1C,MAAMA,iBAAAA,WAAAA,GAA2DC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IAC/F,MAAMC,QAAQC,IAAAA,6CAAAA,EAA2BH,OAAOC;IAEhDG,IAAAA,+DAAAA,EAAiCF;IAEjCG,IAAAA,gDAAAA,EAA4B,oCAAoCH;IAEhE,OAAOI,IAAAA,mDAAAA,EAA8BJ;AACvC;AAEAL,eAAeU,WAAW,GAAG"}