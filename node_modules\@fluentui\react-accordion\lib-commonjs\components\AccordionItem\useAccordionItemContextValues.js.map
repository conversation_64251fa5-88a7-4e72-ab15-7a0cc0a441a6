{"version": 3, "sources": ["../src/components/AccordionItem/useAccordionItemContextValues.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { AccordionItemContextValues, AccordionItemState } from './AccordionItem.types';\nimport { AccordionItemContextValue } from '../../contexts/accordionItem';\n\nexport function useAccordionItemContextValues_unstable(state: AccordionItemState): AccordionItemContextValues {\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  const { disabled, open, value, onHeaderClick } = state;\n  const accordionItem = React.useMemo<AccordionItemContextValue>(\n    () => ({ disabled, open, value, onHeaderClick }),\n    [disabled, open, value, onHeaderClick],\n  );\n\n  return { accordionItem };\n}\n"], "names": ["useAccordionItemContextValues_unstable", "state", "disabled", "open", "value", "onHeaderClick", "accordionItem", "React", "useMemo"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAIgBA;;;eAAAA;;;;iEAJO;AAIhB,SAASA,uCAAuCC,KAAyB;IAC9E,4DAA4D;IAC5D,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,EAAEC,aAAa,EAAE,GAAGJ;IACjD,MAAMK,gBAAgBC,OAAMC,OAAO,CACjC,IAAO,CAAA;YAAEN;YAAUC;YAAMC;YAAOC;QAAc,CAAA,GAC9C;QAACH;QAAUC;QAAMC;QAAOC;KAAc;IAGxC,OAAO;QAAEC;IAAc;AACzB"}