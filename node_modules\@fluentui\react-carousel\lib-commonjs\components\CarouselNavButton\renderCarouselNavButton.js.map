{"version": 3, "sources": ["../src/components/CarouselNavButton/renderCarouselNavButton.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { CarouselNavButtonState, CarouselNavButtonSlots } from './CarouselNavButton.types';\n\n/**\n * Render the final JSX of CarouselNavButton\n */\nexport const renderCarouselNavButton_unstable = (state: CarouselNavButtonState) => {\n  assertSlots<CarouselNavButtonSlots>(state);\n\n  // TODO Add additional slots in the appropriate place\n  return <state.root />;\n};\n"], "names": ["renderCarouselNavButton_unstable", "state", "assertSlots", "_jsx", "root"], "rangeMappings": ";;;;;;;;;;;;;;;;", "mappings": ";;;;+BASaA;;;eAAAA;;;4BARb;gCAE4B;AAMrB,MAAMA,mCAAmC,CAACC;IAC/CC,IAAAA,2BAAAA,EAAoCD;IAEpC,qDAAqD;IACrD,OAAA,WAAA,GAAOE,IAAAA,eAAA,EAACF,MAAMG,IAAI,EAAA,CAAA;AACpB"}