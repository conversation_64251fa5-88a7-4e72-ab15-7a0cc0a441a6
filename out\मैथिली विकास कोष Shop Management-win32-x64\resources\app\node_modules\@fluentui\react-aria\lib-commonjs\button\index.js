"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    useARIAButtonProps: function() {
        return _useARIAButtonProps.useARIAButtonProps;
    },
    useARIAButtonShorthand: function() {
        return _useARIAButtonShorthand.useARIAButtonShorthand;
    }
});
const _useARIAButtonProps = require("./useARIAButtonProps");
const _useARIAButtonShorthand = require("./useARIAButtonShorthand");
