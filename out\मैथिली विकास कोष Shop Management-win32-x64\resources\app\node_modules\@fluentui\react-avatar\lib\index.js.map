{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["export {\n  Avatar,\n  avatarClassNames,\n  renderAvatar_unstable,\n  useAvatarStyles_unstable,\n  useAvatar_unstable,\n} from './Avatar';\nexport type {\n  AvatarNamedColor,\n  AvatarProps,\n  AvatarSlots,\n  AvatarState,\n  AvatarShape,\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  AvatarSizes,\n  AvatarSize,\n} from './Avatar';\nexport { getInitials, partitionAvatarGroupItems } from './utils/index';\nexport type { PartitionAvatarGroupItems, PartitionAvatarGroupItemsOptions } from './utils/index';\nexport {\n  AvatarGroup,\n  avatarGroupClassNames,\n  renderAvatarGroup_unstable,\n  useAvatarGroupContextValues,\n  useAvatarGroupStyles_unstable,\n  useAvatarGroup_unstable,\n} from './AvatarGroup';\nexport type {\n  AvatarGroupProps,\n  AvatarGroupSlots,\n  AvatarGroupState,\n  AvatarGroupContextValue,\n  AvatarGroupContextValues,\n} from './AvatarGroup';\nexport {\n  AvatarGroupItem,\n  avatarGroupItemClassNames,\n  renderAvatarGroupItem_unstable,\n  useAvatarGroupItemStyles_unstable,\n  useAvatarGroupItem_unstable,\n} from './AvatarGroupItem';\nexport type { AvatarGroupItemProps, AvatarGroupItemSlots, AvatarGroupItemState } from './AvatarGroupItem';\nexport {\n  AvatarGroupPopover,\n  avatarGroupPopoverClassNames,\n  renderAvatarGroupPopover_unstable,\n  useAvatarGroupPopover_unstable,\n  useAvatarGroupPopoverContextValues_unstable,\n  useAvatarGroupPopoverStyles_unstable,\n} from './AvatarGroupPopover';\nexport type { AvatarGroupPopoverProps, AvatarGroupPopoverSlots, AvatarGroupPopoverState } from './AvatarGroupPopover';\nexport {\n  AvatarContextProvider,\n  AvatarGroupProvider,\n  useAvatarContext,\n  useAvatarGroupContext_unstable,\n} from './contexts/index';\nexport type { AvatarContextValue } from './contexts/index';\n"], "names": ["Avatar", "avatarClassNames", "renderAvatar_unstable", "useAvatarStyles_unstable", "useAvatar_unstable", "getInitials", "partitionAvatarGroupItems", "AvatarGroup", "avatarGroupClassNames", "renderAvatarGroup_unstable", "useAvatarGroupContextValues", "useAvatarGroupStyles_unstable", "useAvatarGroup_unstable", "AvatarGroupItem", "avatarGroupItemClassNames", "renderAvatarGroupItem_unstable", "useAvatarGroupItemStyles_unstable", "useAvatarGroupItem_unstable", "AvatarGroupPopover", "avatarGroupPopoverClassNames", "renderAvatarGroupPopover_unstable", "useAvatarGroupPopover_unstable", "useAvatarGroupPopoverContextValues_unstable", "useAvatarGroupPopoverStyles_unstable", "AvatarContextProvider", "AvatarGroupProvider", "useAvatarContext", "useAvatarGroupContext_unstable"], "rangeMappings": ";;;;;", "mappings": "AAAA,SACEA,MAAM,EACNC,gBAAgB,EAChBC,qBAAqB,EACrBC,wBAAwB,EACxBC,kBAAkB,QACb,WAAW;AAWlB,SAASC,WAAW,EAAEC,yBAAyB,QAAQ,gBAAgB;AAEvE,SACEC,WAAW,EACXC,qBAAqB,EACrBC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,uBAAuB,QAClB,gBAAgB;AAQvB,SACEC,eAAe,EACfC,yBAAyB,EACzBC,8BAA8B,EAC9BC,iCAAiC,EACjCC,2BAA2B,QACtB,oBAAoB;AAE3B,SACEC,kBAAkB,EAClBC,4BAA4B,EAC5BC,iCAAiC,EACjCC,8BAA8B,EAC9BC,2CAA2C,EAC3CC,oCAAoC,QAC/B,uBAAuB;AAE9B,SACEC,qBAAqB,EACrBC,mBAAmB,EACnBC,gBAAgB,EAChBC,8BAA8B,QACzB,mBAAmB"}