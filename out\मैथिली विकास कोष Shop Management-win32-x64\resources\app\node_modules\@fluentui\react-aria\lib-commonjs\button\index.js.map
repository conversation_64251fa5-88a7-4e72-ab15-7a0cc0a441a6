{"version": 3, "sources": ["../src/button/index.ts"], "sourcesContent": ["export { useARIAButtonProps } from './useARIAButtonProps';\n// eslint-disable-next-line @typescript-eslint/no-deprecated\nexport { useARIAButtonShorthand } from './useARIAButtonShorthand';\nexport type {\n  ARIAButtonAlteredProps,\n  ARIAButtonElement,\n  ARIAButtonElementIntersection,\n  ARIAButtonProps,\n  ARIAButtonResultProps,\n  ARIAButtonSlotProps,\n  ARIAButtonType,\n} from './types';\n"], "names": ["useARIAButtonProps", "useARIAButtonShorthand"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAASA,kBAAkB;eAAlBA,sCAAkB;;IAElBC,sBAAsB;eAAtBA,8CAAsB;;;oCAFI;wCAEI"}