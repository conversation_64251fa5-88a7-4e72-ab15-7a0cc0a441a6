"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AvatarGroup: function() {
        return _AvatarGroup.AvatarGroup;
    },
    avatarGroupClassNames: function() {
        return _useAvatarGroupStylesstyles.avatarGroupClassNames;
    },
    defaultAvatarGroupSize: function() {
        return _useAvatarGroup.defaultAvatarGroupSize;
    },
    renderAvatarGroup_unstable: function() {
        return _renderAvatarGroup.renderAvatarGroup_unstable;
    },
    useAvatarGroupContextValues: function() {
        return _useAvatarGroupContextValues.useAvatarGroupContextValues;
    },
    useAvatarGroupStyles_unstable: function() {
        return _useAvatarGroupStylesstyles.useAvatarGroupStyles_unstable;
    },
    useAvatarGroup_unstable: function() {
        return _useAvatarGroup.useAvatarGroup_unstable;
    }
});
const _AvatarGroup = require("./AvatarGroup");
const _renderAvatarGroup = require("./renderAvatarGroup");
const _useAvatarGroup = require("./useAvatarGroup");
const _useAvatarGroupStylesstyles = require("./useAvatarGroupStyles.styles");
const _useAvatarGroupContextValues = require("./useAvatarGroupContextValues");
