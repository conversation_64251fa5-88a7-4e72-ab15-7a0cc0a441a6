export { Avatar, avatarClassNames, renderAvatar_unstable, useAvatarStyles_unstable, useAvatar_unstable } from './Avatar';
export { getInitials, partitionAvatarGroupItems } from './utils/index';
export { AvatarGroup, avatarGroupClassNames, renderAvatarGroup_unstable, useAvatarGroupContextValues, useAvatarGroupStyles_unstable, useAvatarGroup_unstable } from './AvatarGroup';
export { AvatarGroupItem, avatarGroupItemClassNames, renderAvatarGroupItem_unstable, useAvatarGroupItemStyles_unstable, useAvatarGroupItem_unstable } from './AvatarGroupItem';
export { AvatarGroupPopover, avatarGroupPopoverClassNames, renderAvatarGroupPopover_unstable, useAvatarGroupPopover_unstable, useAvatarGroupPopoverContextValues_unstable, useAvatarGroupPopoverStyles_unstable } from './AvatarGroupPopover';
export { AvatarContextProvider, AvatarGroupProvider, useAvatarContext, useAvatarGroupContext_unstable } from './contexts/index';
