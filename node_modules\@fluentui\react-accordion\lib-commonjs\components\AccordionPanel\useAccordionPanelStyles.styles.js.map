{"version": 3, "sources": ["useAccordionPanelStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses } from '@griffel/react';\nimport { tokens } from '@fluentui/react-theme';\nexport const accordionPanelClassNames = {\n    root: 'fui-AccordionPanel'\n};\n/**\n * Styles for the root slot\n */ const useStyles = makeStyles({\n    root: {\n        margin: `0 ${tokens.spacingHorizontalM}`\n    }\n});\n/** Applies style classnames to slots */ export const useAccordionPanelStyles_unstable = (state)=>{\n    'use no memo';\n    const styles = useStyles();\n    state.root.className = mergeClasses(accordionPanelClassNames.root, styles.root, state.root.className);\n    return state;\n};\n"], "names": ["accordionPanelClassNames", "useAccordionPanelStyles_unstable", "root", "useStyles", "__styles", "j<PERSON><PERSON>", "Frg6f3", "t21cq0", "B6of3ja", "B74szlk", "d", "p", "state", "styles", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAEaA,wBAAwB;eAAxBA;;IAUyCC,gCAAgC;eAAhCA;;;uBAZb;AAElC,MAAMD,2BAA2B;IACpCE,MAAM;AACV;AACA;;CAEA,GAAI,MAAMC,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAF,MAAA;QAAAG,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;KAAA;AAAA;AAK0B,MAAMV,mCAAoCW,CAAAA;IACtF;IACA,MAAMC,SAASV;IACfS,MAAMV,IAAI,CAACY,SAAS,GAAGC,IAAAA,mBAAY,EAACf,yBAAyBE,IAAI,EAAEW,OAAOX,IAAI,EAAEU,MAAMV,IAAI,CAACY,SAAS;IACpG,OAAOF;AACX"}