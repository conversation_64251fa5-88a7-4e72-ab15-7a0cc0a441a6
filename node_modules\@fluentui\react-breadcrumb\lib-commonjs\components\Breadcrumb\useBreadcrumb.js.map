{"version": 3, "sources": ["../src/components/Breadcrumb/useBreadcrumb.ts"], "sourcesContent": ["import * as React from 'react';\nimport { getIntrinsicElementProps, slot } from '@fluentui/react-utilities';\nimport type { BreadcrumbProps, BreadcrumbState } from './Breadcrumb.types';\nimport { useArrowNavigationGroup } from '@fluentui/react-tabster';\n\n/**\n * Create the state required to render Breadcrumb.\n *\n * The returned state can be modified with hooks such as useBreadcrumbStyles_unstable,\n * before being passed to renderBreadcrumb_unstable.\n *\n * @param props - props from this instance of Breadcrumb\n * @param ref - reference to root HTMLElement of Breadcrumb\n */\nexport const useBreadcrumb_unstable = (props: BreadcrumbProps, ref: React.Ref<HTMLElement>): BreadcrumbState => {\n  const { focusMode = 'tab', size = 'medium', list, ...rest } = props;\n\n  const focusAttributes = useArrowNavigationGroup({\n    circular: true,\n    axis: 'horizontal',\n    memorizeCurrent: true,\n  });\n\n  return {\n    components: {\n      root: 'nav',\n      list: 'ol',\n    },\n    root: slot.always(\n      getIntrinsicElementProps('nav', {\n        ref,\n        'aria-label': props['aria-label'] ?? 'breadcrumb',\n        ...(focusMode === 'arrow' ? focusAttributes : {}),\n        ...rest,\n      }),\n      { elementType: 'nav' },\n    ),\n    list: slot.optional(list, { renderByDefault: true, defaultProps: { role: 'list' }, elementType: 'ol' }),\n    size,\n  };\n};\n"], "names": ["useBreadcrumb_unstable", "props", "ref", "focusMode", "size", "list", "rest", "focusAttributes", "useArrowNavigationGroup", "circular", "axis", "memorizeCurrent", "components", "root", "slot", "always", "getIntrinsicElementProps", "elementType", "optional", "renderByDefault", "defaultProps", "role"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAcaA;;;eAAAA;;;;iEAdU;gCACwB;8BAEP;AAWjC,MAAMA,yBAAyB,CAACC,OAAwBC;IAC7D,MAAM,EAAEC,YAAY,KAAK,EAAEC,OAAO,QAAQ,EAAEC,IAAI,EAAE,GAAGC,MAAM,GAAGL;IAE9D,MAAMM,kBAAkBC,IAAAA,qCAAAA,EAAwB;QAC9CC,UAAU;QACVC,MAAM;QACNC,iBAAiB;IACnB;QAUoBV;IARpB,OAAO;QACLW,YAAY;YACVC,MAAM;YACNR,MAAM;QACR;QACAQ,MAAMC,oBAAAA,CAAKC,MAAM,CACfC,IAAAA,wCAAAA,EAAyB,OAAO;YAC9Bd;YACA,cAAcD,CAAAA,mBAAAA,KAAK,CAAC,aAAa,AAAb,MAAa,QAAnBA,qBAAAA,KAAAA,IAAAA,mBAAuB;YACrC,GAAIE,cAAc,UAAUI,kBAAkB,CAAC,CAAC;YAChD,GAAGD,IAAI;QACT,IACA;YAAEW,aAAa;QAAM;QAEvBZ,MAAMS,oBAAAA,CAAKI,QAAQ,CAACb,MAAM;YAAEc,iBAAiB;YAAMC,cAAc;gBAAEC,MAAM;YAAO;YAAGJ,aAAa;QAAK;QACrGb;IACF;AACF"}