{"version": 3, "sources": ["../src/components/Button/useButton.ts"], "sourcesContent": ["import * as React from 'react';\nimport { ARIAButtonSlotProps, useARIAButtonProps } from '@fluentui/react-aria';\nimport { getIntrinsicElementProps, slot } from '@fluentui/react-utilities';\nimport { useButtonContext } from '../../contexts/ButtonContext';\nimport type { ButtonProps, ButtonState } from './Button.types';\n\n/**\n * Given user props, defines default props for the Button, calls useButtonState, and returns processed state.\n * @param props - User provided props to the Button component.\n * @param ref - User provided ref to be passed to the Button component.\n */\nexport const useButton_unstable = (\n  props: ButtonProps,\n  ref: React.Ref<HTMLButtonElement | HTMLAnchorElement>,\n): ButtonState => {\n  const { size: contextSize } = useButtonContext();\n  const {\n    appearance = 'secondary',\n    as = 'button',\n    disabled = false,\n    disabledFocusable = false,\n    icon,\n    iconPosition = 'before',\n    shape = 'rounded',\n    size = contextSize ?? 'medium',\n  } = props;\n  const iconShorthand = slot.optional(icon, { elementType: 'span' });\n  return {\n    // Props passed at the top-level\n    appearance,\n    disabled,\n    disabledFocusable,\n    iconPosition,\n    shape,\n    size, // State calculated from a set of props\n    iconOnly: Boolean(iconShorthand?.children && !props.children), // Slots definition\n    components: { root: 'button', icon: 'span' },\n    root: slot.always<ARIAButtonSlotProps<'a'>>(getIntrinsicElementProps(as, useARIAButtonProps(props.as, props)), {\n      elementType: 'button',\n      defaultProps: {\n        ref: ref as React.Ref<HTMLButtonElement & HTMLAnchorElement>,\n        type: as === 'button' ? 'button' : undefined,\n      },\n    }),\n    icon: iconShorthand,\n  };\n};\n"], "names": ["useButton_unstable", "props", "ref", "size", "contextSize", "useButtonContext", "appearance", "as", "disabled", "disabledFocusable", "icon", "iconPosition", "shape", "icon<PERSON><PERSON><PERSON>d", "slot", "optional", "elementType", "iconOnly", "Boolean", "children", "components", "root", "always", "getIntrinsicElementProps", "useARIAButtonProps", "defaultProps", "type", "undefined"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAWaA;;;eAAAA;;;;iEAXU;2BACiC;gCACT;+BACd;AAQ1B,MAAMA,qBAAqB,CAChCC,OACAC;IAEA,MAAM,EAAEC,MAAMC,WAAW,EAAE,GAAGC,IAAAA,+BAAAA;IAC9B,MAAM,EACJC,aAAa,WAAW,EACxBC,KAAK,QAAQ,EACbC,WAAW,KAAK,EAChBC,oBAAoB,KAAK,EACzBC,IAAI,EACJC,eAAe,QAAQ,EACvBC,QAAQ,SAAS,EACjBT,OAAOC,gBAAAA,QAAAA,gBAAAA,KAAAA,IAAAA,cAAe,QAAQ,EAC/B,GAAGH;IACJ,MAAMY,gBAAgBC,oBAAAA,CAAKC,QAAQ,CAACL,MAAM;QAAEM,aAAa;IAAO;IAChE,OAAO;QACL,gCAAgC;QAChCV;QACAE;QACAC;QACAE;QACAC;QACAT;QACAc,UAAUC,QAAQL,CAAAA,kBAAAA,QAAAA,kBAAAA,KAAAA,IAAAA,KAAAA,IAAAA,cAAeM,QAAQ,AAARA,KAAY,CAAClB,MAAMkB,QAAQ;QAC5DC,YAAY;YAAEC,MAAM;YAAUX,MAAM;QAAO;QAC3CW,MAAMP,oBAAAA,CAAKQ,MAAM,CAA2BC,IAAAA,wCAAAA,EAAyBhB,IAAIiB,IAAAA,6BAAAA,EAAmBvB,MAAMM,EAAE,EAAEN,SAAS;YAC7Ge,aAAa;YACbS,cAAc;gBACZvB,KAAKA;gBACLwB,MAAMnB,OAAO,WAAW,WAAWoB;YACrC;QACF;QACAjB,MAAMG;IACR;AACF"}