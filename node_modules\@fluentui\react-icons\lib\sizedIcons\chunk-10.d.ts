import type { FluentIcon } from "../utils/createFluentIcon";
export declare const LinkToolbox20Regular: FluentIcon;
export declare const List16Filled: FluentIcon;
export declare const List16Regular: FluentIcon;
export declare const List20Filled: FluentIcon;
export declare const List20Regular: FluentIcon;
export declare const List24Filled: FluentIcon;
export declare const List24Regular: FluentIcon;
export declare const List28Filled: FluentIcon;
export declare const List28Regular: FluentIcon;
export declare const ListBar16Color: FluentIcon;
export declare const ListBar16Filled: FluentIcon;
export declare const ListBar16Regular: FluentIcon;
export declare const ListBar20Color: FluentIcon;
export declare const ListBar20Filled: FluentIcon;
export declare const ListBar20Regular: FluentIcon;
export declare const ListBar24Color: FluentIcon;
export declare const ListBar24Filled: FluentIcon;
export declare const ListBar24Regular: FluentIcon;
export declare const ListBar32Color: FluentIcon;
export declare const ListBar32Filled: FluentIcon;
export declare const ListBar32Regular: FluentIcon;
export declare const ListBarTree16Filled: FluentIcon;
export declare const ListBarTree16Regular: FluentIcon;
export declare const ListBarTree20Filled: FluentIcon;
export declare const ListBarTree20Regular: FluentIcon;
export declare const ListBarTreeOffset16Filled: FluentIcon;
export declare const ListBarTreeOffset16Regular: FluentIcon;
export declare const ListBarTreeOffset20Filled: FluentIcon;
export declare const ListBarTreeOffset20Regular: FluentIcon;
export declare const ListRtl16Filled: FluentIcon;
export declare const ListRtl16Regular: FluentIcon;
export declare const ListRtl20Filled: FluentIcon;
export declare const ListRtl20Regular: FluentIcon;
export declare const Live20Filled: FluentIcon;
export declare const Live20Regular: FluentIcon;
export declare const Live24Filled: FluentIcon;
export declare const Live24Regular: FluentIcon;
export declare const LiveOff20Filled: FluentIcon;
export declare const LiveOff20Regular: FluentIcon;
export declare const LiveOff24Filled: FluentIcon;
export declare const LiveOff24Regular: FluentIcon;
export declare const LocalLanguage16Filled: FluentIcon;
export declare const LocalLanguage16Regular: FluentIcon;
export declare const LocalLanguage20Filled: FluentIcon;
export declare const LocalLanguage20Regular: FluentIcon;
export declare const LocalLanguage24Filled: FluentIcon;
export declare const LocalLanguage24Regular: FluentIcon;
export declare const LocalLanguage28Filled: FluentIcon;
export declare const LocalLanguage28Regular: FluentIcon;
export declare const Location12Filled: FluentIcon;
export declare const Location12Regular: FluentIcon;
export declare const Location16Filled: FluentIcon;
export declare const Location16Regular: FluentIcon;
export declare const Location20Filled: FluentIcon;
export declare const Location20Regular: FluentIcon;
export declare const Location24Filled: FluentIcon;
export declare const Location24Regular: FluentIcon;
export declare const Location28Filled: FluentIcon;
export declare const Location28Regular: FluentIcon;
export declare const Location48Filled: FluentIcon;
export declare const Location48Regular: FluentIcon;
export declare const LocationAdd16Filled: FluentIcon;
export declare const LocationAdd16Regular: FluentIcon;
export declare const LocationAdd20Filled: FluentIcon;
export declare const LocationAdd20Regular: FluentIcon;
export declare const LocationAdd24Filled: FluentIcon;
export declare const LocationAdd24Regular: FluentIcon;
export declare const LocationAddLeft20Filled: FluentIcon;
export declare const LocationAddLeft20Regular: FluentIcon;
export declare const LocationAddRight20Filled: FluentIcon;
export declare const LocationAddRight20Regular: FluentIcon;
export declare const LocationAddUp20Filled: FluentIcon;
export declare const LocationAddUp20Regular: FluentIcon;
export declare const LocationArrow12Filled: FluentIcon;
export declare const LocationArrow12Regular: FluentIcon;
export declare const LocationArrow16Filled: FluentIcon;
export declare const LocationArrow16Regular: FluentIcon;
export declare const LocationArrow20Filled: FluentIcon;
export declare const LocationArrow20Regular: FluentIcon;
export declare const LocationArrow24Filled: FluentIcon;
export declare const LocationArrow24Regular: FluentIcon;
export declare const LocationArrow28Filled: FluentIcon;
export declare const LocationArrow28Regular: FluentIcon;
export declare const LocationArrow32Filled: FluentIcon;
export declare const LocationArrow32Regular: FluentIcon;
export declare const LocationArrow48Filled: FluentIcon;
export declare const LocationArrow48Regular: FluentIcon;
export declare const LocationArrowLeft16Filled: FluentIcon;
export declare const LocationArrowLeft16Regular: FluentIcon;
export declare const LocationArrowLeft20Filled: FluentIcon;
export declare const LocationArrowLeft20Regular: FluentIcon;
export declare const LocationArrowLeft48Filled: FluentIcon;
export declare const LocationArrowLeft48Regular: FluentIcon;
export declare const LocationArrowRight16Filled: FluentIcon;
export declare const LocationArrowRight16Regular: FluentIcon;
export declare const LocationArrowRight20Filled: FluentIcon;
export declare const LocationArrowRight20Regular: FluentIcon;
export declare const LocationArrowRight48Filled: FluentIcon;
export declare const LocationArrowRight48Regular: FluentIcon;
export declare const LocationArrowUp16Filled: FluentIcon;
export declare const LocationArrowUp16Regular: FluentIcon;
export declare const LocationArrowUp20Filled: FluentIcon;
export declare const LocationArrowUp20Regular: FluentIcon;
export declare const LocationArrowUp48Filled: FluentIcon;
export declare const LocationArrowUp48Regular: FluentIcon;
export declare const LocationCheckmark12Filled: FluentIcon;
export declare const LocationCheckmark12Regular: FluentIcon;
export declare const LocationCheckmark16Filled: FluentIcon;
export declare const LocationCheckmark16Regular: FluentIcon;
export declare const LocationCheckmark20Filled: FluentIcon;
export declare const LocationCheckmark20Regular: FluentIcon;
export declare const LocationCheckmark24Filled: FluentIcon;
export declare const LocationCheckmark24Regular: FluentIcon;
export declare const LocationCheckmark48Filled: FluentIcon;
export declare const LocationCheckmark48Regular: FluentIcon;
export declare const LocationDismiss20Filled: FluentIcon;
export declare const LocationDismiss20Regular: FluentIcon;
export declare const LocationDismiss24Filled: FluentIcon;
export declare const LocationDismiss24Regular: FluentIcon;
export declare const LocationLive20Filled: FluentIcon;
export declare const LocationLive20Regular: FluentIcon;
export declare const LocationLive24Filled: FluentIcon;
export declare const LocationLive24Regular: FluentIcon;
export declare const LocationOff16Filled: FluentIcon;
export declare const LocationOff16Regular: FluentIcon;
export declare const LocationOff20Filled: FluentIcon;
export declare const LocationOff20Regular: FluentIcon;
export declare const LocationOff24Filled: FluentIcon;
export declare const LocationOff24Regular: FluentIcon;
export declare const LocationOff28Filled: FluentIcon;
export declare const LocationOff28Regular: FluentIcon;
export declare const LocationOff48Filled: FluentIcon;
export declare const LocationOff48Regular: FluentIcon;
export declare const LocationRipple12Filled: FluentIcon;
export declare const LocationRipple12Regular: FluentIcon;
export declare const LocationRipple16Color: FluentIcon;
export declare const LocationRipple16Filled: FluentIcon;
export declare const LocationRipple16Regular: FluentIcon;
export declare const LocationRipple20Color: FluentIcon;
export declare const LocationRipple20Filled: FluentIcon;
export declare const LocationRipple20Regular: FluentIcon;
export declare const LocationRipple24Color: FluentIcon;
export declare const LocationRipple24Filled: FluentIcon;
export declare const LocationRipple24Regular: FluentIcon;
export declare const LocationSettings20Filled: FluentIcon;
export declare const LocationSettings20Regular: FluentIcon;
export declare const LocationSettings24Filled: FluentIcon;
export declare const LocationSettings24Regular: FluentIcon;
export declare const LocationSettings28Filled: FluentIcon;
export declare const LocationSettings28Regular: FluentIcon;
export declare const LocationSettings48Filled: FluentIcon;
export declare const LocationSettings48Regular: FluentIcon;
export declare const LocationTargetSquare16Filled: FluentIcon;
export declare const LocationTargetSquare16Regular: FluentIcon;
export declare const LocationTargetSquare20Filled: FluentIcon;
export declare const LocationTargetSquare20Regular: FluentIcon;
export declare const LocationTargetSquare24Filled: FluentIcon;
export declare const LocationTargetSquare24Regular: FluentIcon;
export declare const LocationTargetSquare32Filled: FluentIcon;
export declare const LocationTargetSquare32Regular: FluentIcon;
export declare const LockClosed12Filled: FluentIcon;
export declare const LockClosed12Regular: FluentIcon;
export declare const LockClosed16Color: FluentIcon;
export declare const LockClosed16Filled: FluentIcon;
export declare const LockClosed16Regular: FluentIcon;
export declare const LockClosed20Color: FluentIcon;
export declare const LockClosed20Filled: FluentIcon;
export declare const LockClosed20Regular: FluentIcon;
export declare const LockClosed24Color: FluentIcon;
export declare const LockClosed24Filled: FluentIcon;
export declare const LockClosed24Regular: FluentIcon;
export declare const LockClosed28Color: FluentIcon;
export declare const LockClosed28Filled: FluentIcon;
export declare const LockClosed28Regular: FluentIcon;
export declare const LockClosed32Color: FluentIcon;
export declare const LockClosed32Filled: FluentIcon;
export declare const LockClosed32Light: FluentIcon;
export declare const LockClosed32Regular: FluentIcon;
export declare const LockClosed48Color: FluentIcon;
export declare const LockClosed48Filled: FluentIcon;
export declare const LockClosed48Regular: FluentIcon;
export declare const LockClosedKey16Filled: FluentIcon;
export declare const LockClosedKey16Regular: FluentIcon;
export declare const LockClosedKey20Filled: FluentIcon;
export declare const LockClosedKey20Regular: FluentIcon;
export declare const LockClosedKey24Filled: FluentIcon;
export declare const LockClosedKey24Regular: FluentIcon;
export declare const LockClosedRibbon16Filled: FluentIcon;
export declare const LockClosedRibbon16Regular: FluentIcon;
export declare const LockClosedRibbon20Filled: FluentIcon;
export declare const LockClosedRibbon20Regular: FluentIcon;
export declare const LockClosedRibbon24Filled: FluentIcon;
export declare const LockClosedRibbon24Regular: FluentIcon;
export declare const LockClosedRibbon28Filled: FluentIcon;
export declare const LockClosedRibbon28Regular: FluentIcon;
export declare const LockClosedRibbon32Filled: FluentIcon;
export declare const LockClosedRibbon32Regular: FluentIcon;
export declare const LockClosedRibbon48Filled: FluentIcon;
export declare const LockClosedRibbon48Regular: FluentIcon;
export declare const LockMultiple20Filled: FluentIcon;
export declare const LockMultiple20Regular: FluentIcon;
export declare const LockMultiple24Filled: FluentIcon;
export declare const LockMultiple24Regular: FluentIcon;
export declare const LockOpen12Filled: FluentIcon;
export declare const LockOpen12Regular: FluentIcon;
export declare const LockOpen16Filled: FluentIcon;
export declare const LockOpen16Regular: FluentIcon;
export declare const LockOpen20Filled: FluentIcon;
export declare const LockOpen20Regular: FluentIcon;
export declare const LockOpen24Filled: FluentIcon;
export declare const LockOpen24Regular: FluentIcon;
export declare const LockOpen28Filled: FluentIcon;
export declare const LockOpen28Regular: FluentIcon;
export declare const LockOpen32Filled: FluentIcon;
export declare const LockOpen32Light: FluentIcon;
export declare const LockOpen32Regular: FluentIcon;
export declare const LockOpen48Filled: FluentIcon;
export declare const LockOpen48Regular: FluentIcon;
export declare const LockShield16Color: FluentIcon;
export declare const LockShield16Filled: FluentIcon;
export declare const LockShield16Regular: FluentIcon;
export declare const LockShield20Color: FluentIcon;
export declare const LockShield20Filled: FluentIcon;
export declare const LockShield20Regular: FluentIcon;
export declare const LockShield24Color: FluentIcon;
export declare const LockShield24Filled: FluentIcon;
export declare const LockShield24Regular: FluentIcon;
export declare const LockShield28Color: FluentIcon;
export declare const LockShield28Filled: FluentIcon;
export declare const LockShield28Regular: FluentIcon;
export declare const LockShield32Color: FluentIcon;
export declare const LockShield32Filled: FluentIcon;
export declare const LockShield32Regular: FluentIcon;
export declare const LockShield48Color: FluentIcon;
export declare const LockShield48Filled: FluentIcon;
export declare const LockShield48Regular: FluentIcon;
export declare const Lottery20Filled: FluentIcon;
export declare const Lottery20Regular: FluentIcon;
export declare const Lottery24Filled: FluentIcon;
export declare const Lottery24Regular: FluentIcon;
export declare const Luggage16Filled: FluentIcon;
export declare const Luggage16Regular: FluentIcon;
export declare const Luggage20Filled: FluentIcon;
export declare const Luggage20Regular: FluentIcon;
export declare const Luggage24Filled: FluentIcon;
export declare const Luggage24Regular: FluentIcon;
export declare const Luggage28Filled: FluentIcon;
export declare const Luggage28Regular: FluentIcon;
export declare const Luggage32Filled: FluentIcon;
export declare const Luggage32Regular: FluentIcon;
export declare const Luggage48Filled: FluentIcon;
export declare const Luggage48Regular: FluentIcon;
export declare const Mail12Filled: FluentIcon;
export declare const Mail12Regular: FluentIcon;
export declare const Mail16Color: FluentIcon;
export declare const Mail16Filled: FluentIcon;
export declare const Mail16Regular: FluentIcon;
export declare const Mail20Color: FluentIcon;
export declare const Mail20Filled: FluentIcon;
export declare const Mail20Regular: FluentIcon;
export declare const Mail24Color: FluentIcon;
export declare const Mail24Filled: FluentIcon;
export declare const Mail24Regular: FluentIcon;
export declare const Mail28Color: FluentIcon;
export declare const Mail28Filled: FluentIcon;
export declare const Mail28Regular: FluentIcon;
export declare const Mail32Color: FluentIcon;
export declare const Mail32Filled: FluentIcon;
export declare const Mail32Light: FluentIcon;
export declare const Mail32Regular: FluentIcon;
export declare const Mail48Color: FluentIcon;
export declare const Mail48Filled: FluentIcon;
export declare const Mail48Regular: FluentIcon;
export declare const MailAdd16Filled: FluentIcon;
export declare const MailAdd16Regular: FluentIcon;
export declare const MailAdd20Filled: FluentIcon;
export declare const MailAdd20Regular: FluentIcon;
export declare const MailAdd24Filled: FluentIcon;
export declare const MailAdd24Regular: FluentIcon;
export declare const MailAlert16Color: FluentIcon;
export declare const MailAlert16Filled: FluentIcon;
export declare const MailAlert16Regular: FluentIcon;
export declare const MailAlert20Color: FluentIcon;
export declare const MailAlert20Filled: FluentIcon;
export declare const MailAlert20Regular: FluentIcon;
export declare const MailAlert24Color: FluentIcon;
export declare const MailAlert24Filled: FluentIcon;
export declare const MailAlert24Regular: FluentIcon;
export declare const MailAlert28Color: FluentIcon;
export declare const MailAlert28Filled: FluentIcon;
export declare const MailAlert28Regular: FluentIcon;
export declare const MailAlert32Color: FluentIcon;
export declare const MailAlert32Filled: FluentIcon;
export declare const MailAlert32Light: FluentIcon;
export declare const MailAlert32Regular: FluentIcon;
export declare const MailAllRead16Filled: FluentIcon;
export declare const MailAllRead16Regular: FluentIcon;
export declare const MailAllRead20Filled: FluentIcon;
export declare const MailAllRead20Regular: FluentIcon;
export declare const MailAllRead24Filled: FluentIcon;
export declare const MailAllRead24Regular: FluentIcon;
export declare const MailAllRead28Filled: FluentIcon;
export declare const MailAllRead28Regular: FluentIcon;
export declare const MailAllUnread20Filled: FluentIcon;
export declare const MailAllUnread20Regular: FluentIcon;
export declare const MailArrowClockwise16Filled: FluentIcon;
export declare const MailArrowClockwise16Regular: FluentIcon;
export declare const MailArrowClockwise20Filled: FluentIcon;
export declare const MailArrowClockwise20Regular: FluentIcon;
export declare const MailArrowClockwise24Filled: FluentIcon;
export declare const MailArrowClockwise24Regular: FluentIcon;
export declare const MailArrowClockwise32Filled: FluentIcon;
export declare const MailArrowClockwise32Light: FluentIcon;
export declare const MailArrowClockwise32Regular: FluentIcon;
export declare const MailArrowDoubleBack16Filled: FluentIcon;
export declare const MailArrowDoubleBack16Regular: FluentIcon;
export declare const MailArrowDoubleBack20Filled: FluentIcon;
export declare const MailArrowDoubleBack20Regular: FluentIcon;
export declare const MailArrowDoubleBack24Filled: FluentIcon;
export declare const MailArrowDoubleBack24Regular: FluentIcon;
export declare const MailArrowDoubleBack32Filled: FluentIcon;
export declare const MailArrowDoubleBack32Light: FluentIcon;
export declare const MailArrowDoubleBack32Regular: FluentIcon;
export declare const MailArrowDown16Filled: FluentIcon;
export declare const MailArrowDown16Regular: FluentIcon;
export declare const MailArrowDown20Filled: FluentIcon;
export declare const MailArrowDown20Regular: FluentIcon;
export declare const MailArrowForward16Filled: FluentIcon;
export declare const MailArrowForward16Regular: FluentIcon;
export declare const MailArrowForward20Filled: FluentIcon;
export declare const MailArrowForward20Regular: FluentIcon;
export declare const MailArrowUp16Filled: FluentIcon;
export declare const MailArrowUp16Regular: FluentIcon;
export declare const MailArrowUp20Filled: FluentIcon;
export declare const MailArrowUp20Regular: FluentIcon;
export declare const MailArrowUp24Filled: FluentIcon;
export declare const MailArrowUp24Regular: FluentIcon;
export declare const MailAttach16Filled: FluentIcon;
export declare const MailAttach16Regular: FluentIcon;
export declare const MailAttach20Filled: FluentIcon;
export declare const MailAttach20Regular: FluentIcon;
export declare const MailAttach24Filled: FluentIcon;
export declare const MailAttach24Regular: FluentIcon;
export declare const MailAttach28Filled: FluentIcon;
export declare const MailAttach28Regular: FluentIcon;
export declare const MailBriefcase48Filled: FluentIcon;
export declare const MailBriefcase48Regular: FluentIcon;
export declare const MailCheckmark16Filled: FluentIcon;
export declare const MailCheckmark16Regular: FluentIcon;
export declare const MailCheckmark20Filled: FluentIcon;
export declare const MailCheckmark20Regular: FluentIcon;
export declare const MailCheckmark24Filled: FluentIcon;
export declare const MailCheckmark24Regular: FluentIcon;
export declare const MailClock16Color: FluentIcon;
export declare const MailClock16Filled: FluentIcon;
export declare const MailClock16Regular: FluentIcon;
export declare const MailClock20Color: FluentIcon;
export declare const MailClock20Filled: FluentIcon;
export declare const MailClock20Regular: FluentIcon;
export declare const MailClock24Color: FluentIcon;
export declare const MailClock24Filled: FluentIcon;
export declare const MailClock24Regular: FluentIcon;
export declare const MailClock32Color: FluentIcon;
export declare const MailClock32Filled: FluentIcon;
export declare const MailClock32Regular: FluentIcon;
export declare const MailCopy20Filled: FluentIcon;
export declare const MailCopy20Regular: FluentIcon;
export declare const MailCopy24Filled: FluentIcon;
export declare const MailCopy24Regular: FluentIcon;
export declare const MailCopy32Filled: FluentIcon;
export declare const MailCopy32Light: FluentIcon;
export declare const MailCopy32Regular: FluentIcon;
export declare const MailDataBar16Filled: FluentIcon;
export declare const MailDataBar16Regular: FluentIcon;
export declare const MailDataBar20Filled: FluentIcon;
export declare const MailDataBar20Regular: FluentIcon;
export declare const MailDataBar24Filled: FluentIcon;
export declare const MailDataBar24Regular: FluentIcon;
export declare const MailDismiss16Filled: FluentIcon;
export declare const MailDismiss16Regular: FluentIcon;
export declare const MailDismiss20Filled: FluentIcon;
export declare const MailDismiss20Regular: FluentIcon;
export declare const MailDismiss24Filled: FluentIcon;
export declare const MailDismiss24Regular: FluentIcon;
export declare const MailDismiss28Filled: FluentIcon;
export declare const MailDismiss28Regular: FluentIcon;
export declare const MailEdit20Filled: FluentIcon;
export declare const MailEdit20Regular: FluentIcon;
export declare const MailEdit24Filled: FluentIcon;
export declare const MailEdit24Regular: FluentIcon;
export declare const MailEdit32Filled: FluentIcon;
export declare const MailEdit32Light: FluentIcon;
export declare const MailEdit32Regular: FluentIcon;
export declare const MailError16Filled: FluentIcon;
export declare const MailError16Regular: FluentIcon;
export declare const MailError20Filled: FluentIcon;
export declare const MailError20Regular: FluentIcon;
export declare const MailError24Filled: FluentIcon;
export declare const MailError24Regular: FluentIcon;
export declare const MailFishHook16Filled: FluentIcon;
export declare const MailFishHook16Regular: FluentIcon;
export declare const MailFishHook20Filled: FluentIcon;
export declare const MailFishHook20Regular: FluentIcon;
export declare const MailFishHook24Filled: FluentIcon;
export declare const MailFishHook24Regular: FluentIcon;
export declare const MailFishHook28Filled: FluentIcon;
export declare const MailFishHook28Regular: FluentIcon;
export declare const MailFishHook32Filled: FluentIcon;
export declare const MailFishHook32Regular: FluentIcon;
export declare const MailFishHook48Filled: FluentIcon;
export declare const MailFishHook48Regular: FluentIcon;
export declare const MailInbox16Filled: FluentIcon;
export declare const MailInbox16Regular: FluentIcon;
export declare const MailInbox20Filled: FluentIcon;
export declare const MailInbox20Regular: FluentIcon;
export declare const MailInbox24Filled: FluentIcon;
export declare const MailInbox24Regular: FluentIcon;
export declare const MailInbox28Filled: FluentIcon;
export declare const MailInbox28Regular: FluentIcon;
export declare const MailInbox32Filled: FluentIcon;
export declare const MailInbox32Regular: FluentIcon;
export declare const MailInbox48Filled: FluentIcon;
export declare const MailInbox48Regular: FluentIcon;
export declare const MailInboxAdd16Filled: FluentIcon;
export declare const MailInboxAdd16Regular: FluentIcon;
export declare const MailInboxAdd20Filled: FluentIcon;
export declare const MailInboxAdd20Regular: FluentIcon;
export declare const MailInboxAdd24Filled: FluentIcon;
export declare const MailInboxAdd24Regular: FluentIcon;
export declare const MailInboxAdd28Filled: FluentIcon;
export declare const MailInboxAdd28Regular: FluentIcon;
export declare const MailInboxAll20Filled: FluentIcon;
export declare const MailInboxAll20Regular: FluentIcon;
export declare const MailInboxAll24Filled: FluentIcon;
export declare const MailInboxAll24Regular: FluentIcon;
export declare const MailInboxArrowDown16Filled: FluentIcon;
export declare const MailInboxArrowDown16Regular: FluentIcon;
export declare const MailInboxArrowDown20Filled: FluentIcon;
export declare const MailInboxArrowDown20Regular: FluentIcon;
export declare const MailInboxArrowRight20Filled: FluentIcon;
export declare const MailInboxArrowRight20Regular: FluentIcon;
export declare const MailInboxArrowRight24Filled: FluentIcon;
export declare const MailInboxArrowRight24Regular: FluentIcon;
export declare const MailInboxArrowUp20Filled: FluentIcon;
export declare const MailInboxArrowUp20Regular: FluentIcon;
export declare const MailInboxArrowUp24Filled: FluentIcon;
export declare const MailInboxArrowUp24Regular: FluentIcon;
export declare const MailInboxCheckmark16Filled: FluentIcon;
export declare const MailInboxCheckmark16Regular: FluentIcon;
export declare const MailInboxCheckmark20Filled: FluentIcon;
export declare const MailInboxCheckmark20Regular: FluentIcon;
export declare const MailInboxCheckmark24Filled: FluentIcon;
export declare const MailInboxCheckmark24Regular: FluentIcon;
export declare const MailInboxCheckmark28Filled: FluentIcon;
export declare const MailInboxCheckmark28Regular: FluentIcon;
export declare const MailInboxDismiss16Filled: FluentIcon;
export declare const MailInboxDismiss16Regular: FluentIcon;
export declare const MailInboxDismiss20Filled: FluentIcon;
export declare const MailInboxDismiss20Regular: FluentIcon;
export declare const MailInboxDismiss24Filled: FluentIcon;
export declare const MailInboxDismiss24Regular: FluentIcon;
export declare const MailInboxDismiss28Filled: FluentIcon;
export declare const MailInboxDismiss28Regular: FluentIcon;
export declare const MailInboxPerson16Filled: FluentIcon;
export declare const MailInboxPerson16Regular: FluentIcon;
export declare const MailInboxPerson20Filled: FluentIcon;
export declare const MailInboxPerson20Regular: FluentIcon;
export declare const MailInboxPerson32Filled: FluentIcon;
export declare const MailInboxPerson32Regular: FluentIcon;
export declare const MailInboxPerson48Filled: FluentIcon;
export declare const MailInboxPerson48Regular: FluentIcon;
export declare const MailLink20Filled: FluentIcon;
export declare const MailLink20Regular: FluentIcon;
export declare const MailLink24Filled: FluentIcon;
export declare const MailLink24Regular: FluentIcon;
export declare const MailList16Filled: FluentIcon;
export declare const MailList16Regular: FluentIcon;
export declare const MailList20Filled: FluentIcon;
export declare const MailList20Regular: FluentIcon;
export declare const MailList24Filled: FluentIcon;
export declare const MailList24Regular: FluentIcon;
export declare const MailList28Filled: FluentIcon;
export declare const MailList28Regular: FluentIcon;
export declare const MailList32Filled: FluentIcon;
export declare const MailList32Light: FluentIcon;
export declare const MailList32Regular: FluentIcon;
export declare const MailMultiple16Color: FluentIcon;
export declare const MailMultiple16Filled: FluentIcon;
export declare const MailMultiple16Regular: FluentIcon;
export declare const MailMultiple20Color: FluentIcon;
export declare const MailMultiple20Filled: FluentIcon;
export declare const MailMultiple20Regular: FluentIcon;
export declare const MailMultiple24Color: FluentIcon;
export declare const MailMultiple24Filled: FluentIcon;
export declare const MailMultiple24Regular: FluentIcon;
export declare const MailMultiple28Color: FluentIcon;
export declare const MailMultiple28Filled: FluentIcon;
export declare const MailMultiple28Regular: FluentIcon;
export declare const MailMultiple32Color: FluentIcon;
export declare const MailMultiple32Filled: FluentIcon;
export declare const MailMultiple32Light: FluentIcon;
export declare const MailMultiple32Regular: FluentIcon;
export declare const MailOff16Filled: FluentIcon;
export declare const MailOff16Regular: FluentIcon;
export declare const MailOff20Filled: FluentIcon;
export declare const MailOff20Regular: FluentIcon;
export declare const MailOff24Filled: FluentIcon;
export declare const MailOff24Regular: FluentIcon;
export declare const MailOpenPerson16Filled: FluentIcon;
export declare const MailOpenPerson16Regular: FluentIcon;
export declare const MailOpenPerson20Filled: FluentIcon;
export declare const MailOpenPerson20Regular: FluentIcon;
export declare const MailOpenPerson24Filled: FluentIcon;
export declare const MailOpenPerson24Regular: FluentIcon;
export declare const MailPause16Filled: FluentIcon;
export declare const MailPause16Regular: FluentIcon;
export declare const MailPause20Filled: FluentIcon;
export declare const MailPause20Regular: FluentIcon;
export declare const MailProhibited16Filled: FluentIcon;
export declare const MailProhibited16Regular: FluentIcon;
export declare const MailProhibited20Filled: FluentIcon;
export declare const MailProhibited20Regular: FluentIcon;
export declare const MailProhibited24Filled: FluentIcon;
export declare const MailProhibited24Regular: FluentIcon;
export declare const MailProhibited28Filled: FluentIcon;
export declare const MailProhibited28Regular: FluentIcon;
export declare const MailRead16Filled: FluentIcon;
export declare const MailRead16Regular: FluentIcon;
export declare const MailRead20Filled: FluentIcon;
export declare const MailRead20Regular: FluentIcon;
export declare const MailRead24Filled: FluentIcon;
export declare const MailRead24Regular: FluentIcon;
export declare const MailRead28Filled: FluentIcon;
export declare const MailRead28Regular: FluentIcon;
export declare const MailRead32Filled: FluentIcon;
export declare const MailRead32Light: FluentIcon;
export declare const MailRead32Regular: FluentIcon;
export declare const MailRead48Filled: FluentIcon;
export declare const MailRead48Regular: FluentIcon;
export declare const MailReadBriefcase20Filled: FluentIcon;
export declare const MailReadBriefcase20Regular: FluentIcon;
export declare const MailReadBriefcase24Filled: FluentIcon;
export declare const MailReadBriefcase24Regular: FluentIcon;
export declare const MailReadBriefcase48Filled: FluentIcon;
export declare const MailReadBriefcase48Regular: FluentIcon;
export declare const MailReadMultiple16Filled: FluentIcon;
export declare const MailReadMultiple16Regular: FluentIcon;
export declare const MailReadMultiple20Filled: FluentIcon;
export declare const MailReadMultiple20Regular: FluentIcon;
export declare const MailReadMultiple24Filled: FluentIcon;
export declare const MailReadMultiple24Regular: FluentIcon;
export declare const MailReadMultiple28Filled: FluentIcon;
export declare const MailReadMultiple28Regular: FluentIcon;
export declare const MailReadMultiple32Filled: FluentIcon;
export declare const MailReadMultiple32Light: FluentIcon;
export declare const MailReadMultiple32Regular: FluentIcon;
export declare const MailRewind16Filled: FluentIcon;
export declare const MailRewind16Regular: FluentIcon;
export declare const MailRewind20Filled: FluentIcon;
export declare const MailRewind20Regular: FluentIcon;
export declare const MailRewind24Filled: FluentIcon;
export declare const MailRewind24Regular: FluentIcon;
export declare const MailRewind32Light: FluentIcon;
export declare const MailSettings16Filled: FluentIcon;
export declare const MailSettings16Regular: FluentIcon;
export declare const MailSettings20Filled: FluentIcon;
export declare const MailSettings20Regular: FluentIcon;
export declare const MailSettings32Light: FluentIcon;
export declare const MailShield16Filled: FluentIcon;
export declare const MailShield16Regular: FluentIcon;
export declare const MailShield20Filled: FluentIcon;
export declare const MailShield20Regular: FluentIcon;
export declare const MailTemplate16Filled: FluentIcon;
export declare const MailTemplate16Regular: FluentIcon;
export declare const MailTemplate20Filled: FluentIcon;
export declare const MailTemplate20Regular: FluentIcon;
export declare const MailTemplate24Filled: FluentIcon;
export declare const MailTemplate24Regular: FluentIcon;
export declare const MailTemplate28Filled: FluentIcon;
export declare const MailTemplate28Regular: FluentIcon;
export declare const MailTemplate32Filled: FluentIcon;
export declare const MailTemplate32Light: FluentIcon;
export declare const MailTemplate32Regular: FluentIcon;
export declare const MailTemplate48Filled: FluentIcon;
export declare const MailTemplate48Regular: FluentIcon;
export declare const MailUnread12Filled: FluentIcon;
export declare const MailUnread12Regular: FluentIcon;
export declare const MailUnread16Filled: FluentIcon;
export declare const MailUnread16Regular: FluentIcon;
export declare const MailUnread20Filled: FluentIcon;
export declare const MailUnread20Regular: FluentIcon;
export declare const MailUnread24Filled: FluentIcon;
export declare const MailUnread24Regular: FluentIcon;
export declare const MailUnread28Filled: FluentIcon;
export declare const MailUnread28Regular: FluentIcon;
export declare const MailUnread32Filled: FluentIcon;
export declare const MailUnread32Light: FluentIcon;
export declare const MailUnread32Regular: FluentIcon;
export declare const MailUnread48Filled: FluentIcon;
export declare const MailUnread48Regular: FluentIcon;
export declare const MailWarning16Filled: FluentIcon;
export declare const MailWarning16Regular: FluentIcon;
export declare const MailWarning20Filled: FluentIcon;
export declare const MailWarning20Regular: FluentIcon;
export declare const MailWarning24Filled: FluentIcon;
export declare const MailWarning24Regular: FluentIcon;
export declare const Mailbox16Filled: FluentIcon;
export declare const Mailbox16Regular: FluentIcon;
export declare const Mailbox20Filled: FluentIcon;
export declare const Mailbox20Regular: FluentIcon;
export declare const Map16Filled: FluentIcon;
export declare const Map16Regular: FluentIcon;
export declare const Map20Filled: FluentIcon;
export declare const Map20Regular: FluentIcon;
export declare const Map24Filled: FluentIcon;
export declare const Map24Regular: FluentIcon;
export declare const MapDrive16Filled: FluentIcon;
export declare const MapDrive16Regular: FluentIcon;
export declare const MapDrive20Filled: FluentIcon;
export declare const MapDrive20Regular: FluentIcon;
export declare const MapDrive24Filled: FluentIcon;
export declare const MapDrive24Regular: FluentIcon;
export declare const Markdown20Filled: FluentIcon;
export declare const Markdown20Regular: FluentIcon;
export declare const MatchAppLayout20Filled: FluentIcon;
export declare const MatchAppLayout20Regular: FluentIcon;
export declare const MatchAppLayout24Filled: FluentIcon;
export declare const MatchAppLayout24Regular: FluentIcon;
export declare const MathFormatLinear20Filled: FluentIcon;
export declare const MathFormatLinear20Regular: FluentIcon;
export declare const MathFormatLinear24Filled: FluentIcon;
export declare const MathFormatLinear24Regular: FluentIcon;
export declare const MathFormatProfessional16Filled: FluentIcon;
export declare const MathFormatProfessional16Regular: FluentIcon;
export declare const MathFormatProfessional20Filled: FluentIcon;
export declare const MathFormatProfessional20Regular: FluentIcon;
export declare const MathFormatProfessional24Filled: FluentIcon;
export declare const MathFormatProfessional24Regular: FluentIcon;
export declare const MathFormula16Filled: FluentIcon;
export declare const MathFormula16Regular: FluentIcon;
export declare const MathFormula20Filled: FluentIcon;
export declare const MathFormula20Regular: FluentIcon;
export declare const MathFormula24Filled: FluentIcon;
export declare const MathFormula24Regular: FluentIcon;
export declare const MathFormula32Filled: FluentIcon;
export declare const MathFormula32Regular: FluentIcon;
export declare const MathFormulaSparkle16Filled: FluentIcon;
export declare const MathFormulaSparkle16Regular: FluentIcon;
export declare const MathFormulaSparkle20Filled: FluentIcon;
export declare const MathFormulaSparkle20Regular: FluentIcon;
export declare const MathSymbols16Filled: FluentIcon;
export declare const MathSymbols16Regular: FluentIcon;
export declare const MathSymbols20Filled: FluentIcon;
export declare const MathSymbols20Regular: FluentIcon;
export declare const MathSymbols24Filled: FluentIcon;
export declare const MathSymbols24Regular: FluentIcon;
export declare const MathSymbols28Filled: FluentIcon;
export declare const MathSymbols28Regular: FluentIcon;
export declare const MathSymbols32Filled: FluentIcon;
export declare const MathSymbols32Regular: FluentIcon;
export declare const MathSymbols48Filled: FluentIcon;
export declare const MathSymbols48Regular: FluentIcon;
export declare const Maximize16Filled: FluentIcon;
export declare const Maximize16Regular: FluentIcon;
export declare const Maximize20Filled: FluentIcon;
export declare const Maximize20Regular: FluentIcon;
export declare const Maximize24Filled: FluentIcon;
export declare const Maximize24Regular: FluentIcon;
export declare const Maximize28Filled: FluentIcon;
export declare const Maximize28Regular: FluentIcon;
export declare const Maximize48Filled: FluentIcon;
export declare const Maximize48Regular: FluentIcon;
export declare const MeetNow16Filled: FluentIcon;
export declare const MeetNow16Regular: FluentIcon;
export declare const MeetNow20Filled: FluentIcon;
export declare const MeetNow20Regular: FluentIcon;
export declare const MeetNow24Filled: FluentIcon;
export declare const MeetNow24Regular: FluentIcon;
export declare const MeetNow28Filled: FluentIcon;
export declare const MeetNow28Regular: FluentIcon;
export declare const MeetNow32Filled: FluentIcon;
export declare const MeetNow32Regular: FluentIcon;
export declare const MeetNow48Filled: FluentIcon;
export declare const MeetNow48Regular: FluentIcon;
export declare const Megaphone12Filled: FluentIcon;
export declare const Megaphone12Regular: FluentIcon;
export declare const Megaphone16Filled: FluentIcon;
export declare const Megaphone16Regular: FluentIcon;
export declare const Megaphone20Filled: FluentIcon;
export declare const Megaphone20Regular: FluentIcon;
export declare const Megaphone24Filled: FluentIcon;
export declare const Megaphone24Regular: FluentIcon;
export declare const Megaphone28Filled: FluentIcon;
export declare const Megaphone28Regular: FluentIcon;
export declare const MegaphoneCircle20Filled: FluentIcon;
export declare const MegaphoneCircle20Regular: FluentIcon;
export declare const MegaphoneCircle24Filled: FluentIcon;
export declare const MegaphoneCircle24Regular: FluentIcon;
export declare const MegaphoneLoud16Color: FluentIcon;
export declare const MegaphoneLoud16Filled: FluentIcon;
export declare const MegaphoneLoud16Regular: FluentIcon;
export declare const MegaphoneLoud20Color: FluentIcon;
export declare const MegaphoneLoud20Filled: FluentIcon;
export declare const MegaphoneLoud20Regular: FluentIcon;
export declare const MegaphoneLoud24Color: FluentIcon;
export declare const MegaphoneLoud24Filled: FluentIcon;
export declare const MegaphoneLoud24Regular: FluentIcon;
export declare const MegaphoneLoud28Color: FluentIcon;
export declare const MegaphoneLoud28Filled: FluentIcon;
export declare const MegaphoneLoud28Regular: FluentIcon;
export declare const MegaphoneLoud32Color: FluentIcon;
export declare const MegaphoneLoud32Filled: FluentIcon;
export declare const MegaphoneLoud32Regular: FluentIcon;
export declare const MegaphoneLoud48Filled: FluentIcon;
export declare const MegaphoneLoud48Regular: FluentIcon;
export declare const MegaphoneOff16Filled: FluentIcon;
export declare const MegaphoneOff16Regular: FluentIcon;
export declare const MegaphoneOff20Filled: FluentIcon;
export declare const MegaphoneOff20Regular: FluentIcon;
export declare const MegaphoneOff24Filled: FluentIcon;
export declare const MegaphoneOff24Regular: FluentIcon;
export declare const MegaphoneOff28Filled: FluentIcon;
export declare const MegaphoneOff28Regular: FluentIcon;
export declare const Memory16Filled: FluentIcon;
export declare const Memory16Regular: FluentIcon;
export declare const Mention12Filled: FluentIcon;
export declare const Mention12Regular: FluentIcon;
export declare const Mention16Filled: FluentIcon;
export declare const Mention16Regular: FluentIcon;
export declare const Mention20Filled: FluentIcon;
export declare const Mention20Regular: FluentIcon;
export declare const Mention24Filled: FluentIcon;
export declare const Mention24Regular: FluentIcon;
export declare const Mention32Filled: FluentIcon;
export declare const Mention32Regular: FluentIcon;
export declare const Mention48Filled: FluentIcon;
export declare const Mention48Regular: FluentIcon;
export declare const MentionArrowDown20Filled: FluentIcon;
export declare const MentionArrowDown20Regular: FluentIcon;
export declare const MentionBrackets20Filled: FluentIcon;
export declare const MentionBrackets20Regular: FluentIcon;
export declare const Merge16Filled: FluentIcon;
export declare const Merge16Regular: FluentIcon;
export declare const Merge20Filled: FluentIcon;
export declare const Merge20Regular: FluentIcon;
export declare const Merge24Filled: FluentIcon;
export declare const Merge24Regular: FluentIcon;
export declare const Mic16Color: FluentIcon;
export declare const Mic16Filled: FluentIcon;
export declare const Mic16Regular: FluentIcon;
export declare const Mic20Color: FluentIcon;
export declare const Mic20Filled: FluentIcon;
export declare const Mic20Regular: FluentIcon;
export declare const Mic24Color: FluentIcon;
export declare const Mic24Filled: FluentIcon;
export declare const Mic24Regular: FluentIcon;
export declare const Mic28Color: FluentIcon;
export declare const Mic28Filled: FluentIcon;
export declare const Mic28Regular: FluentIcon;
export declare const Mic32Color: FluentIcon;
export declare const Mic32Filled: FluentIcon;
export declare const Mic32Light: FluentIcon;
export declare const Mic32Regular: FluentIcon;
export declare const Mic48Color: FluentIcon;
export declare const Mic48Filled: FluentIcon;
export declare const Mic48Regular: FluentIcon;
export declare const MicLink16Filled: FluentIcon;
export declare const MicLink16Regular: FluentIcon;
export declare const MicLink20Filled: FluentIcon;
export declare const MicLink20Regular: FluentIcon;
export declare const MicLink24Filled: FluentIcon;
export declare const MicLink24Regular: FluentIcon;
export declare const MicLink28Filled: FluentIcon;
export declare const MicLink28Regular: FluentIcon;
export declare const MicLink32Filled: FluentIcon;
export declare const MicLink32Regular: FluentIcon;
export declare const MicLink48Filled: FluentIcon;
export declare const MicLink48Regular: FluentIcon;
export declare const MicOff12Filled: FluentIcon;
export declare const MicOff12Regular: FluentIcon;
export declare const MicOff16Filled: FluentIcon;
export declare const MicOff16Regular: FluentIcon;
export declare const MicOff20Filled: FluentIcon;
export declare const MicOff20Regular: FluentIcon;
export declare const MicOff24Filled: FluentIcon;
export declare const MicOff24Regular: FluentIcon;
export declare const MicOff28Filled: FluentIcon;
export declare const MicOff28Regular: FluentIcon;
export declare const MicOff32Filled: FluentIcon;
export declare const MicOff32Regular: FluentIcon;
export declare const MicOff48Filled: FluentIcon;
export declare const MicOff48Regular: FluentIcon;
export declare const MicProhibited16Filled: FluentIcon;
export declare const MicProhibited16Regular: FluentIcon;
export declare const MicProhibited20Filled: FluentIcon;
export declare const MicProhibited20Regular: FluentIcon;
export declare const MicProhibited24Filled: FluentIcon;
export declare const MicProhibited24Regular: FluentIcon;
export declare const MicProhibited28Filled: FluentIcon;
export declare const MicProhibited28Regular: FluentIcon;
export declare const MicProhibited48Filled: FluentIcon;
export declare const MicProhibited48Regular: FluentIcon;
export declare const MicPulse16Filled: FluentIcon;
export declare const MicPulse16Regular: FluentIcon;
export declare const MicPulse20Filled: FluentIcon;
export declare const MicPulse20Regular: FluentIcon;
export declare const MicPulse24Filled: FluentIcon;
export declare const MicPulse24Regular: FluentIcon;
export declare const MicPulse28Filled: FluentIcon;
export declare const MicPulse28Regular: FluentIcon;
export declare const MicPulse32Filled: FluentIcon;
export declare const MicPulse32Regular: FluentIcon;
export declare const MicPulse48Filled: FluentIcon;
export declare const MicPulse48Regular: FluentIcon;
export declare const MicPulseOff16Filled: FluentIcon;
export declare const MicPulseOff16Regular: FluentIcon;
export declare const MicPulseOff20Filled: FluentIcon;
export declare const MicPulseOff20Regular: FluentIcon;
export declare const MicPulseOff24Filled: FluentIcon;
export declare const MicPulseOff24Regular: FluentIcon;
export declare const MicPulseOff28Filled: FluentIcon;
export declare const MicPulseOff28Regular: FluentIcon;
export declare const MicPulseOff32Filled: FluentIcon;
export declare const MicPulseOff32Regular: FluentIcon;
export declare const MicPulseOff48Filled: FluentIcon;
export declare const MicPulseOff48Regular: FluentIcon;
export declare const MicRecord20Filled: FluentIcon;
export declare const MicRecord20Regular: FluentIcon;
export declare const MicRecord24Filled: FluentIcon;
export declare const MicRecord24Regular: FluentIcon;
export declare const MicRecord28Filled: FluentIcon;
export declare const MicRecord28Regular: FluentIcon;
export declare const MicSettings20Filled: FluentIcon;
export declare const MicSettings20Regular: FluentIcon;
export declare const MicSettings24Filled: FluentIcon;
export declare const MicSettings24Regular: FluentIcon;
export declare const MicSparkle16Filled: FluentIcon;
export declare const MicSparkle16Regular: FluentIcon;
export declare const MicSparkle20Filled: FluentIcon;
export declare const MicSparkle20Regular: FluentIcon;
export declare const MicSparkle24Filled: FluentIcon;
export declare const MicSparkle24Regular: FluentIcon;
export declare const MicSync20Filled: FluentIcon;
export declare const MicSync20Regular: FluentIcon;
export declare const Microscope20Filled: FluentIcon;
export declare const Microscope20Regular: FluentIcon;
export declare const Microscope24Filled: FluentIcon;
export declare const Microscope24Regular: FluentIcon;
export declare const Microscope32Filled: FluentIcon;
export declare const Microscope32Regular: FluentIcon;
export declare const Midi20Filled: FluentIcon;
export declare const Midi20Regular: FluentIcon;
export declare const Midi24Filled: FluentIcon;
export declare const Midi24Regular: FluentIcon;
export declare const MobileOptimized20Filled: FluentIcon;
export declare const MobileOptimized20Regular: FluentIcon;
export declare const MobileOptimized24Filled: FluentIcon;
export declare const MobileOptimized24Regular: FluentIcon;
export declare const MobileOptimized28Filled: FluentIcon;
export declare const MobileOptimized28Regular: FluentIcon;
export declare const MobileOptimized32Filled: FluentIcon;
export declare const MobileOptimized32Regular: FluentIcon;
export declare const MobileOptimized48Filled: FluentIcon;
export declare const MobileOptimized48Regular: FluentIcon;
export declare const Mold20Filled: FluentIcon;
export declare const Mold20Regular: FluentIcon;
export declare const Mold24Filled: FluentIcon;
export declare const Mold24Regular: FluentIcon;
export declare const Mold28Filled: FluentIcon;
export declare const Mold28Regular: FluentIcon;
export declare const Molecule16Color: FluentIcon;
export declare const Molecule16Filled: FluentIcon;
export declare const Molecule16Regular: FluentIcon;
export declare const Molecule20Color: FluentIcon;
export declare const Molecule20Filled: FluentIcon;
export declare const Molecule20Regular: FluentIcon;
export declare const Molecule24Color: FluentIcon;
export declare const Molecule24Filled: FluentIcon;
export declare const Molecule24Regular: FluentIcon;
export declare const Molecule28Color: FluentIcon;
export declare const Molecule28Filled: FluentIcon;
export declare const Molecule28Regular: FluentIcon;
export declare const Molecule32Color: FluentIcon;
export declare const Molecule32Filled: FluentIcon;
export declare const Molecule32Light: FluentIcon;
export declare const Molecule32Regular: FluentIcon;
export declare const Molecule48Color: FluentIcon;
export declare const Molecule48Filled: FluentIcon;
export declare const Molecule48Regular: FluentIcon;
export declare const Money16Filled: FluentIcon;
export declare const Money16Regular: FluentIcon;
export declare const Money20Filled: FluentIcon;
export declare const Money20Regular: FluentIcon;
export declare const Money24Filled: FluentIcon;
export declare const Money24Regular: FluentIcon;
export declare const MoneyCalculator20Filled: FluentIcon;
export declare const MoneyCalculator20Regular: FluentIcon;
export declare const MoneyCalculator24Filled: FluentIcon;
export declare const MoneyCalculator24Regular: FluentIcon;
export declare const MoneyDismiss20Filled: FluentIcon;
export declare const MoneyDismiss20Regular: FluentIcon;
export declare const MoneyDismiss24Filled: FluentIcon;
export declare const MoneyDismiss24Regular: FluentIcon;
export declare const MoneyHand16Filled: FluentIcon;
export declare const MoneyHand16Regular: FluentIcon;
export declare const MoneyHand20Filled: FluentIcon;
export declare const MoneyHand20Regular: FluentIcon;
export declare const MoneyHand24Filled: FluentIcon;
export declare const MoneyHand24Regular: FluentIcon;
export declare const MoneyOff20Filled: FluentIcon;
export declare const MoneyOff20Regular: FluentIcon;
export declare const MoneyOff24Filled: FluentIcon;
export declare const MoneyOff24Regular: FluentIcon;
export declare const MoneySettings16Filled: FluentIcon;
export declare const MoneySettings16Regular: FluentIcon;
export declare const MoneySettings20Filled: FluentIcon;
export declare const MoneySettings20Regular: FluentIcon;
export declare const MoneySettings24Filled: FluentIcon;
export declare const MoneySettings24Regular: FluentIcon;
export declare const MoreCircle16Filled: FluentIcon;
export declare const MoreCircle16Regular: FluentIcon;
export declare const MoreCircle20Filled: FluentIcon;
export declare const MoreCircle20Regular: FluentIcon;
export declare const MoreCircle24Filled: FluentIcon;
export declare const MoreCircle24Regular: FluentIcon;
export declare const MoreCircle28Filled: FluentIcon;
export declare const MoreCircle28Regular: FluentIcon;
export declare const MoreCircle32Filled: FluentIcon;
export declare const MoreCircle32Regular: FluentIcon;
export declare const MoreCircle48Filled: FluentIcon;
export declare const MoreCircle48Regular: FluentIcon;
export declare const MoreHorizontal16Filled: FluentIcon;
export declare const MoreHorizontal16Regular: FluentIcon;
export declare const MoreHorizontal20Filled: FluentIcon;
export declare const MoreHorizontal20Regular: FluentIcon;
export declare const MoreHorizontal24Filled: FluentIcon;
export declare const MoreHorizontal24Regular: FluentIcon;
export declare const MoreHorizontal28Filled: FluentIcon;
export declare const MoreHorizontal28Regular: FluentIcon;
export declare const MoreHorizontal32Filled: FluentIcon;
export declare const MoreHorizontal32Regular: FluentIcon;
export declare const MoreHorizontal48Filled: FluentIcon;
export declare const MoreHorizontal48Regular: FluentIcon;
export declare const MoreVertical16Filled: FluentIcon;
export declare const MoreVertical16Regular: FluentIcon;
export declare const MoreVertical20Filled: FluentIcon;
export declare const MoreVertical20Regular: FluentIcon;
export declare const MoreVertical24Filled: FluentIcon;
export declare const MoreVertical24Regular: FluentIcon;
export declare const MoreVertical28Filled: FluentIcon;
export declare const MoreVertical28Regular: FluentIcon;
export declare const MoreVertical32Filled: FluentIcon;
export declare const MoreVertical32Regular: FluentIcon;
export declare const MoreVertical48Filled: FluentIcon;
export declare const MoreVertical48Regular: FluentIcon;
export declare const MountainLocationBottom20Filled: FluentIcon;
export declare const MountainLocationBottom20Regular: FluentIcon;
export declare const MountainLocationBottom24Filled: FluentIcon;
export declare const MountainLocationBottom24Regular: FluentIcon;
export declare const MountainLocationBottom28Filled: FluentIcon;
export declare const MountainLocationBottom28Regular: FluentIcon;
export declare const MountainLocationTop20Filled: FluentIcon;
export declare const MountainLocationTop20Regular: FluentIcon;
export declare const MountainLocationTop24Filled: FluentIcon;
export declare const MountainLocationTop24Regular: FluentIcon;
export declare const MountainLocationTop28Filled: FluentIcon;
export declare const MountainLocationTop28Regular: FluentIcon;
export declare const MountainTrail20Filled: FluentIcon;
export declare const MountainTrail20Regular: FluentIcon;
export declare const MountainTrail24Filled: FluentIcon;
export declare const MountainTrail24Regular: FluentIcon;
export declare const MountainTrail28Filled: FluentIcon;
export declare const MountainTrail28Regular: FluentIcon;
export declare const MoviesAndTv16Filled: FluentIcon;
export declare const MoviesAndTv16Regular: FluentIcon;
export declare const MoviesAndTv20Filled: FluentIcon;
export declare const MoviesAndTv20Regular: FluentIcon;
export declare const MoviesAndTv24Filled: FluentIcon;
export declare const MoviesAndTv24Regular: FluentIcon;
export declare const Multiplier12X20Filled: FluentIcon;
export declare const Multiplier12X20Regular: FluentIcon;
export declare const Multiplier12X24Filled: FluentIcon;
export declare const Multiplier12X24Regular: FluentIcon;
export declare const Multiplier12X28Filled: FluentIcon;
export declare const Multiplier12X28Regular: FluentIcon;
export declare const Multiplier12X32Filled: FluentIcon;
export declare const Multiplier12X32Regular: FluentIcon;
export declare const Multiplier12X48Filled: FluentIcon;
export declare const Multiplier12X48Regular: FluentIcon;
export declare const Multiplier15X20Filled: FluentIcon;
export declare const Multiplier15X20Regular: FluentIcon;
export declare const Multiplier15X24Filled: FluentIcon;
export declare const Multiplier15X24Regular: FluentIcon;
export declare const Multiplier15X28Filled: FluentIcon;
export declare const Multiplier15X28Regular: FluentIcon;
export declare const Multiplier15X32Filled: FluentIcon;
export declare const Multiplier15X32Regular: FluentIcon;
export declare const Multiplier15X48Filled: FluentIcon;
export declare const Multiplier15X48Regular: FluentIcon;
export declare const Multiplier18X20Filled: FluentIcon;
