/// <reference types="react" />
export declare const BookmarkSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkSearch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingFactory16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingFactory20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingFactory28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingFactory32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingFactory48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarChat20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarChat24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarToolbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CameraSwitch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultiple32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultipleLink16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultipleLink20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultipleLink24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultipleLink28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultipleLink32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCardRibbon16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCardRibbon20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCardRibbon24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCardRibbon28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCardRibbon32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCardRibbon48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DatabasePerson20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DatabasePerson24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentFlowchart20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentFlowchart24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableSearch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextExtract20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextExtract24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FastForward16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FastForward28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Flowchart20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Flowchart24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlowchartCircle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlowchartCircle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderSync24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HeartCircle16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HeartCircle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HeartCircle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MegaphoneLoud24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleLock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleLock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonCircle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PhoneUpdateCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PhoneUpdateCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PlugConnected24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rewind16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rewind28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SaveMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SaveMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanTypeCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanTypeCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectObjectSkew20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectObjectSkew24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectObjectSkewDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectObjectSkewDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectObjectSkewEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectObjectSkewEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPause20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPause24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagError16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagError20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagError24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagQuestionMark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagQuestionMark32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextColumnOneWideLightning20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextColumnOneWideLightning24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextFontInfo16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextFontInfo20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextFontInfo24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrayItemAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrayItemAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrayItemRemove20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrayItemRemove24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckBag20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckBag24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckmarkStarburst20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckmarkStarburst24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AccessTime20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AccessibilityCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AddSquare20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Album20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlbumAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlertOn20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AppGeneric20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AppRecent20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AppTitle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitHeight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitHeightDotted20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitWidth20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitWidthDotted20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBounce20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleDownRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleUpLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowExpand20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowFit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowMinimizeVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSortDownLines24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSquareDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepBack20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepOut20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepOver20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTurnBidirectionalDownRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTurnRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowsBidirectional20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AttachText20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AutoFitHeight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AutoFitWidth20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Autocorrect20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Badge20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BatteryCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BatteryWarning20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BluetoothConnected20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BluetoothDisabled20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BluetoothSearching20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkMultiple28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkMultiple32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkMultiple48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BranchForkHint20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BranchForkHint24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BranchForkLink20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BranchForkLink24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarLtr48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarRtl48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataUsageToolbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataUsageToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopCursor16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopCursor20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopCursor24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopCursor28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopFlow20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopFlow24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopSignal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopSignal24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSadSlight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSadSlight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSmileSlight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSmileSlight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodApple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodApple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonArrowLeft16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextBoxSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextBoxSettings24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckCube20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckCube24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowForwardDownLightning20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowForwardDownLightning24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowForwardDownPerson20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowForwardDownPerson24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepIn28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInLeft28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInRight28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepOut24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepOut28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingText20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingText24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingWrench20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingWrench24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Battery1020Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Beach32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Beach48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookClock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookCoins20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookCompass20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookDatabase20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookExclamationMark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookGlobe20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookInformation20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookLetter20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookPulse20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookQuestionMark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookQuestionMarkRtl20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookStar20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookTheta20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderBottom20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderBottomDouble20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderBottomThick20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderNone20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderOutside20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderOutsideThick20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderTop20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderTopBottom20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderTopBottomDouble20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderTopBottomThick20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Branch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingGovernment20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CubeTree20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CubeTree24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawImage20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawImage24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawShape20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawText20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerArrowDownload20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerArrowDownload24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerPlay20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerPlay24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerSubtract20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerSubtract24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Emoji28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Emoji32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Emoji48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodGrains20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodGrains24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardArrowRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Heart32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Heart48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Link12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Link32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Luggage28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Luggage32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Luggage48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyHand20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyHand24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Next28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Next32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Next48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleList28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Previous28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Previous32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Previous48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const QuestionCircle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextParagraph16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarCollision28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarCollision32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Wallet28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Wallet32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Wallet48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WeatherSunny28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WrenchScrewdriver20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WrenchScrewdriver24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBetweenDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBetweenDown24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowRedo28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalculatorArrowClockwise20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalculatorArrowClockwise24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarArrowDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarArrowRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarError20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarMail20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarWeekNumbers20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CallAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CameraOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CellularOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleDown32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleUp32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Circle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockPause20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockPause24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Door28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DoorArrowRight28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImmersiveReader28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const KeyReset20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const KeyReset24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LightbulbFilament48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Line20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Line24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Line48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineDashes20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineDashes24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineDashes48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowUp16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NotepadPerson16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pentagon48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleAdd28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleList24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleQueue20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleQueue24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleSettings28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Print28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScreenSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScreenSearch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SettingsChat20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SettingsChat24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonP16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonP20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonP24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonP28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideArrowRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SoundWaveCircle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Star48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarAdd28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarDismiss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarDismiss28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Triangle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Trophy28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Trophy32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Trophy48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Window28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Window32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Window48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownLeft32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownLeft48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowMaximize48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowMaximizeVertical48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowOutlineUpRight48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowUpLeft48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowUpRight48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookArrowClockwise20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookArrowClockwise24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxArrowLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxArrowLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxArrowUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxArrowUp24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleArrowLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleArrowLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleArrowRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleSearch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxSearch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CellularWarning20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChartMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChartMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxWarning20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockToolbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CubeMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CubeMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopToolbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentBulletListMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentBulletListMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueue20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueue24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueueAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueueAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueueMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueueMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableCube20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableCube24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableTruck20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableTruck24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextToolbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EqualCircle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EqualCircle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardMoney20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardMoney24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageMultiple32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageMultiple48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailAlert28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PuzzlePieceShield20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Ribbon12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Share28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagArrowLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagArrowLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPlay20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPlay24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintApps20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintApps24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Tablet12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Tablet16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Tablet32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Tablet48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
