"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AccordionPanel", {
    enumerable: true,
    get: function() {
        return AccordionPanel;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _useAccordionPanel = require("./useAccordionPanel");
const _renderAccordionPanel = require("./renderAccordionPanel");
const _reactsharedcontexts = require("@fluentui/react-shared-contexts");
const _useAccordionPanelStylesstyles = require("./useAccordionPanelStyles.styles");
const AccordionPanel = /*#__PURE__*/ _react.forwardRef((props, ref)=>{
    const state = (0, _useAccordionPanel.useAccordionPanel_unstable)(props, ref);
    (0, _useAccordionPanelStylesstyles.useAccordionPanelStyles_unstable)(state);
    (0, _reactsharedcontexts.useCustomStyleHook_unstable)('useAccordionPanelStyles_unstable')(state);
    return (0, _renderAccordionPanel.renderAccordionPanel_unstable)(state);
});
AccordionPanel.displayName = 'AccordionPanel';
