{"version": 3, "sources": ["../src/components/Accordion/renderAccordion.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\n\nimport type { AccordionState, AccordionSlots, AccordionContextValues } from './Accordion.types';\nimport { AccordionProvider } from '../../contexts/accordion';\n\n/**\n * Function that renders the final JSX of the component\n */\nexport const renderAccordion_unstable = (state: AccordionState, contextValues: AccordionContextValues) => {\n  assertSlots<AccordionSlots>(state);\n\n  return (\n    <state.root>\n      <AccordionProvider value={contextValues.accordion}>{state.root.children}</AccordionProvider>\n    </state.root>\n  );\n};\n"], "names": ["renderAccordion_unstable", "state", "contextValues", "assertSlots", "_jsx", "root", "Accordi<PERSON><PERSON><PERSON><PERSON>", "value", "accordion", "children"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAWaA;;;eAAAA;;;4BAVb;gCAE4B;2BAGM;AAK3B,MAAMA,2BAA2B,CAACC,OAAuBC;IAC9DC,IAAAA,2BAAAA,EAA4BF;IAE5B,OAAA,WAAA,GACEG,IAAAA,eAAA,EAACH,MAAMI,IAAI,EAAA;kBACT,WAAA,GAAAD,IAAAA,eAAA,EAACE,4BAAAA,EAAAA;YAAkBC,OAAOL,cAAcM,SAAS;sBAAGP,MAAMI,IAAI,CAACI,QAAQ;;;AAG7E"}