{"version": 3, "sources": ["../src/components/Accordion/Accordion.types.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\nimport { AccordionContextValue } from '../../contexts/accordion';\nimport type { AccordionItemValue } from '../AccordionItem/AccordionItem.types';\n\nexport type AccordionIndex = number | number[];\n\nexport type AccordionToggleEvent<E = HTMLElement> = React.MouseEvent<E> | React.KeyboardEvent<E>;\n\nexport type AccordionToggleEventHandler<Value = AccordionItemValue> = (\n  event: AccordionToggleEvent,\n  data: AccordionToggleData<Value>,\n) => void;\n\nexport type AccordionContextValues = {\n  accordion: AccordionContextValue;\n};\n\nexport type AccordionSlots = {\n  root: NonNullable<Slot<'div'>>;\n};\n\nexport type AccordionToggleData<Value = AccordionItemValue> = {\n  value: Value;\n  openItems: Value[];\n};\n\nexport type AccordionProps<Value = AccordionItemValue> = ComponentProps<AccordionSlots> & {\n  /**\n   * Default value for the uncontrolled state of the panel.\n   */\n  defaultOpenItems?: Value | Value[];\n\n  /**\n   * Indicates if Accordion support multiple Panels closed at the same time.\n   */\n  collapsible?: boolean;\n\n  /**\n   * Indicates if Accordion support multiple Panels opened at the same time.\n   */\n  multiple?: boolean;\n\n  /**\n   * @deprecated Arrow keyboard navigation is not recommended for accordions. Consider using Tree if arrow navigation is a hard requirement.\n   * Indicates if keyboard navigation is available and gives two options, linear or circular navigation.\n   */\n  navigation?: 'linear' | 'circular';\n\n  /**\n   * Callback to be called when the opened items change.\n   */\n  // eslint-disable-next-line @nx/workspace-consistent-callback-type -- can't change type of existing callback\n  onToggle?: AccordionToggleEventHandler<Value>;\n\n  /**\n   * Controls the state of the panel.\n   */\n  openItems?: Value | Value[];\n};\n\nexport type AccordionState<Value = AccordionItemValue> = ComponentState<AccordionSlots> & AccordionContextValue<Value>;\n"], "names": [], "rangeMappings": ";;;;;", "mappings": ";;;;;iEAAuB"}