"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useCarouselViewport_unstable", {
    enumerable: true,
    get: function() {
        return useCarouselViewport_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _reactutilities = require("@fluentui/react-utilities");
const _CarouselContext = require("../CarouselContext");
const useCarouselViewport_unstable = (props, ref)=>{
    const hasFocus = _react.useRef(false);
    const hasMouse = _react.useRef(false);
    const viewportRef = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.viewportRef);
    const enableAutoplay = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.enableAutoplay);
    const handleFocusCapture = _react.useCallback(()=>{
        hasFocus.current = true;
        // Will pause autoplay when focus is captured within viewport (if autoplay is initialized)
        enableAutoplay(false, true);
    }, [
        enableAutoplay
    ]);
    const handleBlurCapture = _react.useCallback((e)=>{
        // Will enable autoplay (if initialized) when focus exits viewport
        if (!e.currentTarget.contains(e.relatedTarget)) {
            hasFocus.current = false;
            if (!hasMouse.current) {
                enableAutoplay(true, true);
            }
        }
    }, [
        enableAutoplay
    ]);
    const handleMouseEnter = _react.useCallback(()=>{
        hasMouse.current = true;
        enableAutoplay(false, true);
    }, [
        enableAutoplay
    ]);
    const handleMouseLeave = _react.useCallback(()=>{
        hasMouse.current = false;
        if (!hasFocus.current) {
            enableAutoplay(true, true);
        }
    }, [
        enableAutoplay
    ]);
    const onFocusCapture = (0, _reactutilities.mergeCallbacks)(props.onFocusCapture, handleFocusCapture);
    const onBlurCapture = (0, _reactutilities.mergeCallbacks)(props.onBlurCapture, handleBlurCapture);
    const onMouseEnter = (0, _reactutilities.mergeCallbacks)(props.onMouseEnter, handleMouseEnter);
    const onMouseLeave = (0, _reactutilities.mergeCallbacks)(props.onMouseLeave, handleMouseLeave);
    return {
        components: {
            root: 'div'
        },
        root: _reactutilities.slot.always((0, _reactutilities.getIntrinsicElementProps)('div', {
            ref: (0, _reactutilities.useMergedRefs)(ref, viewportRef),
            role: 'presentation',
            // Draggable ensures dragging is supported (even if not enabled)
            draggable: true,
            ...props,
            onFocusCapture,
            onBlurCapture,
            onMouseEnter,
            onMouseLeave
        }), {
            elementType: 'div'
        })
    };
};
