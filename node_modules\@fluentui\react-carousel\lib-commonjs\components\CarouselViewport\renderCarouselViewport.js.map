{"version": 3, "sources": ["../src/components/CarouselViewport/renderCarouselViewport.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { CarouselViewportState, CarouselViewportSlots } from './CarouselViewport.types';\nimport { CarouselSliderContextValues, CarouselSliderContextProvider } from '../CarouselSlider/CarouselSliderContext';\n\n/**\n * Render the final JSX of CarouselViewport\n */\nexport const renderCarouselViewport_unstable = (\n  state: CarouselViewportState,\n  contextValues: CarouselSliderContextValues,\n) => {\n  assertSlots<CarouselViewportSlots>(state);\n\n  return (\n    <CarouselSliderContextProvider value={contextValues.carouselSlider}>\n      <state.root />\n    </CarouselSliderContextProvider>\n  );\n};\n"], "names": ["renderCarouselViewport_unstable", "state", "contextValues", "assertSlots", "_jsx", "CarouselSliderContextProvider", "value", "carouselSlider", "root"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAUaA;;;eAAAA;;;4BATb;gCAE4B;uCAE+C;AAKpE,MAAMA,kCAAkC,CAC7CC,OACAC;IAEAC,IAAAA,2BAAAA,EAAmCF;IAEnC,OAAA,WAAA,GACEG,IAAAA,eAAA,EAACC,oDAAAA,EAAAA;QAA8BC,OAAOJ,cAAcK,cAAc;kBAChE,WAAA,GAAAH,IAAAA,eAAA,EAACH,MAAMO,IAAI,EAAA,CAAA;;AAGjB"}