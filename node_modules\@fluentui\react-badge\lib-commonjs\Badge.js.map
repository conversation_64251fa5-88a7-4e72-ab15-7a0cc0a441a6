{"version": 3, "sources": ["../src/Badge.ts"], "sourcesContent": ["export type { BadgeProps, BadgeSlots, BadgeState } from './components/Badge/index';\nexport {\n  Badge,\n  badgeClassNames,\n  renderBadge_unstable,\n  useBadgeStyles_unstable,\n  useBadge_unstable,\n} from './components/Badge/index';\n"], "names": ["Badge", "badgeClassNames", "renderBadge_unstable", "useBadgeStyles_unstable", "useBadge_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAEEA,KAAK;eAALA,YAAK;;IACLC,eAAe;eAAfA,sBAAe;;IACfC,oBAAoB;eAApBA,2BAAoB;;IACpBC,uBAAuB;eAAvBA,8BAAuB;;IACvBC,iBAAiB;eAAjBA,wBAAiB;;;uBACZ"}