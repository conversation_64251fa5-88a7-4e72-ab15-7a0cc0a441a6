{"version": 3, "sources": ["../src/activedescendant/scrollIntoView.ts"], "sourcesContent": ["export const scrollIntoView = (target: HTMLElement | null | undefined) => {\n  if (!target) {\n    return;\n  }\n\n  const scrollParent = findScrollableParent(target.parentElement as HTMLElement);\n  if (!scrollParent) {\n    return;\n  }\n\n  const { offsetHeight } = target;\n  const offsetTop = getTotalOffsetTop(target, scrollParent);\n\n  const { scrollMarginTop, scrollMarginBottom } = getScrollMargins(target);\n\n  const { offsetHeight: parentOffsetHeight, scrollTop } = scrollParent;\n\n  const isAbove = offsetTop - scrollMarginTop < scrollTop;\n  const isBelow = offsetTop + offsetHeight + scrollMarginBottom > scrollTop + parentOffsetHeight;\n\n  const buffer = 2;\n\n  if (isAbove) {\n    scrollParent.scrollTo(0, offsetTop - scrollMarginTop - buffer);\n  } else if (isBelow) {\n    scrollParent.scrollTo(0, offsetTop + offsetHeight + scrollMarginBottom - parentOffsetHeight + buffer);\n  }\n};\n\nconst findScrollableParent = (element: HTMLElement | null): HTMLElement | null => {\n  if (!element) {\n    return null;\n  }\n\n  if (element.scrollHeight > element.offsetHeight) {\n    return element;\n  }\n\n  return findScrollableParent(element.parentElement);\n};\n\nconst getTotalOffsetTop = (element: HTMLElement, scrollParent: HTMLElement): number => {\n  if (!element || element === scrollParent) {\n    return 0;\n  }\n\n  if (element.contains(scrollParent)) {\n    // subtract the scroll parent's offset top from the running total if the offsetParent is above it\n    return scrollParent.offsetTop * -1;\n  }\n\n  return element.offsetTop + getTotalOffsetTop(element.offsetParent as HTMLElement, scrollParent);\n};\n\nconst getScrollMargins = (element: HTMLElement) => {\n  const win = element.ownerDocument?.defaultView;\n  if (!win) {\n    return {\n      scrollMarginTop: 0,\n      scrollMarginBottom: 0,\n    };\n  }\n\n  const computedStyles = win.getComputedStyle(element);\n  const scrollMarginTop =\n    getIntValueOfComputedStyle(computedStyles.scrollMarginTop) ??\n    getIntValueOfComputedStyle(computedStyles.scrollMarginBlockStart);\n  const scrollMarginBottom =\n    getIntValueOfComputedStyle(computedStyles.scrollMarginBottom) ??\n    getIntValueOfComputedStyle(computedStyles.scrollMarginBlockEnd);\n  return {\n    scrollMarginTop,\n    scrollMarginBottom,\n  };\n};\n\nconst getIntValueOfComputedStyle = (computedStyle: string) => {\n  return computedStyle ? parseInt(computedStyle, 10) : 0;\n};\n"], "names": ["scrollIntoView", "target", "scrollParent", "findScrollableParent", "parentElement", "offsetHeight", "offsetTop", "getTotalOffsetTop", "scrollMarginTop", "scrollMarginBottom", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentOffsetHeight", "scrollTop", "isAbove", "isBelow", "buffer", "scrollTo", "element", "scrollHeight", "contains", "offsetParent", "win", "ownerDocument", "defaultView", "computedStyles", "getComputedStyle", "getIntValueOfComputedStyle", "scrollMarginBlockStart", "scrollMarginBlockEnd", "computedStyle", "parseInt"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAAaA;;;eAAAA;;;AAAN,MAAMA,iBAAiB,CAACC;IAC7B,IAAI,CAACA,QAAQ;QACX;IACF;IAEA,MAAMC,eAAeC,qBAAqBF,OAAOG,aAAa;IAC9D,IAAI,CAACF,cAAc;QACjB;IACF;IAEA,MAAM,EAAEG,YAAY,EAAE,GAAGJ;IACzB,MAAMK,YAAYC,kBAAkBN,QAAQC;IAE5C,MAAM,EAAEM,eAAe,EAAEC,kBAAkB,EAAE,GAAGC,iBAAiBT;IAEjE,MAAM,EAAEI,cAAcM,kBAAkB,EAAEC,SAAS,EAAE,GAAGV;IAExD,MAAMW,UAAUP,YAAYE,kBAAkBI;IAC9C,MAAME,UAAUR,YAAYD,eAAeI,qBAAqBG,YAAYD;IAE5E,MAAMI,SAAS;IAEf,IAAIF,SAAS;QACXX,aAAac,QAAQ,CAAC,GAAGV,YAAYE,kBAAkBO;IACzD,OAAO,IAAID,SAAS;QAClBZ,aAAac,QAAQ,CAAC,GAAGV,YAAYD,eAAeI,qBAAqBE,qBAAqBI;IAChG;AACF;AAEA,MAAMZ,uBAAuB,CAACc;IAC5B,IAAI,CAACA,SAAS;QACZ,OAAO;IACT;IAEA,IAAIA,QAAQC,YAAY,GAAGD,QAAQZ,YAAY,EAAE;QAC/C,OAAOY;IACT;IAEA,OAAOd,qBAAqBc,QAAQb,aAAa;AACnD;AAEA,MAAMG,oBAAoB,CAACU,SAAsBf;IAC/C,IAAI,CAACe,WAAWA,YAAYf,cAAc;QACxC,OAAO;IACT;IAEA,IAAIe,QAAQE,QAAQ,CAACjB,eAAe;QAClC,iGAAiG;QACjG,OAAOA,aAAaI,SAAS,GAAG,CAAC;IACnC;IAEA,OAAOW,QAAQX,SAAS,GAAGC,kBAAkBU,QAAQG,YAAY,EAAiBlB;AACpF;AAEA,MAAMQ,mBAAmB,CAACO;QACZA;IAAZ,MAAMI,OAAMJ,yBAAAA,QAAQK,aAAa,cAArBL,6CAAAA,uBAAuBM,WAAW;IAC9C,IAAI,CAACF,KAAK;QACR,OAAO;YACLb,iBAAiB;YACjBC,oBAAoB;QACtB;IACF;IAEA,MAAMe,iBAAiBH,IAAII,gBAAgB,CAACR;QAE1CS;IADF,MAAMlB,kBACJkB,CAAAA,8BAAAA,2BAA2BF,eAAehB,eAAe,eAAzDkB,yCAAAA,8BACAA,2BAA2BF,eAAeG,sBAAsB;QAEhED;IADF,MAAMjB,qBACJiB,CAAAA,+BAAAA,2BAA2BF,eAAef,kBAAkB,eAA5DiB,0CAAAA,+BACAA,2BAA2BF,eAAeI,oBAAoB;IAChE,OAAO;QACLpB;QACAC;IACF;AACF;AAEA,MAAMiB,6BAA6B,CAACG;IAClC,OAAOA,gBAAgBC,SAASD,eAAe,MAAM;AACvD"}