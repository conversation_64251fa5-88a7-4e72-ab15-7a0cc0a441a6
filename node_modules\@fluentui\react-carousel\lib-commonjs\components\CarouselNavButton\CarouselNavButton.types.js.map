{"version": 3, "sources": ["../src/components/CarouselNavButton/CarouselNavButton.types.ts"], "sourcesContent": ["import { ARIAButtonSlotProps } from '@fluentui/react-aria';\nimport type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\nimport { CarouselNavState } from '../CarouselNav/CarouselNav.types';\n\nexport type CarouselNavButtonSlots = {\n  /**\n   * ARIA compliant nav buttons used to jump to pages\n   */\n  root: NonNullable<Slot<ARIAButtonSlotProps>>;\n};\n\n/**\n * CarouselNavButton Props\n */\nexport type CarouselNavButtonProps = ComponentProps<CarouselNavButtonSlots> & {};\n\n/**\n * State used in rendering CarouselNavButton\n */\nexport type CarouselNavButtonState = ComponentState<CarouselNavButtonSlots> & {\n  /**\n   * Enables selection state control\n   */\n  selected?: boolean;\n} & Pick<CarouselNavState, 'appearance'>;\n"], "names": [], "rangeMappings": ";;", "mappings": "AAgBA;;CAEC"}