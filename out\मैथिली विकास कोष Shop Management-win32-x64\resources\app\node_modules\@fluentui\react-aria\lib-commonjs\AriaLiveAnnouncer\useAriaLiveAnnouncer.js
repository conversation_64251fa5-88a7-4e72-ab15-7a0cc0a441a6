"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useAriaLiveAnnouncer_unstable", {
    enumerable: true,
    get: function() {
        return useAriaLiveAnnouncer_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _reactsharedcontexts = require("@fluentui/react-shared-contexts");
const _useDomAnnounce = require("./useDomAnnounce");
const _useAriaNotifyAnnounce = require("./useAriaNotifyAnnounce");
const useAriaLiveAnnouncer_unstable = (props)=>{
    const { targetDocument } = (0, _reactsharedcontexts.useFluent_unstable)();
    const domAnnounce = (0, _useDomAnnounce.useDomAnnounce_unstable)();
    const ariaNotifyAnnounce = (0, _useAriaNotifyAnnounce.useAriaNotifyAnnounce_unstable)();
    const announce = _react.useMemo(()=>{
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const supportsAriaNotify = typeof (targetDocument === null || targetDocument === void 0 ? void 0 : targetDocument.ariaNotify) === 'function';
        return supportsAriaNotify ? ariaNotifyAnnounce : domAnnounce;
    }, [
        targetDocument,
        ariaNotifyAnnounce,
        domAnnounce
    ]);
    return {
        announce,
        children: props.children
    };
};
