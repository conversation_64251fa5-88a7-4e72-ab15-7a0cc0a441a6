{"version": 3, "file": "platform.js", "sourceRoot": "", "sources": ["../src/platform.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAA0B;AAC1B,gDAAwB;AACxB,0DAAkD;AAElD,qCAQkB;AAClB,+CAA+C;AAC/C,mCAAyC;AACzC,oDAA4B;AAG5B,MAAa,GAAG;IAOd,YAAY,IAAkB,EAAE,YAAoB;QANpD,kBAAa,GAAoF,SAAS,CAAC;QAE3G,sBAAiB,GAAuB,SAAS,CAAC;QAKhD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,IAAA,uBAAc,EAAC,IAAI,CAAC,CAAC;QAExC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QACzB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM;QACV,0BAA0B;QAC1B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,uBAAuB;QACzB,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,IAAI,oBAAoB;QACtB,0BAA0B;QAC1B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,eAAe;QACjB,0BAA0B;QAC1B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IACpD,CAAC;IAED,IAAI,WAAW;QACb,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YAC/B,OAAO,IAAA,0BAAiB,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,IAAA,oBAAW,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvC,kBAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBACvB,IAAI,CAAC,iBAAiB,GAAG,kBAAE,CAAC,WAAW,CAAC,cAAI,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YACzE,CAAC;YACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,CAAC;IACH,CAAC;IAED,IAAI,WAAW;QACb,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,cAAc;QAChB,OAAO;YACL,IAAI,CAAC,IAAI,CAAC,eAAe;YACzB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAClB,IAAI,CAAC,IAAI,CAAC,IAAI;SACf,CAAC;IACJ,CAAC;IAED,IAAI,mCAAmC;QACrC,OAAO;YACL,IAAI,CAAC,uBAAuB;YAC5B,GAAG,IAAI,CAAC,cAAc;SACvB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,OAAe,EAAE,OAAe;QACrE,IAAA,cAAK,EAAC,YAAY,OAAO,OAAO,OAAO,OAAO,QAAQ,EAAE,CAAC,CAAC;QAC1D,MAAM,kBAAE,CAAC,MAAM,CAAC,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACtG,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,UAAU;QACd,IAAA,cAAK,EAAC,uBAAuB,IAAI,CAAC,WAAW,SAAS,IAAI,CAAC,YAAY,WAAW,CAAC,CAAC;QAEpF,MAAM,kBAAE,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACxE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9B,IAAI,CAAC,aAAa,GAAG;gBACnB,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;aAC1F,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;QAED,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,mCAAmC,CAAC,CAAC;IAC5F,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC1B,MAAM,IAAA,4BAAmB,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACvE,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAErF,MAAM,kBAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACzD,MAAM,EAAE,IAAA,4BAAc,EAAC,IAAI,CAAC,IAAI,CAAC;YACjC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa;SACrC,CAAC,CAAC;QACH,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpF,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACpB,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,aAAa;YACb,kBAAkB;SACnB,CAAC,GAAG,CAAC,KAAK,EAAC,QAAQ,EAAC,EAAE,CAAC,kBAAE,CAAC,MAAM,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAClC,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YACtB,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,cAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;QACrG,CAAC;QAED,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACtC,OAAO,YAAY,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,0BAA0B;YAC1B,IAAA,gBAAO,EAAC,wBAAwB,YAAY,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAED,mBAAmB,CAAC,MAA0B,EAAE,cAAuB;QACrE,IAAI,cAAc,EAAE,CAAC;YACnB,IAAA,gBAAO,EAAC,oBAAoB,MAAM,mCAAmC,MAAM,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzG,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAA,gBAAO,EAAC,mEAAmE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChG,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAU,EAAE,CAAC;YAC1E,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,oCAAoC,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAG,IAAI,CAAC,IAEvC,CAAC,cAAc,CAAC,CAAC;QACnB,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC;QAEjF,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAa,CAAC,CAAC;QAElD,MAAM,IAAI,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,kDAAkD,CAAC,CAAC;QAC5E,CAAC;QAED,IAAA,cAAK,EAAC,iBAAiB,GAAG,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QACrD,MAAM,kBAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;IACjF,CAAC;IAED,uBAAuB,CAAC,CAAS;QAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACnC,OAAO,cAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,cAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,IAAA,cAAK,EAAC,iCAAiC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAE3E,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAErF,MAAM,cAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACtG,IAAI,CAAC,aAAa,GAAG;YACnB,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;SAC1F,CAAC;QACF,MAAM,kBAAE,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAE9C,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,mCAAmC,CAAC,CAAC;IACtF,CAAC;IAED,gBAAgB,CAAC,IAAY;QAC3B,MAAM,EAAE,YAAY,EAAE,GAAG,cAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACjD,OAAO;YACL,SAAS,EAAE,QAAQ;YACnB,IAAI,EAAE,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SACrE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,MAAM,cAAc,GAAG,IAAA,oBAAW,EAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE5D,MAAM,QAAQ,GAAG;YACf,IAAI,CAAC,WAAW;YAChB,GAAG,IAAI,CAAC,cAAc;SACvB,CAAC;QAEF,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;QAEnE,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAClC,QAAQ,CAAC,EAAE,CAAC,kBAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC1G,CAAC,CAAC;QAEH,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,SAAS,GAAG,IAAA,0BAAiB,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YAC/B,IAAA,cAAK,EAAC,UAAU,IAAI,CAAC,WAAW,OAAO,SAAS,EAAE,CAAC,CAAC;YACpD,MAAM,kBAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG;gBACf,SAAS;gBACT,GAAG,IAAI,CAAC,cAAc;aACvB,CAAC;YAEF,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAlSD,kBAkSC"}