{"version": 3, "sources": ["../src/AriaLiveAnnouncer/useAriaLiveAnnouncer.ts"], "sourcesContent": ["import * as React from 'react';\nimport { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nimport { useDomAnnounce_unstable } from './useDomAnnounce';\nimport { useAriaNotifyAnnounce_unstable } from './useAriaNotifyAnnounce';\n\nimport type { AriaLiveAnnouncerState, AriaLiveAnnouncerProps } from './AriaLiveAnnouncer.types';\n\nexport const useAriaLiveAnnouncer_unstable = (props: AriaLiveAnnouncerProps): AriaLiveAnnouncerState => {\n  const { targetDocument } = useFluent();\n  const domAnnounce = useDomAnnounce_unstable();\n  const ariaNotifyAnnounce = useAriaNotifyAnnounce_unstable();\n\n  const announce = React.useMemo(() => {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const supportsAriaNotify = typeof (targetDocument as any)?.ariaNotify === 'function';\n    return supportsAriaNotify ? ariaNotifyAnnounce : domAnnounce;\n  }, [targetDocument, ariaNotifyAnnounce, domAnnounce]);\n\n  return {\n    announce,\n    children: props.children,\n  };\n};\n"], "names": ["useAriaLiveAnnouncer_unstable", "props", "targetDocument", "useFluent", "domAnnounce", "useDomAnnounce_unstable", "ariaNotifyAnnounce", "useAriaNotifyAnnounce_unstable", "announce", "React", "useMemo", "supportsAriaNotify", "ariaNotify", "children"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAOaA;;;eAAAA;;;;iEAPU;qCACyB;gCACR;uCACO;AAIxC,MAAMA,gCAAgC,CAACC;IAC5C,MAAM,EAAEC,cAAc,EAAE,GAAGC,IAAAA,uCAAS;IACpC,MAAMC,cAAcC,IAAAA,uCAAuB;IAC3C,MAAMC,qBAAqBC,IAAAA,qDAA8B;IAEzD,MAAMC,WAAWC,OAAMC,OAAO,CAAC;QAC7B,8DAA8D;QAC9D,MAAMC,qBAAqB,QAAQT,2BAAAA,qCAAD,AAACA,eAAwBU,UAAU,MAAK;QAC1E,OAAOD,qBAAqBL,qBAAqBF;IACnD,GAAG;QAACF;QAAgBI;QAAoBF;KAAY;IAEpD,OAAO;QACLI;QACAK,UAAUZ,MAAMY,QAAQ;IAC1B;AACF"}