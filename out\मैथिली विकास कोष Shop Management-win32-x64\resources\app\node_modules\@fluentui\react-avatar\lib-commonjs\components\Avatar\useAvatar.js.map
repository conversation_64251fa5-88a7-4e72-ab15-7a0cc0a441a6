{"version": 3, "sources": ["../src/components/Avatar/useAvatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { getIntrinsicElementProps, mergeCallbacks, useId, slot } from '@fluentui/react-utilities';\nimport { getInitials } from '../../utils/index';\nimport type { AvatarNamedColor, AvatarProps, AvatarState } from './Avatar.types';\nimport { PersonRegular } from '@fluentui/react-icons';\nimport { PresenceBadge } from '@fluentui/react-badge';\nimport { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nimport { useAvatarContext } from '../../contexts/AvatarContext';\n\nexport const DEFAULT_STRINGS = {\n  active: 'active',\n  inactive: 'inactive',\n};\n\nexport const useAvatar_unstable = (props: AvatarProps, ref: React.Ref<HTMLElement>): AvatarState => {\n  const { dir } = useFluent();\n  const { shape: contextShape, size: contextSize } = useAvatarContext();\n  const {\n    name,\n    size = contextSize ?? (32 as const),\n    shape = contextShape ?? 'circular',\n    active = 'unset',\n    activeAppearance = 'ring',\n    idForColor,\n  } = props;\n  let { color = 'neutral' } = props;\n\n  // Resolve 'colorful' to a specific color name\n  if (color === 'colorful') {\n    color = avatarColors[getHashCode(idForColor ?? name ?? '') % avatarColors.length];\n  }\n\n  const baseId = useId('avatar-');\n\n  const root: AvatarState['root'] = slot.always(\n    getIntrinsicElementProps(\n      'span',\n      {\n        role: 'img',\n        id: baseId,\n        // aria-label and/or aria-labelledby are resolved below\n        ...props,\n        ref,\n      },\n      /* excludedPropNames: */ ['name'],\n    ),\n    { elementType: 'span' },\n  );\n  const [imageHidden, setImageHidden] = React.useState<true | undefined>(undefined);\n  let image: AvatarState['image'] = slot.optional(props.image, {\n    defaultProps: { alt: '', role: 'presentation', 'aria-hidden': true, hidden: imageHidden },\n    elementType: 'img',\n  }); // Image shouldn't be rendered if its src is not set\n  if (!image?.src) {\n    image = undefined;\n  } // Hide the image if it fails to load and restore it on a successful load\n  if (image) {\n    image.onError = mergeCallbacks(image.onError, () => setImageHidden(true));\n    image.onLoad = mergeCallbacks(image.onLoad, () => setImageHidden(undefined));\n  } // Resolve the initials slot, defaulted to getInitials.\n  let initials: AvatarState['initials'] = slot.optional(props.initials, {\n    renderByDefault: true,\n    defaultProps: {\n      children: getInitials(name, dir === 'rtl', { firstInitialOnly: size <= 16 }),\n      id: baseId + '__initials',\n    },\n    elementType: 'span',\n  }); // Don't render the initials slot if it's empty\n  if (!initials?.children) {\n    initials = undefined;\n  } // Render the icon slot *only if* there aren't any initials or image to display\n  let icon: AvatarState['icon'] = undefined;\n  if (!initials && (!image || imageHidden)) {\n    icon = slot.optional(props.icon, {\n      renderByDefault: true,\n      defaultProps: { children: <PersonRegular />, 'aria-hidden': true },\n      elementType: 'span',\n    });\n  }\n  const badge: AvatarState['badge'] = slot.optional(props.badge, {\n    defaultProps: { size: getBadgeSize(size), id: baseId + '__badge' },\n    elementType: PresenceBadge,\n  });\n  let activeAriaLabelElement: AvatarState['activeAriaLabelElement']; // Resolve aria-label and/or aria-labelledby if not provided by the user\n  if (!root['aria-label'] && !root['aria-labelledby']) {\n    if (name) {\n      root['aria-label'] = name; // Include the badge in labelledby if it exists\n      if (badge) {\n        root['aria-labelledby'] = root.id + ' ' + badge.id;\n      }\n    } else if (initials) {\n      // root's aria-label should be the name, but fall back to being labelledby the initials if name is missing\n      root['aria-labelledby'] = initials.id + (badge ? ' ' + badge.id : '');\n    } // Add the active state to the aria label\n    if (active === 'active' || active === 'inactive') {\n      const activeText = DEFAULT_STRINGS[active];\n      if (root['aria-labelledby']) {\n        // If using aria-labelledby, render a hidden span and append it to the labelledby\n        const activeId = baseId + '__active';\n        root['aria-labelledby'] += ' ' + activeId;\n        activeAriaLabelElement = (\n          <span hidden id={activeId}>\n            {activeText}\n          </span>\n        );\n      } else if (root['aria-label']) {\n        // Otherwise, just append it to the aria-label\n        root['aria-label'] += ' ' + activeText;\n      }\n    }\n  }\n  return {\n    size,\n    shape,\n    active,\n    activeAppearance,\n    activeAriaLabelElement,\n    color,\n    components: { root: 'span', initials: 'span', icon: 'span', image: 'img', badge: PresenceBadge },\n    root,\n    initials,\n    icon,\n    image,\n    badge,\n  };\n};\nconst getBadgeSize = (size: AvatarState['size']) => {\n  if (size >= 96) {\n    return 'extra-large';\n  } else if (size >= 64) {\n    return 'large';\n  } else if (size >= 56) {\n    return 'medium';\n  } else if (size >= 40) {\n    return 'small';\n  } else if (size >= 28) {\n    return 'extra-small';\n  } else {\n    return 'tiny';\n  }\n};\n\nconst avatarColors: AvatarNamedColor[] = [\n  'dark-red',\n  'cranberry',\n  'red',\n  'pumpkin',\n  'peach',\n  'marigold',\n  'gold',\n  'brass',\n  'brown',\n  'forest',\n  'seafoam',\n  'dark-green',\n  'light-teal',\n  'teal',\n  'steel',\n  'blue',\n  'royal-blue',\n  'cornflower',\n  'navy',\n  'lavender',\n  'purple',\n  'grape',\n  'lilac',\n  'pink',\n  'magenta',\n  'plum',\n  'beige',\n  'mink',\n  'platinum',\n  'anchor',\n];\n\nconst getHashCode = (str: string): number => {\n  let hashCode = 0;\n  for (let len: number = str.length - 1; len >= 0; len--) {\n    const ch = str.charCodeAt(len);\n    const shift = len % 8;\n    hashCode ^= (ch << shift) + (ch >> (8 - shift)); // eslint-disable-line no-bitwise\n  }\n\n  return hashCode;\n};\n"], "names": ["DEFAULT_STRINGS", "useAvatar_unstable", "active", "inactive", "props", "ref", "dir", "useFluent", "shape", "contextShape", "size", "contextSize", "useAvatarContext", "name", "activeAppearance", "idForColor", "color", "avatarColors", "getHashCode", "length", "baseId", "useId", "root", "slot", "always", "getIntrinsicElementProps", "role", "id", "elementType", "imageHidden", "setImageHidden", "React", "useState", "undefined", "image", "optional", "defaultProps", "alt", "hidden", "src", "onError", "mergeCallbacks", "onLoad", "initials", "renderByDefault", "children", "getInitials", "firstInitialOnly", "icon", "createElement", "PersonRegular", "badge", "getBadgeSize", "PresenceBadge", "activeAriaLabelElement", "activeText", "activeId", "span", "components", "str", "hashCode", "len", "ch", "charCodeAt", "shift"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IASaA,eAAAA;eAAAA;;IAKAC,kBAAAA;eAAAA;;;;iEAdU;gCAC+C;uBAC1C;4BAEE;4BACA;qCACkB;+BACf;AAE1B,MAAMD,kBAAkB;IAC7BE,QAAQ;IACRC,UAAU;AACZ;AAEO,MAAMF,qBAAqB,CAACG,OAAoBC;IACrD,MAAM,EAAEC,GAAG,EAAE,GAAGC,IAAAA,uCAAAA;IAChB,MAAM,EAAEC,OAAOC,YAAY,EAAEC,MAAMC,WAAW,EAAE,GAAGC,IAAAA,+BAAAA;IACnD,MAAM,EACJC,IAAI,EACJH,OAAOC,gBAAAA,QAAAA,gBAAAA,KAAAA,IAAAA,cAAgB,EAAY,EACnCH,QAAQC,iBAAAA,QAAAA,iBAAAA,KAAAA,IAAAA,eAAgB,UAAU,EAClCP,SAAS,OAAO,EAChBY,mBAAmB,MAAM,EACzBC,UAAU,EACX,GAAGX;IACJ,IAAI,EAAEY,QAAQ,SAAS,EAAE,GAAGZ;IAE5B,8CAA8C;IAC9C,IAAIY,UAAU,YAAY;YACSD;QAAjCC,QAAQC,YAAY,CAACC,YAAYH,CAAAA,OAAAA,eAAAA,QAAAA,eAAAA,KAAAA,IAAAA,aAAcF,IAAAA,MAAAA,QAAdE,SAAAA,KAAAA,IAAAA,OAAsB,MAAME,aAAaE,MAAM,CAAC;IACnF;IAEA,MAAMC,SAASC,IAAAA,qBAAAA,EAAM;IAErB,MAAMC,OAA4BC,oBAAAA,CAAKC,MAAM,CAC3CC,IAAAA,wCAAAA,EACE,QACA;QACEC,MAAM;QACNC,IAAIP;QACJ,uDAAuD;QACvD,GAAGhB,KAAK;QACRC;IACF,GACA,sBAAsB,GAAG;QAAC;KAAO,GAEnC;QAAEuB,aAAa;IAAO;IAExB,MAAM,CAACC,aAAaC,eAAe,GAAGC,OAAMC,QAAQ,CAAmBC;IACvE,IAAIC,QAA8BX,oBAAAA,CAAKY,QAAQ,CAAC/B,MAAM8B,KAAK,EAAE;QAC3DE,cAAc;YAAEC,KAAK;YAAIX,MAAM;YAAgB,eAAe;YAAMY,QAAQT;QAAY;QACxFD,aAAa;IACf,IAAI,oDAAoD;IACxD,IAAI,CAACM,CAAAA,UAAAA,QAAAA,UAAAA,KAAAA,IAAAA,KAAAA,IAAAA,MAAOK,GAAG,AAAHA,GAAK;QACfL,QAAQD;IACV,EAAE,yEAAyE;IAC3E,IAAIC,OAAO;QACTA,MAAMM,OAAO,GAAGC,IAAAA,8BAAAA,EAAeP,MAAMM,OAAO,EAAE,IAAMV,eAAe;QACnEI,MAAMQ,MAAM,GAAGD,IAAAA,8BAAAA,EAAeP,MAAMQ,MAAM,EAAE,IAAMZ,eAAeG;IACnE,EAAE,uDAAuD;IACzD,IAAIU,WAAoCpB,oBAAAA,CAAKY,QAAQ,CAAC/B,MAAMuC,QAAQ,EAAE;QACpEC,iBAAiB;QACjBR,cAAc;YACZS,UAAUC,IAAAA,kBAAAA,EAAYjC,MAAMP,QAAQ,OAAO;gBAAEyC,kBAAkBrC,QAAQ;YAAG;YAC1EiB,IAAIP,SAAS;QACf;QACAQ,aAAa;IACf,IAAI,+CAA+C;IACnD,IAAI,CAACe,CAAAA,aAAAA,QAAAA,aAAAA,KAAAA,IAAAA,KAAAA,IAAAA,SAAUE,QAAQ,AAARA,GAAU;QACvBF,WAAWV;IACb,EAAE,+EAA+E;IACjF,IAAIe,OAA4Bf;IAChC,IAAI,CAACU,YAAa,CAAA,CAACT,SAASL,WAAAA,GAAc;QACxCmB,OAAOzB,oBAAAA,CAAKY,QAAQ,CAAC/B,MAAM4C,IAAI,EAAE;YAC/BJ,iBAAiB;YACjBR,cAAc;gBAAES,UAAAA,WAAAA,GAAUd,OAAAkB,aAAA,CAACC,yBAAAA,EAAAA;gBAAkB,eAAe;YAAK;YACjEtB,aAAa;QACf;IACF;IACA,MAAMuB,QAA8B5B,oBAAAA,CAAKY,QAAQ,CAAC/B,MAAM+C,KAAK,EAAE;QAC7Df,cAAc;YAAE1B,MAAM0C,aAAa1C;YAAOiB,IAAIP,SAAS;QAAU;QACjEQ,aAAayB,yBAAAA;IACf;IACA,IAAIC,wBAA+D,wEAAwE;IAC3I,IAAI,CAAChC,IAAI,CAAC,aAAa,IAAI,CAACA,IAAI,CAAC,kBAAkB,EAAE;QACnD,IAAIT,MAAM;YACRS,IAAI,CAAC,aAAa,GAAGT,MAAM,+CAA+C;YAC1E,IAAIsC,OAAO;gBACT7B,IAAI,CAAC,kBAAkB,GAAGA,KAAKK,EAAE,GAAG,MAAMwB,MAAMxB,EAAE;YACpD;QACF,OAAO,IAAIgB,UAAU;YACnB,0GAA0G;YAC1GrB,IAAI,CAAC,kBAAkB,GAAGqB,SAAShB,EAAE,GAAIwB,CAAAA,QAAQ,MAAMA,MAAMxB,EAAE,GAAG,EAAA;QACpE,EAAE,yCAAyC;QAC3C,IAAIzB,WAAW,YAAYA,WAAW,YAAY;YAChD,MAAMqD,aAAavD,eAAe,CAACE,OAAO;YAC1C,IAAIoB,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,iFAAiF;gBACjF,MAAMkC,WAAWpC,SAAS;gBAC1BE,IAAI,CAAC,kBAAkB,IAAI,MAAMkC;gBACjCF,yBAAAA,WAAAA,GACEvB,OAAAkB,aAAA,CAACQ,QAAAA;oBAAKnB,QAAAA;oBAAOX,IAAI6B;mBACdD;YAGP,OAAO,IAAIjC,IAAI,CAAC,aAAa,EAAE;gBAC7B,8CAA8C;gBAC9CA,IAAI,CAAC,aAAa,IAAI,MAAMiC;YAC9B;QACF;IACF;IACA,OAAO;QACL7C;QACAF;QACAN;QACAY;QACAwC;QACAtC;QACA0C,YAAY;YAAEpC,MAAM;YAAQqB,UAAU;YAAQK,MAAM;YAAQd,OAAO;YAAOiB,OAAOE,yBAAAA;QAAc;QAC/F/B;QACAqB;QACAK;QACAd;QACAiB;IACF;AACF;AACA,MAAMC,eAAe,CAAC1C;IACpB,IAAIA,QAAQ,IAAI;QACd,OAAO;IACT,OAAO,IAAIA,QAAQ,IAAI;QACrB,OAAO;IACT,OAAO,IAAIA,QAAQ,IAAI;QACrB,OAAO;IACT,OAAO,IAAIA,QAAQ,IAAI;QACrB,OAAO;IACT,OAAO,IAAIA,QAAQ,IAAI;QACrB,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA,MAAMO,eAAmC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,cAAc,CAACyC;IACnB,IAAIC,WAAW;IACf,IAAK,IAAIC,MAAcF,IAAIxC,MAAM,GAAG,GAAG0C,OAAO,GAAGA,MAAO;QACtD,MAAMC,KAAKH,IAAII,UAAU,CAACF;QAC1B,MAAMG,QAAQH,MAAM;QACpBD,YAAY,AAACE,CAAAA,MAAME,KAAAA,IAAUF,CAAAA,MAAO,IAAIE,KAAAA,GAAS,iCAAiC;IACpF;IAEA,OAAOJ;AACT"}