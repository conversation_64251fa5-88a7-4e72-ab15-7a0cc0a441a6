{"version": 3, "sources": ["../src/AriaLiveAnnouncer/renderAriaLiveAnnouncer.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { AnnounceProvider } from '@fluentui/react-shared-contexts';\n\nimport type { AriaLiveAnnouncerContextValues, AriaLiveAnnouncerState } from './AriaLiveAnnouncer.types';\n\nexport const renderAriaLiveAnnouncer_unstable = (\n  state: AriaLiveAnnouncerState,\n  contextValues: AriaLiveAnnouncerContextValues,\n) => {\n  return <AnnounceProvider value={contextValues.announce}>{state.children}</AnnounceProvider>;\n};\n"], "names": ["renderAriaLiveAnnouncer_unstable", "state", "contextValues", "Ann<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "announce", "children"], "rangeMappings": ";;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAKaA;;;eAAAA;;;;iEALU;qCACU;AAI1B,MAAMA,mCAAmC,CAC9CC,OACAC;IAEA,qBAAO,qBAACC,qCAAgB;QAACC,OAAOF,cAAcG,QAAQ;OAAGJ,MAAMK,QAAQ;AACzE"}