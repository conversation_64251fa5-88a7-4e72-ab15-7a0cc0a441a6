{"version": 3, "sources": ["../src/utils/index.ts"], "sourcesContent": ["export { getInitials } from './getInitials';\nexport { partitionAvatarGroupItems } from './partitionAvatarGroupItems';\nexport type { PartitionAvatarGroupItems, PartitionAvatarGroupItemsOptions } from './partitionAvatarGroupItems';\n"], "names": ["getInitials", "partitionAvatarGroupItems"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAASA,WAAW;eAAXA,wBAAW;;IACXC,yBAAyB;eAAzBA,oDAAyB;;;6BADN;2CACc"}