{"version": 3, "sources": ["../src/AriaLiveAnnouncer/index.ts"], "sourcesContent": ["export { AriaLiveAnnouncer } from './AriaLiveAnnouncer';\nexport type { AriaLiveAnnouncerProps, AriaLiveAnnouncerState } from './AriaLiveAnnouncer.types';\nexport { renderAriaLiveAnnouncer_unstable } from './renderAriaLiveAnnouncer';\nexport { useAriaLiveAnnouncer_unstable } from './useAriaLiveAnnouncer';\nexport { useAriaLiveAnnouncerContextValues_unstable } from './useAriaLiveAnnouncerContextValues';\n"], "names": ["AriaLiveAnnouncer", "renderAriaLiveAnnouncer_unstable", "useAriaLiveAnnouncer_unstable", "useAriaLiveAnnouncerContextValues_unstable"], "rangeMappings": ";;;", "mappings": "AAAA,SAASA,iBAAiB,QAAQ,sBAAsB;AAExD,SAASC,gCAAgC,QAAQ,4BAA4B;AAC7E,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,SAASC,0CAA0C,QAAQ,sCAAsC"}