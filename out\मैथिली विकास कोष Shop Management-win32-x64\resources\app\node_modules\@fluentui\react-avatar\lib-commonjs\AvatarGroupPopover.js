"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AvatarGroupPopover: function() {
        return _index.AvatarGroupPopover;
    },
    avatarGroupPopoverClassNames: function() {
        return _index.avatarGroupPopoverClassNames;
    },
    renderAvatarGroupPopover_unstable: function() {
        return _index.renderAvatarGroupPopover_unstable;
    },
    useAvatarGroupPopoverContextValues_unstable: function() {
        return _index.useAvatarGroupPopoverContextValues_unstable;
    },
    useAvatarGroupPopoverStyles_unstable: function() {
        return _index.useAvatarGroupPopoverStyles_unstable;
    },
    useAvatarGroupPopover_unstable: function() {
        return _index.useAvatarGroupPopover_unstable;
    }
});
const _index = require("./components/AvatarGroupPopover/index");
