{"version": 3, "sources": ["../src/components/AvatarGroup/AvatarGroup.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAvatarGroup_unstable } from './renderAvatarGroup';\nimport { useAvatarGroup_unstable } from './useAvatarGroup';\nimport { useAvatarGroupContextValues } from './useAvatarGroupContextValues';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAvatarGroupStyles_unstable } from './useAvatarGroupStyles.styles';\nimport type { AvatarGroupProps } from './AvatarGroup.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\n/**\n * The AvatarGroup component represents a group of multiple people or entities by taking care of the arrangement\n * of individual Avatars in a spread, stack, or pie layout.\n */\nexport const AvatarGroup: ForwardRefComponent<AvatarGroupProps> = React.forwardRef((props, ref) => {\n  const state = useAvatarGroup_unstable(props, ref);\n  const contextValues = useAvatarGroupContextValues(state);\n\n  useAvatarGroupStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAvatarGroupStyles_unstable')(state);\n\n  return renderAvatarGroup_unstable(state, contextValues);\n});\n\nAvatarGroup.displayName = 'AvatarGroup';\n"], "names": ["React", "renderAvatarGroup_unstable", "useAvatarGroup_unstable", "useAvatarGroupContextValues", "useCustomStyleHook_unstable", "useAvatarGroupStyles_unstable", "AvatarGroup", "forwardRef", "props", "ref", "state", "contextValues", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,2BAA2B,QAAQ,gCAAgC;AAC5E,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,6BAA6B,QAAQ,gCAAgC;AAI9E;;;CAGC,GACD,OAAO,MAAMC,4BAAqDN,MAAMO,UAAU,CAAC,CAACC,OAAOC;IACzF,MAAMC,QAAQR,wBAAwBM,OAAOC;IAC7C,MAAME,gBAAgBR,4BAA4BO;IAElDL,8BAA8BK;IAE9BN,4BAA4B,iCAAiCM;IAE7D,OAAOT,2BAA2BS,OAAOC;AAC3C,GAAG;AAEHL,YAAYM,WAAW,GAAG"}