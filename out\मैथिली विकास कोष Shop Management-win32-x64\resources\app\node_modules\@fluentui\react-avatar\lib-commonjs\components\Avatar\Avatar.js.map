{"version": 3, "sources": ["../src/components/Avatar/Avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAvatar_unstable } from './renderAvatar';\nimport { useAvatar_unstable } from './useAvatar';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAvatarStyles_unstable } from './useAvatarStyles.styles';\nimport type { AvatarProps } from './Avatar.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\nexport const Avatar: ForwardRefComponent<AvatarProps> = React.forwardRef((props, ref) => {\n  const state = useAvatar_unstable(props, ref);\n\n  useAvatarStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAvatarStyles_unstable')(state);\n\n  return renderAvatar_unstable(state);\n});\n\nAvatar.displayName = 'Avatar';\n"], "names": ["Avatar", "React", "forwardRef", "props", "ref", "state", "useAvatar_unstable", "useAvatarStyles_unstable", "useCustomStyleHook_unstable", "renderAvatar_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAQaA;;;eAAAA;;;;iEARU;8BACe;2BACH;qCACS;uCACH;AAIlC,MAAMA,SAAAA,WAAAA,GAA2CC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IAC/E,MAAMC,QAAQC,IAAAA,6BAAAA,EAAmBH,OAAOC;IAExCG,IAAAA,+CAAAA,EAAyBF;IAEzBG,IAAAA,gDAAAA,EAA4B,4BAA4BH;IAExD,OAAOI,IAAAA,mCAAAA,EAAsBJ;AAC/B;AAEAL,OAAOU,WAAW,GAAG"}