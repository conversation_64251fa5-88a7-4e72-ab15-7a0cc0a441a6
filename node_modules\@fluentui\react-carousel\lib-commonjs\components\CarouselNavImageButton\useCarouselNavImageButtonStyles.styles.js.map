{"version": 3, "sources": ["useCarouselNavImageButtonStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses, shorthands } from '@griffel/react';\nimport { tokens } from '@fluentui/react-theme';\nexport const carouselNavImageButtonClassNames = {\n    root: 'fui-CarouselNavImageButton',\n    image: 'fui-CarouselNavImageButton__image'\n};\nconst imageButtonSize = 40;\nconst selectedImageButtonSize = 48;\n/**\n * Styles for the root slot\n */ const useStyles = makeStyles({\n    root: {\n        boxSizing: 'content-box',\n        padding: '0px',\n        margin: `0 ${tokens.spacingHorizontalXS}`,\n        ...shorthands.borderColor(tokens.colorTransparentStroke),\n        borderRadius: tokens.borderRadiusSmall,\n        width: imageButtonSize + 'px',\n        height: imageButtonSize + 'px',\n        overflow: 'hidden',\n        ':hover': {\n            cursor: 'pointer'\n        }\n    },\n    image: {\n        width: imageButtonSize + 'px',\n        height: imageButtonSize + 'px',\n        borderRadius: tokens.borderRadiusSmall\n    },\n    selected: {\n        width: selectedImageButtonSize + 'px',\n        height: selectedImageButtonSize + 'px'\n    }\n});\n/**\n * Apply styling to the CarouselNavImageButton slots based on the state\n */ export const useCarouselNavImageButtonStyles_unstable = (state)=>{\n    'use no memo';\n    const { selected } = state;\n    const styles = useStyles();\n    state.root.className = mergeClasses(carouselNavImageButtonClassNames.root, styles.root, selected && styles.selected, state.root.className);\n    if (state.image) {\n        var _state_image;\n        state.image.className = mergeClasses(carouselNavImageButtonClassNames.image, styles.image, selected && styles.selected, (_state_image = state.image) === null || _state_image === void 0 ? void 0 : _state_image.className);\n    }\n    return state;\n};\n"], "names": ["carouselNavImageButtonClassNames", "useCarouselNavImageButtonStyles_unstable", "root", "image", "imageButtonSize", "selectedImageButtonSize", "useStyles", "__styles", "B7ck84d", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "j<PERSON><PERSON>", "Frg6f3", "t21cq0", "B6of3ja", "B74szlk", "g2u3we", "h3c5rm", "B9xav0g", "zhjwy3", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "a9b677", "Bqenvij", "B68tc82", "Bmxbyg5", "Bpg54ce", "eoavqd", "selected", "d", "p", "h", "state", "styles", "className", "mergeClasses", "_state_image"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAEaA,gCAAgC;eAAhCA;;IAkCIC,wCAAwC;eAAxCA;;;uBApCoC;AAE9C,MAAMD,mCAAmC;IAC5CE,MAAM;IACNC,OAAO;AACX;AACA,MAAMC,kBAAkB;AACxB,MAAMC,0BAA0B;AAChC;;CAEA,GAAI,MAAMC,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAL,MAAA;QAAAM,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAA9B,OAAA;QAAAyB,QAAA;QAAAC,SAAA;QAAAN,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAAO,UAAA;QAAAN,QAAA;QAAAC,SAAA;IAAA;AAAA,GAAA;IAAAM,GAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;KAAA;AAAA;AA0BX,MAAMpC,2CAA4CqC,CAAAA;IACzD;IACA,MAAM,EAAEJ,QAAAA,EAAU,GAAGI;IACrB,MAAMC,SAASjC;IACfgC,MAAMpC,IAAI,CAACsC,SAAS,GAAGC,IAAAA,mBAAY,EAACzC,iCAAiCE,IAAI,EAAEqC,OAAOrC,IAAI,EAAEgC,YAAYK,OAAOL,QAAQ,EAAEI,MAAMpC,IAAI,CAACsC,SAAS;IACzI,IAAIF,MAAMnC,KAAK,EAAE;QACb,IAAIuC;QACJJ,MAAMnC,KAAK,CAACqC,SAAS,GAAGC,IAAAA,mBAAY,EAACzC,iCAAiCG,KAAK,EAAEoC,OAAOpC,KAAK,EAAE+B,YAAYK,OAAOL,QAAQ,EAAE,AAACQ,CAAAA,eAAeJ,MAAMnC,KAAK,AAALA,MAAW,QAAQuC,iBAAiB,KAAK,IAAI,KAAK,IAAIA,aAAaF,SAAS;IAC9N;IACA,OAAOF;AACX"}