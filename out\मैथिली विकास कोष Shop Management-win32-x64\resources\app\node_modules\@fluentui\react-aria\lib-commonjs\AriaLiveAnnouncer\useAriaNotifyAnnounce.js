"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useAriaNotifyAnnounce_unstable", {
    enumerable: true,
    get: function() {
        return useAriaNotifyAnnounce_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _reactsharedcontexts = require("@fluentui/react-shared-contexts");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const useAriaNotifyAnnounce_unstable = ()=>{
    const { targetDocument } = (0, _reactsharedcontexts.useFluent_unstable)();
    const announce = _react.useCallback((message, options = {})=>{
        if (!targetDocument) {
            return;
        }
        const { alert = false, polite } = options;
        // default priority to 0 if polite, 2 if alert, and 1 by default
        // used to set both ariaNotify's priority and interrupt
        const defaultPriority = polite ? 0 : alert ? 2 : 1;
        var _options_priority;
        const priority = (_options_priority = options.priority) !== null && _options_priority !== void 0 ? _options_priority : defaultPriority;
        // map fluent announce options to ariaNotify options
        const ariaNotifyOptions = {
            priority: priority > 1 ? 'high' : 'normal'
        };
        targetDocument.ariaNotify(message, ariaNotifyOptions);
    }, [
        targetDocument
    ]);
    return announce;
};
