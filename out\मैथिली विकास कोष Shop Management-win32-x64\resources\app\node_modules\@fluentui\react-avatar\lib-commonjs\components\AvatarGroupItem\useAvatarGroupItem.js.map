{"version": 3, "sources": ["../src/components/AvatarGroupItem/useAvatarGroupItem.ts"], "sourcesContent": ["import * as React from 'react';\nimport { Avatar } from '../Avatar/Avatar';\nimport { AvatarGroupContext, useAvatarGroupContext_unstable } from '../../contexts/AvatarGroupContext';\nimport { defaultAvatarGroupSize } from '../AvatarGroup/useAvatarGroup';\nimport { slot } from '@fluentui/react-utilities';\nimport { useHasParentContext } from '@fluentui/react-context-selector';\nimport type { AvatarGroupItemProps, AvatarGroupItemState } from './AvatarGroupItem.types';\n\n/**\n * Create the state required to render AvatarGroupItem.\n *\n * The returned state can be modified with hooks such as useAvatarGroupItemStyles_unstable,\n * before being passed to renderAvatarGroupItem_unstable.\n *\n * @param props - props from this instance of AvatarGroupItem\n * @param ref - reference to root HTMLElement of AvatarGroupItem\n */\nexport const useAvatarGroupItem_unstable = (\n  props: AvatarGroupItemProps,\n  ref: React.Ref<HTMLElement>,\n): AvatarGroupItemState => {\n  const groupIsOverflow = useAvatarGroupContext_unstable(ctx => ctx.isOverflow);\n  const groupSize = useAvatarGroupContext_unstable(ctx => ctx.size);\n  const layout = useAvatarGroupContext_unstable(ctx => ctx.layout);\n  // Since the primary slot is not an intrinsic element, getPartitionedNativeProps cannot be used here.\n  const { style, className, ...avatarSlotProps } = props;\n  const size = groupSize ?? defaultAvatarGroupSize;\n  const hasAvatarGroupContext = useHasParentContext(AvatarGroupContext);\n\n  if (process.env.NODE_ENV !== 'production' && !hasAvatarGroupContext) {\n    // eslint-disable-next-line no-console\n    console.warn('AvatarGroupItem must only be used inside an AvatarGroup component.');\n  }\n\n  return {\n    isOverflowItem: groupIsOverflow,\n    layout,\n    size,\n    components: {\n      root: groupIsOverflow ? 'li' : 'div',\n      avatar: Avatar,\n      overflowLabel: 'span',\n    },\n    root: slot.always(props.root, {\n      defaultProps: {\n        style,\n        className,\n      },\n      elementType: groupIsOverflow ? 'li' : 'div',\n    }),\n    avatar: slot.always(props.avatar, {\n      defaultProps: {\n        ref,\n        size,\n        color: 'colorful',\n        ...avatarSlotProps,\n      },\n      elementType: Avatar,\n    }),\n    overflowLabel: slot.always(props.overflowLabel, {\n      defaultProps: {\n        // Avatar already has its aria-label set to the name, this will prevent the name to be read twice.\n        'aria-hidden': true,\n        children: props.name,\n      },\n      elementType: 'span',\n    }),\n  };\n};\n"], "names": ["useAvatarGroupItem_unstable", "props", "ref", "groupIsOverflow", "useAvatarGroupContext_unstable", "ctx", "isOverflow", "groupSize", "size", "layout", "style", "className", "avatarSlotProps", "defaultAvatarGroupSize", "hasAvatarGroupContext", "useHasParentContext", "AvatarGroupContext", "process", "env", "NODE_ENV", "console", "warn", "isOverflowItem", "components", "root", "avatar", "Avatar", "overflowLabel", "slot", "always", "defaultProps", "elementType", "color", "children", "name"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAiBaA;;;eAAAA;;;;iEAjBU;wBACA;oCAC4C;gCAC5B;gCAClB;sCACe;AAY7B,MAAMA,8BAA8B,CACzCC,OACAC;IAEA,MAAMC,kBAAkBC,IAAAA,kDAAAA,EAA+BC,CAAAA,MAAOA,IAAIC,UAAU;IAC5E,MAAMC,YAAYH,IAAAA,kDAAAA,EAA+BC,CAAAA,MAAOA,IAAIG,IAAI;IAChE,MAAMC,SAASL,IAAAA,kDAAAA,EAA+BC,CAAAA,MAAOA,IAAII,MAAM;IAC/D,qGAAqG;IACrG,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAE,GAAGC,iBAAiB,GAAGX;IACjD,MAAMO,OAAOD,cAAAA,QAAAA,cAAAA,KAAAA,IAAAA,YAAaM,sCAAAA;IAC1B,MAAMC,wBAAwBC,IAAAA,yCAAAA,EAAoBC,sCAAAA;IAElD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,CAACL,uBAAuB;QACnE,sCAAsC;QACtCM,QAAQC,IAAI,CAAC;IACf;IAEA,OAAO;QACLC,gBAAgBnB;QAChBM;QACAD;QACAe,YAAY;YACVC,MAAMrB,kBAAkB,OAAO;YAC/BsB,QAAQC,cAAAA;YACRC,eAAe;QACjB;QACAH,MAAMI,oBAAAA,CAAKC,MAAM,CAAC5B,MAAMuB,IAAI,EAAE;YAC5BM,cAAc;gBACZpB;gBACAC;YACF;YACAoB,aAAa5B,kBAAkB,OAAO;QACxC;QACAsB,QAAQG,oBAAAA,CAAKC,MAAM,CAAC5B,MAAMwB,MAAM,EAAE;YAChCK,cAAc;gBACZ5B;gBACAM;gBACAwB,OAAO;gBACP,GAAGpB,eAAe;YACpB;YACAmB,aAAaL,cAAAA;QACf;QACAC,eAAeC,oBAAAA,CAAKC,MAAM,CAAC5B,MAAM0B,aAAa,EAAE;YAC9CG,cAAc;gBACZ,kGAAkG;gBAClG,eAAe;gBACfG,UAAUhC,MAAMiC,IAAI;YACtB;YACAH,aAAa;QACf;IACF;AACF"}