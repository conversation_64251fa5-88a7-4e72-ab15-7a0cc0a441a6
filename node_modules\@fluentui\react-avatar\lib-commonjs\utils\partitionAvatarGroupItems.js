/**
 * Get the inline items and overflowing items based on the array of AvatarGroupItems needed for AvatarGroup.
 *
 * @param options - Configure the partition options
 *
 * @returns Two arrays split into inline items and overflow items based on maxInlineItems.
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "partitionAvatarGroupItems", {
    enumerable: true,
    get: function() {
        return partitionAvatarGroupItems;
    }
});
const partitionAvatarGroupItems = (options)=>{
    const { items } = options;
    const isPie = options.layout === 'pie';
    if (isPie) {
        return {
            inlineItems: items.slice(0, 3),
            overflowItems: items.length > 0 ? items : undefined
        };
    }
    var _options_maxInlineItems;
    const maxInlineItems = (_options_maxInlineItems = options.maxInlineItems) !== null && _options_maxInlineItems !== void 0 ? _options_maxInlineItems : 5;
    const inlineCount = -(maxInlineItems - (items.length > maxInlineItems ? 1 : 0));
    const overflowItems = items.slice(0, inlineCount);
    return {
        inlineItems: items.slice(inlineCount),
        overflowItems: overflowItems.length > 0 ? overflowItems : undefined
    };
};
