{"version": 3, "sources": ["../src/components/AccordionItem/renderAccordionItem.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { AccordionItemState, AccordionItemSlots, AccordionItemContextValues } from './AccordionItem.types';\nimport { AccordionItemProvider } from '../../contexts/accordionItem';\n\n/**\n * Function that renders the final JSX of the component\n */\nexport const renderAccordionItem_unstable = (state: AccordionItemState, contextValues: AccordionItemContextValues) => {\n  assertSlots<AccordionItemSlots>(state);\n\n  return (\n    <state.root>\n      <AccordionItemProvider value={contextValues.accordionItem}>{state.root.children}</AccordionItemProvider>\n    </state.root>\n  );\n};\n"], "names": ["renderAccordionItem_unstable", "state", "contextValues", "assertSlots", "_jsx", "root", "AccordionItemProvider", "value", "accordionItem", "children"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAUaA;;;eAAAA;;;4BATb;gCAE4B;+BAEU;AAK/B,MAAMA,+BAA+B,CAACC,OAA2BC;IACtEC,IAAAA,2BAAAA,EAAgCF;IAEhC,OAAA,WAAA,GACEG,IAAAA,eAAA,EAACH,MAAMI,IAAI,EAAA;kBACT,WAAA,GAAAD,IAAAA,eAAA,EAACE,oCAAAA,EAAAA;YAAsBC,OAAOL,cAAcM,aAAa;sBAAGP,MAAMI,IAAI,CAACI,QAAQ;;;AAGrF"}