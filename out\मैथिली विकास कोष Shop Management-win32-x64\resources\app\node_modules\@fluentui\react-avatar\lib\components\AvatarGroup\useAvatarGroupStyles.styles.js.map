{"version": 3, "names": ["__styles", "mergeClasses", "tokens", "useSizeStyles", "avatarGroupClassNames", "root", "useStyles", "base", "mc9l5x", "qhf8xq", "pie", "Bgl5zvf", "De3pzq", "Bsw6fvg", "d", "m", "useAvatarGroupStyles_unstable", "state", "layout", "size", "styles", "sizeStyles", "className"], "sources": ["useAvatarGroupStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses } from '@griffel/react';\nimport { tokens } from '@fluentui/react-theme';\nimport { useSizeStyles } from '../Avatar/useAvatarStyles.styles';\nexport const avatarGroupClassNames = {\n    root: 'fui-AvatarGroup'\n};\n/**\n * Styles for the root slot.\n */ const useStyles = makeStyles({\n    base: {\n        display: 'inline-flex',\n        position: 'relative'\n    },\n    pie: {\n        clipPath: 'circle(50%)',\n        backgroundColor: tokens.colorTransparentStroke,\n        '@media (forced-colors: active)': {\n            backgroundColor: 'CanvasText'\n        }\n    }\n});\n/**\n * Apply styling to the AvatarGroup slots based on the state\n */ export const useAvatarGroupStyles_unstable = (state)=>{\n    'use no memo';\n    const { layout, size } = state;\n    const styles = useStyles();\n    const sizeStyles = useSizeStyles();\n    state.root.className = mergeClasses(avatarGroupClassNames.root, styles.base, layout === 'pie' && sizeStyles[size], layout === 'pie' && styles.pie, state.root.className);\n    return state;\n};\n"], "mappings": "AAAA,SAAAA,QAAA,EAAqBC,YAAY,QAAQ,gBAAgB;AACzD,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,aAAa,QAAQ,kCAAkC;AAChE,OAAO,MAAMC,qBAAqB,GAAG;EACjCC,IAAI,EAAE;AACV,CAAC;AACD;AACA;AACA;AAAI,MAAMC,SAAS,gBAAGN,QAAA;EAAAO,IAAA;IAAAC,MAAA;IAAAC,MAAA;EAAA;EAAAC,GAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;AAAA;EAAAC,CAAA;EAAAC,CAAA;IAAAA,CAAA;EAAA;AAAA,CAYrB,CAAC;AACF;AACA;AACA;AAAI,OAAO,MAAMC,6BAA6B,GAAIC,KAAK,IAAG;EACtD,aAAa;;EACb,MAAM;IAAEC,MAAM;IAAEC;EAAK,CAAC,GAAGF,KAAK;EAC9B,MAAMG,MAAM,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAMe,UAAU,GAAGlB,aAAa,CAAC,CAAC;EAClCc,KAAK,CAACZ,IAAI,CAACiB,SAAS,GAAGrB,YAAY,CAACG,qBAAqB,CAACC,IAAI,EAAEe,MAAM,CAACb,IAAI,EAAEW,MAAM,KAAK,KAAK,IAAIG,UAAU,CAACF,IAAI,CAAC,EAAED,MAAM,KAAK,KAAK,IAAIE,MAAM,CAACV,GAAG,EAAEO,KAAK,CAACZ,IAAI,CAACiB,SAAS,CAAC;EACxK,OAAOL,KAAK;AAChB,CAAC", "ignoreList": []}