import { __styles, mergeClasses } from '@griffel/react';
import { tokens } from '@fluentui/react-theme';
import { useSizeStyles } from '../Avatar/useAvatarStyles.styles';
export const avatarGroupClassNames = {
  root: 'fui-AvatarGroup'
};
/**
 * Styles for the root slot.
 */
const useStyles = /*#__PURE__*/__styles({
  base: {
    mc9l5x: "ftuwxu6",
    qhf8xq: "f10pi13n"
  },
  pie: {
    Bgl5zvf: "f1uz6ud1",
    De3pzq: "f1ganh6p",
    Bsw6fvg: "fe2ae1k"
  }
}, {
  d: [".ftuwxu6{display:inline-flex;}", ".f10pi13n{position:relative;}", ".f1uz6ud1{clip-path:circle(50%);}", ".f1ganh6p{background-color:var(--colorTransparentStroke);}"],
  m: [["@media (forced-colors: active){.fe2ae1k{background-color:CanvasText;}}", {
    m: "(forced-colors: active)"
  }]]
});
/**
 * Apply styling to the AvatarGroup slots based on the state
 */
export const useAvatarGroupStyles_unstable = state => {
  'use no memo';

  const {
    layout,
    size
  } = state;
  const styles = useStyles();
  const sizeStyles = useSizeStyles();
  state.root.className = mergeClasses(avatarGroupClassNames.root, styles.base, layout === 'pie' && sizeStyles[size], layout === 'pie' && styles.pie, state.root.className);
  return state;
};