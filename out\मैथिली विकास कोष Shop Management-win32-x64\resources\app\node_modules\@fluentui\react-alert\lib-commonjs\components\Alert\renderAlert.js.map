{"version": 3, "sources": ["renderAlert.js"], "sourcesContent": ["  import { jsx as _jsx, jsxs as _jsxs } from \"@fluentui/react-jsx-runtime/jsx-runtime\";\nimport { assertSlots } from '@fluentui/react-utilities';\n/**\n * @deprecated please use the Toast or MessageBar component\n */ // eslint-disable-next-line deprecation/deprecation\nexport const renderAlert_unstable = (state)=>{\n    // eslint-disable-next-line deprecation/deprecation\n    assertSlots(state);\n    return /*#__PURE__*/ _jsxs(state.root, {\n        children: [\n            state.icon && /*#__PURE__*/ _jsx(state.icon, {}),\n            state.avatar && /*#__PURE__*/ _jsx(state.avatar, {}),\n            state.root.children,\n            state.action && /*#__PURE__*/ _jsx(state.action, {})\n        ]\n    });\n};\n"], "names": ["renderAlert_unstable", "state", "assertSlots", "_jsxs", "root", "children", "icon", "_jsx", "avatar", "action"], "mappings": ";;;;+BAKaA;;;eAAAA;;;4BALgC;gCACjB;AAIrB,MAAMA,uBAAuB,CAACC;IACjC,mDAAmD;IACnDC,IAAAA,2BAAW,EAACD;IACZ,OAAO,WAAW,GAAGE,IAAAA,gBAAK,EAACF,MAAMG,IAAI,EAAE;QACnCC,UAAU;YACNJ,MAAMK,IAAI,IAAI,WAAW,GAAGC,IAAAA,eAAI,EAACN,MAAMK,IAAI,EAAE,CAAC;YAC9CL,MAAMO,MAAM,IAAI,WAAW,GAAGD,IAAAA,eAAI,EAACN,MAAMO,MAAM,EAAE,CAAC;YAClDP,MAAMG,IAAI,CAACC,QAAQ;YACnBJ,MAAMQ,MAAM,IAAI,WAAW,GAAGF,IAAAA,eAAI,EAACN,MAAMQ,MAAM,EAAE,CAAC;SACrD;IACL;AACJ"}