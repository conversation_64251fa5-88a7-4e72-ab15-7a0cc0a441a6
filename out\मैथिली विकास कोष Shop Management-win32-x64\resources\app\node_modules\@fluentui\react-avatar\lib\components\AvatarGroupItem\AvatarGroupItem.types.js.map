{"version": 3, "sources": ["../src/components/AvatarGroupItem/AvatarGroupItem.types.ts"], "sourcesContent": ["import { AvatarGroupProps } from '../AvatarGroup/AvatarGroup.types';\nimport type { Avatar, AvatarSize } from '../../Avatar';\nimport type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\n\nexport type AvatarGroupItemSlots = {\n  root: NonNullable<Slot<'div', 'li'>>;\n\n  /**\n   * Avatar that represents a person or entity.\n   */\n  avatar: NonNullable<Slot<typeof Avatar>>;\n\n  /**\n   * Label used for the name of the AvatarGroupItem when rendered as an overflow item.\n   * The content of the label, by default, is the `name` prop from the `avatar` slot.\n   */\n  overflowLabel: NonNullable<Slot<'span'>>;\n};\n\n/**\n * AvatarGroupItem Props\n */\nexport type AvatarGroupItemProps = Omit<ComponentProps<Partial<AvatarGroupItemSlots>, 'avatar'>, 'size' | 'shape'>;\n\n/**\n * State used in rendering AvatarGroupItem\n */\nexport type AvatarGroupItemState = ComponentState<AvatarGroupItemSlots> & {\n  /**\n   * Whether the Avatar is an overflow item.\n   *\n   * @default false\n   */\n  isOverflowItem?: boolean;\n\n  layout: AvatarGroupProps['layout'];\n  size: AvatarSize;\n};\n"], "names": [], "rangeMappings": ";;", "mappings": "AAwBA;;CAEC,GACD,WAUE"}