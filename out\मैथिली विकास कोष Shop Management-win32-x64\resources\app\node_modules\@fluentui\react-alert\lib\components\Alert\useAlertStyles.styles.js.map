{"version": 3, "names": ["tokens", "__styles", "mergeClasses", "shorthands", "createCustomFocusIndicatorStyle", "alertClassNames", "root", "icon", "action", "avatar", "useStyles", "mc9l5x", "Bt984gj", "sshi5w", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "Bgfg5da", "B9xav0g", "oivjwe", "Bn0qgzm", "B4g9neb", "zhjwy3", "wvpqe5", "ibv6hh", "u1mtju", "h3c5rm", "vrafjx", "Bekrc4i", "i8vvqc", "g2u3we", "<PERSON><PERSON><PERSON><PERSON>", "B4j52fo", "irswps", "E5pizo", "Be2twd7", "Bhrd7zp", "sj55zd", "De3pzq", "inverted", "Bqenvij", "j<PERSON><PERSON>", "Frg6f3", "t21cq0", "B6of3ja", "B74szlk", "Bf4jedk", "d", "p", "useIntentIconStyles", "success", "error", "warning", "info", "useIntentIconStylesInverted", "useActionButtonColorInverted", "B8q5s1w", "Bci5o5g", "n8qw10", "Bdrgwmp", "Bfpq7zp", "useAlertStyles_unstable", "state", "appearance", "styles", "intentIconStylesPrimary", "intentIconStylesInverted", "actionStylesInverted", "className", "intent"], "sources": ["useAlertStyles.styles.js"], "sourcesContent": ["import { tokens } from '@fluentui/react-theme';\nimport { makeStyles, mergeClasses, shorthands } from '@griffel/react';\nimport { createCustomFocusIndicatorStyle } from '@fluentui/react-tabster';\n// eslint-disable-next-line deprecation/deprecation\nexport const alertClassNames = {\n    root: 'fui-Alert',\n    icon: 'fui-Alert__icon',\n    action: 'fui-Alert__action',\n    avatar: 'fui-Alert__avatar'\n};\nconst useStyles = makeStyles({\n    root: {\n        display: 'flex',\n        alignItems: 'center',\n        minHeight: '44px',\n        padding: '0 12px',\n        borderRadius: '4px',\n        border: `1px solid ${tokens.colorTransparentStroke}`,\n        boxShadow: tokens.shadow8,\n        fontSize: tokens.fontSizeBase300,\n        fontWeight: tokens.fontWeightSemibold,\n        color: tokens.colorNeutralForeground1,\n        backgroundColor: tokens.colorNeutralBackground1\n    },\n    inverted: {\n        color: tokens.colorNeutralForegroundInverted2,\n        backgroundColor: tokens.colorNeutralBackgroundInverted\n    },\n    icon: {\n        height: '16px',\n        fontSize: '16px',\n        padding: '0 8px 0 0'\n    },\n    avatar: {\n        margin: '0 8px 0 0'\n    },\n    action: {\n        padding: '5px 10px',\n        minWidth: 0,\n        marginLeft: 'auto',\n        color: tokens.colorBrandForeground1\n    }\n});\nconst useIntentIconStyles = makeStyles({\n    success: {\n        color: tokens.colorPaletteGreenForeground3\n    },\n    error: {\n        color: tokens.colorPaletteRedForeground3\n    },\n    warning: {\n        color: tokens.colorPaletteYellowForeground2\n    },\n    info: {\n        color: tokens.colorNeutralForeground2\n    }\n});\nconst useIntentIconStylesInverted = makeStyles({\n    success: {\n        color: tokens.colorPaletteGreenForegroundInverted\n    },\n    error: {\n        color: tokens.colorPaletteRedForegroundInverted\n    },\n    warning: {\n        color: tokens.colorPaletteYellowForegroundInverted\n    },\n    info: {\n        color: tokens.colorNeutralForegroundInverted2\n    }\n});\nconst useActionButtonColorInverted = makeStyles({\n    action: {\n        color: tokens.colorBrandForegroundInverted,\n        ...createCustomFocusIndicatorStyle({\n            ...shorthands.borderColor(tokens.colorTransparentStrokeInteractive),\n            outlineColor: tokens.colorNeutralBackground5Pressed\n        }, {\n            enableOutline: true\n        })\n    }\n});\n/**\n * @deprecated please use the Toast or MessageBar component\n * Apply styling to the Alert slots based on the state\n */ // eslint-disable-next-line deprecation/deprecation\nexport const useAlertStyles_unstable = (state)=>{\n    const inverted = state.appearance === 'inverted';\n    const styles = useStyles();\n    const intentIconStylesPrimary = useIntentIconStyles();\n    const intentIconStylesInverted = useIntentIconStylesInverted();\n    const actionStylesInverted = useActionButtonColorInverted();\n    state.root.className = mergeClasses(alertClassNames.root, styles.root, inverted && styles.inverted, state.root.className);\n    if (state.icon) {\n        state.icon.className = mergeClasses(alertClassNames.icon, styles.icon, state.intent && (inverted ? intentIconStylesInverted[state.intent] : intentIconStylesPrimary[state.intent]), state.icon.className);\n    }\n    if (state.avatar) {\n        state.avatar.className = mergeClasses(alertClassNames.avatar, styles.avatar, state.avatar.className);\n    }\n    if (state.action) {\n        // Note: inverted && actionStylesInverted.action has the highest piority and must be merged last\n        state.action.className = mergeClasses(alertClassNames.action, styles.action, inverted && actionStylesInverted.action, state.action.className);\n    }\n    return state;\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,SAAAC,QAAA,EAAqBC,YAAY,EAAEC,UAAU,QAAQ,gBAAgB;AACrE,SAASC,+BAA+B,QAAQ,yBAAyB;AACzE;AACA,OAAO,MAAMC,eAAe,GAAG;EAC3BC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,iBAAiB;EACvBC,MAAM,EAAE,mBAAmB;EAC3BC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,SAAS,gBAAGT,QAAA;EAAAK,IAAA;IAAAK,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;EAAA;EAAAC,QAAA;IAAAF,MAAA;IAAAC,MAAA;EAAA;EAAAtC,IAAA;IAAAwC,OAAA;IAAAL,OAAA;IAAA5B,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;EAAAT,MAAA;IAAAuC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;EAAA;EAAA5C,MAAA;IAAAM,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAmC,OAAA;IAAAJ,MAAA;IAAAL,MAAA;EAAA;AAAA;EAAAU,CAAA;IAAAC,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;AAAA,CAgCjB,CAAC;AACF,MAAMC,mBAAmB,gBAAGvD,QAAA;EAAAwD,OAAA;IAAAb,MAAA;EAAA;EAAAc,KAAA;IAAAd,MAAA;EAAA;EAAAe,OAAA;IAAAf,MAAA;EAAA;EAAAgB,IAAA;IAAAhB,MAAA;EAAA;AAAA;EAAAU,CAAA;AAAA,CAa3B,CAAC;AACF,MAAMO,2BAA2B,gBAAG5D,QAAA;EAAAwD,OAAA;IAAAb,MAAA;EAAA;EAAAc,KAAA;IAAAd,MAAA;EAAA;EAAAe,OAAA;IAAAf,MAAA;EAAA;EAAAgB,IAAA;IAAAhB,MAAA;EAAA;AAAA;EAAAU,CAAA;AAAA,CAanC,CAAC;AACF,MAAMQ,4BAA4B,gBAAG7D,QAAA;EAAAO,MAAA;IAAAoC,MAAA;IAAAmB,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;EAAA;AAAA;EAAAb,CAAA;AAAA,CAUpC,CAAC;AACF;AACA;AACA;AACA,GAHA,CAGI;AACJ,OAAO,MAAMc,uBAAuB,GAAIC,KAAK,IAAG;EAC5C,MAAMvB,QAAQ,GAAGuB,KAAK,CAACC,UAAU,KAAK,UAAU;EAChD,MAAMC,MAAM,GAAG7D,SAAS,CAAC,CAAC;EAC1B,MAAM8D,uBAAuB,GAAGhB,mBAAmB,CAAC,CAAC;EACrD,MAAMiB,wBAAwB,GAAGZ,2BAA2B,CAAC,CAAC;EAC9D,MAAMa,oBAAoB,GAAGZ,4BAA4B,CAAC,CAAC;EAC3DO,KAAK,CAAC/D,IAAI,CAACqE,SAAS,GAAGzE,YAAY,CAACG,eAAe,CAACC,IAAI,EAAEiE,MAAM,CAACjE,IAAI,EAAEwC,QAAQ,IAAIyB,MAAM,CAACzB,QAAQ,EAAEuB,KAAK,CAAC/D,IAAI,CAACqE,SAAS,CAAC;EACzH,IAAIN,KAAK,CAAC9D,IAAI,EAAE;IACZ8D,KAAK,CAAC9D,IAAI,CAACoE,SAAS,GAAGzE,YAAY,CAACG,eAAe,CAACE,IAAI,EAAEgE,MAAM,CAAChE,IAAI,EAAE8D,KAAK,CAACO,MAAM,KAAK9B,QAAQ,GAAG2B,wBAAwB,CAACJ,KAAK,CAACO,MAAM,CAAC,GAAGJ,uBAAuB,CAACH,KAAK,CAACO,MAAM,CAAC,CAAC,EAAEP,KAAK,CAAC9D,IAAI,CAACoE,SAAS,CAAC;EAC7M;EACA,IAAIN,KAAK,CAAC5D,MAAM,EAAE;IACd4D,KAAK,CAAC5D,MAAM,CAACkE,SAAS,GAAGzE,YAAY,CAACG,eAAe,CAACI,MAAM,EAAE8D,MAAM,CAAC9D,MAAM,EAAE4D,KAAK,CAAC5D,MAAM,CAACkE,SAAS,CAAC;EACxG;EACA,IAAIN,KAAK,CAAC7D,MAAM,EAAE;IACd;IACA6D,KAAK,CAAC7D,MAAM,CAACmE,SAAS,GAAGzE,YAAY,CAACG,eAAe,CAACG,MAAM,EAAE+D,MAAM,CAAC/D,MAAM,EAAEsC,QAAQ,IAAI4B,oBAAoB,CAAClE,MAAM,EAAE6D,KAAK,CAAC7D,MAAM,CAACmE,SAAS,CAAC;EACjJ;EACA,OAAON,KAAK;AAChB,CAAC", "ignoreList": []}