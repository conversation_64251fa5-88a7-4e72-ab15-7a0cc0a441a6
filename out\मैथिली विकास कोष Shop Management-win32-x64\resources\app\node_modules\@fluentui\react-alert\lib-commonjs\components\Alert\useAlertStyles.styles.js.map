{"version": 3, "sources": ["useAlertStyles.styles.js"], "sourcesContent": ["import { tokens } from '@fluentui/react-theme';\nimport { __styles, mergeClasses, shorthands } from '@griffel/react';\nimport { createCustomFocusIndicatorStyle } from '@fluentui/react-tabster';\n// eslint-disable-next-line deprecation/deprecation\nexport const alertClassNames = {\n  root: 'fui-<PERSON>ert',\n  icon: 'fui-Alert__icon',\n  action: 'fui-Alert__action',\n  avatar: 'fui-Alert__avatar'\n};\nconst useStyles = /*#__PURE__*/__styles({\n  root: {\n    mc9l5x: \"f22iagw\",\n    Bt984gj: \"f122n59\",\n    sshi5w: \"f5pgtk9\",\n    Byoj8tv: 0,\n    uwmqm3: 0,\n    z189sj: 0,\n    z8tnut: 0,\n    B0ocmuz: \"f1oic3e7\",\n    Beyfa6y: 0,\n    Bbmb7ep: 0,\n    Btl43ni: 0,\n    B7oj6ja: 0,\n    <PERSON><PERSON>: \"ff3glw6\",\n    Bgfg5da: 0,\n    B9xav0g: 0,\n    oivjwe: 0,\n    Bn0qgzm: 0,\n    B4g9neb: 0,\n    zhjwy3: 0,\n    wvpqe5: 0,\n    ibv6hh: 0,\n    u1mtju: 0,\n    h3c5rm: 0,\n    vrafjx: 0,\n    Bekrc4i: 0,\n    i8vvqc: 0,\n    g2u3we: 0,\n    icvyot: 0,\n    B4j52fo: 0,\n    irswps: \"f9ggezi\",\n    E5pizo: \"fz58gqq\",\n    Be2twd7: \"fkhj508\",\n    Bhrd7zp: \"fl43uef\",\n    sj55zd: \"f19n0e5\",\n    De3pzq: \"fxugw4r\"\n  },\n  inverted: {\n    sj55zd: \"f1w7i9ko\",\n    De3pzq: \"f5pduvr\"\n  },\n  icon: {\n    Bqenvij: \"fd461yt\",\n    Be2twd7: \"f4ybsrx\",\n    Byoj8tv: 0,\n    uwmqm3: 0,\n    z189sj: 0,\n    z8tnut: 0,\n    B0ocmuz: [\"fhivll6\", \"f1cgepy8\"]\n  },\n  avatar: {\n    jrapky: 0,\n    Frg6f3: 0,\n    t21cq0: 0,\n    B6of3ja: 0,\n    B74szlk: [\"fxal17o\", \"ftghr3s\"]\n  },\n  action: {\n    Byoj8tv: 0,\n    uwmqm3: 0,\n    z189sj: 0,\n    z8tnut: 0,\n    B0ocmuz: \"f4jnnbt\",\n    Bf4jedk: \"fy77jfu\",\n    Frg6f3: [\"fcgxt0o\", \"f1ujusj6\"],\n    sj55zd: \"f16muhyy\"\n  }\n}, {\n  d: [\".f22iagw{display:flex;}\", \".f122n59{align-items:center;}\", \".f5pgtk9{min-height:44px;}\", [\".f1oic3e7{padding:0 12px;}\", {\n    p: -1\n  }], [\".ff3glw6{border-radius:4px;}\", {\n    p: -1\n  }], [\".f9ggezi{border:1px solid var(--colorTransparentStroke);}\", {\n    p: -2\n  }], \".fz58gqq{box-shadow:var(--shadow8);}\", \".fkhj508{font-size:var(--fontSizeBase300);}\", \".fl43uef{font-weight:var(--fontWeightSemibold);}\", \".f19n0e5{color:var(--colorNeutralForeground1);}\", \".fxugw4r{background-color:var(--colorNeutralBackground1);}\", \".f1w7i9ko{color:var(--colorNeutralForegroundInverted2);}\", \".f5pduvr{background-color:var(--colorNeutralBackgroundInverted);}\", \".fd461yt{height:16px;}\", \".f4ybsrx{font-size:16px;}\", [\".fhivll6{padding:0 8px 0 0;}\", {\n    p: -1\n  }], [\".f1cgepy8{padding:0 0 0 8px;}\", {\n    p: -1\n  }], [\".fxal17o{margin:0 8px 0 0;}\", {\n    p: -1\n  }], [\".ftghr3s{margin:0 0 0 8px;}\", {\n    p: -1\n  }], [\".f4jnnbt{padding:5px 10px;}\", {\n    p: -1\n  }], \".fy77jfu{min-width:0;}\", \".fcgxt0o{margin-left:auto;}\", \".f1ujusj6{margin-right:auto;}\", \".f16muhyy{color:var(--colorBrandForeground1);}\"]\n});\nconst useIntentIconStyles = /*#__PURE__*/__styles({\n  success: {\n    sj55zd: \"f1m7fhi8\"\n  },\n  error: {\n    sj55zd: \"f1whyuy6\"\n  },\n  warning: {\n    sj55zd: \"fpti2h4\"\n  },\n  info: {\n    sj55zd: \"fkfq4zb\"\n  }\n}, {\n  d: [\".f1m7fhi8{color:var(--colorPaletteGreenForeground3);}\", \".f1whyuy6{color:var(--colorPaletteRedForeground3);}\", \".fpti2h4{color:var(--colorPaletteYellowForeground2);}\", \".fkfq4zb{color:var(--colorNeutralForeground2);}\"]\n});\nconst useIntentIconStylesInverted = /*#__PURE__*/__styles({\n  success: {\n    sj55zd: \"f1pvjcpr\"\n  },\n  error: {\n    sj55zd: \"fcrp5ll\"\n  },\n  warning: {\n    sj55zd: \"f1r8f1cl\"\n  },\n  info: {\n    sj55zd: \"f1w7i9ko\"\n  }\n}, {\n  d: [\".f1pvjcpr{color:var(--colorPaletteGreenForegroundInverted);}\", \".fcrp5ll{color:var(--colorPaletteRedForegroundInverted);}\", \".f1r8f1cl{color:var(--colorPaletteYellowForegroundInverted);}\", \".f1w7i9ko{color:var(--colorNeutralForegroundInverted2);}\"]\n});\nconst useActionButtonColorInverted = /*#__PURE__*/__styles({\n  action: {\n    sj55zd: \"f1qz2gb0\",\n    B8q5s1w: \"fa5e339\",\n    Bci5o5g: [\"fk4svks\", \"fqzoz0o\"],\n    n8qw10: \"fw8q0i0\",\n    Bdrgwmp: [\"fqzoz0o\", \"fk4svks\"],\n    Bfpq7zp: \"f1dlk4fq\"\n  }\n}, {\n  d: [\".f1qz2gb0{color:var(--colorBrandForegroundInverted);}\", \".fa5e339[data-fui-focus-visible]{border-top-color:var(--colorTransparentStrokeInteractive);}\", \".fk4svks[data-fui-focus-visible]{border-right-color:var(--colorTransparentStrokeInteractive);}\", \".fqzoz0o[data-fui-focus-visible]{border-left-color:var(--colorTransparentStrokeInteractive);}\", \".fw8q0i0[data-fui-focus-visible]{border-bottom-color:var(--colorTransparentStrokeInteractive);}\", \".f1dlk4fq[data-fui-focus-visible]{outline-color:var(--colorNeutralBackground5Pressed);}\"]\n});\n/**\n * @deprecated please use the Toast or MessageBar component\n * Apply styling to the Alert slots based on the state\n */ // eslint-disable-next-line deprecation/deprecation\nexport const useAlertStyles_unstable = state => {\n  const inverted = state.appearance === 'inverted';\n  const styles = useStyles();\n  const intentIconStylesPrimary = useIntentIconStyles();\n  const intentIconStylesInverted = useIntentIconStylesInverted();\n  const actionStylesInverted = useActionButtonColorInverted();\n  state.root.className = mergeClasses(alertClassNames.root, styles.root, inverted && styles.inverted, state.root.className);\n  if (state.icon) {\n    state.icon.className = mergeClasses(alertClassNames.icon, styles.icon, state.intent && (inverted ? intentIconStylesInverted[state.intent] : intentIconStylesPrimary[state.intent]), state.icon.className);\n  }\n  if (state.avatar) {\n    state.avatar.className = mergeClasses(alertClassNames.avatar, styles.avatar, state.avatar.className);\n  }\n  if (state.action) {\n    // Note: inverted && actionStylesInverted.action has the highest piority and must be merged last\n    state.action.className = mergeClasses(alertClassNames.action, styles.action, inverted && actionStylesInverted.action, state.action.className);\n  }\n  return state;\n};\n//# sourceMappingURL=useAlertStyles.styles.js.map"], "names": ["alertClassNames", "useAlertStyles_unstable", "root", "icon", "action", "avatar", "useStyles", "__styles", "mc9l5x", "Bt984gj", "sshi5w", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "Bgfg5da", "B9xav0g", "oivjwe", "Bn0qgzm", "B4g9neb", "zhjwy3", "wvpqe5", "ibv6hh", "u1mtju", "h3c5rm", "vrafjx", "Bekrc4i", "i8vvqc", "g2u3we", "<PERSON><PERSON><PERSON><PERSON>", "B4j52fo", "irswps", "E5pizo", "Be2twd7", "Bhrd7zp", "sj55zd", "De3pzq", "inverted", "Bqenvij", "j<PERSON><PERSON>", "Frg6f3", "t21cq0", "B6of3ja", "B74szlk", "Bf4jedk", "d", "p", "useIntentIconStyles", "success", "error", "warning", "info", "useIntentIconStylesInverted", "useActionButtonColorInverted", "B8q5s1w", "Bci5o5g", "n8qw10", "Bdrgwmp", "Bfpq7zp", "state", "appearance", "styles", "intentIconStylesPrimary", "intentIconStylesInverted", "actionStylesInverted", "className", "mergeClasses", "intent"], "mappings": ";;;;;;;;;;;IAIaA,eAAe;eAAfA;;IA6IAC,uBAAuB;eAAvBA;;;uBAhJsC;AAG5C,MAAMD,kBAAkB;IAC7BE,MAAM;IACNC,MAAM;IACNC,QAAQ;IACRC,QAAQ;AACV;AACA,MAAMC,YAAY,WAAW,GAAEC,IAAAA,eAAQ,EAAC;IACtCL,MAAM;QACJM,QAAQ;QACRC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,QAAQ;QACRC,SAAS;QACTC,SAAS;QACTC,SAAS;QACTC,SAAS;QACTC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,QAAQ;QACRC,QAAQ;QACRC,QAAQ;QACRC,QAAQ;QACRC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,QAAQ;QACRC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,SAAS;QACTC,SAAS;QACTC,QAAQ;QACRC,QAAQ;IACV;IACAC,UAAU;QACRF,QAAQ;QACRC,QAAQ;IACV;IACAvC,MAAM;QACJyC,SAAS;QACTL,SAAS;QACT5B,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,QAAQ;QACRC,SAAS;YAAC;YAAW;SAAW;IAClC;IACAV,QAAQ;QACNwC,QAAQ;QACRC,QAAQ;QACRC,QAAQ;QACRC,SAAS;QACTC,SAAS;YAAC;YAAW;SAAU;IACjC;IACA7C,QAAQ;QACNO,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,QAAQ;QACRC,SAAS;QACTmC,SAAS;QACTJ,QAAQ;YAAC;YAAW;SAAW;QAC/BL,QAAQ;IACV;AACF,GAAG;IACDU,GAAG;QAAC;QAA2B;QAAiC;QAA8B;YAAC;YAA8B;gBAC3HC,GAAG,CAAC;YACN;SAAE;QAAE;YAAC;YAAgC;gBACnCA,GAAG,CAAC;YACN;SAAE;QAAE;YAAC;YAA6D;gBAChEA,GAAG,CAAC;YACN;SAAE;QAAE;QAAwC;QAA+C;QAAoD;QAAmD;QAA8D;QAA4D;QAAqE;QAA0B;QAA6B;YAAC;YAAgC;gBACvdA,GAAG,CAAC;YACN;SAAE;QAAE;YAAC;YAAiC;gBACpCA,GAAG,CAAC;YACN;SAAE;QAAE;YAAC;YAA+B;gBAClCA,GAAG,CAAC;YACN;SAAE;QAAE;YAAC;YAA+B;gBAClCA,GAAG,CAAC;YACN;SAAE;QAAE;YAAC;YAA+B;gBAClCA,GAAG,CAAC;YACN;SAAE;QAAE;QAA0B;QAA+B;QAAiC;KAAiD;AACjJ;AACA,MAAMC,sBAAsB,WAAW,GAAE9C,IAAAA,eAAQ,EAAC;IAChD+C,SAAS;QACPb,QAAQ;IACV;IACAc,OAAO;QACLd,QAAQ;IACV;IACAe,SAAS;QACPf,QAAQ;IACV;IACAgB,MAAM;QACJhB,QAAQ;IACV;AACF,GAAG;IACDU,GAAG;QAAC;QAAyD;QAAuD;QAAyD;KAAkD;AACjO;AACA,MAAMO,8BAA8B,WAAW,GAAEnD,IAAAA,eAAQ,EAAC;IACxD+C,SAAS;QACPb,QAAQ;IACV;IACAc,OAAO;QACLd,QAAQ;IACV;IACAe,SAAS;QACPf,QAAQ;IACV;IACAgB,MAAM;QACJhB,QAAQ;IACV;AACF,GAAG;IACDU,GAAG;QAAC;QAAgE;QAA6D;QAAiE;KAA2D;AAC/P;AACA,MAAMQ,+BAA+B,WAAW,GAAEpD,IAAAA,eAAQ,EAAC;IACzDH,QAAQ;QACNqC,QAAQ;QACRmB,SAAS;QACTC,SAAS;YAAC;YAAW;SAAU;QAC/BC,QAAQ;QACRC,SAAS;YAAC;YAAW;SAAU;QAC/BC,SAAS;IACX;AACF,GAAG;IACDb,GAAG;QAAC;QAAyD;QAAgG;QAAkG;QAAiG;QAAmG;KAA0F;AAC/hB;AAKO,MAAMlD,0BAA0BgE,CAAAA;IACrC,MAAMtB,WAAWsB,MAAMC,UAAU,KAAK;IACtC,MAAMC,SAAS7D;IACf,MAAM8D,0BAA0Bf;IAChC,MAAMgB,2BAA2BX;IACjC,MAAMY,uBAAuBX;IAC7BM,MAAM/D,IAAI,CAACqE,SAAS,GAAGC,IAAAA,mBAAY,EAACxE,gBAAgBE,IAAI,EAAEiE,OAAOjE,IAAI,EAAEyC,YAAYwB,OAAOxB,QAAQ,EAAEsB,MAAM/D,IAAI,CAACqE,SAAS;IACxH,IAAIN,MAAM9D,IAAI,EAAE;QACd8D,MAAM9D,IAAI,CAACoE,SAAS,GAAGC,IAAAA,mBAAY,EAACxE,gBAAgBG,IAAI,EAAEgE,OAAOhE,IAAI,EAAE8D,MAAMQ,MAAM,IAAK9B,CAAAA,WAAW0B,wBAAwB,CAACJ,MAAMQ,MAAM,CAAC,GAAGL,uBAAuB,CAACH,MAAMQ,MAAM,CAAC,AAAD,GAAIR,MAAM9D,IAAI,CAACoE,SAAS;IAC1M;IACA,IAAIN,MAAM5D,MAAM,EAAE;QAChB4D,MAAM5D,MAAM,CAACkE,SAAS,GAAGC,IAAAA,mBAAY,EAACxE,gBAAgBK,MAAM,EAAE8D,OAAO9D,MAAM,EAAE4D,MAAM5D,MAAM,CAACkE,SAAS;IACrG;IACA,IAAIN,MAAM7D,MAAM,EAAE;QAChB,gGAAgG;QAChG6D,MAAM7D,MAAM,CAACmE,SAAS,GAAGC,IAAAA,mBAAY,EAACxE,gBAAgBI,MAAM,EAAE+D,OAAO/D,MAAM,EAAEuC,YAAY2B,qBAAqBlE,MAAM,EAAE6D,MAAM7D,MAAM,CAACmE,SAAS;IAC9I;IACA,OAAON;AACT,GACA,iDAAiD"}