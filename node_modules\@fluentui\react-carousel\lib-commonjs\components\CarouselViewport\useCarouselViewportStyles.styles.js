"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    carouselViewportClassNames: function() {
        return carouselViewportClassNames;
    },
    useCarouselViewportStyles_unstable: function() {
        return useCarouselViewportStyles_unstable;
    }
});
const _react = require("@griffel/react");
const carouselViewportClassNames = {
    root: 'fui-CarouselViewport'
};
/**
 * Styles for the root slot
 */ const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    root: {
        B2u0y6b: "f6dzj5z",
        a9b677: "f14z66ap"
    }
}, {
    d: [
        ".f6dzj5z{max-width:100%;}",
        ".f14z66ap{width:auto;}"
    ]
});
const useCarouselViewportStyles_unstable = (state)=>{
    'use no memo';
    const styles = useStyles();
    state.root.className = (0, _react.mergeClasses)(carouselViewportClassNames.root, styles.root, state.root.className);
    return state;
};
