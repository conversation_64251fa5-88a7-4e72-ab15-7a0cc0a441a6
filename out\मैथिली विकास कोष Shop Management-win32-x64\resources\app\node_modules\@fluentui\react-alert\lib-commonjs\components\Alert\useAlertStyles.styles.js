"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    alertClassNames: function() {
        return alertClassNames;
    },
    useAlertStyles_unstable: function() {
        return useAlertStyles_unstable;
    }
});
const _react = require("@griffel/react");
const alertClassNames = {
    root: 'fui-Alert',
    icon: 'fui-Alert__icon',
    action: 'fui-Alert__action',
    avatar: 'fui-Alert__avatar'
};
const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    root: {
        mc9l5x: "f22iagw",
        Bt984gj: "f122n59",
        sshi5w: "f5pgtk9",
        Byoj8tv: 0,
        uwmqm3: 0,
        z189sj: 0,
        z8tnut: 0,
        B0ocmuz: "f1oic3e7",
        Beyfa6y: 0,
        Bbmb7ep: 0,
        Btl43ni: 0,
        B7oj6ja: 0,
        <PERSON><PERSON>: "ff3glw6",
        Bgfg5da: 0,
        B9xav0g: 0,
        oivjwe: 0,
        Bn0qgzm: 0,
        B4g9neb: 0,
        zhjwy3: 0,
        wvpqe5: 0,
        ibv6hh: 0,
        u1mtju: 0,
        h3c5rm: 0,
        vrafjx: 0,
        Bekrc4i: 0,
        i8vvqc: 0,
        g2u3we: 0,
        icvyot: 0,
        B4j52fo: 0,
        irswps: "f9ggezi",
        E5pizo: "fz58gqq",
        Be2twd7: "fkhj508",
        Bhrd7zp: "fl43uef",
        sj55zd: "f19n0e5",
        De3pzq: "fxugw4r"
    },
    inverted: {
        sj55zd: "f1w7i9ko",
        De3pzq: "f5pduvr"
    },
    icon: {
        Bqenvij: "fd461yt",
        Be2twd7: "f4ybsrx",
        Byoj8tv: 0,
        uwmqm3: 0,
        z189sj: 0,
        z8tnut: 0,
        B0ocmuz: [
            "fhivll6",
            "f1cgepy8"
        ]
    },
    avatar: {
        jrapky: 0,
        Frg6f3: 0,
        t21cq0: 0,
        B6of3ja: 0,
        B74szlk: [
            "fxal17o",
            "ftghr3s"
        ]
    },
    action: {
        Byoj8tv: 0,
        uwmqm3: 0,
        z189sj: 0,
        z8tnut: 0,
        B0ocmuz: "f4jnnbt",
        Bf4jedk: "fy77jfu",
        Frg6f3: [
            "fcgxt0o",
            "f1ujusj6"
        ],
        sj55zd: "f16muhyy"
    }
}, {
    d: [
        ".f22iagw{display:flex;}",
        ".f122n59{align-items:center;}",
        ".f5pgtk9{min-height:44px;}",
        [
            ".f1oic3e7{padding:0 12px;}",
            {
                p: -1
            }
        ],
        [
            ".ff3glw6{border-radius:4px;}",
            {
                p: -1
            }
        ],
        [
            ".f9ggezi{border:1px solid var(--colorTransparentStroke);}",
            {
                p: -2
            }
        ],
        ".fz58gqq{box-shadow:var(--shadow8);}",
        ".fkhj508{font-size:var(--fontSizeBase300);}",
        ".fl43uef{font-weight:var(--fontWeightSemibold);}",
        ".f19n0e5{color:var(--colorNeutralForeground1);}",
        ".fxugw4r{background-color:var(--colorNeutralBackground1);}",
        ".f1w7i9ko{color:var(--colorNeutralForegroundInverted2);}",
        ".f5pduvr{background-color:var(--colorNeutralBackgroundInverted);}",
        ".fd461yt{height:16px;}",
        ".f4ybsrx{font-size:16px;}",
        [
            ".fhivll6{padding:0 8px 0 0;}",
            {
                p: -1
            }
        ],
        [
            ".f1cgepy8{padding:0 0 0 8px;}",
            {
                p: -1
            }
        ],
        [
            ".fxal17o{margin:0 8px 0 0;}",
            {
                p: -1
            }
        ],
        [
            ".ftghr3s{margin:0 0 0 8px;}",
            {
                p: -1
            }
        ],
        [
            ".f4jnnbt{padding:5px 10px;}",
            {
                p: -1
            }
        ],
        ".fy77jfu{min-width:0;}",
        ".fcgxt0o{margin-left:auto;}",
        ".f1ujusj6{margin-right:auto;}",
        ".f16muhyy{color:var(--colorBrandForeground1);}"
    ]
});
const useIntentIconStyles = /*#__PURE__*/ (0, _react.__styles)({
    success: {
        sj55zd: "f1m7fhi8"
    },
    error: {
        sj55zd: "f1whyuy6"
    },
    warning: {
        sj55zd: "fpti2h4"
    },
    info: {
        sj55zd: "fkfq4zb"
    }
}, {
    d: [
        ".f1m7fhi8{color:var(--colorPaletteGreenForeground3);}",
        ".f1whyuy6{color:var(--colorPaletteRedForeground3);}",
        ".fpti2h4{color:var(--colorPaletteYellowForeground2);}",
        ".fkfq4zb{color:var(--colorNeutralForeground2);}"
    ]
});
const useIntentIconStylesInverted = /*#__PURE__*/ (0, _react.__styles)({
    success: {
        sj55zd: "f1pvjcpr"
    },
    error: {
        sj55zd: "fcrp5ll"
    },
    warning: {
        sj55zd: "f1r8f1cl"
    },
    info: {
        sj55zd: "f1w7i9ko"
    }
}, {
    d: [
        ".f1pvjcpr{color:var(--colorPaletteGreenForegroundInverted);}",
        ".fcrp5ll{color:var(--colorPaletteRedForegroundInverted);}",
        ".f1r8f1cl{color:var(--colorPaletteYellowForegroundInverted);}",
        ".f1w7i9ko{color:var(--colorNeutralForegroundInverted2);}"
    ]
});
const useActionButtonColorInverted = /*#__PURE__*/ (0, _react.__styles)({
    action: {
        sj55zd: "f1qz2gb0",
        B8q5s1w: "fa5e339",
        Bci5o5g: [
            "fk4svks",
            "fqzoz0o"
        ],
        n8qw10: "fw8q0i0",
        Bdrgwmp: [
            "fqzoz0o",
            "fk4svks"
        ],
        Bfpq7zp: "f1dlk4fq"
    }
}, {
    d: [
        ".f1qz2gb0{color:var(--colorBrandForegroundInverted);}",
        ".fa5e339[data-fui-focus-visible]{border-top-color:var(--colorTransparentStrokeInteractive);}",
        ".fk4svks[data-fui-focus-visible]{border-right-color:var(--colorTransparentStrokeInteractive);}",
        ".fqzoz0o[data-fui-focus-visible]{border-left-color:var(--colorTransparentStrokeInteractive);}",
        ".fw8q0i0[data-fui-focus-visible]{border-bottom-color:var(--colorTransparentStrokeInteractive);}",
        ".f1dlk4fq[data-fui-focus-visible]{outline-color:var(--colorNeutralBackground5Pressed);}"
    ]
});
const useAlertStyles_unstable = (state)=>{
    const inverted = state.appearance === 'inverted';
    const styles = useStyles();
    const intentIconStylesPrimary = useIntentIconStyles();
    const intentIconStylesInverted = useIntentIconStylesInverted();
    const actionStylesInverted = useActionButtonColorInverted();
    state.root.className = (0, _react.mergeClasses)(alertClassNames.root, styles.root, inverted && styles.inverted, state.root.className);
    if (state.icon) {
        state.icon.className = (0, _react.mergeClasses)(alertClassNames.icon, styles.icon, state.intent && (inverted ? intentIconStylesInverted[state.intent] : intentIconStylesPrimary[state.intent]), state.icon.className);
    }
    if (state.avatar) {
        state.avatar.className = (0, _react.mergeClasses)(alertClassNames.avatar, styles.avatar, state.avatar.className);
    }
    if (state.action) {
        // Note: inverted && actionStylesInverted.action has the highest piority and must be merged last
        state.action.className = (0, _react.mergeClasses)(alertClassNames.action, styles.action, inverted && actionStylesInverted.action, state.action.className);
    }
    return state;
}; //# sourceMappingURL=useAlertStyles.styles.js.map
