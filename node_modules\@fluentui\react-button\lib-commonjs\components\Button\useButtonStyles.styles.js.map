{"version": 3, "sources": ["useButtonStyles.styles.js"], "sourcesContent": ["import { iconFilledClassName, iconRegularClassName } from '@fluentui/react-icons';\nimport { createCustomFocusIndicatorStyle } from '@fluentui/react-tabster';\nimport { tokens } from '@fluentui/react-theme';\nimport { shorthands, makeStyles, makeResetStyles, mergeClasses } from '@griffel/react';\nexport const buttonClassNames = {\n    root: 'fui-Button',\n    icon: 'fui-Button__icon'\n};\nconst iconSpacingVar = '--fui-Button__icon--spacing';\nconst buttonSpacingSmall = '3px';\nconst buttonSpacingSmallWithIcon = '1px';\nconst buttonSpacingMedium = '5px';\nconst buttonSpacingLarge = '8px';\nconst buttonSpacingLargeWithIcon = '7px';\n/* Firefox has box shadow sizing issue at some zoom levels\n * this will ensure the inset boxShadow is always uniform\n * without affecting other browser platforms\n */ const boxShadowStrokeWidthThinMoz = `calc(${tokens.strokeWidthThin} + 0.25px)`;\nconst useRootBaseClassName = makeResetStyles({\n    alignItems: 'center',\n    boxSizing: 'border-box',\n    display: 'inline-flex',\n    justifyContent: 'center',\n    textDecorationLine: 'none',\n    verticalAlign: 'middle',\n    margin: 0,\n    overflow: 'hidden',\n    backgroundColor: tokens.colorNeutralBackground1,\n    color: tokens.colorNeutralForeground1,\n    border: `${tokens.strokeWidthThin} solid ${tokens.colorNeutralStroke1}`,\n    fontFamily: tokens.fontFamilyBase,\n    outlineStyle: 'none',\n    ':hover': {\n        backgroundColor: tokens.colorNeutralBackground1Hover,\n        borderColor: tokens.colorNeutralStroke1Hover,\n        color: tokens.colorNeutralForeground1Hover,\n        cursor: 'pointer'\n    },\n    ':hover:active': {\n        backgroundColor: tokens.colorNeutralBackground1Pressed,\n        borderColor: tokens.colorNeutralStroke1Pressed,\n        color: tokens.colorNeutralForeground1Pressed,\n        outlineStyle: 'none'\n    },\n    padding: `${buttonSpacingMedium} ${tokens.spacingHorizontalM}`,\n    minWidth: '96px',\n    borderRadius: tokens.borderRadiusMedium,\n    fontSize: tokens.fontSizeBase300,\n    fontWeight: tokens.fontWeightSemibold,\n    lineHeight: tokens.lineHeightBase300,\n    // Transition styles\n    transitionDuration: tokens.durationFaster,\n    transitionProperty: 'background, border, color',\n    transitionTimingFunction: tokens.curveEasyEase,\n    '@media screen and (prefers-reduced-motion: reduce)': {\n        transitionDuration: '0.01ms'\n    },\n    // High contrast styles\n    '@media (forced-colors: active)': {\n        ':focus': {\n            borderColor: 'ButtonText'\n        },\n        ':hover': {\n            backgroundColor: 'HighlightText',\n            borderColor: 'Highlight',\n            color: 'Highlight',\n            forcedColorAdjust: 'none'\n        },\n        ':hover:active': {\n            backgroundColor: 'HighlightText',\n            borderColor: 'Highlight',\n            color: 'Highlight',\n            forcedColorAdjust: 'none'\n        }\n    },\n    // Focus styles\n    ...createCustomFocusIndicatorStyle({\n        borderColor: tokens.colorStrokeFocus2,\n        borderRadius: tokens.borderRadiusMedium,\n        borderWidth: '1px',\n        outline: `${tokens.strokeWidthThick} solid ${tokens.colorTransparentStroke}`,\n        boxShadow: `0 0 0 ${tokens.strokeWidthThin} ${tokens.colorStrokeFocus2}\n      inset\n    `,\n        zIndex: 1\n    }),\n    // BUGFIX: Mozilla specific styles (Mozilla BugID: 1857642)\n    '@supports (-moz-appearance:button)': {\n        ...createCustomFocusIndicatorStyle({\n            boxShadow: `0 0 0 ${boxShadowStrokeWidthThinMoz} ${tokens.colorStrokeFocus2}\n      inset\n    `\n        })\n    }\n});\nconst useIconBaseClassName = makeResetStyles({\n    alignItems: 'center',\n    display: 'inline-flex',\n    justifyContent: 'center',\n    fontSize: '20px',\n    height: '20px',\n    width: '20px',\n    [iconSpacingVar]: tokens.spacingHorizontalSNudge\n});\nconst useRootStyles = makeStyles({\n    // Appearance variations\n    outline: {\n        backgroundColor: tokens.colorTransparentBackground,\n        ':hover': {\n            backgroundColor: tokens.colorTransparentBackgroundHover\n        },\n        ':hover:active': {\n            backgroundColor: tokens.colorTransparentBackgroundPressed\n        }\n    },\n    primary: {\n        backgroundColor: tokens.colorBrandBackground,\n        ...shorthands.borderColor('transparent'),\n        color: tokens.colorNeutralForegroundOnBrand,\n        ':hover': {\n            backgroundColor: tokens.colorBrandBackgroundHover,\n            ...shorthands.borderColor('transparent'),\n            color: tokens.colorNeutralForegroundOnBrand\n        },\n        ':hover:active': {\n            backgroundColor: tokens.colorBrandBackgroundPressed,\n            ...shorthands.borderColor('transparent'),\n            color: tokens.colorNeutralForegroundOnBrand\n        },\n        '@media (forced-colors: active)': {\n            backgroundColor: 'Highlight',\n            ...shorthands.borderColor('HighlightText'),\n            color: 'HighlightText',\n            forcedColorAdjust: 'none',\n            ':hover': {\n                backgroundColor: 'HighlightText',\n                ...shorthands.borderColor('Highlight'),\n                color: 'Highlight'\n            },\n            ':hover:active': {\n                backgroundColor: 'HighlightText',\n                ...shorthands.borderColor('Highlight'),\n                color: 'Highlight'\n            }\n        }\n    },\n    secondary: {\n    },\n    subtle: {\n        backgroundColor: tokens.colorSubtleBackground,\n        ...shorthands.borderColor('transparent'),\n        color: tokens.colorNeutralForeground2,\n        ':hover': {\n            backgroundColor: tokens.colorSubtleBackgroundHover,\n            ...shorthands.borderColor('transparent'),\n            color: tokens.colorNeutralForeground2Hover,\n            [`& .${iconFilledClassName}`]: {\n                display: 'inline'\n            },\n            [`& .${iconRegularClassName}`]: {\n                display: 'none'\n            },\n            [`& .${buttonClassNames.icon}`]: {\n                color: tokens.colorNeutralForeground2BrandHover\n            }\n        },\n        ':hover:active': {\n            backgroundColor: tokens.colorSubtleBackgroundPressed,\n            ...shorthands.borderColor('transparent'),\n            color: tokens.colorNeutralForeground2Pressed,\n            [`& .${iconFilledClassName}`]: {\n                display: 'inline'\n            },\n            [`& .${iconRegularClassName}`]: {\n                display: 'none'\n            },\n            [`& .${buttonClassNames.icon}`]: {\n                color: tokens.colorNeutralForeground2BrandPressed\n            }\n        },\n        '@media (forced-colors: active)': {\n            ':hover': {\n                color: 'Highlight',\n                [`& .${buttonClassNames.icon}`]: {\n                    color: 'Highlight'\n                }\n            },\n            ':hover:active': {\n                color: 'Highlight',\n                [`& .${buttonClassNames.icon}`]: {\n                    color: 'Highlight'\n                }\n            }\n        }\n    },\n    transparent: {\n        backgroundColor: tokens.colorTransparentBackground,\n        ...shorthands.borderColor('transparent'),\n        color: tokens.colorNeutralForeground2,\n        ':hover': {\n            backgroundColor: tokens.colorTransparentBackgroundHover,\n            ...shorthands.borderColor('transparent'),\n            color: tokens.colorNeutralForeground2BrandHover,\n            [`& .${iconFilledClassName}`]: {\n                display: 'inline'\n            },\n            [`& .${iconRegularClassName}`]: {\n                display: 'none'\n            }\n        },\n        ':hover:active': {\n            backgroundColor: tokens.colorTransparentBackgroundPressed,\n            ...shorthands.borderColor('transparent'),\n            color: tokens.colorNeutralForeground2BrandPressed,\n            [`& .${iconFilledClassName}`]: {\n                display: 'inline'\n            },\n            [`& .${iconRegularClassName}`]: {\n                display: 'none'\n            }\n        },\n        '@media (forced-colors: active)': {\n            ':hover': {\n                backgroundColor: tokens.colorTransparentBackground,\n                color: 'Highlight'\n            },\n            ':hover:active': {\n                backgroundColor: tokens.colorTransparentBackground,\n                color: 'Highlight'\n            }\n        }\n    },\n    // Shape variations\n    circular: {\n        borderRadius: tokens.borderRadiusCircular\n    },\n    rounded: {\n    },\n    square: {\n        borderRadius: tokens.borderRadiusNone\n    },\n    // Size variations\n    small: {\n        minWidth: '64px',\n        padding: `${buttonSpacingSmall} ${tokens.spacingHorizontalS}`,\n        borderRadius: tokens.borderRadiusMedium,\n        fontSize: tokens.fontSizeBase200,\n        fontWeight: tokens.fontWeightRegular,\n        lineHeight: tokens.lineHeightBase200\n    },\n    smallWithIcon: {\n        paddingBottom: buttonSpacingSmallWithIcon,\n        paddingTop: buttonSpacingSmallWithIcon\n    },\n    medium: {\n    },\n    large: {\n        minWidth: '96px',\n        padding: `${buttonSpacingLarge} ${tokens.spacingHorizontalL}`,\n        borderRadius: tokens.borderRadiusMedium,\n        fontSize: tokens.fontSizeBase400,\n        fontWeight: tokens.fontWeightSemibold,\n        lineHeight: tokens.lineHeightBase400\n    },\n    largeWithIcon: {\n        paddingBottom: buttonSpacingLargeWithIcon,\n        paddingTop: buttonSpacingLargeWithIcon\n    }\n});\nconst useRootDisabledStyles = makeStyles({\n    // Base styles\n    base: {\n        backgroundColor: tokens.colorNeutralBackgroundDisabled,\n        ...shorthands.borderColor(tokens.colorNeutralStrokeDisabled),\n        color: tokens.colorNeutralForegroundDisabled,\n        cursor: 'not-allowed',\n        [`& .${buttonClassNames.icon}`]: {\n            color: tokens.colorNeutralForegroundDisabled\n        },\n        ':hover': {\n            backgroundColor: tokens.colorNeutralBackgroundDisabled,\n            ...shorthands.borderColor(tokens.colorNeutralStrokeDisabled),\n            color: tokens.colorNeutralForegroundDisabled,\n            cursor: 'not-allowed',\n            [`& .${iconFilledClassName}`]: {\n                display: 'none'\n            },\n            [`& .${iconRegularClassName}`]: {\n                display: 'inline'\n            },\n            [`& .${buttonClassNames.icon}`]: {\n                color: tokens.colorNeutralForegroundDisabled\n            }\n        },\n        ':hover:active': {\n            backgroundColor: tokens.colorNeutralBackgroundDisabled,\n            ...shorthands.borderColor(tokens.colorNeutralStrokeDisabled),\n            color: tokens.colorNeutralForegroundDisabled,\n            cursor: 'not-allowed',\n            [`& .${iconFilledClassName}`]: {\n                display: 'none'\n            },\n            [`& .${iconRegularClassName}`]: {\n                display: 'inline'\n            },\n            [`& .${buttonClassNames.icon}`]: {\n                color: tokens.colorNeutralForegroundDisabled\n            }\n        }\n    },\n    // High contrast styles\n    highContrast: {\n        '@media (forced-colors: active)': {\n            backgroundColor: 'ButtonFace',\n            ...shorthands.borderColor('GrayText'),\n            color: 'GrayText',\n            [`& .${buttonClassNames.icon}`]: {\n                color: 'GrayText'\n            },\n            ':focus': {\n                ...shorthands.borderColor('GrayText')\n            },\n            ':hover': {\n                backgroundColor: 'ButtonFace',\n                ...shorthands.borderColor('GrayText'),\n                color: 'GrayText',\n                [`& .${buttonClassNames.icon}`]: {\n                    color: 'GrayText'\n                }\n            },\n            ':hover:active': {\n                backgroundColor: 'ButtonFace',\n                ...shorthands.borderColor('GrayText'),\n                color: 'GrayText',\n                [`& .${buttonClassNames.icon}`]: {\n                    color: 'GrayText'\n                }\n            }\n        }\n    },\n    // Appearance variations\n    outline: {\n        backgroundColor: tokens.colorTransparentBackground,\n        ':hover': {\n            backgroundColor: tokens.colorTransparentBackground\n        },\n        ':hover:active': {\n            backgroundColor: tokens.colorTransparentBackground\n        }\n    },\n    primary: {\n        ...shorthands.borderColor('transparent'),\n        ':hover': {\n            ...shorthands.borderColor('transparent')\n        },\n        ':hover:active': {\n            ...shorthands.borderColor('transparent')\n        }\n    },\n    secondary: {\n    },\n    subtle: {\n        backgroundColor: tokens.colorTransparentBackground,\n        ...shorthands.borderColor('transparent'),\n        ':hover': {\n            backgroundColor: tokens.colorTransparentBackground,\n            ...shorthands.borderColor('transparent')\n        },\n        ':hover:active': {\n            backgroundColor: tokens.colorTransparentBackground,\n            ...shorthands.borderColor('transparent')\n        }\n    },\n    transparent: {\n        backgroundColor: tokens.colorTransparentBackground,\n        ...shorthands.borderColor('transparent'),\n        ':hover': {\n            backgroundColor: tokens.colorTransparentBackground,\n            ...shorthands.borderColor('transparent')\n        },\n        ':hover:active': {\n            backgroundColor: tokens.colorTransparentBackground,\n            ...shorthands.borderColor('transparent')\n        }\n    }\n});\nconst useRootFocusStyles = makeStyles({\n    // Shape variations\n    circular: createCustomFocusIndicatorStyle({\n        borderRadius: tokens.borderRadiusCircular\n    }),\n    rounded: {\n    },\n    square: createCustomFocusIndicatorStyle({\n        borderRadius: tokens.borderRadiusNone\n    }),\n    // Primary styles\n    primary: {\n        ...createCustomFocusIndicatorStyle({\n            ...shorthands.borderColor(tokens.colorStrokeFocus2),\n            boxShadow: `${tokens.shadow2}, 0 0 0 ${tokens.strokeWidthThin} ${tokens.colorStrokeFocus2} inset,  0 0 0 ${tokens.strokeWidthThick} ${tokens.colorNeutralForegroundOnBrand} inset`,\n            ':hover': {\n                boxShadow: `${tokens.shadow2}, 0 0 0 ${tokens.strokeWidthThin} ${tokens.colorStrokeFocus2} inset`,\n                ...shorthands.borderColor(tokens.colorStrokeFocus2)\n            }\n        }),\n        // BUGFIX: Mozilla specific styles (Mozilla BugID: 1857642)\n        '@supports (-moz-appearance:button)': {\n            ...createCustomFocusIndicatorStyle({\n                boxShadow: `${tokens.shadow2}, 0 0 0 ${boxShadowStrokeWidthThinMoz} ${tokens.colorStrokeFocus2} inset,  0 0 0 ${tokens.strokeWidthThick} ${tokens.colorNeutralForegroundOnBrand} inset`,\n                ':hover': {\n                    boxShadow: `${tokens.shadow2}, 0 0 0 ${boxShadowStrokeWidthThinMoz} ${tokens.colorStrokeFocus2} inset`\n                }\n            })\n        }\n    },\n    // Size variations\n    small: createCustomFocusIndicatorStyle({\n        borderRadius: tokens.borderRadiusSmall\n    }),\n    medium: {\n    },\n    large: createCustomFocusIndicatorStyle({\n        borderRadius: tokens.borderRadiusLarge\n    })\n});\nconst useRootIconOnlyStyles = makeStyles({\n    // Size variations\n    small: {\n        padding: buttonSpacingSmallWithIcon,\n        minWidth: '24px',\n        maxWidth: '24px'\n    },\n    medium: {\n        padding: buttonSpacingMedium,\n        minWidth: '32px',\n        maxWidth: '32px'\n    },\n    large: {\n        padding: buttonSpacingLargeWithIcon,\n        minWidth: '40px',\n        maxWidth: '40px'\n    }\n});\nconst useIconStyles = makeStyles({\n    // Size variations\n    small: {\n        fontSize: '20px',\n        height: '20px',\n        width: '20px',\n        [iconSpacingVar]: tokens.spacingHorizontalXS\n    },\n    medium: {\n    },\n    large: {\n        fontSize: '24px',\n        height: '24px',\n        width: '24px',\n        [iconSpacingVar]: tokens.spacingHorizontalSNudge\n    },\n    // Icon position variations\n    before: {\n        marginRight: `var(${iconSpacingVar})`\n    },\n    after: {\n        marginLeft: `var(${iconSpacingVar})`\n    }\n});\nexport const useButtonStyles_unstable = (state)=>{\n    'use no memo';\n    const rootBaseClassName = useRootBaseClassName();\n    const iconBaseClassName = useIconBaseClassName();\n    const rootStyles = useRootStyles();\n    const rootDisabledStyles = useRootDisabledStyles();\n    const rootFocusStyles = useRootFocusStyles();\n    const rootIconOnlyStyles = useRootIconOnlyStyles();\n    const iconStyles = useIconStyles();\n    const { appearance, disabled, disabledFocusable, icon, iconOnly, iconPosition, shape, size } = state;\n    state.root.className = mergeClasses(buttonClassNames.root, rootBaseClassName, appearance && rootStyles[appearance], rootStyles[size], icon && size === 'small' && rootStyles.smallWithIcon, icon && size === 'large' && rootStyles.largeWithIcon, rootStyles[shape], // Disabled styles\n    (disabled || disabledFocusable) && rootDisabledStyles.base, (disabled || disabledFocusable) && rootDisabledStyles.highContrast, appearance && (disabled || disabledFocusable) && rootDisabledStyles[appearance], // Focus styles\n    appearance === 'primary' && rootFocusStyles.primary, rootFocusStyles[size], rootFocusStyles[shape], // Icon-only styles\n    iconOnly && rootIconOnlyStyles[size], // User provided class name\n    state.root.className);\n    if (state.icon) {\n        state.icon.className = mergeClasses(buttonClassNames.icon, iconBaseClassName, !!state.root.children && iconStyles[iconPosition], iconStyles[size], state.icon.className);\n    }\n    return state;\n};\n"], "names": ["buttonClassNames", "useButtonStyles_unstable", "root", "icon", "iconSpacingVar", "buttonSpacingSmall", "buttonSpacingSmallWithIcon", "buttonSpacingMedium", "buttonSpacingLarge", "buttonSpacingLargeWithIcon", "boxShadowStrokeWidthThinMoz", "tokens", "strokeWid<PERSON><PERSON><PERSON>", "useRootBaseClassName", "__resetStyles", "r", "s", "useIconBaseClassName", "useRootStyles", "__styles", "outline", "De3pzq", "Jwef8y", "iro3zm", "primary", "g2u3we", "h3c5rm", "B9xav0g", "zhjwy3", "sj55zd", "Bgoe8wy", "Bwzppfd", "oetu4i", "gg5e9n", "Bi91k9c", "b661bw", "Bk6r4ia", "B9zn80p", "Bpld233", "B2d53fq", "Bsw6fvg", "Bjwas2f", "Bn1d65q", "Bxeuatn", "n51gp8", "Bbusuzp", "ycbfsm", "Bqrx1nm", "pgvf35", "Bh7lczh", "dpv3f4", "Bpnjhaq", "ze5xyy", "g2kj27", "Bf756sw", "Bow2dr7", "Bvhedfk", "Gye4lf", "pc6evw", "secondary", "subtle", "Bk3fhr4", "Bmfj8id", "Bbdnnc7", "em6i61", "vm6p8p", "x3br3k", "Bx3q9su", "xd2cci", "transparent", "circular", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "rounded", "square", "small", "Bf4jedk", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "Be2twd7", "Bhrd7zp", "Bg96gwp", "smallWithIcon", "medium", "large", "largeWithIcon", "d", "p", "h", "m", "useRootDisabledStyles", "base", "Bceei9c", "Bfinmwp", "eoavqd", "c3iz72", "highContrast", "Bm2fdqk", "G867l3", "gdbnj", "mxns5l", "o3nasb", "useRootFocusStyles", "Bw81rd7", "kdpuga", "dm238s", "B6xbmo0", "B3whbx2", "B8q5s1w", "Bci5o5g", "n8qw10", "Bdrgwmp", "j6ew2k", "he4mth", "Byr4aka", "lks7q5", "Bnan3qt", "k1dn9", "Boium3a", "tm8e47", "t", "useRootIconOnlyStyles", "B2u0y6b", "useIconStyles", "Bqenvij", "a9b677", "Bqrlyyl", "before", "t21cq0", "after", "Frg6f3", "state", "rootBaseClassName", "iconBaseClassName", "rootStyles", "rootDisabledStyles", "rootFocusStyles", "rootIconOnlyStyles", "iconStyles", "appearance", "disabled", "disabledFocusable", "iconOnly", "iconPosition", "shape", "size", "className", "mergeClasses", "children"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAIaA,gBAAgB;eAAhBA;;IAgdAC,wBAAwB;eAAxBA;;;4BAldU;uBAC+C;AAC/D,MAAMD,mBAAmB;IAC5BE,MAAM;IACNC,MAAM;AACV;AACA,MAAMC,iBAAiB;AACvB,MAAMC,qBAAqB;AAC3B,MAAMC,6BAA6B;AACnC,MAAMC,sBAAsB;AAC5B,MAAMC,qBAAqB;AAC3B,MAAMC,6BAA6B;AACnC;;;CAGA,GAAI,MAAMC,8BAA8B,CAAA,KAAA,EAAQC,kBAAM,CAACC,eAAe,CAAA,UAAA,CAAY;AAClF,MAAMC,uBAAoB,WAAA,GAAGC,IAAAA,oBAAA,EAAA,YAAA,MAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AA6E7B,MAAMC,uBAAoB,WAAA,GAAGH,IAAAA,oBAAA,EAAA,WAAA,MAAA;IAAA;CAQ5B;AACD,MAAMI,gBAAa,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;IAAA;IAAAC,SAAA;QAAAH,QAAA;QAAAI,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAP,QAAA;QAAAQ,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAX,QAAA;QAAAY,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;IAAA;IAAAC,WAAA,CAAA;IAAAC,QAAA;QAAAvC,QAAA;QAAAI,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAP,QAAA;QAAAQ,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAA2B,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAxC,QAAA;QAAAY,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAyB,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAd,QAAA;QAAAe,SAAA;QAAAT,QAAA;QAAAU,QAAA;IAAA;IAAAC,aAAA;QAAAhD,QAAA;QAAAI,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAP,QAAA;QAAAQ,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAA2B,SAAA;QAAAC,SAAA;QAAAvC,QAAA;QAAAY,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAyB,QAAA;QAAAC,QAAA;QAAAlB,SAAA;QAAAK,QAAA;QAAAC,QAAA;QAAAK,QAAA;IAAA;IAAAY,UAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAAC,SAAA,CAAA;IAAAC,QAAA;QAAAN,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAAG,OAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAb,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAU,SAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAAC,eAAA;QAAAR,SAAA;QAAAG,QAAA;IAAA;IAAAM,QAAA,CAAA;IAAAC,OAAA;QAAAX,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAb,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAU,SAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAAI,eAAA;QAAAX,SAAA;QAAAG,QAAA;IAAA;AAAA,GAAA;IAAAS,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;KAAA;AAAA;AAqKtB,MAAMC,wBAAqB,WAAA,GAAG7E,IAAAA,eAAA,EAAA;IAAA8E,MAAA;QAAA5E,QAAA;QAAAI,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAqE,SAAA;QAAAC,SAAA;QAAA7E,QAAA;QAAAQ,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAkE,QAAA;QAAAvC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAxC,QAAA;QAAAY,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAA8D,QAAA;QAAArC,QAAA;QAAAC,QAAA;QAAAC,QAAA;IAAA;IAAAoC,cAAA;QAAA9D,SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAA0D,SAAA;QAAAC,QAAA;QAAAC,OAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAA5D,SAAA;QAAAC,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAe,SAAA;QAAAd,QAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAU,QAAA;IAAA;IAAAhD,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;IAAA;IAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAE,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAE,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;IAAA;IAAAqB,WAAA,CAAA;IAAAC,QAAA;QAAAvC,QAAA;QAAAI,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAN,QAAA;QAAAQ,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAV,QAAA;QAAAY,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;IAAA;IAAA+B,aAAA;QAAAhD,QAAA;QAAAI,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAN,QAAA;QAAAQ,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAV,QAAA;QAAAY,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;IAAA;AAAA,GAAA;IAAAsD,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAAE,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;KAAA;AAAA;AAqH9B,MAAMa,qBAAkB,WAAA,GAAGzF,IAAAA,eAAA,EAAA;IAAAmD,UAAA;QAAAuC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAArC,SAAA,CAAA;IAAAC,QAAA;QAAAgC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAAzF,SAAA;QAAA0F,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,OAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;IAAA;IAAA/C,OAAA;QAAA+B,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAAxB,QAAA,CAAA;IAAAC,OAAA;QAAAmB,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;AAAA,GAAA;IAAArB,GAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;KAAA;IAAAiC,GAAA;QAAA;QAAA;KAAA;AAAA;AAwC3B,MAAMC,wBAAqB,WAAA,GAAG5G,IAAAA,eAAA,EAAA;IAAA2D,OAAA;QAAAE,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAL,SAAA;QAAAiD,SAAA;IAAA;IAAAvC,QAAA;QAAAT,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAL,SAAA;QAAAiD,SAAA;IAAA;IAAAtC,OAAA;QAAAV,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAL,SAAA;QAAAiD,SAAA;IAAA;AAAA,GAAA;IAAApC,GAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;KAAA;AAAA;AAkB9B,MAAMoC,gBAAa,WAAA,GAAG9G,IAAAA,eAAA,EAAA;IAAA2D,OAAA;QAAAO,SAAA;QAAA6C,SAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAA3C,QAAA,CAAA;IAAAC,OAAA;QAAAL,SAAA;QAAA6C,SAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAC,OAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;AAAA,GAAA;IAAA5C,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AAwBf,MAAM3F,2BAA4BwI,CAAAA;IACrC;IACA,MAAMC,oBAAoB7H;IAC1B,MAAM8H,oBAAoB1H;IAC1B,MAAM2H,aAAa1H;IACnB,MAAM2H,qBAAqB7C;IAC3B,MAAM8C,kBAAkBlC;IACxB,MAAMmC,qBAAqBhB;IAC3B,MAAMiB,aAAaf;IACnB,MAAM,EAAEgB,UAAU,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEhJ,IAAI,EAAEiJ,QAAQ,EAAEC,YAAY,EAAEC,KAAK,EAAEC,IAAAA,EAAM,GAAGd;IAC/FA,MAAMvI,IAAI,CAACsJ,SAAS,GAAGC,IAAAA,mBAAY,EAACzJ,iBAAiBE,IAAI,EAAEwI,mBAAmBO,cAAcL,UAAU,CAACK,WAAW,EAAEL,UAAU,CAACW,KAAK,EAAEpJ,QAAQoJ,SAAS,WAAWX,WAAWpD,aAAa,EAAErF,QAAQoJ,SAAS,WAAWX,WAAWjD,aAAa,EAAEiD,UAAU,CAACU,MAAM,EACnQ,AADqQ,kBAAA;IACpQJ,CAAAA,YAAYC,iBAAAA,KAAsBN,mBAAmB5C,IAAI,EAAE,AAACiD,CAAAA,YAAYC,iBAAAA,KAAsBN,mBAAmBvC,YAAY,EAAE2C,cAAeC,CAAAA,YAAYC,iBAAAA,KAAsBN,kBAAkB,CAACI,WAAW,EAAE,eAAA;IACjNA,eAAe,aAAaH,gBAAgBtH,OAAO,EAAEsH,eAAe,CAACS,KAAK,EAAET,eAAe,CAACQ,MAAM,EAAE,mBAAA;IACpGF,YAAYL,kBAAkB,CAACQ,KAAK,EAAE,2BAAA;IACtCd,MAAMvI,IAAI,CAACsJ,SAAS;IACpB,IAAIf,MAAMtI,IAAI,EAAE;QACZsI,MAAMtI,IAAI,CAACqJ,SAAS,GAAGC,IAAAA,mBAAY,EAACzJ,iBAAiBG,IAAI,EAAEwI,mBAAmB,CAAC,CAACF,MAAMvI,IAAI,CAACwJ,QAAQ,IAAIV,UAAU,CAACK,aAAa,EAAEL,UAAU,CAACO,KAAK,EAAEd,MAAMtI,IAAI,CAACqJ,SAAS;IAC3K;IACA,OAAOf;AACX"}