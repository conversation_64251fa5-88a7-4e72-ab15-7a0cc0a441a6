{"version": 3, "sources": ["../src/components/CarouselNavButton/useCarouselNavButton.ts"], "sourcesContent": ["import { type ARIAButtonElement, type ARIAButtonSlotProps, useARIAButtonProps } from '@fluentui/react-aria';\nimport { useTabsterAttributes } from '@fluentui/react-tabster';\nimport {\n  getIntrinsicElementProps,\n  isHTMLElement,\n  slot,\n  useEventCallback,\n  useIsomorphicLayoutEffect,\n  useMergedRefs,\n} from '@fluentui/react-utilities';\nimport * as React from 'react';\n\nimport { useCarouselContext_unstable as useCarouselContext } from '../CarouselContext';\nimport { useCarouselNavContext } from '../CarouselNav/CarouselNavContext';\nimport { useCarouselNavIndexContext } from '../CarouselNav/CarouselNavIndexContext';\nimport type { CarouselNavButtonProps, CarouselNavButtonState } from './CarouselNavButton.types';\n\n/**\n * Create the state required to render CarouselNavButton.\n *\n * The returned state can be modified with hooks such as useCarouselNavButtonStyles_unstable,\n * before being passed to renderCarouselNavButton_unstable.\n *\n * @param props - props from this instance of CarouselNavButton\n * @param ref - reference to root HTMLDivElement of CarouselNavButton\n */\nexport const useCarouselNavButton_unstable = (\n  props: CarouselNavButtonProps,\n  ref: React.Ref<ARIAButtonElement>,\n): CarouselNavButtonState => {\n  const { onClick, as = 'button' } = props;\n\n  const { appearance } = useCarouselNavContext();\n  const index = useCarouselNavIndexContext();\n\n  const selectPageByIndex = useCarouselContext(ctx => ctx.selectPageByIndex);\n  const selected = useCarouselContext(ctx => ctx.activeIndex === index);\n  const subscribeForValues = useCarouselContext(ctx => ctx.subscribeForValues);\n  const resetAutoplay = useCarouselContext(ctx => ctx.resetAutoplay);\n\n  const handleClick: ARIAButtonSlotProps['onClick'] = useEventCallback(event => {\n    onClick?.(event);\n\n    if (!event.defaultPrevented && isHTMLElement(event.target)) {\n      selectPageByIndex(event, index);\n    }\n\n    // Ensure any autoplay timers are extended/reset\n    resetAutoplay();\n  });\n\n  const defaultTabProps = useTabsterAttributes({\n    focusable: { isDefault: selected },\n  });\n\n  const buttonRef = React.useRef<HTMLElement>();\n  const _carouselButton = slot.always<ARIAButtonSlotProps>(\n    getIntrinsicElementProps(as, useARIAButtonProps(props.as, props)),\n    {\n      elementType: 'button',\n      defaultProps: {\n        ref: useMergedRefs(ref, buttonRef),\n        role: 'tab',\n        type: 'button',\n        'aria-selected': selected,\n        ...defaultTabProps,\n      },\n    },\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    return subscribeForValues(data => {\n      const controlList = data.groupIndexList?.[index] ?? [];\n      const _controlledSlideIds = controlList\n        .map((slideIndex: number) => {\n          return data.slideNodes[slideIndex].id;\n        })\n        .join(' ');\n      if (buttonRef.current) {\n        buttonRef.current.setAttribute('aria-controls', _controlledSlideIds);\n      }\n    });\n  }, [index, subscribeForValues]);\n\n  // Override onClick\n  _carouselButton.onClick = handleClick;\n\n  const state: CarouselNavButtonState = {\n    selected,\n    appearance,\n    components: {\n      root: 'button',\n    },\n    root: _carouselButton,\n  };\n\n  return state;\n};\n"], "names": ["useCarouselNavButton_unstable", "props", "ref", "onClick", "as", "appearance", "useCarouselNavContext", "index", "useCarouselNavIndexContext", "selectPageByIndex", "useCarouselContext", "ctx", "selected", "activeIndex", "subscribeForValues", "resetAutoplay", "handleClick", "useEventCallback", "event", "defaultPrevented", "isHTMLElement", "target", "defaultTabProps", "useTabsterAttributes", "focusable", "isDefault", "buttonRef", "React", "useRef", "_carousel<PERSON><PERSON>on", "slot", "always", "getIntrinsicElementProps", "useARIAButtonProps", "elementType", "defaultProps", "useMergedRefs", "role", "type", "useIsomorphicLayoutEffect", "data", "controlList", "groupIndexList", "_controlledSlideIds", "map", "slideIndex", "slideNodes", "id", "join", "current", "setAttribute", "state", "components", "root"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BA0BaA;;;eAAAA;;;;2BA1BwE;8BAChD;gCAQ9B;iEACgB;iCAE2C;oCAC5B;yCACK;AAYpC,MAAMA,gCAAgC,CAC3CC,OACAC;IAEA,MAAM,EAAEC,OAAO,EAAEC,KAAK,QAAQ,EAAE,GAAGH;IAEnC,MAAM,EAAEI,UAAU,EAAE,GAAGC,IAAAA,yCAAAA;IACvB,MAAMC,QAAQC,IAAAA,mDAAAA;IAEd,MAAMC,oBAAoBC,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIF,iBAAiB;IACzE,MAAMG,WAAWF,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIE,WAAW,KAAKN;IAC/D,MAAMO,qBAAqBJ,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIG,kBAAkB;IAC3E,MAAMC,gBAAgBL,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAII,aAAa;IAEjE,MAAMC,cAA8CC,IAAAA,gCAAAA,EAAiBC,CAAAA;QACnEf,YAAAA,QAAAA,YAAAA,KAAAA,IAAAA,KAAAA,IAAAA,QAAUe;QAEV,IAAI,CAACA,MAAMC,gBAAgB,IAAIC,IAAAA,6BAAAA,EAAcF,MAAMG,MAAM,GAAG;YAC1DZ,kBAAkBS,OAAOX;QAC3B;QAEA,gDAAgD;QAChDQ;IACF;IAEA,MAAMO,kBAAkBC,IAAAA,kCAAAA,EAAqB;QAC3CC,WAAW;YAAEC,WAAWb;QAAS;IACnC;IAEA,MAAMc,YAAYC,OAAMC,MAAM;IAC9B,MAAMC,kBAAkBC,oBAAAA,CAAKC,MAAM,CACjCC,IAAAA,wCAAAA,EAAyB5B,IAAI6B,IAAAA,6BAAAA,EAAmBhC,MAAMG,EAAE,EAAEH,SAC1D;QACEiC,aAAa;QACbC,cAAc;YACZjC,KAAKkC,IAAAA,6BAAAA,EAAclC,KAAKwB;YACxBW,MAAM;YACNC,MAAM;YACN,iBAAiB1B;YACjB,GAAGU,eAAe;QACpB;IACF;IAGFiB,IAAAA,yCAAAA,EAA0B;QACxB,OAAOzB,mBAAmB0B,CAAAA;gBACJA;gBAAAA;YAApB,MAAMC,cAAcD,CAAAA,6BAAAA,CAAAA,uBAAAA,KAAKE,cAAc,AAAdA,MAAc,QAAnBF,yBAAAA,KAAAA,IAAAA,KAAAA,IAAAA,oBAAqB,CAACjC,MAAM,AAANA,MAAM,QAA5BiC,+BAAAA,KAAAA,IAAAA,6BAAgC,EAAE;YACtD,MAAMG,sBAAsBF,YACzBG,GAAG,CAAC,CAACC;gBACJ,OAAOL,KAAKM,UAAU,CAACD,WAAW,CAACE,EAAE;YACvC,GACCC,IAAI,CAAC;YACR,IAAItB,UAAUuB,OAAO,EAAE;gBACrBvB,UAAUuB,OAAO,CAACC,YAAY,CAAC,iBAAiBP;YAClD;QACF;IACF,GAAG;QAACpC;QAAOO;KAAmB;IAE9B,mBAAmB;IACnBe,gBAAgB1B,OAAO,GAAGa;IAE1B,MAAMmC,QAAgC;QACpCvC;QACAP;QACA+C,YAAY;YACVC,MAAM;QACR;QACAA,MAAMxB;IACR;IAEA,OAAOsB;AACT"}