/// <reference types="react" />
export declare const MailProhibited24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailSettings16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailShield16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailTemplate20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailTemplate24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailWarning16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MeetNow28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MeetNow32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MeetNow48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MegaphoneLoud20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Microscope20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Microscope24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Molecule16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Molecule20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Molecule24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Molecule28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Molecule32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Molecule48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Note16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NotePin16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Notepad16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NotepadEdit16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Open32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaddingDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaddingDown24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaddingLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaddingLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaddingRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaddingRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaddingTop20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaddingTop24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Patch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Patch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PauseCircle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleSync16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleToolbox16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonChat16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonChat20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonChat24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonInfo16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonLock16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonLock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSubtract16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Phone16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PhoneCheckmark16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pill16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pill20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pill24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pill28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Print16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PrintAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Production20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Production24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProductionCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProductionCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Prohibited16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RatioOneToOne20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RatioOneToOne24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptBag20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptCube20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptMoney20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Record12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Record28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Record32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Record48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RecordStop12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RecordStop16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RecordStop20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RecordStop24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RecordStop28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RecordStop32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RecordStop48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Server20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Server24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldBadge20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBag16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBag20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBag24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideMultipleSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideMultipleSearch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Smartwatch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Smartwatch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SmartwatchDot20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SmartwatchDot24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Stack16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Stack20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Stack24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircle16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircle28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TargetArrow16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TargetArrow20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextBulletListSquareEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextBulletListSquareEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TooltipQuote20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarProfileLtr16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarProfileRtl16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckProfile16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VoicemailArrowBack16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VoicemailArrowForward16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VoicemailSubtract16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WifiWarning24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowEdit16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSortDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSortDown24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSortUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSortUp24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTurnBidirectionalDownRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTurnRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookQuestionMarkRtl24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrainCircuit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingBankToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarCheckmark28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarSearch16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CallPark32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatBubblesQuestion16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Checkmark32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckmarkCircle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardBulletListLtr16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardBulletListRtl16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ColumnEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ColumnEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCard28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCard32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCard48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diagram24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DismissCircle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentArrowDown16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentBulletListClock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentBulletListClock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentBulletListOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentPill20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentPill24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSave20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSave24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Door16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DualScreenArrowUp24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DualScreenClosedAlert24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GanttChart20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GanttChart24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HandDraw24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageArrowBack24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageArrowForward24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageReflection24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageShadow24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowDoubleBack16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowForward16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailAttach16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoreHorizontal32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoreVertical32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Organization16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Organization32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Organization48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Orientation24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleList16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleMoney20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleMoney24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonPill20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonPill24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSettings16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pivot20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pivot24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Play12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PrintAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pulse28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptBag24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptCube24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanObject20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanObject24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Search12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Search32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Search48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Share16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldBadge24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldLock16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideEraser16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideEraser20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideGrid24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareAdd16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintArrowBack16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Steps20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Steps24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TabDesktopBottom24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TabDesktopMultipleBottom24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TaskListSquareAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TaskListSquareAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TaskListSquareLtr20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TaskListSquareLtr24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TaskListSquareRtl20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TaskListSquareRtl24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignDistributedEvenly24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignDistributedVertical24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignJustifyLow24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextboxMore24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Video32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Video48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClip16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowAdPerson20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowDevEdit16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitHeightDotted24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitWidthDotted24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MultiselectRtl16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AnimalDog16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArchiveSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowClockwise16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowClockwiseDashes20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowClockwiseDashes24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCounterclockwiseDashes20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCounterclockwiseDashes24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowExportLtr16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowExportUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowExportUp24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BinFull20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BinFull24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Box16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BracesVariable20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BracesVariable24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Briefcase16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BriefcaseMedical16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingRetail20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingRetailMoney20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingRetailMoney24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingRetailShield20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingRetailShield24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingRetailToolbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingRetailToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalculatorMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalculatorMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Calendar3Day16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarArrowRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarDay16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarInfo20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarSettings16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CallCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CallEnd16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CameraDome16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CameraDome20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CameraDome24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CameraDome28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CameraDome48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Chat12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronDoubleDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronDoubleLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronDoubleRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardBulletListLtr20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardBulletListRtl20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardError20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardError24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardHeart24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardTask20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardTask24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardTaskAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardTaskAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardTaskListLtr20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardTaskListLtr24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockAlarm16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudLink20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Color16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ColumnTriple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ColumnTripleEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ColumnTripleEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentError20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommunicationPerson20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommunicationPerson24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContactCardLink20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataTrending16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataTrending20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataTrending24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataUsageEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Database20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Database24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DatabaseSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DatabaseSearch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DeleteArrowBack16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopEdit16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DismissCircle12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DividerTall16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentArrowDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentHeart20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentHeart24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentHeartPulse20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentHeartPulse24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentMultipleProhibited20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentMultipleProhibited24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSearch16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Door20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiLaugh16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EqualOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ErrorCircleSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EyeOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Fax20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FilterDismiss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FilterDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Flashlight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Flashlight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Flow20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Fluid16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderAdd16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderGlobe20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderMail20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderPerson20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderSync20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Food16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodCake16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FullScreenMaximize16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FullScreenMaximize20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Gif16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Gift16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeStar20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeSurface20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeSurface24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HeartBroken20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const History16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageProhibited20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageProhibited24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Info12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Iot20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Iot24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailOpenPerson16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailTemplate16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Navigation16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const News16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NoteEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NoteEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NotepadEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleList20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleSettings24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleTeamToolbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleTeamToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonFeedback16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonMoney20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonMoney24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PhoneEraser16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PortHdmi24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PortMicroUsb24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PortUsbA24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PortUsbC24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Prohibited12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptPlay20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptPlay24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SaveSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SearchSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldLock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarLineHorizontal316Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Stream20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Stream24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Syringe20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Syringe24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TargetArrow24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Temperature16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ThumbDislike16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TooltipQuote24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Umbrella20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Umbrella24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckProfile20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Warning12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowDevEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Xray20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Xray24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitDown24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitUp24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardTaskListRtl20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardTaskListRtl24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Comma24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignCenterRotate27024Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignJustifyRotate9024Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignLeftRotate27024Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignRightRotate27024Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextStrikethrough16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextStrikethrough20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextStrikethrough24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextboxAlignBottomRotate9024Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextboxAlignMiddleRotate9024Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextboxAlignTopRotate9024Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowClockwise12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowClockwise28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowClockwise32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowClockwise48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCounterclockwise12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCounterclockwise16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCounterclockwise32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCounterclockwise48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowRotateClockwise16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSplit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BatteryCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookOpen16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BroadActivityFeed16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BroadActivityFeed20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarPattern20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarPerson16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarPhone16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarPhone20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarStar16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatArrowBack16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatArrowBack20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatArrowDoubleBack16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatArrowDoubleBack20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Check24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxCheckedSync20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronUpDown16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CursorClick20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CursorClick24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Directions16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DividerTall20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentArrowUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentDismiss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentLink16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentPerson20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EditArrowBack20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSad16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeClock16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeClock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImmersiveReader16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LinkDismiss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LockOpen16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Luggage16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Luggage20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowDoubleBack20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowForward20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailAttach20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailError16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailInboxArrowDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailPause20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailShield20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NotePin20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PauseCircle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleCheckmark16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleSync20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleToolbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PhoneCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Play16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Play28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Play32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ReceiptMoney24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Save16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldError16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SportSoccer16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextQuote16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarProfileLtr20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarProfileRtl20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WeatherPartlyCloudyDay16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WeatherSunny16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionHorizontalLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionHorizontalLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionHorizontalRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionHorizontalRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionRotate270Right20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionRotate270Right24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionRotate90Left20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionRotate90Left24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionRotate90Right20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDirectionRotate90Right24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitContent24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingLines20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingLines24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarError24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CellularWarning24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowDown16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowDown24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowDown28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowDown32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowDown48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowUp16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
