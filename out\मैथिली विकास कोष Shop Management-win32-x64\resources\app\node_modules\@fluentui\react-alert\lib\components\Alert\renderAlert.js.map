{"version": 3, "sources": ["renderAlert.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\nimport { assertSlots } from '@fluentui/react-utilities';\n\nimport type { AlertState, AlertSlots } from './Alert.types';\n\n/**\n * @deprecated please use the Toast or MessageBar component\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const renderAlert_unstable = (state: AlertState) => {\n  // eslint-disable-next-line deprecation/deprecation\n  assertSlots<AlertSlots>(state);\n\n  return (\n    <state.root>\n      {state.icon && <state.icon />}\n      {state.avatar && <state.avatar />}\n      {state.root.children}\n      {state.action && <state.action />}\n    </state.root>\n  );\n};\n"], "names": ["assertSlots", "renderAlert_unstable", "state", "root", "icon", "avatar", "children", "action"], "mappings": "AAAA,0BAA0B,GAC1B,iDAAiD;AACjD,SAASA,WAAW,QAAQ,4BAA4B;AAIxD;;CAEC,GACD,mDAAmD;AACnD,OAAO,MAAMC,uBAAuB,CAACC;IACnC,mDAAmD;IACnDF,YAAwBE;IAExB,qBACE,MAACA,MAAMC,IAAI;;YACRD,MAAME,IAAI,kBAAI,KAACF,MAAME,IAAI;YACzBF,MAAMG,MAAM,kBAAI,KAACH,MAAMG,MAAM;YAC7BH,MAAMC,IAAI,CAACG,QAAQ;YACnBJ,MAAMK,MAAM,kBAAI,KAACL,MAAMK,MAAM;;;AAGpC,EAAE"}