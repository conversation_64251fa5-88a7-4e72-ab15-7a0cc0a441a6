{"version": 3, "sources": ["../src/components/Avatar/useAvatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { getIntrinsicElementProps, mergeCallbacks, useId, slot } from '@fluentui/react-utilities';\nimport { getInitials } from '../../utils/index';\nimport type { AvatarNamedColor, AvatarProps, AvatarState } from './Avatar.types';\nimport { PersonRegular } from '@fluentui/react-icons';\nimport { PresenceBadge } from '@fluentui/react-badge';\nimport { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nimport { useAvatarContext } from '../../contexts/AvatarContext';\n\nexport const DEFAULT_STRINGS = {\n  active: 'active',\n  inactive: 'inactive',\n};\n\nexport const useAvatar_unstable = (props: AvatarProps, ref: React.Ref<HTMLElement>): AvatarState => {\n  const { dir } = useFluent();\n  const { shape: contextShape, size: contextSize } = useAvatarContext();\n  const {\n    name,\n    size = contextSize ?? (32 as const),\n    shape = contextShape ?? 'circular',\n    active = 'unset',\n    activeAppearance = 'ring',\n    idForColor,\n  } = props;\n  let { color = 'neutral' } = props;\n\n  // Resolve 'colorful' to a specific color name\n  if (color === 'colorful') {\n    color = avatarColors[getHashCode(idForColor ?? name ?? '') % avatarColors.length];\n  }\n\n  const baseId = useId('avatar-');\n\n  const root: AvatarState['root'] = slot.always(\n    getIntrinsicElementProps(\n      'span',\n      {\n        role: 'img',\n        id: baseId,\n        // aria-label and/or aria-labelledby are resolved below\n        ...props,\n        ref,\n      },\n      /* excludedPropNames: */ ['name'],\n    ),\n    { elementType: 'span' },\n  );\n  const [imageHidden, setImageHidden] = React.useState<true | undefined>(undefined);\n  let image: AvatarState['image'] = slot.optional(props.image, {\n    defaultProps: { alt: '', role: 'presentation', 'aria-hidden': true, hidden: imageHidden },\n    elementType: 'img',\n  }); // Image shouldn't be rendered if its src is not set\n  if (!image?.src) {\n    image = undefined;\n  } // Hide the image if it fails to load and restore it on a successful load\n  if (image) {\n    image.onError = mergeCallbacks(image.onError, () => setImageHidden(true));\n    image.onLoad = mergeCallbacks(image.onLoad, () => setImageHidden(undefined));\n  } // Resolve the initials slot, defaulted to getInitials.\n  let initials: AvatarState['initials'] = slot.optional(props.initials, {\n    renderByDefault: true,\n    defaultProps: {\n      children: getInitials(name, dir === 'rtl', { firstInitialOnly: size <= 16 }),\n      id: baseId + '__initials',\n    },\n    elementType: 'span',\n  }); // Don't render the initials slot if it's empty\n  if (!initials?.children) {\n    initials = undefined;\n  } // Render the icon slot *only if* there aren't any initials or image to display\n  let icon: AvatarState['icon'] = undefined;\n  if (!initials && (!image || imageHidden)) {\n    icon = slot.optional(props.icon, {\n      renderByDefault: true,\n      defaultProps: { children: <PersonRegular />, 'aria-hidden': true },\n      elementType: 'span',\n    });\n  }\n  const badge: AvatarState['badge'] = slot.optional(props.badge, {\n    defaultProps: { size: getBadgeSize(size), id: baseId + '__badge' },\n    elementType: PresenceBadge,\n  });\n  let activeAriaLabelElement: AvatarState['activeAriaLabelElement']; // Resolve aria-label and/or aria-labelledby if not provided by the user\n  if (!root['aria-label'] && !root['aria-labelledby']) {\n    if (name) {\n      root['aria-label'] = name; // Include the badge in labelledby if it exists\n      if (badge) {\n        root['aria-labelledby'] = root.id + ' ' + badge.id;\n      }\n    } else if (initials) {\n      // root's aria-label should be the name, but fall back to being labelledby the initials if name is missing\n      root['aria-labelledby'] = initials.id + (badge ? ' ' + badge.id : '');\n    } // Add the active state to the aria label\n    if (active === 'active' || active === 'inactive') {\n      const activeText = DEFAULT_STRINGS[active];\n      if (root['aria-labelledby']) {\n        // If using aria-labelledby, render a hidden span and append it to the labelledby\n        const activeId = baseId + '__active';\n        root['aria-labelledby'] += ' ' + activeId;\n        activeAriaLabelElement = (\n          <span hidden id={activeId}>\n            {activeText}\n          </span>\n        );\n      } else if (root['aria-label']) {\n        // Otherwise, just append it to the aria-label\n        root['aria-label'] += ' ' + activeText;\n      }\n    }\n  }\n  return {\n    size,\n    shape,\n    active,\n    activeAppearance,\n    activeAriaLabelElement,\n    color,\n    components: { root: 'span', initials: 'span', icon: 'span', image: 'img', badge: PresenceBadge },\n    root,\n    initials,\n    icon,\n    image,\n    badge,\n  };\n};\nconst getBadgeSize = (size: AvatarState['size']) => {\n  if (size >= 96) {\n    return 'extra-large';\n  } else if (size >= 64) {\n    return 'large';\n  } else if (size >= 56) {\n    return 'medium';\n  } else if (size >= 40) {\n    return 'small';\n  } else if (size >= 28) {\n    return 'extra-small';\n  } else {\n    return 'tiny';\n  }\n};\n\nconst avatarColors: AvatarNamedColor[] = [\n  'dark-red',\n  'cranberry',\n  'red',\n  'pumpkin',\n  'peach',\n  'marigold',\n  'gold',\n  'brass',\n  'brown',\n  'forest',\n  'seafoam',\n  'dark-green',\n  'light-teal',\n  'teal',\n  'steel',\n  'blue',\n  'royal-blue',\n  'cornflower',\n  'navy',\n  'lavender',\n  'purple',\n  'grape',\n  'lilac',\n  'pink',\n  'magenta',\n  'plum',\n  'beige',\n  'mink',\n  'platinum',\n  'anchor',\n];\n\nconst getHashCode = (str: string): number => {\n  let hashCode = 0;\n  for (let len: number = str.length - 1; len >= 0; len--) {\n    const ch = str.charCodeAt(len);\n    const shift = len % 8;\n    hashCode ^= (ch << shift) + (ch >> (8 - shift)); // eslint-disable-line no-bitwise\n  }\n\n  return hashCode;\n};\n"], "names": ["React", "getIntrinsicElementProps", "mergeCallbacks", "useId", "slot", "getInitials", "PersonRegular", "PresenceBadge", "useFluent_unstable", "useFluent", "useAvatarContext", "DEFAULT_STRINGS", "active", "inactive", "useAvatar_unstable", "props", "ref", "dir", "shape", "contextShape", "size", "contextSize", "name", "activeAppearance", "idForColor", "color", "avatarColors", "getHashCode", "length", "baseId", "root", "always", "role", "id", "elementType", "imageHidden", "setImageHidden", "useState", "undefined", "image", "optional", "defaultProps", "alt", "hidden", "src", "onError", "onLoad", "initials", "renderByDefault", "children", "firstInitialOnly", "icon", "badge", "getBadgeSize", "activeAriaLabelElement", "activeText", "activeId", "span", "components", "str", "hashCode", "len", "ch", "charCodeAt", "shift"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,wBAAwB,EAAEC,cAAc,EAAEC,KAAK,EAAEC,IAAI,QAAQ,4BAA4B;AAClG,SAASC,WAAW,QAAQ,oBAAoB;AAEhD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,sBAAsBC,SAAS,QAAQ,kCAAkC;AAClF,SAASC,gBAAgB,QAAQ,+BAA+B;AAEhE,OAAO,MAAMC,kBAAkB;IAC7BC,QAAQ;IACRC,UAAU;AACZ,EAAE;AAEF,OAAO,MAAMC,qBAAqB,CAACC,OAAoBC;IACrD,MAAM,EAAEC,GAAG,EAAE,GAAGR;IAChB,MAAM,EAAES,OAAOC,YAAY,EAAEC,MAAMC,WAAW,EAAE,GAAGX;IACnD,MAAM,EACJY,IAAI,EACJF,OAAOC,wBAAAA,yBAAAA,cAAgB,EAAY,EACnCH,QAAQC,yBAAAA,0BAAAA,eAAgB,UAAU,EAClCP,SAAS,OAAO,EAChBW,mBAAmB,MAAM,EACzBC,UAAU,EACX,GAAGT;IACJ,IAAI,EAAEU,QAAQ,SAAS,EAAE,GAAGV;IAE5B,8CAA8C;IAC9C,IAAIU,UAAU,YAAY;YACSD;QAAjCC,QAAQC,YAAY,CAACC,YAAYH,CAAAA,OAAAA,uBAAAA,wBAAAA,aAAcF,kBAAdE,kBAAAA,OAAsB,MAAME,aAAaE,MAAM,CAAC;IACnF;IAEA,MAAMC,SAAS1B,MAAM;IAErB,MAAM2B,OAA4B1B,KAAK2B,MAAM,CAC3C9B,yBACE,QACA;QACE+B,MAAM;QACNC,IAAIJ;QACJ,uDAAuD;QACvD,GAAGd,KAAK;QACRC;IACF,GACA,sBAAsB,GAAG;QAAC;KAAO,GAEnC;QAAEkB,aAAa;IAAO;IAExB,MAAM,CAACC,aAAaC,eAAe,GAAGpC,MAAMqC,QAAQ,CAAmBC;IACvE,IAAIC,QAA8BnC,KAAKoC,QAAQ,CAACzB,MAAMwB,KAAK,EAAE;QAC3DE,cAAc;YAAEC,KAAK;YAAIV,MAAM;YAAgB,eAAe;YAAMW,QAAQR;QAAY;QACxFD,aAAa;IACf,IAAI,oDAAoD;IACxD,IAAI,EAACK,kBAAAA,4BAAAA,MAAOK,GAAG,GAAE;QACfL,QAAQD;IACV,EAAE,yEAAyE;IAC3E,IAAIC,OAAO;QACTA,MAAMM,OAAO,GAAG3C,eAAeqC,MAAMM,OAAO,EAAE,IAAMT,eAAe;QACnEG,MAAMO,MAAM,GAAG5C,eAAeqC,MAAMO,MAAM,EAAE,IAAMV,eAAeE;IACnE,EAAE,uDAAuD;IACzD,IAAIS,WAAoC3C,KAAKoC,QAAQ,CAACzB,MAAMgC,QAAQ,EAAE;QACpEC,iBAAiB;QACjBP,cAAc;YACZQ,UAAU5C,YAAYiB,MAAML,QAAQ,OAAO;gBAAEiC,kBAAkB9B,QAAQ;YAAG;YAC1Ea,IAAIJ,SAAS;QACf;QACAK,aAAa;IACf,IAAI,+CAA+C;IACnD,IAAI,EAACa,qBAAAA,+BAAAA,SAAUE,QAAQ,GAAE;QACvBF,WAAWT;IACb,EAAE,+EAA+E;IACjF,IAAIa,OAA4Bb;IAChC,IAAI,CAACS,YAAa,CAAA,CAACR,SAASJ,WAAU,GAAI;QACxCgB,OAAO/C,KAAKoC,QAAQ,CAACzB,MAAMoC,IAAI,EAAE;YAC/BH,iBAAiB;YACjBP,cAAc;gBAAEQ,wBAAU,oBAAC3C;gBAAkB,eAAe;YAAK;YACjE4B,aAAa;QACf;IACF;IACA,MAAMkB,QAA8BhD,KAAKoC,QAAQ,CAACzB,MAAMqC,KAAK,EAAE;QAC7DX,cAAc;YAAErB,MAAMiC,aAAajC;YAAOa,IAAIJ,SAAS;QAAU;QACjEK,aAAa3B;IACf;IACA,IAAI+C,wBAA+D,wEAAwE;IAC3I,IAAI,CAACxB,IAAI,CAAC,aAAa,IAAI,CAACA,IAAI,CAAC,kBAAkB,EAAE;QACnD,IAAIR,MAAM;YACRQ,IAAI,CAAC,aAAa,GAAGR,MAAM,+CAA+C;YAC1E,IAAI8B,OAAO;gBACTtB,IAAI,CAAC,kBAAkB,GAAGA,KAAKG,EAAE,GAAG,MAAMmB,MAAMnB,EAAE;YACpD;QACF,OAAO,IAAIc,UAAU;YACnB,0GAA0G;YAC1GjB,IAAI,CAAC,kBAAkB,GAAGiB,SAASd,EAAE,GAAImB,CAAAA,QAAQ,MAAMA,MAAMnB,EAAE,GAAG,EAAC;QACrE,EAAE,yCAAyC;QAC3C,IAAIrB,WAAW,YAAYA,WAAW,YAAY;YAChD,MAAM2C,aAAa5C,eAAe,CAACC,OAAO;YAC1C,IAAIkB,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,iFAAiF;gBACjF,MAAM0B,WAAW3B,SAAS;gBAC1BC,IAAI,CAAC,kBAAkB,IAAI,MAAM0B;gBACjCF,uCACE,oBAACG;oBAAKd,QAAAA;oBAAOV,IAAIuB;mBACdD;YAGP,OAAO,IAAIzB,IAAI,CAAC,aAAa,EAAE;gBAC7B,8CAA8C;gBAC9CA,IAAI,CAAC,aAAa,IAAI,MAAMyB;YAC9B;QACF;IACF;IACA,OAAO;QACLnC;QACAF;QACAN;QACAW;QACA+B;QACA7B;QACAiC,YAAY;YAAE5B,MAAM;YAAQiB,UAAU;YAAQI,MAAM;YAAQZ,OAAO;YAAOa,OAAO7C;QAAc;QAC/FuB;QACAiB;QACAI;QACAZ;QACAa;IACF;AACF,EAAE;AACF,MAAMC,eAAe,CAACjC;IACpB,IAAIA,QAAQ,IAAI;QACd,OAAO;IACT,OAAO,IAAIA,QAAQ,IAAI;QACrB,OAAO;IACT,OAAO,IAAIA,QAAQ,IAAI;QACrB,OAAO;IACT,OAAO,IAAIA,QAAQ,IAAI;QACrB,OAAO;IACT,OAAO,IAAIA,QAAQ,IAAI;QACrB,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA,MAAMM,eAAmC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,cAAc,CAACgC;IACnB,IAAIC,WAAW;IACf,IAAK,IAAIC,MAAcF,IAAI/B,MAAM,GAAG,GAAGiC,OAAO,GAAGA,MAAO;QACtD,MAAMC,KAAKH,IAAII,UAAU,CAACF;QAC1B,MAAMG,QAAQH,MAAM;QACpBD,YAAY,AAACE,CAAAA,MAAME,KAAI,IAAMF,CAAAA,MAAO,IAAIE,KAAK,GAAI,iCAAiC;IACpF;IAEA,OAAOJ;AACT"}