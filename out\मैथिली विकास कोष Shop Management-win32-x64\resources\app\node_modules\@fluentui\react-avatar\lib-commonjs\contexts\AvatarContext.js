"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AvatarContextProvider: function() {
        return AvatarContextProvider;
    },
    useAvatarContext: function() {
        return useAvatarContext;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const avatarContext = /*#__PURE__*/ _react.createContext(undefined);
const avatarContextDefaultValue = {};
const AvatarContextProvider = avatarContext.Provider;
const useAvatarContext = ()=>{
    var _React_useContext;
    return (_React_useContext = _react.useContext(avatarContext)) !== null && _React_useContext !== void 0 ? _React_useContext : avatarContextDefaultValue;
};
