{"version": 3, "sources": ["index.ts"], "sourcesContent": ["// eslint-disable-next-line deprecation/deprecation\nexport { Alert, alertClassNames, renderAlert_unstable, useAlertStyles_unstable, useAlert_unstable } from './Alert';\n// eslint-disable-next-line deprecation/deprecation\nexport type { AlertProps, AlertSlots, AlertState } from './Alert';\n"], "names": ["<PERSON><PERSON>", "alertClassNames", "renderAlert_unstable", "useAlertStyles_unstable", "useAlert_unstable"], "mappings": "AAAA,mDAAmD;AACnD,SAASA,KAAK,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,uBAAuB,EAAEC,iBAAiB,QAAQ,UAAU"}