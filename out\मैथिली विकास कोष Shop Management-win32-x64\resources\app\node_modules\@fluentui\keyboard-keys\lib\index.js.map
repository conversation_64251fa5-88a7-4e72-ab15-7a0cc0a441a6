{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["// https://github.com/microsoft/rushstack/issues/2780\n// API extractor can't support namespace exports\n// export * as keyCodes from './keyCodes';\nimport * as keyCodes from './keyCodes';\nexport { keyCodes };\n\nexport {\n  AVRInput,\n  AVRPower,\n  Accept,\n  Again,\n  AllCandidates,\n  Alphanumeric,\n  Alt,\n  AltGraph,\n  AppSwitch,\n  ArrowDown,\n  ArrowLeft,\n  ArrowRight,\n  ArrowUp,\n  Attn,\n  AudioBalanceLeft,\n  AudioBalanceRight,\n  AudioBassBoostDown,\n  AudioBassBoostToggle,\n  AudioBassBoostUp,\n  AudioFaderFront,\n  AudioFaderRear,\n  AudioSurroundModeNext,\n  AudioTrebleDown,\n  AudioTrebleUp,\n  AudioVolumeDown,\n  AudioVolumeMute,\n  AudioVolumeUp,\n  Backspace,\n  BrightnessDown,\n  BrightnessUp,\n  BrowserBack,\n  BrowserFavorites,\n  BrowserForward,\n  BrowserHome,\n  BrowserRefresh,\n  BrowserSearch,\n  BrowserStop,\n  Call,\n  Camera,\n  CameraFocus,\n  Cancel,\n  CapsLock,\n  ChannelDown,\n  ChannelUp,\n  Clear,\n  Close,\n  ClosedCaptionToggle,\n  CodeInput,\n  ColorF0Red,\n  ColorF1Green,\n  ColorF2Yellow,\n  ColorF3Blue,\n  ColorF4Grey,\n  ColorF5Brown,\n  Compose,\n  ContextMenu,\n  Control,\n  Convert,\n  Copy,\n  CrSel,\n  Cut,\n  DVR,\n  Dead,\n  Delete,\n  Dimmer,\n  DisplaySwap,\n  Eisu,\n  Eject,\n  End,\n  EndCall,\n  Enter,\n  EraseEof,\n  Escape,\n  ExSel,\n  Execute,\n  Exit,\n  F1,\n  F10,\n  F11,\n  F12,\n  F2,\n  F3,\n  F4,\n  F5,\n  F6,\n  F7,\n  F8,\n  F9,\n  FavoriteClear0,\n  FavoriteClear1,\n  FavoriteClear2,\n  FavoriteClear3,\n  FavoriteRecall0,\n  FavoriteRecall1,\n  FavoriteRecall2,\n  FavoriteRecall3,\n  FavoriteStore0,\n  FavoriteStore1,\n  FavoriteStore2,\n  FavoriteStore3,\n  FinalMode,\n  Find,\n  Fn,\n  FnLock,\n  GoBack,\n  GoHome,\n  GroupFirst,\n  GroupLast,\n  GroupNext,\n  GroupPrevious,\n  Guide,\n  GuideNextDay,\n  GuidePreviousDay,\n  HangulMode,\n  HanjaMode,\n  Hankaku,\n  HeadsetHook,\n  Help,\n  Hibernate,\n  Hiragana,\n  HiraganaKatakana,\n  Home,\n  Hyper,\n  Info,\n  Insert,\n  InstantReplay,\n  JunjaMode,\n  KanaMode,\n  KanjiMode,\n  Katakana,\n  Key11,\n  Key12,\n  LastNumberRedial,\n  LaunchApplication1,\n  LaunchApplication2,\n  LaunchCalendar,\n  LaunchContacts,\n  LaunchMail,\n  LaunchMediaPlayer,\n  LaunchMusicPlayer,\n  LaunchPhone,\n  LaunchScreenSaver,\n  LaunchSpreadsheet,\n  LaunchWebBrowser,\n  LaunchWebCam,\n  LaunchWordProcessor,\n  Link,\n  ListProgram,\n  LiveContent,\n  Lock,\n  LogOff,\n  MailForward,\n  MailReply,\n  MailSend,\n  MannerMode,\n  MediaApps,\n  MediaAudioTrack,\n  MediaClose,\n  MediaFastForward,\n  MediaLast,\n  MediaNextTrack,\n  MediaPause,\n  MediaPlay,\n  MediaPlayPause,\n  MediaPreviousTrack,\n  MediaRecord,\n  MediaRewind,\n  MediaSkipBackward,\n  MediaSkipForward,\n  MediaStepBackward,\n  MediaStepForward,\n  MediaStop,\n  MediaTopMenu,\n  MediaTrackNext,\n  MediaTrackPrevious,\n  Meta,\n  MicrophoneToggle,\n  MicrophoneVolumeDown,\n  MicrophoneVolumeMute,\n  MicrophoneVolumeUp,\n  ModeChange,\n  NavigateIn,\n  NavigateNext,\n  NavigateOut,\n  NavigatePrevious,\n  New,\n  NextCandidate,\n  NextFavoriteChannel,\n  NextUserProfile,\n  NonConvert,\n  Notification,\n  NumLock,\n  OnDemand,\n  Open,\n  PageDown,\n  PageUp,\n  Pairing,\n  Paste,\n  Pause,\n  PinPDown,\n  PinPMove,\n  PinPToggle,\n  PinPUp,\n  Play,\n  PlaySpeedDown,\n  PlaySpeedReset,\n  PlaySpeedUp,\n  Power,\n  PowerOff,\n  PreviousCandidate,\n  Print,\n  PrintScreen,\n  Process,\n  Props,\n  RandomToggle,\n  RcLowBattery,\n  RecordSpeedNext,\n  Redo,\n  RfBypass,\n  Romaji,\n  STBInput,\n  STBPower,\n  Save,\n  ScanChannelsToggle,\n  ScreenModeNext,\n  ScrollLock,\n  Select,\n  Settings,\n  Shift,\n  SingleCandidate,\n  Soft1,\n  Soft2,\n  Soft3,\n  Soft4,\n  Space,\n  SpeechCorrectionList,\n  SpeechInputToggle,\n  SpellCheck,\n  SplitScreenToggle,\n  Standby,\n  Subtitle,\n  Super,\n  Symbol,\n  SymbolLock,\n  TV,\n  TV3DMode,\n  TVAntennaCable,\n  TVAudioDescription,\n  TVAudioDescriptionMixDown,\n  TVAudioDescriptionMixUp,\n  TVContentsMenu,\n  TVDataService,\n  TVInput,\n  TVInputComponent1,\n  TVInputComponent2,\n  TVInputComposite1,\n  TVInputComposite2,\n  TVInputHDMI1,\n  TVInputHDMI2,\n  TVInputHDMI3,\n  TVInputHDMI4,\n  TVInputVGA1,\n  TVMediaContext,\n  TVNetwork,\n  TVNumberEntry,\n  TVPower,\n  TVRadioService,\n  TVSatellite,\n  TVSatelliteBS,\n  TVSatelliteCS,\n  TVSatelliteToggle,\n  TVTerrestrialAnalog,\n  TVTerrestrialDigital,\n  TVTimer,\n  Tab,\n  Teletext,\n  Undo,\n  Unidentified,\n  VideoModeNext,\n  VoiceDial,\n  WakeUp,\n  Wink,\n  Zenkaku,\n  ZenkakuHankaku,\n  ZoomIn,\n  ZoomOut,\n  ZoomToggle,\n} from './keys';\n"], "names": ["keyCodes", "AVRInput", "AVRPower", "Accept", "Again", "AllCandidates", "Alphanumeric", "Alt", "AltGraph", "AppSwitch", "ArrowDown", "ArrowLeft", "ArrowRight", "ArrowUp", "Attn", "AudioBalanceLeft", "AudioBalanceRight", "AudioBassBoostDown", "AudioBassBoostToggle", "AudioBassBoostUp", "AudioFaderFront", "AudioFaderRear", "AudioSurroundModeNext", "AudioTrebleDown", "AudioTrebleUp", "AudioVolumeDown", "AudioVolumeMute", "AudioVolumeUp", "Backspace", "BrightnessDown", "BrightnessUp", "BrowserBack", "BrowserFavorites", "BrowserForward", "BrowserHome", "BrowserRefresh", "BrowserSearch", "BrowserStop", "Call", "Camera", "CameraFocus", "Cancel", "CapsLock", "ChannelDown", "ChannelUp", "Clear", "Close", "ClosedCaptionToggle", "CodeInput", "ColorF0Red", "ColorF1Green", "ColorF2Yellow", "ColorF3Blue", "ColorF4Grey", "ColorF5Brown", "Compose", "ContextMenu", "Control", "Convert", "Copy", "CrSel", "Cut", "DVR", "Dead", "Delete", "<PERSON><PERSON>", "DisplaySwap", "<PERSON><PERSON><PERSON>", "Eject", "End", "EndCall", "Enter", "EraseEof", "Escape", "ExSel", "Execute", "Exit", "F1", "F10", "F11", "F12", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "FavoriteClear0", "FavoriteClear1", "FavoriteClear2", "FavoriteClear3", "FavoriteRecall0", "FavoriteRecall1", "FavoriteRecall2", "FavoriteRecall3", "FavoriteStore0", "FavoriteStore1", "FavoriteStore2", "FavoriteStore3", "FinalMode", "Find", "Fn", "FnLock", "GoBack", "GoHome", "GroupFirst", "GroupLast", "GroupNext", "GroupPrevious", "Guide", "GuideNextDay", "GuidePreviousDay", "HangulMode", "HanjaMode", "<PERSON><PERSON><PERSON>", "HeadsetHook", "Help", "Hibernate", "Hi<PERSON>na", "HiraganaKatakana", "Home", "Hyper", "Info", "Insert", "InstantReplay", "JunjaMode", "KanaMode", "KanjiMode", "<PERSON><PERSON><PERSON>", "Key11", "Key12", "LastNumberRedial", "LaunchApplication1", "LaunchApplication2", "LaunchCalendar", "LaunchContacts", "LaunchMail", "LaunchMediaPlayer", "LaunchMusicPlayer", "LaunchPhone", "LaunchScreenSaver", "LaunchSpreadsheet", "LaunchWebBrowser", "LaunchWebCam", "LaunchWordProcessor", "Link", "ListProgram", "LiveContent", "Lock", "<PERSON><PERSON><PERSON><PERSON>", "MailForward", "MailReply", "MailSend", "MannerMode", "MediaApps", "MediaAudioTrack", "MediaClose", "MediaFastForward", "MediaLast", "MediaNextTrack", "MediaPause", "MediaPlay", "MediaPlayPause", "MediaPreviousTrack", "MediaRecord", "MediaRewind", "MediaSkipBackward", "MediaSkipForward", "MediaStepBackward", "MediaStepForward", "MediaStop", "MediaTopMenu", "MediaTrackNext", "MediaTrackPrevious", "Meta", "MicrophoneToggle", "MicrophoneVolumeDown", "MicrophoneVolumeMute", "MicrophoneVolumeUp", "ModeChange", "NavigateIn", "NavigateNext", "NavigateOut", "NavigatePrevious", "New", "NextCandidate", "NextFavoriteChannel", "NextUserProfile", "NonConvert", "Notification", "NumLock", "OnDemand", "Open", "PageDown", "PageUp", "Pairing", "Paste", "Pause", "PinPDown", "<PERSON><PERSON><PERSON><PERSON>", "PinPToggle", "PinPUp", "Play", "PlaySpeedDown", "PlaySpeedReset", "PlaySpeedUp", "Power", "PowerOff", "PreviousCandidate", "Print", "PrintScreen", "Process", "Props", "RandomToggle", "RcLowBattery", "RecordSpeedNext", "Redo", "RfBypass", "<PERSON><PERSON>", "STBInput", "STBPower", "Save", "ScanChannelsToggle", "ScreenModeNext", "ScrollLock", "Select", "Settings", "Shift", "SingleCandidate", "Soft1", "Soft2", "Soft3", "Soft4", "Space", "SpeechCorrectionList", "SpeechInputToggle", "SpellCheck", "SplitScreenToggle", "Standby", "Subtitle", "Super", "Symbol", "SymbolLock", "TV", "TV3DMode", "TVAntennaCable", "TVAudioDescription", "TVAudioDescriptionMixDown", "TVAudioDescriptionMixUp", "TVContentsMenu", "TVDataService", "TVInput", "TVInputComponent1", "TVInputComponent2", "TVInputComposite1", "TVInputComposite2", "TVInputHDMI1", "TVInputHDMI2", "TVInputHDMI3", "TVInputHDMI4", "TVInputVGA1", "TVMediaContext", "TVNetwork", "TVNumberEntry", "TVPower", "TVRadioService", "TVSatellite", "TVSatelliteBS", "TVSatelliteCS", "TVSatelliteToggle", "TVTerrestrialAnalog", "TVTerrestrialDigital", "TVTimer", "Tab", "Teletext", "Undo", "Unidentified", "VideoModeNext", "VoiceDial", "WakeUp", "<PERSON><PERSON>", "Zenkaku", "ZenkakuHankaku", "ZoomIn", "ZoomOut", "ZoomToggle"], "rangeMappings": ";;;;;", "mappings": "AAAA,qDAAqD;AACrD,gDAAgD;AAChD,0CAA0C;AAC1C,YAAYA,cAAc,aAAa;AACvC,SAASA,QAAQ,GAAG;AAEpB,SACEC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,aAAa,EACbC,YAAY,EACZC,GAAG,EACHC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,gBAAgB,EAChBC,eAAe,EACfC,cAAc,EACdC,qBAAqB,EACrBC,eAAe,EACfC,aAAa,EACbC,eAAe,EACfC,eAAe,EACfC,aAAa,EACbC,SAAS,EACTC,cAAc,EACdC,YAAY,EACZC,WAAW,EACXC,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,cAAc,EACdC,aAAa,EACbC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,mBAAmB,EACnBC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,aAAa,EACbC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,WAAW,EACXC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,EAAE,EACFC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,EAAE,EACFC,EAAE,EACFC,EAAE,EACFC,EAAE,EACFC,EAAE,EACFC,EAAE,EACFC,EAAE,EACFC,EAAE,EACFC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,SAAS,EACTC,IAAI,EACJC,EAAE,EACFC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,SAAS,EACTC,SAAS,EACTC,aAAa,EACbC,KAAK,EACLC,YAAY,EACZC,gBAAgB,EAChBC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,WAAW,EACXC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,gBAAgB,EAChBC,kBAAkB,EAClBC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,UAAU,EACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,WAAW,EACXC,iBAAiB,EACjBC,iBAAiB,EACjBC,gBAAgB,EAChBC,YAAY,EACZC,mBAAmB,EACnBC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,eAAe,EACfC,UAAU,EACVC,gBAAgB,EAChBC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVC,SAAS,EACTC,cAAc,EACdC,kBAAkB,EAClBC,WAAW,EACXC,WAAW,EACXC,iBAAiB,EACjBC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,SAAS,EACTC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,IAAI,EACJC,gBAAgB,EAChBC,oBAAoB,EACpBC,oBAAoB,EACpBC,kBAAkB,EAClBC,UAAU,EACVC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,gBAAgB,EAChBC,GAAG,EACHC,aAAa,EACbC,mBAAmB,EACnBC,eAAe,EACfC,UAAU,EACVC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,cAAc,EACdC,WAAW,EACXC,KAAK,EACLC,QAAQ,EACRC,iBAAiB,EACjBC,KAAK,EACLC,WAAW,EACXC,OAAO,EACPC,KAAK,EACLC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,IAAI,EACJC,kBAAkB,EAClBC,cAAc,EACdC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,eAAe,EACfC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,oBAAoB,EACpBC,iBAAiB,EACjBC,UAAU,EACVC,iBAAiB,EACjBC,OAAO,EACPC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,EAAE,EACFC,QAAQ,EACRC,cAAc,EACdC,kBAAkB,EAClBC,yBAAyB,EACzBC,uBAAuB,EACvBC,cAAc,EACdC,aAAa,EACbC,OAAO,EACPC,iBAAiB,EACjBC,iBAAiB,EACjBC,iBAAiB,EACjBC,iBAAiB,EACjBC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,SAAS,EACTC,aAAa,EACbC,OAAO,EACPC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB,EACpBC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,IAAI,EACJC,YAAY,EACZC,aAAa,EACbC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,cAAc,EACdC,MAAM,EACNC,OAAO,EACPC,UAAU,QACL,SAAS"}