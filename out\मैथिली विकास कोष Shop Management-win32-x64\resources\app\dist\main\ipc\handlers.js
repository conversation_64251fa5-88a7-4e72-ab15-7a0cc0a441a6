"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerIpcHandlers = registerIpcHandlers;
const electron_1 = require("electron");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const userService_1 = require("../services/userService");
const productService_1 = require("../services/productService");
const database_1 = require("../database/database");
/**
 * Register all IPC handlers for the main process
 */
function registerIpcHandlers() {
    // App-related handlers
    electron_1.ipcMain.handle('app:getVersion', () => {
        return electron_1.app.getVersion();
    });
    // Window control handlers
    electron_1.ipcMain.handle('window:minimize', () => {
        const window = electron_1.BrowserWindow.getFocusedWindow();
        if (window) {
            window.minimize();
        }
    });
    electron_1.ipcMain.handle('window:maximize', () => {
        const window = electron_1.BrowserWindow.getFocusedWindow();
        if (window) {
            if (window.isMaximized()) {
                window.unmaximize();
            }
            else {
                window.maximize();
            }
        }
    });
    electron_1.ipcMain.handle('window:close', () => {
        const window = electron_1.BrowserWindow.getFocusedWindow();
        if (window) {
            window.close();
        }
    });
    // File operation handlers
    electron_1.ipcMain.handle('files:selectImage', async () => {
        const result = await electron_1.dialog.showOpenDialog({
            properties: ['openFile'],
            filters: [
                { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] }
            ]
        });
        if (!result.canceled && result.filePaths.length > 0) {
            return result.filePaths[0];
        }
        return null;
    });
    electron_1.ipcMain.handle('files:saveImage', async (_event, imageData, filename) => {
        try {
            const userDataPath = electron_1.app.getPath('userData');
            const imagesDir = path.join(userDataPath, 'images');
            // Create images directory if it doesn't exist
            if (!fs.existsSync(imagesDir)) {
                fs.mkdirSync(imagesDir, { recursive: true });
            }
            const filePath = path.join(imagesDir, filename);
            fs.writeFileSync(filePath, imageData);
            return filePath;
        }
        catch (error) {
            console.error('Error saving image:', error);
            throw error;
        }
    });
    // Notification handlers
    electron_1.ipcMain.handle('notifications:show', (_event, title, body) => {
        if (electron_1.Notification.isSupported()) {
            const notification = new electron_1.Notification({
                title,
                body,
                icon: path.join(__dirname, '../../../assets/icons/app-icon.png')
            });
            notification.show();
            notification.on('click', () => {
                const window = electron_1.BrowserWindow.getFocusedWindow();
                if (window) {
                    if (window.isMinimized()) {
                        window.restore();
                    }
                    window.focus();
                }
            });
        }
    });
    // Database handlers - now using real database services
    electron_1.ipcMain.handle('db:getUsers', async () => {
        try {
            const result = await userService_1.userService.getUsers();
            if (result.success && result.data) {
                // Remove sensitive data before sending to renderer
                const safeUsers = result.data.data.map(user => {
                    const { passwordHash, salt, ...safeUser } = user;
                    return safeUser;
                });
                return { ...result.data, data: safeUsers };
            }
            return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
        }
        catch (error) {
            console.error('Error getting users:', error);
            return { data: [], total: 0, page: 1, limit: 50, totalPages: 0 };
        }
    });
    electron_1.ipcMain.handle('db:createUser', async (_event, userData) => {
        try {
            const result = await userService_1.userService.createUser(userData);
            if (result.success && result.data) {
                const { passwordHash, salt, ...safeUser } = result.data;
                return safeUser;
            }
            throw new Error(result.error || 'Failed to create user');
        }
        catch (error) {
            console.error('Error creating user:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('db:getProducts', async () => {
        try {
            const result = await productService_1.productService.getProducts();
            if (result.success && result.data) {
                return result.data.data;
            }
            return [];
        }
        catch (error) {
            console.error('Error getting products:', error);
            return [];
        }
    });
    electron_1.ipcMain.handle('db:createProduct', async (_event, productData) => {
        try {
            const result = await productService_1.productService.createProduct(productData);
            if (result.success && result.data) {
                return result.data;
            }
            throw new Error(result.error || 'Failed to create product');
        }
        catch (error) {
            console.error('Error creating product:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('db:getCustomers', async () => {
        try {
            const result = await database_1.databaseService.query('SELECT * FROM customers WHERE is_active = 1 ORDER BY name');
            if (result.success && result.data) {
                return result.data;
            }
            return [];
        }
        catch (error) {
            console.error('Error getting customers:', error);
            return [];
        }
    });
    electron_1.ipcMain.handle('db:createCustomer', async (_event, customerData) => {
        try {
            const result = await database_1.databaseService.execute(`
        INSERT INTO customers (
          customer_code, name, name_hindi, email, phone, address, city, state, pincode,
          country, customer_type, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
      `, [
                customerData.customerCode || `CUST${Date.now()}`,
                customerData.name,
                customerData.nameHindi || null,
                customerData.email || null,
                customerData.phone,
                customerData.address,
                customerData.city,
                customerData.state,
                customerData.pincode,
                customerData.country || 'India',
                customerData.customerType || 'individual'
            ]);
            if (result.success && result.data) {
                return { id: result.data.lastInsertRowid, ...customerData };
            }
            throw new Error(result.error || 'Failed to create customer');
        }
        catch (error) {
            console.error('Error creating customer:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('db:getOrders', async () => {
        try {
            const result = await database_1.databaseService.query(`
        SELECT
          o.*,
          c.name as customerName,
          u.full_name as createdBy
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        LEFT JOIN users u ON o.user_id = u.id
        ORDER BY o.created_at DESC
      `);
            if (result.success && result.data) {
                return result.data;
            }
            return [];
        }
        catch (error) {
            console.error('Error getting orders:', error);
            return [];
        }
    });
    electron_1.ipcMain.handle('db:createOrder', async (_event, orderData) => {
        try {
            const orderNumber = `ORD${Date.now()}`;
            const result = await database_1.databaseService.execute(`
        INSERT INTO orders (
          order_number, customer_id, subtotal, tax_amount, discount_amount,
          shipping_amount, total_amount, shipping_address, billing_address,
          user_id, status, payment_status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft', 'pending')
      `, [
                orderNumber,
                orderData.customerId,
                orderData.subtotal || 0,
                orderData.taxAmount || 0,
                orderData.discountAmount || 0,
                orderData.shippingAmount || 0,
                orderData.totalAmount || 0,
                orderData.shippingAddress,
                orderData.billingAddress,
                orderData.userId || 1
            ]);
            if (result.success && result.data) {
                return { id: result.data.lastInsertRowid, orderNumber, ...orderData };
            }
            throw new Error(result.error || 'Failed to create order');
        }
        catch (error) {
            console.error('Error creating order:', error);
            throw error;
        }
    });
    // Additional database handlers
    electron_1.ipcMain.handle('db:getStats', async () => {
        try {
            const result = await database_1.databaseService.getStats();
            return result.data || {};
        }
        catch (error) {
            console.error('Error getting database stats:', error);
            return {};
        }
    });
    electron_1.ipcMain.handle('db:getCategories', async () => {
        try {
            const result = await database_1.databaseService.query('SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order, name');
            return result.data || [];
        }
        catch (error) {
            console.error('Error getting categories:', error);
            return [];
        }
    });
    electron_1.ipcMain.handle('db:getArtists', async () => {
        try {
            const result = await database_1.databaseService.query('SELECT * FROM artists WHERE is_active = 1 ORDER BY name');
            return result.data || [];
        }
        catch (error) {
            console.error('Error getting artists:', error);
            return [];
        }
    });
    console.log('✅ All IPC handlers registered successfully');
}
//# sourceMappingURL=handlers.js.map