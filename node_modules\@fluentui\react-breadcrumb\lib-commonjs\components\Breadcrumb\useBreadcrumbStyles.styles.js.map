{"version": 3, "sources": ["useBreadcrumbStyles.styles.js"], "sourcesContent": ["import { makeResetStyles, mergeClasses } from '@griffel/react';\nexport const breadcrumbClassNames = {\n    root: 'fui-Breadcrumb',\n    list: 'fui-Breadcrumb__list'\n};\nconst useListClassName = makeResetStyles({\n    listStyleType: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    margin: 0,\n    padding: 0\n});\n/**\n * Apply styling to the Breadcrumb slots based on the state\n */ export const useBreadcrumbStyles_unstable = (state)=>{\n    'use no memo';\n    const listBaseClassName = useListClassName();\n    state.root.className = mergeClasses(breadcrumbClassNames.root, state.root.className);\n    if (state.list) {\n        state.list.className = mergeClasses(listBaseClassName, breadcrumbClassNames.list, state.list.className);\n    }\n    return state;\n};\n"], "names": ["breadcrumbClassNames", "useBreadcrumbStyles_unstable", "root", "list", "useListClassName", "__resetStyles", "state", "listBaseClassName", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IACaA,oBAAoB;eAApBA;;IAaIC,4BAA4B;eAA5BA;;;uBAd6B;AACvC,MAAMD,uBAAuB;IAChCE,MAAM;IACNC,MAAM;AACV;AACA,MAAMC,mBAAgB,WAAA,GAAGC,IAAAA,oBAAA,EAAA,WAAA,MAAA;IAAA;CAMxB;AAGU,MAAMJ,+BAAgCK,CAAAA;IAC7C;IACA,MAAMC,oBAAoBH;IAC1BE,MAAMJ,IAAI,CAACM,SAAS,GAAGC,IAAAA,mBAAY,EAACT,qBAAqBE,IAAI,EAAEI,MAAMJ,IAAI,CAACM,SAAS;IACnF,IAAIF,MAAMH,IAAI,EAAE;QACZG,MAAMH,IAAI,CAACK,SAAS,GAAGC,IAAAA,mBAAY,EAACF,mBAAmBP,qBAAqBG,IAAI,EAAEG,MAAMH,IAAI,CAACK,SAAS;IAC1G;IACA,OAAOF;AACX"}