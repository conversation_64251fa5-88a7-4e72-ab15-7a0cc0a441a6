"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AvatarGroupItem", {
    enumerable: true,
    get: function() {
        return AvatarGroupItem;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _renderAvatarGroupItem = require("./renderAvatarGroupItem");
const _useAvatarGroupItem = require("./useAvatarGroupItem");
const _reactsharedcontexts = require("@fluentui/react-shared-contexts");
const _useAvatarGroupItemStylesstyles = require("./useAvatarGroupItemStyles.styles");
const AvatarGroupItem = /*#__PURE__*/ _react.forwardRef((props, ref)=>{
    const state = (0, _useAvatarGroupItem.useAvatarGroupItem_unstable)(props, ref);
    (0, _useAvatarGroupItemStylesstyles.useAvatarGroupItemStyles_unstable)(state);
    (0, _reactsharedcontexts.useCustomStyleHook_unstable)('useAvatarGroupItemStyles_unstable')(state);
    return (0, _renderAvatarGroupItem.renderAvatarGroupItem_unstable)(state);
});
AvatarGroupItem.displayName = 'AvatarGroupItem';
