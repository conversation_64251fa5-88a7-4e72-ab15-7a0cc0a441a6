"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useARIAButtonShorthand", {
    enumerable: true,
    get: function() {
        return useARIAButtonShorthand;
    }
});
const _reactutilities = require("@fluentui/react-utilities");
const _useARIAButtonProps = require("./useARIAButtonProps");
const useARIAButtonShorthand = (value, options)=>{
    // eslint-disable-next-line @typescript-eslint/no-deprecated
    const shorthand = (0, _reactutilities.resolveShorthand)(value, options);
    var _shorthand_as;
    const shorthandARIAButton = (0, _useARIAButtonProps.useARIAButtonProps)((_shorthand_as = shorthand === null || shorthand === void 0 ? void 0 : shorthand.as) !== null && _shorthand_as !== void 0 ? _shorthand_as : 'button', shorthand);
    return shorthand && shorthandARIAButton;
// eslint-disable-next-line @typescript-eslint/no-deprecated
};
