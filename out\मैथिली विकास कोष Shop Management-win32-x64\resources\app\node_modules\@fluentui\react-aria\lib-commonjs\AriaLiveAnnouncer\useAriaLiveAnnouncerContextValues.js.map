{"version": 3, "sources": ["../src/AriaLiveAnnouncer/useAriaLiveAnnouncerContextValues.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { AriaLiveAnnouncerContextValues, AriaLiveAnnouncerState } from './AriaLiveAnnouncer.types';\n\nexport function useAriaLiveAnnouncerContextValues_unstable(\n  state: AriaLiveAnnouncerState,\n): AriaLiveAnnouncerContextValues {\n  const { announce } = state;\n\n  return React.useMemo(() => ({ announce: { announce } }), [announce]);\n}\n"], "names": ["useAriaLiveAnnouncerContextValues_unstable", "state", "announce", "React", "useMemo"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAGgBA;;;eAAAA;;;;iEAHO;AAGhB,SAASA,2CACdC,KAA6B;IAE7B,MAAM,EAAEC,QAAQ,EAAE,GAAGD;IAErB,OAAOE,OAAMC,OAAO,CAAC,IAAO,CAAA;YAAEF,UAAU;gBAAEA;YAAS;QAAE,CAAA,GAAI;QAACA;KAAS;AACrE"}