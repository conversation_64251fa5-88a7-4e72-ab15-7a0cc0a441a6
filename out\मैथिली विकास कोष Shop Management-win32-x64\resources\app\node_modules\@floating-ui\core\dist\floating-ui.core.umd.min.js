!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).FloatingUICore={})}(this,(function(t){"use strict";const e=["top","right","bottom","left"],n=["start","end"],i=e.reduce(((t,e)=>t.concat(e,e+"-"+n[0],e+"-"+n[1])),[]),o=Math.min,r=Math.max,a={left:"right",right:"left",bottom:"top",top:"bottom"},l={start:"end",end:"start"};function s(t,e,n){return r(t,o(e,n))}function f(t,e){return"function"==typeof t?t(e):t}function c(t){return t.split("-")[0]}function m(t){return t.split("-")[1]}function u(t){return"x"===t?"y":"x"}function d(t){return"y"===t?"height":"width"}const g=new Set(["top","bottom"]);function p(t){return g.has(c(t))?"y":"x"}function h(t){return u(p(t))}function y(t,e,n){void 0===n&&(n=!1);const i=m(t),o=h(t),r=d(o);let a="x"===o?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[r]>e.floating[r]&&(a=P(a)),[a,P(a)]}function w(t){return t.replace(/start|end/g,(t=>l[t]))}const x=["left","right"],v=["right","left"],b=["top","bottom"],A=["bottom","top"];function R(t,e,n,i){const o=m(t);let r=function(t,e,n){switch(t){case"top":case"bottom":return n?e?v:x:e?x:v;case"left":case"right":return e?b:A;default:return[]}}(c(t),"start"===n,i);return o&&(r=r.map((t=>t+"-"+o)),e&&(r=r.concat(r.map(w)))),r}function P(t){return t.replace(/left|right|bottom|top/g,(t=>a[t]))}function D(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function T(t){const{x:e,y:n,width:i,height:o}=t;return{width:i,height:o,top:n,left:e,right:e+i,bottom:n+o,x:e,y:n}}function O(t,e,n){let{reference:i,floating:o}=t;const r=p(e),a=h(e),l=d(a),s=c(e),f="y"===r,u=i.x+i.width/2-o.width/2,g=i.y+i.height/2-o.height/2,y=i[l]/2-o[l]/2;let w;switch(s){case"top":w={x:u,y:i.y-o.height};break;case"bottom":w={x:u,y:i.y+i.height};break;case"right":w={x:i.x+i.width,y:g};break;case"left":w={x:i.x-o.width,y:g};break;default:w={x:i.x,y:i.y}}switch(m(e)){case"start":w[a]-=y*(n&&f?-1:1);break;case"end":w[a]+=y*(n&&f?-1:1)}return w}async function E(t,e){var n;void 0===e&&(e={});const{x:i,y:o,platform:r,rects:a,elements:l,strategy:s}=t,{boundary:c="clippingAncestors",rootBoundary:m="viewport",elementContext:u="floating",altBoundary:d=!1,padding:g=0}=f(e,t),p=D(g),h=l[d?"floating"===u?"reference":"floating":u],y=T(await r.getClippingRect({element:null==(n=await(null==r.isElement?void 0:r.isElement(h)))||n?h:h.contextElement||await(null==r.getDocumentElement?void 0:r.getDocumentElement(l.floating)),boundary:c,rootBoundary:m,strategy:s})),w="floating"===u?{x:i,y:o,width:a.floating.width,height:a.floating.height}:a.reference,x=await(null==r.getOffsetParent?void 0:r.getOffsetParent(l.floating)),v=await(null==r.isElement?void 0:r.isElement(x))&&await(null==r.getScale?void 0:r.getScale(x))||{x:1,y:1},b=T(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:w,offsetParent:x,strategy:s}):w);return{top:(y.top-b.top+p.top)/v.y,bottom:(b.bottom-y.bottom+p.bottom)/v.y,left:(y.left-b.left+p.left)/v.x,right:(b.right-y.right+p.right)/v.x}}function L(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function k(t){return e.some((e=>t[e]>=0))}function C(t){const e=o(...t.map((t=>t.left))),n=o(...t.map((t=>t.top)));return{x:e,y:n,width:r(...t.map((t=>t.right)))-e,height:r(...t.map((t=>t.bottom)))-n}}const S=new Set(["left","top"]);t.arrow=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:i,placement:r,rects:a,platform:l,elements:c,middlewareData:u}=e,{element:g,padding:p=0}=f(t,e)||{};if(null==g)return{};const y=D(p),w={x:n,y:i},x=h(r),v=d(x),b=await l.getDimensions(g),A="y"===x,R=A?"top":"left",P=A?"bottom":"right",T=A?"clientHeight":"clientWidth",O=a.reference[v]+a.reference[x]-w[x]-a.floating[v],E=w[x]-a.reference[x],L=await(null==l.getOffsetParent?void 0:l.getOffsetParent(g));let k=L?L[T]:0;k&&await(null==l.isElement?void 0:l.isElement(L))||(k=c.floating[T]||a.floating[v]);const C=O/2-E/2,S=k/2-b[v]/2-1,B=o(y[R],S),H=o(y[P],S),F=B,j=k-b[v]-H,z=k/2-b[v]/2+C,M=s(F,z,j),V=!u.arrow&&null!=m(r)&&z!==M&&a.reference[v]/2-(z<F?B:H)-b[v]/2<0,W=V?z<F?z-F:z-j:0;return{[x]:w[x]+W,data:{[x]:M,centerOffset:z-M-W,...V&&{alignmentOffset:W}},reset:V}}}),t.autoPlacement=function(t){return void 0===t&&(t={}),{name:"autoPlacement",options:t,async fn(e){var n,o,r;const{rects:a,middlewareData:l,placement:s,platform:u,elements:d}=e,{crossAxis:g=!1,alignment:p,allowedPlacements:h=i,autoAlignment:x=!0,...v}=f(t,e),b=void 0!==p||h===i?function(t,e,n){return(t?[...n.filter((e=>m(e)===t)),...n.filter((e=>m(e)!==t))]:n.filter((t=>c(t)===t))).filter((n=>!t||m(n)===t||!!e&&w(n)!==n))}(p||null,x,h):h,A=await E(e,v),R=(null==(n=l.autoPlacement)?void 0:n.index)||0,P=b[R];if(null==P)return{};const D=y(P,a,await(null==u.isRTL?void 0:u.isRTL(d.floating)));if(s!==P)return{reset:{placement:b[0]}};const T=[A[c(P)],A[D[0]],A[D[1]]],O=[...(null==(o=l.autoPlacement)?void 0:o.overflows)||[],{placement:P,overflows:T}],L=b[R+1];if(L)return{data:{index:R+1,overflows:O},reset:{placement:L}};const k=O.map((t=>{const e=m(t.placement);return[t.placement,e&&g?t.overflows.slice(0,2).reduce(((t,e)=>t+e),0):t.overflows[0],t.overflows]})).sort(((t,e)=>t[1]-e[1])),C=(null==(r=k.filter((t=>t[2].slice(0,m(t[0])?2:3).every((t=>t<=0))))[0])?void 0:r[0])||k[0][0];return C!==s?{data:{index:R+1,overflows:O},reset:{placement:C}}:{}}}},t.computePosition=async(t,e,n)=>{const{placement:i="bottom",strategy:o="absolute",middleware:r=[],platform:a}=n,l=r.filter(Boolean),s=await(null==a.isRTL?void 0:a.isRTL(e));let f=await a.getElementRects({reference:t,floating:e,strategy:o}),{x:c,y:m}=O(f,i,s),u=i,d={},g=0;for(let n=0;n<l.length;n++){const{name:r,fn:p}=l[n],{x:h,y:y,data:w,reset:x}=await p({x:c,y:m,initialPlacement:i,placement:u,strategy:o,middlewareData:d,rects:f,platform:a,elements:{reference:t,floating:e}});c=null!=h?h:c,m=null!=y?y:m,d={...d,[r]:{...d[r],...w}},x&&g<=50&&(g++,"object"==typeof x&&(x.placement&&(u=x.placement),x.rects&&(f=!0===x.rects?await a.getElementRects({reference:t,floating:e,strategy:o}):x.rects),({x:c,y:m}=O(f,u,s))),n=-1)}return{x:c,y:m,placement:u,strategy:o,middlewareData:d}},t.detectOverflow=E,t.flip=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,i;const{placement:o,middlewareData:r,rects:a,initialPlacement:l,platform:s,elements:m}=e,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:g,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:v=!0,...b}=f(t,e);if(null!=(n=r.arrow)&&n.alignmentOffset)return{};const A=c(o),D=p(l),T=c(l)===l,O=await(null==s.isRTL?void 0:s.isRTL(m.floating)),L=g||(T||!v?[P(l)]:function(t){const e=P(t);return[w(t),e,w(e)]}(l)),k="none"!==x;!g&&k&&L.push(...R(l,v,x,O));const C=[l,...L],S=await E(e,b),B=[];let H=(null==(i=r.flip)?void 0:i.overflows)||[];if(u&&B.push(S[A]),d){const t=y(o,a,O);B.push(S[t[0]],S[t[1]])}if(H=[...H,{placement:o,overflows:B}],!B.every((t=>t<=0))){var F,j;const t=((null==(F=r.flip)?void 0:F.index)||0)+1,e=C[t];if(e){if(!("alignment"===d&&D!==p(e))||H.every((t=>t.overflows[0]>0&&p(t.placement)===D)))return{data:{index:t,overflows:H},reset:{placement:e}}}let n=null==(j=H.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:j.placement;if(!n)switch(h){case"bestFit":{var z;const t=null==(z=H.filter((t=>{if(k){const e=p(t.placement);return e===D||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:z[0];t&&(n=t);break}case"initialPlacement":n=l}if(o!==n)return{reset:{placement:n}}}return{}}}},t.hide=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:i="referenceHidden",...o}=f(t,e);switch(i){case"referenceHidden":{const t=L(await E(e,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:k(t)}}}case"escaped":{const t=L(await E(e,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:k(t)}}}default:return{}}}}},t.inline=function(t){return void 0===t&&(t={}),{name:"inline",options:t,async fn(e){const{placement:n,elements:i,rects:a,platform:l,strategy:s}=e,{padding:m=2,x:u,y:d}=f(t,e),g=Array.from(await(null==l.getClientRects?void 0:l.getClientRects(i.reference))||[]),h=function(t){const e=t.slice().sort(((t,e)=>t.y-e.y)),n=[];let i=null;for(let t=0;t<e.length;t++){const o=e[t];!i||o.y-i.y>i.height/2?n.push([o]):n[n.length-1].push(o),i=o}return n.map((t=>T(C(t))))}(g),y=T(C(g)),w=D(m);const x=await l.getElementRects({reference:{getBoundingClientRect:function(){if(2===h.length&&h[0].left>h[1].right&&null!=u&&null!=d)return h.find((t=>u>t.left-w.left&&u<t.right+w.right&&d>t.top-w.top&&d<t.bottom+w.bottom))||y;if(h.length>=2){if("y"===p(n)){const t=h[0],e=h[h.length-1],i="top"===c(n),o=t.top,r=e.bottom,a=i?t.left:e.left,l=i?t.right:e.right;return{top:o,bottom:r,left:a,right:l,width:l-a,height:r-o,x:a,y:o}}const t="left"===c(n),e=r(...h.map((t=>t.right))),i=o(...h.map((t=>t.left))),a=h.filter((n=>t?n.left===i:n.right===e)),l=a[0].top,s=a[a.length-1].bottom;return{top:l,bottom:s,left:i,right:e,width:e-i,height:s-l,x:i,y:l}}return y}},floating:i.floating,strategy:s});return a.reference.x!==x.reference.x||a.reference.y!==x.reference.y||a.reference.width!==x.reference.width||a.reference.height!==x.reference.height?{reset:{rects:x}}:{}}}},t.limitShift=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:i,placement:o,rects:r,middlewareData:a}=e,{offset:l=0,mainAxis:s=!0,crossAxis:m=!0}=f(t,e),d={x:n,y:i},g=p(o),h=u(g);let y=d[h],w=d[g];const x=f(l,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(s){const t="y"===h?"height":"width",e=r.reference[h]-r.floating[t]+v.mainAxis,n=r.reference[h]+r.reference[t]-v.mainAxis;y<e?y=e:y>n&&(y=n)}if(m){var b,A;const t="y"===h?"width":"height",e=S.has(c(o)),n=r.reference[g]-r.floating[t]+(e&&(null==(b=a.offset)?void 0:b[g])||0)+(e?0:v.crossAxis),i=r.reference[g]+r.reference[t]+(e?0:(null==(A=a.offset)?void 0:A[g])||0)-(e?v.crossAxis:0);w<n?w=n:w>i&&(w=i)}return{[h]:y,[g]:w}}}},t.offset=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,i;const{x:o,y:r,placement:a,middlewareData:l}=e,s=await async function(t,e){const{placement:n,platform:i,elements:o}=t,r=await(null==i.isRTL?void 0:i.isRTL(o.floating)),a=c(n),l=m(n),s="y"===p(n),u=S.has(a)?-1:1,d=r&&s?-1:1,g=f(e,t);let{mainAxis:h,crossAxis:y,alignmentAxis:w}="number"==typeof g?{mainAxis:g,crossAxis:0,alignmentAxis:null}:{mainAxis:g.mainAxis||0,crossAxis:g.crossAxis||0,alignmentAxis:g.alignmentAxis};return l&&"number"==typeof w&&(y="end"===l?-1*w:w),s?{x:y*d,y:h*u}:{x:h*u,y:y*d}}(e,t);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(i=l.arrow)&&i.alignmentOffset?{}:{x:o+s.x,y:r+s.y,data:{...s,placement:a}}}}},t.rectToClientRect=T,t.shift=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:i,placement:o}=e,{mainAxis:r=!0,crossAxis:a=!1,limiter:l={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...m}=f(t,e),d={x:n,y:i},g=await E(e,m),h=p(c(o)),y=u(h);let w=d[y],x=d[h];if(r){const t="y"===y?"bottom":"right";w=s(w+g["y"===y?"top":"left"],w,w-g[t])}if(a){const t="y"===h?"bottom":"right";x=s(x+g["y"===h?"top":"left"],x,x-g[t])}const v=l.fn({...e,[y]:w,[h]:x});return{...v,data:{x:v.x-n,y:v.y-i,enabled:{[y]:r,[h]:a}}}}}},t.size=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,i;const{placement:a,rects:l,platform:s,elements:u}=e,{apply:d=()=>{},...g}=f(t,e),h=await E(e,g),y=c(a),w=m(a),x="y"===p(a),{width:v,height:b}=l.floating;let A,R;"top"===y||"bottom"===y?(A=y,R=w===(await(null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(R=y,A="end"===w?"top":"bottom");const P=b-h.top-h.bottom,D=v-h.left-h.right,T=o(b-h[A],P),O=o(v-h[R],D),L=!e.middlewareData.shift;let k=T,C=O;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(C=D),null!=(i=e.middlewareData.shift)&&i.enabled.y&&(k=P),L&&!w){const t=r(h.left,0),e=r(h.right,0),n=r(h.top,0),i=r(h.bottom,0);x?C=v-2*(0!==t||0!==e?t+e:r(h.left,h.right)):k=b-2*(0!==n||0!==i?n+i:r(h.top,h.bottom))}await d({...e,availableWidth:C,availableHeight:k});const S=await s.getDimensions(u.floating);return v!==S.width||b!==S.height?{reset:{rects:!0}}:{}}}}}));
