{"version": 3, "sources": ["../src/components/AvatarGroupPopover/renderAvatarGroupPopover.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\nimport { AvatarGroupProvider } from '../../contexts/AvatarGroupContext';\nimport { AvatarGroupContextValues } from '../AvatarGroup/AvatarGroup.types';\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport { PopoverTrigger } from '@fluentui/react-popover';\nimport type { AvatarGroupPopoverState, AvatarGroupPopoverSlots } from './AvatarGroupPopover.types';\n\n/**\n * Render the final JSX of AvatarGroupPopover\n */\nexport const renderAvatarGroupPopover_unstable = (\n  state: AvatarGroupPopoverState,\n  contextValues: AvatarGroupContextValues,\n) => {\n  assertSlots<AvatarGroupPopoverSlots>(state);\n\n  return (\n    <state.root>\n      <PopoverTrigger disableButtonEnhancement>\n        <state.tooltip>\n          <state.triggerButton />\n        </state.tooltip>\n      </PopoverTrigger>\n      <state.popoverSurface>\n        <AvatarGroupProvider value={contextValues.avatarGroup}>\n          <state.content />\n        </AvatarGroupProvider>\n      </state.popoverSurface>\n    </state.root>\n  );\n};\n"], "names": ["renderAvatarGroupPopover_unstable", "state", "contextValues", "assertSlots", "_jsxs", "root", "_jsx", "PopoverTrigger", "disableButtonEnhancement", "tooltip", "trigger<PERSON>utton", "popoverSurface", "AvatarGroupProvider", "value", "avatarGroup", "content"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAYaA;;;eAAAA;;;4BAXb;oCACoC;gCAGR;8BACG;AAMxB,MAAMA,oCAAoC,CAC/CC,OACAC;IAEAC,IAAAA,2BAAAA,EAAqCF;IAErC,OAAA,WAAA,GACEG,IAAAA,gBAAA,EAACH,MAAMI,IAAI,EAAA;;0BACTC,IAAAA,eAAA,EAACC,4BAAAA,EAAAA;gBAAeC,0BAAwB;0BACtC,WAAA,GAAAF,IAAAA,eAAA,EAACL,MAAMQ,OAAO,EAAA;8BACZ,WAAA,GAAAH,IAAAA,eAAA,EAACL,MAAMS,aAAa,EAAA,CAAA;;;0BAGxBJ,IAAAA,eAAA,EAACL,MAAMU,cAAc,EAAA;0BACnB,WAAA,GAAAL,IAAAA,eAAA,EAACM,uCAAAA,EAAAA;oBAAoBC,OAAOX,cAAcY,WAAW;8BACnD,WAAA,GAAAR,IAAAA,eAAA,EAACL,MAAMc,OAAO,EAAA,CAAA;;;;;AAKxB"}