{"version": 3, "file": "copy-filter.js", "sourceRoot": "", "sources": ["../src/copy-filter.ts"], "names": [], "mappings": ";;;;;;AAAA,qCAAiG;AACjG,gDAAwB;AACxB,gDAAwB;AACxB,mCAA2C;AAC3C,uCAAuD;AAIvD,MAAM,eAAe,GAAG;IACtB,uBAAuB;IACvB,eAAe;IACf,cAAc;IACd,2BAA2B;IAC3B,YAAY;IACZ,qBAAqB;CACtB,CAAC;AAEF,SAAgB,oBAAoB,CAAC,IAAa;IAC/C,IAAwD,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;IAEvF,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAA,oBAAW,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,eAAe,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,oBAAW,EAAC,IAAI,CAAC,CAAC,CAAC;QACtC,CAAC;QAED,IAAA,cAAK,EAAC,mCAAmC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC;AAfD,oDAeC;AAED,SAAgB,sBAAsB,CAAC,IAAkB;IACvD,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/D,MAAM,cAAc,GAAa,EAAE,CAAC;IAEpC,IAAI,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;QAC9D,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,oCAA0B,CAAC,EAAE,CAAC;YAC3E,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,YAAY,GAAG;oBACnB,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,QAAQ;iBACnB,CAAC;gBACF,cAAc,CAAC,IAAI,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAA,8BAAqB,EAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrF,CAAC;QACH,CAAC;IACH,CAAC;SAAM,CAAC;QACN,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACrC,CAAC;IAED,IAAA,cAAK,EAAC,uCAAuC,EAAE,cAAc,CAAC,CAAC;IAE/D,OAAO,cAAc,CAAC;AACxB,CAAC;AAtBD,wDAsBC;AAED,SAAS,sBAAsB,CAAC,MAAkD;IAChF,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE,CAAC;QACnC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;SAAM,CAAC;QACN,MAAM,cAAc,GAAG,IAAA,oBAAW,EAAC,MAAM,CAAC,CAAC;QAE3C,OAAO,SAAS,eAAe,CAAC,IAAI;YAClC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAAgB,cAAc,CAAC,IAAkB;IAC/C,MAAM,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;IAC7D,MAAM,cAAc,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACpD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,cAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAE7E,OAAO,KAAK,UAAU,MAAM,CAAC,IAAI;QAC/B,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC,mBAAmB;YAC5C,IAAI,cAAI,CAAC,EAAE,CAAC,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;gBACrC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,cAAI,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC;YACtB,IAAI,GAAG,IAAA,sBAAa,EAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAChD,IAAI,MAAM,IAAA,gBAAQ,EAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAoB,CAAC;AACvB,CAAC;AAlCD,wCAkCC"}