{"version": 3, "sources": ["../src/contexts/AvatarGroupContext.ts"], "sourcesContent": ["import { createContext, useContextSelector, ContextSelector } from '@fluentui/react-context-selector';\nimport type { Context } from '@fluentui/react-context-selector';\nimport type { AvatarGroupContextValue } from '../AvatarGroup';\n\n/**\n * AvatarGroupContext is provided by AvatarGroup and AvatarGroupPopover. It's consumed by AvatarGroupItem to determine\n * default values of some props.\n */\nexport const AvatarGroupContext: Context<AvatarGroupContextValue> = createContext<AvatarGroupContextValue | undefined>(\n  undefined,\n) as Context<AvatarGroupContextValue>;\n\nconst avatarGroupContextDefaultValue: AvatarGroupContextValue = {};\n\nexport const AvatarGroupProvider = AvatarGroupContext.Provider;\n\nexport const useAvatarGroupContext_unstable = <T>(selector: ContextSelector<AvatarGroupContextValue, T>): T =>\n  useContextSelector(AvatarGroupContext, (ctx = avatarGroupContextDefaultValue) => selector(ctx));\n"], "names": ["createContext", "useContextSelector", "AvatarGroupContext", "undefined", "avatarGroupContextDefaultValue", "AvatarGroupProvider", "Provider", "useAvatarGroupContext_unstable", "selector", "ctx"], "rangeMappings": ";;;;;;;", "mappings": "AAAA,SAASA,aAAa,EAAEC,kBAAkB,QAAyB,mCAAmC;AAItG;;;CAGC,GACD,OAAO,MAAMC,qBAAuDF,cAClEG,WACoC;AAEtC,MAAMC,iCAA0D,CAAC;AAEjE,OAAO,MAAMC,sBAAsBH,mBAAmBI,QAAQ,CAAC;AAE/D,OAAO,MAAMC,iCAAiC,CAAIC,WAChDP,mBAAmBC,oBAAoB,CAACO,MAAML,8BAA8B,GAAKI,SAASC,MAAM"}