{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["export {\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  useARIAButtonShorthand,\n  useARIAButtonProps,\n} from './button/index';\nexport {\n  useActiveDescendant,\n  ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE,\n  ActiveDescendantContextProvider,\n  useActiveDescendantContext,\n  useHasParentActiveDescendantContext,\n} from './activedescendant';\nexport type {\n  ActiveDescendantImperativeRef,\n  ActiveDescendantOptions,\n  ActiveDescendantContextValue,\n  ActiveDescendantChangeEvent,\n} from './activedescendant';\nexport type {\n  ARIAButtonSlotProps,\n  ARIAButtonProps,\n  ARIAButtonResultProps,\n  ARIAButtonType,\n  ARIAButtonElement,\n  ARIAButtonElementIntersection,\n  ARIAButtonAlteredProps,\n} from './button/index';\n\nexport {\n  AriaLiveAnnouncer,\n  renderAriaLiveAnnouncer_unstable,\n  useAriaLiveAnnouncer_unstable,\n  useAriaLiveAnnouncerContextValues_unstable,\n} from './AriaLiveAnnouncer/index';\nexport type { AriaLiveAnnouncerProps, AriaLiveAnnouncerState } from './AriaLiveAnnouncer/index';\n"], "names": ["ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE", "ActiveDescendantContextProvider", "AriaLiveAnnouncer", "renderAriaLiveAnnouncer_unstable", "useARIAButtonProps", "useARIAButtonShorthand", "useActiveDescendant", "useActiveDescendantContext", "useAriaLiveAnnouncerContextValues_unstable", "useAriaLiveAnnouncer_unstable", "useHasParentActiveDescendantContext"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAOEA,uCAAuC;eAAvCA,yDAAuC;;IACvCC,+BAA+B;eAA/BA,iDAA+B;;IAqB/BC,iBAAiB;eAAjBA,yBAAiB;;IACjBC,gCAAgC;eAAhCA,wCAAgC;;IA3BhCC,kBAAkB;eAAlBA,yBAAkB;;IAFlB,4DAA4D;IAC5DC,sBAAsB;eAAtBA,6BAAsB;;IAItBC,mBAAmB;eAAnBA,qCAAmB;;IAGnBC,0BAA0B;eAA1BA,4CAA0B;;IAuB1BC,0CAA0C;eAA1CA,kDAA0C;;IAD1CC,6BAA6B;eAA7BA,qCAA6B;;IArB7BC,mCAAmC;eAAnCA,qDAAmC;;;uBAN9B;kCAOA;wBAsBA"}