import { createContext, useContextSelector } from '@fluentui/react-context-selector';
/**
 * AvatarGroupContext is provided by AvatarGroup and AvatarGroupPopover. It's consumed by AvatarGroupItem to determine
 * default values of some props.
 */ export const AvatarGroupContext = createContext(undefined);
const avatarGroupContextDefaultValue = {};
export const AvatarGroupProvider = AvatarGroupContext.Provider;
export const useAvatarGroupContext_unstable = (selector)=>useContextSelector(AvatarGroupContext, (ctx = avatarGroupContextDefaultValue)=>selector(ctx));
