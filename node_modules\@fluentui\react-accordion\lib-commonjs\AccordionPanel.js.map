{"version": 3, "sources": ["../src/AccordionPanel.ts"], "sourcesContent": ["export type { AccordionPanelProps, AccordionPanelSlots, AccordionPanelState } from './components/AccordionPanel/index';\nexport {\n  AccordionPanel,\n  accordionPanelClassNames,\n  renderAccordionPanel_unstable,\n  useAccordionPanelStyles_unstable,\n  useAccordionPanel_unstable,\n} from './components/AccordionPanel/index';\n"], "names": ["AccordionPanel", "accordionPanelClassNames", "renderAccordionPanel_unstable", "useAccordionPanelStyles_unstable", "useAccordionPanel_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAEEA,cAAc;eAAdA,qBAAc;;IACdC,wBAAwB;eAAxBA,+BAAwB;;IACxBC,6BAA6B;eAA7BA,oCAA6B;;IAC7BC,gCAAgC;eAAhCA,uCAAgC;;IAChCC,0BAA0B;eAA1BA,iCAA0B;;;uBACrB"}