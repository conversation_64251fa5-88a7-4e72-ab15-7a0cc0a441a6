{"version": 3, "sources": ["../src/components/Accordion/Accordion.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAccordion_unstable } from './renderAccordion';\nimport { useAccordion_unstable } from './useAccordion';\nimport { useAccordionContextValues_unstable } from './useAccordionContextValues';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAccordionStyles_unstable } from './useAccordionStyles.styles';\nimport type { AccordionProps } from './Accordion.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\n/**\n * Define a styled Accordion, using the `useAccordion_unstable` and `useAccordionStyles_unstable` hooks.\n */\nexport const Accordion: ForwardRefComponent<AccordionProps> & (<TItem>(props: AccordionProps<TItem>) => JSX.Element) =\n  React.forwardRef<HTMLDivElement, AccordionProps>((props, ref) => {\n    const state = useAccordion_unstable(props, ref);\n    const contextValues = useAccordionContextValues_unstable(state);\n\n    useAccordionStyles_unstable(state);\n\n    useCustomStyleHook_unstable('useAccordionStyles_unstable')(state);\n\n    return renderAccordion_unstable(state, contextValues);\n  }) as ForwardRefComponent<AccordionProps> & (<TItem>(props: AccordionProps<TItem>) => JSX.Element);\n\nAccordion.displayName = 'Accordion';\n"], "names": ["Accordion", "React", "forwardRef", "props", "ref", "state", "useAccordion_unstable", "contextValues", "useAccordionContextValues_unstable", "useAccordionStyles_unstable", "useCustomStyleHook_unstable", "renderAccordion_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAYaA;;;eAAAA;;;;iEAZU;iCACkB;8BACH;2CACa;qCACP;0CACA;AAOrC,MAAMA,YAAAA,WAAAA,GACXC,OAAMC,UAAU,CAAiC,CAACC,OAAOC;IACvD,MAAMC,QAAQC,IAAAA,mCAAAA,EAAsBH,OAAOC;IAC3C,MAAMG,gBAAgBC,IAAAA,6DAAAA,EAAmCH;IAEzDI,IAAAA,qDAAAA,EAA4BJ;IAE5BK,IAAAA,gDAAAA,EAA4B,+BAA+BL;IAE3D,OAAOM,IAAAA,yCAAAA,EAAyBN,OAAOE;AACzC;AAEFP,UAAUY,WAAW,GAAG"}