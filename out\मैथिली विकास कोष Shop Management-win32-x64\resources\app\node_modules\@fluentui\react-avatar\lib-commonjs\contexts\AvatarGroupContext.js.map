{"version": 3, "sources": ["../src/contexts/AvatarGroupContext.ts"], "sourcesContent": ["import { createContext, useContextSelector, ContextSelector } from '@fluentui/react-context-selector';\nimport type { Context } from '@fluentui/react-context-selector';\nimport type { AvatarGroupContextValue } from '../AvatarGroup';\n\n/**\n * AvatarGroupContext is provided by AvatarGroup and AvatarGroupPopover. It's consumed by AvatarGroupItem to determine\n * default values of some props.\n */\nexport const AvatarGroupContext: Context<AvatarGroupContextValue> = createContext<AvatarGroupContextValue | undefined>(\n  undefined,\n) as Context<AvatarGroupContextValue>;\n\nconst avatarGroupContextDefaultValue: AvatarGroupContextValue = {};\n\nexport const AvatarGroupProvider = AvatarGroupContext.Provider;\n\nexport const useAvatarGroupContext_unstable = <T>(selector: ContextSelector<AvatarGroupContextValue, T>): T =>\n  useContextSelector(AvatarGroupContext, (ctx = avatarGroupContextDefaultValue) => selector(ctx));\n"], "names": ["AvatarGroupContext", "AvatarGroupProvider", "useAvatarGroupContext_unstable", "createContext", "undefined", "avatarGroupContextDefaultValue", "Provider", "selector", "useContextSelector", "ctx"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAQaA,kBAAAA;eAAAA;;IAMAC,mBAAAA;eAAAA;;IAEAC,8BAAAA;eAAAA;;;sCAhBsD;AAQ5D,MAAMF,qBAAuDG,IAAAA,mCAAAA,EAClEC;AAGF,MAAMC,iCAA0D,CAAC;AAE1D,MAAMJ,sBAAsBD,mBAAmBM,QAAQ;AAEvD,MAAMJ,iCAAiC,CAAIK,WAChDC,IAAAA,wCAAAA,EAAmBR,oBAAoB,CAACS,MAAMJ,8BAA8B,GAAKE,SAASE"}