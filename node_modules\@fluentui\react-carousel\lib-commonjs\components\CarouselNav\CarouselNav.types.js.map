{"version": 3, "sources": ["../src/components/CarouselNav/CarouselNav.types.ts"], "sourcesContent": ["import type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\nimport * as React from 'react';\n\nexport type CarouselNavSlots = {\n  /**\n   * The element wrapping the carousel pagination. By default, this is a div.\n   */\n  root: NonNullable<Slot<'div'>>;\n};\n\nexport type NavButtonRenderFunction = (index: number) => React.ReactNode;\n\nexport type CarouselNavState = ComponentState<CarouselNavSlots> & {\n  /**\n   * Enables an alternate brand style when set to 'brand'\n   */\n  appearance?: 'brand';\n\n  /**\n   * The function that will render nav items based on total slides and their index.\n   */\n  renderNavButton: NavButtonRenderFunction;\n\n  /**\n   * The total number of slides available.\n   * Users may override if using the component without a Carousel wrapper or implementing custom functionality.\n   */\n  totalSlides: number;\n};\n\nexport type CarouselNavProps = Omit<ComponentProps<Partial<CarouselNavSlots>>, 'children'> & {\n  children: NavButtonRenderFunction;\n} & Partial<Pick<CarouselNavState, 'appearance' | 'totalSlides'>>;\n\nexport type CarouselNavContextValue = Pick<CarouselNavState, 'appearance'>;\n"], "names": [], "rangeMappings": ";;;;;", "mappings": ";;;;;iEACuB"}