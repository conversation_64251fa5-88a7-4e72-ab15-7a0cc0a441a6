{"version": 3, "sources": ["../src/components/Carousel/Carousel.types.ts"], "sourcesContent": ["import type { ComponentProps, ComponentState, EventHandler, Slot } from '@fluentui/react-utilities';\nimport type { CarouselContextValue, CarouselIndexChangeData } from '../CarouselContext.types';\n\nexport type CarouselSlots = {\n  root: Slot<'div'>;\n};\n\n/**\n * Children function replacement, passes through updated context index and carousel information for localization\n */\nexport type CarouselAnnouncerFunction = (index: number, totalSlides: number, slideGroupList: number[][]) => string;\n\n/**\n * List of integrated motion types\n */\nexport type CarouselMotion = 'slide' | { kind: 'slide'; duration?: number } | 'fade';\n\n/**\n * Carousel Props\n */\nexport type CarouselProps = ComponentProps<CarouselSlots> & {\n  /**\n   * The initial page to display in uncontrolled mode.\n   */\n  defaultActiveIndex?: number;\n\n  /**\n   * The alignment of the carousel.\n   */\n  align?: 'center' | 'start' | 'end';\n\n  /**\n   * The value of the currently active page.\n   */\n  activeIndex?: number;\n\n  /**\n   * Callback to notify a page change.\n   */\n  onActiveIndexChange?: EventHandler<CarouselIndexChangeData>;\n\n  /**\n   * Circular enables the carousel to loop back around on navigation past trailing index.\n   */\n  circular?: boolean;\n\n  /**\n   * Controls the number of carousel cards per navigation element, will default to 'auto'\n   * Recommended to set to '1' when using full page carousel cards.\n   */\n  groupSize?: number | 'auto';\n\n  /**\n   * Enables drag to scroll on carousel items.\n   * Defaults to: False\n   */\n  draggable?: boolean;\n\n  /**\n   * Adds whitespace to start/end so that 'align' prop is always respected for current index\n   * Defaults to: False\n   */\n  whitespace?: boolean;\n\n  /**\n   * Sets motion type as either 'slide' or 'fade'\n   * Defaults: 'slide'\n   *\n   * Users can also pass 'slide' & duration via CarouselMotion object to control carousel speed.\n   * Drag interactions are not affected because duration is then determined by the drag force.\n   *\n   * Note: Duration is not in milliseconds because Carousel uses an\n   * attraction physics simulation when scrolling instead of easings.\n   * Only values between 20-60 are recommended, 25 is the default.\n   */\n  motion?: CarouselMotion;\n\n  /**\n   * Localizes the string used to announce carousel page changes\n   * Defaults to: undefined\n   */\n  announcement?: CarouselAnnouncerFunction;\n\n  /**\n   * Choose a delay between autoplay transitions in milliseconds.\n   * Only active if Autoplay is enabled via CarouselAutoplayButton\n   *\n   * Defaults: 4000\n   */\n  autoplayInterval?: number;\n};\n\n/**\n * State used in rendering Carousel\n */\nexport type CarouselState = ComponentState<CarouselSlots> & CarouselContextValue;\n\nexport interface CarouselVisibilityEventDetail {\n  isVisible: boolean;\n}\n\nexport type CarouselVisibilityChangeEvent = CustomEvent<CarouselVisibilityEventDetail>;\n\n/**\n * @internal\n */\nexport interface CarouselUpdateData {\n  /**\n   * The current carousel index, a change in index will not trigger the callback (use context index instead).\n   */\n  activeIndex: number;\n  /**\n   * The total number of slides to be navigated, accounts for grouping.\n   */\n  navItemsCount: number;\n  /**\n   * A breakdown of the card indexes contained within each slide index.\n   */\n  groupIndexList: number[][];\n  /**\n   * An array of the card DOM elements after render\n   */\n  slideNodes: HTMLElement[];\n  /**\n   * Whether the carousel has enough cards present to enable looping without issues.\n   */\n  canLoop?: boolean;\n}\n"], "names": [], "rangeMappings": ";;", "mappings": "AAuGA;;CAEC"}