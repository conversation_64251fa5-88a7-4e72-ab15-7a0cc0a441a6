{"version": 3, "sources": ["../src/components/CarouselNavImageButton/CarouselNavImageButton.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\nimport { useCarouselNavImageButton_unstable } from './useCarouselNavImageButton';\nimport { renderCarouselNavImageButton_unstable } from './renderCarouselNavImageButton';\nimport { useCarouselNavImageButtonStyles_unstable } from './useCarouselNavImageButtonStyles.styles';\nimport type { CarouselNavImageButtonProps } from './CarouselNavImageButton.types';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\n\n/**\n * A variant child element of CarouselNav, a singular image button that displays a\n * preview of card content and will set the carousels active value on click.\n */\nexport const CarouselNavImageButton: ForwardRefComponent<CarouselNavImageButtonProps> = React.forwardRef(\n  (props, ref) => {\n    const state = useCarouselNavImageButton_unstable(props, ref);\n\n    useCarouselNavImageButtonStyles_unstable(state);\n    useCustomStyleHook_unstable('useCarouselNavImageButtonStyles_unstable')(state);\n\n    return renderCarouselNavImageButton_unstable(state);\n  },\n);\n\nCarouselNavImageButton.displayName = 'CarouselNavImageButton';\n"], "names": ["CarouselNavImageButton", "React", "forwardRef", "props", "ref", "state", "useCarouselNavImageButton_unstable", "useCarouselNavImageButtonStyles_unstable", "useCustomStyleHook_unstable", "renderCarouselNavImageButton_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAYaA;;;eAAAA;;;;iEAZU;2CAE4B;8CACG;uDACG;qCAEb;AAMrC,MAAMA,yBAAAA,WAAAA,GAA2EC,OAAMC,UAAU,CACtG,CAACC,OAAOC;IACN,MAAMC,QAAQC,IAAAA,6DAAAA,EAAmCH,OAAOC;IAExDG,IAAAA,+EAAAA,EAAyCF;IACzCG,IAAAA,gDAAAA,EAA4B,4CAA4CH;IAExE,OAAOI,IAAAA,mEAAAA,EAAsCJ;AAC/C;AAGFL,uBAAuBU,WAAW,GAAG"}