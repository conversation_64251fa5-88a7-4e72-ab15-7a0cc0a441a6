{"name": "maithilishop", "productName": "मैथिली विकास कोष Shop Management", "version": "1.0.0", "description": "Offline Mithila Handcraft Business Management Application", "main": "dist/main/main.js", "homepage": "./", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["electron", "typescript", "mithila", "handcraft", "business-management", "offline-first", "fluent-ui"], "scripts": {"build": "npm run build:main && npm run build:renderer", "build:main": "tsc", "build:renderer": "webpack --mode production", "build:watch": "tsc --watch", "start": "npm run build && electron .", "dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:main": "npm run build:main && electron . --dev", "dev:renderer": "webpack serve --mode development", "clean": "<PERSON><PERSON><PERSON> dist", "rebuild": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "jest --config jest.e2e.config.js", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json,css,scss}\"", "type-check": "tsc --noEmit", "package": "npm run build && electron-forge package", "make": "npm run build && electron-forge make", "publish": "npm run build && electron-forge publish", "prepare": "husky"}, "dependencies": {"@fluentui/react-components": "^9.54.13", "@fluentui/react-icons": "^2.0.258", "@reduxjs/toolkit": "^2.3.0", "better-sqlite3": "^11.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.1.2"}, "devDependencies": {"@electron-forge/cli": "^7.5.0", "@electron-forge/maker-deb": "^7.5.0", "@electron-forge/maker-rpm": "^7.5.0", "@electron-forge/maker-squirrel": "^7.5.0", "@electron-forge/maker-zip": "^7.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/better-sqlite3": "^7.6.11", "@types/jest": "^29.5.14", "@types/node": "^24.0.8", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-redux": "^7.1.34", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "concurrently": "^9.0.1", "css-loader": "^7.1.2", "electron": "^37.1.0", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "spectron": "^19.0.0", "style-loader": "^4.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "typescript": "^5.8.3", "webpack": "^5.95.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"], "src/**/*.{json,css,scss,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "config": {"forge": {"packagerConfig": {"name": "मैथिली विकास कोष Shop Management", "executableName": "maithili-shop", "icon": "./assets/icons/app-icon", "ignore": ["^/src", "^/tests", "^/docs", "^/\\.vscode", "^/\\.git", "^/\\.husky", "^/webpack\\.config\\.js", "^/tsconfig.*\\.json", "^/\\.eslintrc\\.js", "^/\\.prettierrc\\.js", "^/\\.prettierignore", "^/\\.gitignore"]}, "makers": [{"name": "@electron-forge/maker-squirrel", "config": {"name": "maithili_shop"}}, {"name": "@electron-forge/maker-zip", "platforms": ["darwin"]}, {"name": "@electron-forge/maker-deb", "config": {}}, {"name": "@electron-forge/maker-rpm", "config": {}}]}}}