{"name": "maithilishop", "productName": "मैथिली विकास कोष Shop Management", "version": "1.0.0", "description": "Offline Mithila Handcraft Business Management Application", "main": "dist/main/main.js", "homepage": "./", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["electron", "typescript", "mithila", "handcraft", "business-management", "offline-first", "fluent-ui"], "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start": "npm run build && electron .", "dev": "npm run build:watch & electron . --dev", "clean": "<PERSON><PERSON><PERSON> dist", "rebuild": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json,css,scss}\"", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/node": "^24.0.8", "electron": "^37.1.0", "typescript": "^5.8.3"}}