{"version": 3, "sources": ["../src/contexts/AvatarContext.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { AvatarShape, AvatarSize } from '../components/Avatar/Avatar.types';\n\nconst avatarContext = React.createContext<AvatarContextValue | undefined>(undefined);\n\n/**\n * @internal\n */\nexport interface AvatarContextValue {\n  shape?: AvatarShape;\n  size?: AvatarSize;\n}\n\nconst avatarContextDefaultValue: AvatarContextValue = {};\n\n/**\n * @internal\n */\nexport const AvatarContextProvider = avatarContext.Provider;\n\n/**\n * @internal\n */\nexport const useAvatarContext = () => React.useContext(avatarContext) ?? avatarContextDefaultValue;\n"], "names": ["AvatarContextProvider", "useAvatarContext", "avatarContext", "React", "createContext", "undefined", "avatarContextDefaultValue", "Provider", "useContext"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAkBaA,qBAAAA;eAAAA;;IAKAC,gBAAAA;eAAAA;;;;iEAvBU;AAGvB,MAAMC,8BAAgBC,OAAMC,aAAa,CAAiCC;AAU1E,MAAMC,4BAAgD,CAAC;AAKhD,MAAMN,wBAAwBE,cAAcK,QAAQ;AAKpD,MAAMN,mBAAmB;QAAME;WAAAA,CAAAA,oBAAAA,OAAMK,UAAU,CAACN,cAAAA,MAAAA,QAAjBC,sBAAAA,KAAAA,IAAAA,oBAAmCG;AAAwB"}