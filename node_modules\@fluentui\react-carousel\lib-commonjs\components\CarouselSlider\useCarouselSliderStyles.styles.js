"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    carouselSliderClassNames: function() {
        return carouselSliderClassNames;
    },
    useCarouselSliderStyles_unstable: function() {
        return useCarouselSliderStyles_unstable;
    }
});
const _react = require("@griffel/react");
const carouselSliderClassNames = {
    root: 'fui-CarouselSlider'
};
/**
 * Styles for the root slot
 */ const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    root: {
        mc9l5x: "f22iagw",
        Eiaeu8: "f1115ve7"
    }
}, {
    d: [
        ".f22iagw{display:flex;}",
        ".f1115ve7{overflow-anchor:none;}"
    ]
});
const useCarouselSliderStyles_unstable = (state)=>{
    'use no memo';
    const styles = useStyles();
    state.root.className = (0, _react.mergeClasses)(carouselSliderClassNames.root, styles.root, state.root.className);
    return state;
};
