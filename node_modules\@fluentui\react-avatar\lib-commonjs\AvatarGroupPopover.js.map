{"version": 3, "sources": ["../src/AvatarGroupPopover.ts"], "sourcesContent": ["export type {\n  AvatarGroupPopoverProps,\n  AvatarGroupPopoverSlots,\n  AvatarGroupPopoverState,\n} from './components/AvatarGroupPopover/index';\nexport {\n  AvatarGroupPopover,\n  avatarGroupPopoverClassNames,\n  renderAvatarGroupPopover_unstable,\n  useAvatarGroupPopoverContextValues_unstable,\n  useAvatarGroupPopoverStyles_unstable,\n  useAvatarGroupPopover_unstable,\n} from './components/AvatarGroupPopover/index';\n"], "names": ["AvatarGroupPopover", "avatarGroupPopoverClassNames", "renderAvatarGroupPopover_unstable", "useAvatarGroupPopoverContextValues_unstable", "useAvatarGroupPopoverStyles_unstable", "useAvatarGroupPopover_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAMEA,kBAAkB;eAAlBA,yBAAkB;;IAClBC,4BAA4B;eAA5BA,mCAA4B;;IAC5BC,iCAAiC;eAAjCA,wCAAiC;;IACjCC,2CAA2C;eAA3CA,kDAA2C;;IAC3CC,oCAAoC;eAApCA,2CAAoC;;IACpCC,8BAA8B;eAA9BA,qCAA8B;;;uBACzB"}