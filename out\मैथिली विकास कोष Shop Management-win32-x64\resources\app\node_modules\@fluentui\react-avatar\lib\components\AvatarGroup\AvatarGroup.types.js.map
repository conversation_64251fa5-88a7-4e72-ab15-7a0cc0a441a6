{"version": 3, "sources": ["../src/components/AvatarGroup/AvatarGroup.types.ts"], "sourcesContent": ["import type { AvatarSize } from '../Avatar/Avatar.types';\nimport type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\n\nexport type AvatarGroupSlots = {\n  root: NonNullable<Slot<'div'>>;\n};\n\n/**\n * AvatarGroup Props\n */\nexport type AvatarGroupProps = ComponentProps<AvatarGroupSlots> & {\n  /**\n   * Layout the AvatarGroupItems should be displayed as.\n   * @default spread\n   */\n  layout?: 'spread' | 'stack' | 'pie';\n\n  /**\n   * Size of the AvatarGroupItems.\n   * @default 32\n   */\n  size?: AvatarSize;\n};\n\n/**\n * State used in rendering AvatarGroup\n */\nexport type AvatarGroupState = ComponentState<AvatarGroupSlots> & Required<Pick<AvatarGroupProps, 'layout' | 'size'>>;\n\nexport type AvatarGroupContextValue = Pick<AvatarGroupProps, 'size' | 'layout'> & {\n  isOverflow?: boolean;\n};\n\nexport type AvatarGroupContextValues = {\n  avatarGroup: AvatarGroupContextValue;\n};\n"], "names": [], "rangeMappings": "", "mappings": "AAiCA,WAEE"}