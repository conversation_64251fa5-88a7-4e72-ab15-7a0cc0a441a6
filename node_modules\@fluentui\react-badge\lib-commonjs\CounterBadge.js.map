{"version": 3, "sources": ["../src/CounterBadge.ts"], "sourcesContent": ["export type { CounterBadgeProps, CounterBadgeState } from './components/CounterBadge/index';\nexport {\n  CounterBadge,\n  counterBadgeClassNames,\n  useCounterBadgeStyles_unstable,\n  useCounterBadge_unstable,\n} from './components/CounterBadge/index';\n"], "names": ["CounterBadge", "counterBadgeClassNames", "useCounterBadgeStyles_unstable", "useCounterBadge_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAEEA,YAAY;eAAZA,mBAAY;;IACZC,sBAAsB;eAAtBA,6BAAsB;;IACtBC,8BAA8B;eAA9BA,qCAA8B;;IAC9BC,wBAAwB;eAAxBA,+BAAwB;;;uBACnB"}