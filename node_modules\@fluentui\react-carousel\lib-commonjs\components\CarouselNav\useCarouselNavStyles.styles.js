"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    carouselNavClassNames: function() {
        return carouselNavClassNames;
    },
    useCarouselNavStyles_unstable: function() {
        return useCarouselNavStyles_unstable;
    }
});
const _react = require("@griffel/react");
const carouselNavClassNames = {
    root: 'fui-CarouselNav'
};
/**
 * Styles for the root slot
 */ const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    root: {
        mc9l5x: "f22iagw",
        Beiy3e4: "f1063pyq",
        Bt984gj: "f122n59",
        Brf1p80: "f4d9j23",
        Bkecrkj: "fc5wo7j",
        Bfpq7zp: 0,
        g9k6zt: 0,
        Bn4voq9: 0,
        giviqs: "f89hs3r",
        Bw81rd7: 0,
        kdpuga: 0,
        dm238s: 0,
        B6xbmo0: 0,
        B3whbx2: "f2krc9w",
        B8q5s1w: "f8hki3x",
        Bci5o5g: [
            "f1d2448m",
            "ffh67wi"
        ],
        n8qw10: "f1bjia2o",
        Bdrgwmp: [
            "ffh67wi",
            "f1d2448m"
        ],
        Beyfa6y: 0,
        Bbmb7ep: 0,
        Btl43ni: 0,
        B7oj6ja: 0,
        Dimara: "f1kijzfu",
        jrapky: 0,
        Frg6f3: 0,
        t21cq0: 0,
        B6of3ja: 0,
        B74szlk: "fkb7v5e",
        De3pzq: "fkfdr9r"
    }
}, {
    d: [
        ".f22iagw{display:flex;}",
        ".f1063pyq{flex-direction:row;}",
        ".f122n59{align-items:center;}",
        ".f4d9j23{justify-content:center;}",
        ".fc5wo7j{pointer-events:all;}",
        [
            ".f89hs3r[data-fui-focus-visible]{outline:var(--strokeWidthThick) solid var(--colorStrokeFocus2);}",
            {
                p: -1
            }
        ],
        [
            ".f2krc9w[data-fui-focus-visible]{border-radius:var(--borderRadiusMedium);}",
            {
                p: -1
            }
        ],
        ".f8hki3x[data-fui-focus-visible]{border-top-color:transparent;}",
        ".f1d2448m[data-fui-focus-visible]{border-right-color:transparent;}",
        ".ffh67wi[data-fui-focus-visible]{border-left-color:transparent;}",
        ".f1bjia2o[data-fui-focus-visible]{border-bottom-color:transparent;}",
        [
            ".f1kijzfu{border-radius:var(--borderRadiusXLarge);}",
            {
                p: -1
            }
        ],
        [
            ".fkb7v5e{margin:auto var(--spacingHorizontalS);}",
            {
                p: -1
            }
        ],
        ".fkfdr9r{background-color:var(--colorNeutralBackgroundAlpha);}"
    ]
});
const useCarouselNavStyles_unstable = (state)=>{
    'use no memo';
    const styles = useStyles();
    state.root.className = (0, _react.mergeClasses)(carouselNavClassNames.root, styles.root, state.root.className);
    return state;
};
