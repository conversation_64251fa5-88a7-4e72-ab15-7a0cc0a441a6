{"version": 3, "sources": ["../src/components/CarouselViewport/useCarouselViewport.ts"], "sourcesContent": ["import * as React from 'react';\nimport { getIntrinsicElementProps, mergeCallbacks, slot, useMergedRefs } from '@fluentui/react-utilities';\nimport type { CarouselViewportProps, CarouselViewportState } from './CarouselViewport.types';\nimport { useCarouselContext_unstable as useCarouselContext } from '../CarouselContext';\n\n/**\n * Create the state required to render CarouselViewport.\n *\n * The returned state can be modified with hooks such as useCarouselViewportStyles_unstable,\n * before being passed to renderCarouselViewport_unstable.\n *\n * @param props - props from this instance of CarouselViewport\n * @param ref - reference to root HTMLDivElement of CarouselViewport\n */\nexport const useCarouselViewport_unstable = (\n  props: CarouselViewportProps,\n  ref: React.Ref<HTMLDivElement>,\n): CarouselViewportState => {\n  const hasFocus = React.useRef(false);\n  const hasMouse = React.useRef(false);\n  const viewportRef = useCarouselContext(ctx => ctx.viewportRef);\n  const enableAutoplay = useCarouselContext(ctx => ctx.enableAutoplay);\n\n  const handleFocusCapture = React.useCallback(() => {\n    hasFocus.current = true;\n    // Will pause autoplay when focus is captured within viewport (if autoplay is initialized)\n    enableAutoplay(false, true);\n  }, [enableAutoplay]);\n\n  const handleBlurCapture = React.useCallback(\n    (e: React.FocusEvent) => {\n      // Will enable autoplay (if initialized) when focus exits viewport\n      if (!e.currentTarget.contains(e.relatedTarget)) {\n        hasFocus.current = false;\n        if (!hasMouse.current) {\n          enableAutoplay(true, true);\n        }\n      }\n    },\n    [enableAutoplay],\n  );\n\n  const handleMouseEnter = React.useCallback(() => {\n    hasMouse.current = true;\n    enableAutoplay(false, true);\n  }, [enableAutoplay]);\n  const handleMouseLeave = React.useCallback(() => {\n    hasMouse.current = false;\n    if (!hasFocus.current) {\n      enableAutoplay(true, true);\n    }\n  }, [enableAutoplay]);\n\n  const onFocusCapture = mergeCallbacks(props.onFocusCapture, handleFocusCapture);\n  const onBlurCapture = mergeCallbacks(props.onBlurCapture, handleBlurCapture);\n  const onMouseEnter = mergeCallbacks(props.onMouseEnter, handleMouseEnter);\n  const onMouseLeave = mergeCallbacks(props.onMouseLeave, handleMouseLeave);\n\n  return {\n    components: {\n      root: 'div',\n    },\n    root: slot.always(\n      getIntrinsicElementProps('div', {\n        ref: useMergedRefs(ref, viewportRef),\n        role: 'presentation',\n        // Draggable ensures dragging is supported (even if not enabled)\n        draggable: true,\n        ...props,\n        onFocusCapture,\n        onBlurCapture,\n        onMouseEnter,\n        onMouseLeave,\n      }),\n      { elementType: 'div' },\n    ),\n  };\n};\n"], "names": ["useCarouselViewport_unstable", "props", "ref", "hasFocus", "React", "useRef", "hasMouse", "viewportRef", "useCarouselContext", "ctx", "enableAutoplay", "handleFocusCapture", "useCallback", "current", "handleBlurCapture", "e", "currentTarget", "contains", "relatedTarget", "handleMouseEnter", "handleMouseLeave", "onFocusCapture", "mergeCallbacks", "onBlurCapture", "onMouseEnter", "onMouseLeave", "components", "root", "slot", "always", "getIntrinsicElementProps", "useMergedRefs", "role", "draggable", "elementType"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAcaA;;;eAAAA;;;;iEAdU;gCACuD;iCAEZ;AAW3D,MAAMA,+BAA+B,CAC1CC,OACAC;IAEA,MAAMC,WAAWC,OAAMC,MAAM,CAAC;IAC9B,MAAMC,WAAWF,OAAMC,MAAM,CAAC;IAC9B,MAAME,cAAcC,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIF,WAAW;IAC7D,MAAMG,iBAAiBF,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIC,cAAc;IAEnE,MAAMC,qBAAqBP,OAAMQ,WAAW,CAAC;QAC3CT,SAASU,OAAO,GAAG;QACnB,0FAA0F;QAC1FH,eAAe,OAAO;IACxB,GAAG;QAACA;KAAe;IAEnB,MAAMI,oBAAoBV,OAAMQ,WAAW,CACzC,CAACG;QACC,kEAAkE;QAClE,IAAI,CAACA,EAAEC,aAAa,CAACC,QAAQ,CAACF,EAAEG,aAAa,GAAG;YAC9Cf,SAASU,OAAO,GAAG;YACnB,IAAI,CAACP,SAASO,OAAO,EAAE;gBACrBH,eAAe,MAAM;YACvB;QACF;IACF,GACA;QAACA;KAAe;IAGlB,MAAMS,mBAAmBf,OAAMQ,WAAW,CAAC;QACzCN,SAASO,OAAO,GAAG;QACnBH,eAAe,OAAO;IACxB,GAAG;QAACA;KAAe;IACnB,MAAMU,mBAAmBhB,OAAMQ,WAAW,CAAC;QACzCN,SAASO,OAAO,GAAG;QACnB,IAAI,CAACV,SAASU,OAAO,EAAE;YACrBH,eAAe,MAAM;QACvB;IACF,GAAG;QAACA;KAAe;IAEnB,MAAMW,iBAAiBC,IAAAA,8BAAAA,EAAerB,MAAMoB,cAAc,EAAEV;IAC5D,MAAMY,gBAAgBD,IAAAA,8BAAAA,EAAerB,MAAMsB,aAAa,EAAET;IAC1D,MAAMU,eAAeF,IAAAA,8BAAAA,EAAerB,MAAMuB,YAAY,EAAEL;IACxD,MAAMM,eAAeH,IAAAA,8BAAAA,EAAerB,MAAMwB,YAAY,EAAEL;IAExD,OAAO;QACLM,YAAY;YACVC,MAAM;QACR;QACAA,MAAMC,oBAAAA,CAAKC,MAAM,CACfC,IAAAA,wCAAAA,EAAyB,OAAO;YAC9B5B,KAAK6B,IAAAA,6BAAAA,EAAc7B,KAAKK;YACxByB,MAAM;YACN,gEAAgE;YAChEC,WAAW;YACX,GAAGhC,KAAK;YACRoB;YACAE;YACAC;YACAC;QACF,IACA;YAAES,aAAa;QAAM;IAEzB;AACF"}