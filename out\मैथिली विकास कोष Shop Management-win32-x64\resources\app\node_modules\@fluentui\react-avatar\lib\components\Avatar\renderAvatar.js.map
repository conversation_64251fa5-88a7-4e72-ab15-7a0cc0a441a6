{"version": 3, "sources": ["../src/components/Avatar/renderAvatar.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { AvatarSlots, AvatarState } from './Avatar.types';\n\nexport const renderAvatar_unstable = (state: AvatarState) => {\n  assertSlots<AvatarSlots>(state);\n\n  return (\n    <state.root>\n      {state.initials && <state.initials />}\n      {state.icon && <state.icon />}\n      {state.image && <state.image />}\n      {state.badge && <state.badge />}\n      {state.activeAriaLabelElement}\n    </state.root>\n  );\n};\n"], "names": ["assertSlots", "renderAvatar_unstable", "state", "root", "initials", "icon", "image", "badge", "activeAriaLabelElement"], "rangeMappings": ";;;;;;;;;;;;;", "mappings": "AAAA,0BAA0B,GAC1B,iDAAiD;AAEjD,SAASA,WAAW,QAAQ,4BAA4B;AAGxD,OAAO,MAAMC,wBAAwB,CAACC;IACpCF,YAAyBE;IAEzB,qBACE,MAACA,MAAMC,IAAI;;YACRD,MAAME,QAAQ,kBAAI,KAACF,MAAME,QAAQ;YACjCF,MAAMG,IAAI,kBAAI,KAACH,MAAMG,IAAI;YACzBH,MAAMI,KAAK,kBAAI,KAACJ,MAAMI,KAAK;YAC3BJ,MAAMK,KAAK,kBAAI,KAACL,MAAMK,KAAK;YAC3BL,MAAMM,sBAAsB;;;AAGnC,EAAE"}