"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    // App info
    getAppVersion: () => electron_1.ipcRenderer.invoke('app:getVersion'),
    // Window controls
    minimizeWindow: () => electron_1.ipcRenderer.invoke('window:minimize'),
    maximizeWindow: () => electron_1.ipcRenderer.invoke('window:maximize'),
    closeWindow: () => electron_1.ipcRenderer.invoke('window:close'),
    // Database operations (to be implemented)
    database: {
        // User operations
        getUsers: () => electron_1.ipcRenderer.invoke('db:getUsers'),
        createUser: (userData) => electron_1.ipcRenderer.invoke('db:createUser', userData),
        // Product operations
        getProducts: () => electron_1.ipcRenderer.invoke('db:getProducts'),
        createProduct: (productData) => electron_1.ipcRenderer.invoke('db:createProduct', productData),
        // Customer operations
        getCustomers: () => electron_1.ipcRenderer.invoke('db:getCustomers'),
        createCustomer: (customerData) => electron_1.ipcRenderer.invoke('db:createCustomer', customerData),
        // Order operations
        getOrders: () => electron_1.ipcRenderer.invoke('db:getOrders'),
        createOrder: (orderData) => electron_1.ipcRenderer.invoke('db:createOrder', orderData),
    },
    // File operations
    files: {
        selectImage: () => electron_1.ipcRenderer.invoke('files:selectImage'),
        saveImage: (imageData, filename) => electron_1.ipcRenderer.invoke('files:saveImage', imageData, filename),
    },
    // Notifications
    notifications: {
        show: (title, body) => electron_1.ipcRenderer.invoke('notifications:show', title, body),
    },
    // Event listeners
    on: (channel, callback) => {
        const validChannels = [
            'app:update-available',
            'app:update-downloaded',
            'database:error',
            'notification:clicked'
        ];
        if (validChannels.includes(channel)) {
            electron_1.ipcRenderer.on(channel, (_event, ...args) => callback(...args));
        }
    },
    // Remove event listeners
    removeAllListeners: (channel) => {
        const validChannels = [
            'app:update-available',
            'app:update-downloaded',
            'database:error',
            'notification:clicked'
        ];
        if (validChannels.includes(channel)) {
            electron_1.ipcRenderer.removeAllListeners(channel);
        }
    }
});
//# sourceMappingURL=preload.js.map