{"version": 3, "sources": ["../src/components/AvatarGroupPopover/AvatarGroupPopover.types.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { AvatarSize } from '../Avatar/Avatar.types';\nimport type { AvatarGroupProps } from '../AvatarGroup/AvatarGroup.types';\nimport type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\nimport type { PopoverProps, PopoverSurface } from '@fluentui/react-popover';\nimport type { TooltipProps } from '@fluentui/react-tooltip';\n\nexport type AvatarGroupPopoverSlots = {\n  root: NonNullable<Slot<PopoverProps>>;\n\n  /**\n   * Button that triggers the Popover.\n   */\n  triggerButton: NonNullable<Slot<'button'>>;\n\n  /**\n   * List that contains the overflowed AvatarGroupItems.\n   */\n  content: NonNullable<Slot<'ul'>>;\n\n  /**\n   * PopoverSurface that contains the content.\n   */\n  popoverSurface: NonNullable<Slot<typeof PopoverSurface>>;\n\n  /**\n   * Tooltip shown when triggerButton is hovered.\n   */\n  tooltip: NonNullable<Slot<TooltipProps>>;\n};\n\n/**\n * AvatarGroupPopover Props\n */\nexport type AvatarGroupPopoverProps = Omit<ComponentProps<Partial<AvatarGroupPopoverSlots>>, 'children'> & {\n  /**\n   * Whether the triggerButton should render an icon instead of the number of overflowed AvatarGroupItems.\n   * Note: The indicator will default to `icon` when the size is less than 24.\n   * @default count\n   */\n  indicator?: 'count' | 'icon';\n\n  /**\n   * Number of AvatarGroupItems that will be rendered.\n   *\n   * Note: AvatarGroupPopover handles counting the number of children, but when using a react fragment to wrap the\n   * children, this is not possible and therefore it has do be added manually.\n   */\n  count?: number;\n\n  children: React.ReactNode;\n};\n\n/**\n * State used in rendering AvatarGroupPopover\n */\nexport type AvatarGroupPopoverState = ComponentState<AvatarGroupPopoverSlots> &\n  Required<Pick<AvatarGroupPopoverProps, 'count' | 'indicator'>> & {\n    popoverOpen: boolean;\n    layout: AvatarGroupProps['layout'];\n    size: AvatarSize;\n  };\n"], "names": [], "rangeMappings": ";;;;;", "mappings": ";;;;;iEAAuB"}