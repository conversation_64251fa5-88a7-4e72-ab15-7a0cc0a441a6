{"name": "@fluentui/react-alert", "version": "9.0.0-beta.124", "description": "An alert component to display brief messages", "main": "lib-commonjs/index.js", "module": "lib/index.js", "typings": "./dist/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/microsoft/fluentui"}, "license": "MIT", "scripts": {"build": "just-scripts build", "bundle-size": "monosize measure", "clean": "just-scripts clean", "code-style": "just-scripts code-style", "just": "just-scripts", "lint": "just-scripts lint", "start": "yarn storybook", "test": "jest --passWithNoTests", "storybook": "start-storybook", "type-check": "tsc -b tsconfig.json", "generate-api": "just-scripts generate-api", "test-ssr": "test-ssr \"./stories/**/*.stories.tsx\""}, "devDependencies": {"@fluentui/eslint-plugin": "*", "@fluentui/react-conformance": "*", "@fluentui/react-conformance-griffel": "*", "@fluentui/scripts-api-extractor": "*", "@fluentui/scripts-tasks": "*"}, "dependencies": {"@fluentui/react-avatar": "^9.6.29", "@fluentui/react-button": "^9.3.83", "@fluentui/react-icons": "^2.0.239", "@fluentui/react-tabster": "^9.21.5", "@fluentui/react-theme": "^9.1.19", "@fluentui/react-utilities": "^9.18.10", "@fluentui/react-jsx-runtime": "^9.0.39", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}, "beachball": {"disallowedChangeTypes": ["major", "minor", "patch"]}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./lib-commonjs/index.js", "import": "./lib/index.js", "require": "./lib-commonjs/index.js"}, "./package.json": "./package.json"}, "files": ["*.md", "dist/*.d.ts", "lib", "lib-commonjs"]}