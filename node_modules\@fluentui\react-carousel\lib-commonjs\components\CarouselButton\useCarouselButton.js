"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useCarouselButton_unstable", {
    enumerable: true,
    get: function() {
        return useCarouselButton_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _reactbutton = require("@fluentui/react-button");
const _reacticons = require("@fluentui/react-icons");
const _reactutilities = require("@fluentui/react-utilities");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _CarouselContext = require("../CarouselContext");
const _useCarouselButtonStylesstyles = require("./useCarouselButtonStyles.styles");
const _reactsharedcontexts = require("@fluentui/react-shared-contexts");
const useCarouselButton_unstable = (props, ref)=>{
    const { navType = 'next' } = props;
    // Locally tracks the total number of slides, will only update if this changes.
    const [totalSlides, setTotalSlides] = _react.useState(0);
    const { dir } = (0, _reactsharedcontexts.useFluent_unstable)();
    const buttonRef = _react.useRef();
    const circular = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.circular);
    const [canLoop, setCanLoop] = _react.useState(circular);
    const containerRef = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.containerRef);
    const selectPageByDirection = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.selectPageByDirection);
    const subscribeForValues = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.subscribeForValues);
    const resetAutoplay = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.resetAutoplay);
    const isTrailing = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>{
        if (circular && canLoop) {
            return false;
        }
        if (navType === 'prev') {
            return ctx.activeIndex === 0;
        }
        return ctx.activeIndex === totalSlides - 1;
    });
    const handleClick = (event)=>{
        if (event.isDefaultPrevented()) {
            return;
        }
        const nextIndex = selectPageByDirection(event, navType);
        let _trailing = false;
        if (navType === 'prev') {
            _trailing = nextIndex === 0;
        } else {
            _trailing = nextIndex === totalSlides - 1;
        }
        if (!circular && _trailing && (containerRef === null || containerRef === void 0 ? void 0 : containerRef.current)) {
            // Focus non-disabled element
            const buttonRefs = containerRef.current.querySelectorAll(`.${_useCarouselButtonStylesstyles.carouselButtonClassNames.root}`);
            buttonRefs.forEach((_buttonRef)=>{
                if (_buttonRef !== buttonRef.current) {
                    _buttonRef.focus();
                }
            });
        }
        resetAutoplay();
    };
    (0, _reactutilities.useIsomorphicLayoutEffect)(()=>{
        return subscribeForValues((data)=>{
            if (data.canLoop !== undefined) {
                // Only update canLoop if it has been defined by the carousel engine
                setCanLoop(data.canLoop);
            }
            setTotalSlides(data.navItemsCount);
        });
    }, [
        subscribeForValues
    ]);
    const nextArrowIcon = dir === 'ltr' ? /*#__PURE__*/ _react.createElement(_reacticons.ChevronRightRegular, null) : /*#__PURE__*/ _react.createElement(_reacticons.ChevronLeftRegular, null);
    const prevArrowIcon = dir === 'ltr' ? /*#__PURE__*/ _react.createElement(_reacticons.ChevronLeftRegular, null) : /*#__PURE__*/ _react.createElement(_reacticons.ChevronRightRegular, null);
    return {
        navType,
        // We lean on react-button class to handle styling and icon enhancements
        ...(0, _reactbutton.useButton_unstable)({
            icon: _reactutilities.slot.optional(props.icon, {
                defaultProps: {
                    children: navType === 'next' ? nextArrowIcon : prevArrowIcon
                },
                renderByDefault: true,
                elementType: 'span'
            }),
            disabled: isTrailing,
            tabIndex: isTrailing ? -1 : 0,
            'aria-disabled': isTrailing,
            appearance: 'subtle',
            ...props,
            onClick: (0, _reactutilities.useEventCallback)((0, _reactutilities.mergeCallbacks)(handleClick, props.onClick))
        }, (0, _reactutilities.useMergedRefs)(ref, buttonRef))
    };
};
