{"version": 3, "sources": ["../src/components/CarouselNav/CarouselNavIndexContext.ts"], "sourcesContent": ["import * as React from 'react';\n\nconst carouselNavIndexContext = React.createContext<number | undefined>(undefined);\n\nexport const carouselNavIndexContextDefaultValue: number = 0;\n\nexport const useCarouselNavIndexContext = () =>\n  React.useContext(carouselNavIndexContext) ?? carouselNavIndexContextDefaultValue;\n\nexport const CarouselNavIndexContextProvider = carouselNavIndexContext.Provider;\n"], "names": ["CarouselNavIndexContextProvider", "carouselNavIndexContextDefaultValue", "useCarouselNavIndexContext", "carouselNavIndexContext", "React", "createContext", "undefined", "useContext", "Provider"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IASaA,+BAAAA;eAAAA;;IALAC,mCAAAA;eAAAA;;IAEAC,0BAAAA;eAAAA;;;;iEANU;AAEvB,MAAMC,wCAA0BC,OAAMC,aAAa,CAAqBC;AAEjE,MAAML,sCAA8C;AAEpD,MAAMC,6BAA6B;QACxCE;WAAAA,CAAAA,oBAAAA,OAAMG,UAAU,CAACJ,wBAAAA,MAAAA,QAAjBC,sBAAAA,KAAAA,IAAAA,oBAA6CH;AAAkC;AAE1E,MAAMD,kCAAkCG,wBAAwBK,QAAQ"}