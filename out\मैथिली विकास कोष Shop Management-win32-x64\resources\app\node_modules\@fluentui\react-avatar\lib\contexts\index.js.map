{"version": 3, "sources": ["../src/contexts/index.ts"], "sourcesContent": ["export { AvatarGroupContext, AvatarGroupProvider, useAvatarGroupContext_unstable } from './AvatarGroupContext';\nexport type { AvatarContextValue } from './AvatarContext';\nexport { AvatarContextProvider, useAvatarContext } from './AvatarContext';\n"], "names": ["AvatarGroupContext", "AvatarGroupProvider", "useAvatarGroupContext_unstable", "AvatarContextProvider", "useAvatarContext"], "rangeMappings": ";", "mappings": "AAAA,SAASA,kBAAkB,EAAEC,mBAAmB,EAAEC,8BAA8B,QAAQ,uBAAuB;AAE/G,SAASC,qBAAqB,EAAEC,gBAAgB,QAAQ,kBAAkB"}