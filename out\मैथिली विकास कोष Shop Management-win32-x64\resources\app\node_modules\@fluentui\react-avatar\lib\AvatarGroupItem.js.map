{"version": 3, "sources": ["../src/AvatarGroupItem.ts"], "sourcesContent": ["export type {\n  AvatarGroupItemProps,\n  AvatarGroupItemSlots,\n  AvatarGroupItemState,\n} from './components/AvatarGroupItem/index';\nexport {\n  AvatarGroupItem,\n  avatarGroupItemClassNames,\n  renderAvatarGroupItem_unstable,\n  useAvatarGroupItemStyles_unstable,\n  useAvatarGroupItem_unstable,\n  useGroupChildClassName,\n} from './components/AvatarGroupItem/index';\n"], "names": ["AvatarGroupItem", "avatarGroupItemClassNames", "renderAvatarGroupItem_unstable", "useAvatarGroupItemStyles_unstable", "useAvatarGroupItem_unstable", "useGroupChildClassName"], "rangeMappings": "", "mappings": "AAKA,SACEA,eAAe,EACfC,yBAAyB,EACzBC,8BAA8B,EAC9BC,iCAAiC,EACjCC,2BAA2B,EAC3BC,sBAAsB,QACjB,qCAAqC"}