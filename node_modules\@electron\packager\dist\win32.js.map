{"version": 3, "file": "win32.js", "sourceRoot": "", "sources": ["../src/win32.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AACxB,yDAA8C;AAE9C,yCAAiC;AACjC,qCAA2D;AAE3D,uCAAiD;AAEjD,MAAa,UAAW,SAAQ,cAAG;IACjC,IAAI,oBAAoB;QACtB,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,GAAG,IAAA,wBAAe,EAAC,IAAI,CAAC,cAAe,CAAC,MAAM,CAAC;IACxD,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC3D,CAAC;IAED,8BAA8B;QAC5B,MAAM,aAAa,GAA6B;YAC9C,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;YAC/B,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;YAC5B,gBAAgB,EAAE,IAAI,CAAC,eAAe;YACtC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;YAC3B,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa;SAC3B,CAAC;QAEF,OAAO;YACL,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;YACpC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU;YAC3D,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY;YACtC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;YACnE,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,aAAa;SACd,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACpB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAED,YAAY;QACV,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1J,CAAC;IAED,KAAK,CAAC,UAAU;QACd,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAEtD,sGAAsG;QACtG,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC1B,CAAC;QAED,IAAA,cAAK,EAAC,oCAAoC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACrE,MAAM,IAAA,iBAAO,EAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;QAEhD,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,QAAQ,GAAG,cAAc,CAAC,cAAc,EAAE,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACnF,IAAA,cAAK,EAAC,mDAAmD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACrF,IAAI,CAAC;gBACH,MAAM,IAAA,mBAAI,EAAC,QAAsC,CAAC,CAAC;YACrD,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,+DAA+D;gBAC/D,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;oBAC7B,IAAA,gBAAO,EAAC,4CAA4C,GAAG,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9E,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,CAAC;gBACZ,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM;QACV,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAChC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;IACrB,CAAC;CACF;AA1FD,gCA0FC;AAqBsB,yBAAG;AAnB1B,SAAS,cAAc,CACrB,UAAuC,EACvC,eAA8C,EAC9C,YAAoB;IAEpB,IAAI,MAAM,GAAuB,EAAE,CAAC;IAEpC,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;QACnC,MAAM,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED,8BAA8B;IAC9B,IAAI,eAAe,IAAI,eAAe,CAAC,eAAe,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC9E,MAAM,CAAC,WAAW,GAAG,eAAe,CAAC,eAAe,CAAC;IACvD,CAAC;IAED,OAAO,EAAE,GAAG,MAAM,EAAE,YAAY,EAAE,CAAC;AACrC,CAAC"}