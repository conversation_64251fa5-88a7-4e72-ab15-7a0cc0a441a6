{"version": 3, "sources": ["../src/components/CarouselSlider/useCarouselSlider.ts"], "sourcesContent": ["import { getIntrinsicElementProps, slot } from '@fluentui/react-utilities';\nimport * as React from 'react';\n\nimport type { CarouselSliderProps, CarouselSliderState } from './CarouselSlider.types';\nimport { useArrowNavigationGroup } from '@fluentui/react-tabster';\nimport { useCarouselContext_unstable as useCarouselContext } from '../CarouselContext';\n\n/**\n * Create the state required to render CarouselSlider.\n *\n * The returned state can be modified with hooks such as useCarouselSliderStyles_unstable,\n * before being passed to renderCarouselSlider_unstable.\n *\n * @param props - props from this instance of CarouselSlider\n * @param ref - reference to root HTMLDivElement of CarouselSlider\n */\nexport const useCarouselSlider_unstable = (\n  props: CarouselSliderProps,\n  ref: React.Ref<HTMLDivElement>,\n): CarouselSliderState => {\n  const { cardFocus = false } = props;\n  const circular = useCarouselContext(ctx => ctx.circular);\n  const focusableGroupAttr = useArrowNavigationGroup({\n    circular,\n    axis: 'horizontal',\n    memorizeCurrent: false,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_hasDefault: true,\n  });\n\n  const focusProps = cardFocus ? focusableGroupAttr : {};\n\n  return {\n    cardFocus,\n    components: {\n      root: 'div',\n    },\n    root: slot.always(\n      getIntrinsicElementProps('div', {\n        ref,\n        role: 'group',\n        ...props,\n        ...focusProps,\n      }),\n      { elementType: 'div' },\n    ),\n  };\n};\n"], "names": ["useCarouselSlider_unstable", "props", "ref", "cardFocus", "circular", "useCarouselContext", "ctx", "focusableGroupAttr", "useArrowNavigationGroup", "axis", "memorizeCurrent", "unstable_hasDefault", "focusProps", "components", "root", "slot", "always", "getIntrinsicElementProps", "role", "elementType"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAgBaA;;;eAAAA;;;;gCAhBkC;iEACxB;8BAGiB;iCAC0B;AAW3D,MAAMA,6BAA6B,CACxCC,OACAC;IAEA,MAAM,EAAEC,YAAY,KAAK,EAAE,GAAGF;IAC9B,MAAMG,WAAWC,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIF,QAAQ;IACvD,MAAMG,qBAAqBC,IAAAA,qCAAAA,EAAwB;QACjDJ;QACAK,MAAM;QACNC,iBAAiB;QACjB,gEAAgE;QAChEC,qBAAqB;IACvB;IAEA,MAAMC,aAAaT,YAAYI,qBAAqB,CAAC;IAErD,OAAO;QACLJ;QACAU,YAAY;YACVC,MAAM;QACR;QACAA,MAAMC,oBAAAA,CAAKC,MAAM,CACfC,IAAAA,wCAAAA,EAAyB,OAAO;YAC9Bf;YACAgB,MAAM;YACN,GAAGjB,KAAK;YACR,GAAGW,UAAU;QACf,IACA;YAAEO,aAAa;QAAM;IAEzB;AACF"}