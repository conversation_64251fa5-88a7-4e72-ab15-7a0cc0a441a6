{"version": 3, "sources": ["../src/components/Accordion/useAccordion.ts"], "sourcesContent": ["import * as React from 'react';\nimport { getIntrinsicElementProps, useControllableState, useEventCallback, slot } from '@fluentui/react-utilities';\nimport type { AccordionProps, AccordionState } from './Accordion.types';\nimport type { AccordionItemValue } from '../AccordionItem/AccordionItem.types';\nimport { useArrowNavigationGroup } from '@fluentui/react-tabster';\nimport { AccordionRequestToggleData } from '../../contexts/accordion';\n\n/**\n * Returns the props and state required to render the component\n * @param props - Accordion properties\n * @param ref - reference to root HTMLElement of Accordion\n */\nexport const useAccordion_unstable = <Value = AccordionItemValue>(\n  props: AccordionProps<Value>,\n  ref: React.Ref<HTMLElement>,\n): AccordionState<Value> => {\n  const {\n    openItems: controlledOpenItems,\n    defaultOpenItems,\n    multiple = false,\n    collapsible = false,\n    onToggle,\n    // eslint-disable-next-line @typescript-eslint/no-deprecated\n    navigation,\n  } = props;\n  const [openItems, setOpenItems] = useControllableState({\n    state: React.useMemo(() => normalizeValues(controlledOpenItems), [controlledOpenItems]),\n    defaultState: defaultOpenItems && (() => initializeUncontrolledOpenItems({ defaultOpenItems, multiple })),\n    initialState: [],\n  });\n\n  /** FIXME: deprecated will be removed after navigation prop is removed */\n  const arrowNavigationProps = useArrowNavigationGroup({\n    circular: navigation === 'circular',\n    tabbable: true,\n  });\n\n  const requestToggle = useEventCallback((data: AccordionRequestToggleData<Value>) => {\n    const nextOpenItems = updateOpenItems(data.value, openItems, multiple, collapsible);\n    onToggle?.(data.event, { value: data.value, openItems: nextOpenItems });\n    setOpenItems(nextOpenItems);\n  });\n\n  return {\n    collapsible,\n    multiple,\n    navigation,\n    openItems,\n    requestToggle,\n    components: {\n      root: 'div',\n    },\n    root: slot.always(\n      getIntrinsicElementProps('div', {\n        ...props,\n\n        ...(navigation ? arrowNavigationProps : undefined),\n        // FIXME:\n        // `ref` is wrongly assigned to be `HTMLElement` instead of `HTMLDivElement`\n        // but since it would be a breaking change to fix it, we are casting ref to it's proper type\n        ref: ref as React.Ref<HTMLDivElement>,\n      }),\n      { elementType: 'div' },\n    ),\n  };\n};\n\n/**\n * Initial value for the uncontrolled case of the list of open indexes\n */\nfunction initializeUncontrolledOpenItems<Value = AccordionItemValue>({\n  defaultOpenItems,\n  multiple,\n}: Pick<AccordionProps<Value>, 'defaultOpenItems' | 'multiple'>): Value[] {\n  if (defaultOpenItems !== undefined) {\n    if (Array.isArray(defaultOpenItems)) {\n      return multiple ? defaultOpenItems : [defaultOpenItems[0]];\n    }\n    return [defaultOpenItems];\n  }\n  return [];\n}\n\n/**\n * Updates the list of open indexes based on an index that changes\n * @param value - the index that will change\n * @param previousOpenItems - list of current open indexes\n * @param multiple - if Accordion support multiple Panels opened at the same time\n * @param collapsible - if Accordion support multiple Panels closed at the same time\n */\nfunction updateOpenItems<Value = AccordionItemValue>(\n  value: Value,\n  previousOpenItems: Value[],\n  multiple: boolean,\n  collapsible: boolean,\n) {\n  if (multiple) {\n    if (previousOpenItems.includes(value)) {\n      if (previousOpenItems.length > 1 || collapsible) {\n        return previousOpenItems.filter(i => i !== value);\n      }\n    } else {\n      return [...previousOpenItems, value].sort();\n    }\n  } else {\n    return previousOpenItems[0] === value && collapsible ? [] : [value];\n  }\n  return previousOpenItems;\n}\n\n/**\n * Normalizes Accordion index into an array of indexes\n */\nfunction normalizeValues<Value = AccordionItemValue>(index?: Value | Value[]): Value[] | undefined {\n  if (index === undefined) {\n    return undefined;\n  }\n  return Array.isArray(index) ? index : [index];\n}\n"], "names": ["useAccordion_unstable", "props", "ref", "openItems", "controlledOpenItems", "defaultOpenItems", "multiple", "collapsible", "onToggle", "navigation", "setOpenItems", "useControllableState", "state", "React", "useMemo", "normalizeValues", "defaultState", "initializeUncontrolledOpenItems", "initialState", "arrowNavigationProps", "useArrowNavigationGroup", "circular", "tabbable", "requestToggle", "useEventCallback", "data", "nextOpenItems", "updateOpenItems", "value", "event", "components", "root", "slot", "always", "getIntrinsicElementProps", "undefined", "elementType", "Array", "isArray", "previousOpenItems", "includes", "length", "filter", "i", "sort", "index"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAYaA;;;eAAAA;;;;iEAZU;gCACgE;8BAG/C;AAQjC,MAAMA,wBAAwB,CACnCC,OACAC;IAEA,MAAM,EACJC,WAAWC,mBAAmB,EAC9BC,gBAAgB,EAChBC,WAAW,KAAK,EAChBC,cAAc,KAAK,EACnBC,QAAQ,EAERC,UAAU,EACX,GAAGR;IACJ,MAAM,CAACE,WAAWO,aAAa,GAAGC,IAAAA,oCAAAA,EAAqB;QACrDC,OAAOC,OAAMC,OAAO,CAAC,IAAMC,gBAAgBX,sBAAsB;YAACA;SAAoB;QACtFY,cAAcX,oBAAqB,CAAA,IAAMY,gCAAgC;gBAAEZ;gBAAkBC;YAAS,EAAA;QACtGY,cAAc,EAAE;IAClB;IAEA,uEAAuE,GACvE,MAAMC,uBAAuBC,IAAAA,qCAAAA,EAAwB;QACnDC,UAAUZ,eAAe;QACzBa,UAAU;IACZ;IAEA,MAAMC,gBAAgBC,IAAAA,gCAAAA,EAAiB,CAACC;QACtC,MAAMC,gBAAgBC,gBAAgBF,KAAKG,KAAK,EAAEzB,WAAWG,UAAUC;QACvEC,aAAAA,QAAAA,aAAAA,KAAAA,IAAAA,KAAAA,IAAAA,SAAWiB,KAAKI,KAAK,EAAE;YAAED,OAAOH,KAAKG,KAAK;YAAEzB,WAAWuB;QAAc;QACrEhB,aAAagB;IACf;IAEA,OAAO;QACLnB;QACAD;QACAG;QACAN;QACAoB;QACAO,YAAY;YACVC,MAAM;QACR;QACAA,MAAMC,oBAAAA,CAAKC,MAAM,CACfC,IAAAA,wCAAAA,EAAyB,OAAO;YAC9B,GAAGjC,KAAK;YAER,GAAIQ,aAAaU,uBAAuBgB,SAAS;YACjD,SAAS;YACT,4EAA4E;YAC5E,4FAA4F;YAC5FjC,KAAKA;QACP,IACA;YAAEkC,aAAa;QAAM;IAEzB;AACF;AAEA;;CAEC,GACD,SAASnB,gCAA4D,EACnEZ,gBAAgB,EAChBC,QAAQ,EACqD;IAC7D,IAAID,qBAAqB8B,WAAW;QAClC,IAAIE,MAAMC,OAAO,CAACjC,mBAAmB;YACnC,OAAOC,WAAWD,mBAAmB;gBAACA,gBAAgB,CAAC,EAAE;aAAC;QAC5D;QACA,OAAO;YAACA;SAAiB;IAC3B;IACA,OAAO,EAAE;AACX;AAEA;;;;;;CAMC,GACD,SAASsB,gBACPC,KAAY,EACZW,iBAA0B,EAC1BjC,QAAiB,EACjBC,WAAoB;IAEpB,IAAID,UAAU;QACZ,IAAIiC,kBAAkBC,QAAQ,CAACZ,QAAQ;YACrC,IAAIW,kBAAkBE,MAAM,GAAG,KAAKlC,aAAa;gBAC/C,OAAOgC,kBAAkBG,MAAM,CAACC,CAAAA,IAAKA,MAAMf;YAC7C;QACF,OAAO;YACL,OAAO;mBAAIW;gBAAmBX;aAAM,CAACgB,IAAI;QAC3C;IACF,OAAO;QACL,OAAOL,iBAAiB,CAAC,EAAE,KAAKX,SAASrB,cAAc,EAAE,GAAG;YAACqB;SAAM;IACrE;IACA,OAAOW;AACT;AAEA;;CAEC,GACD,SAASxB,gBAA4C8B,KAAuB;IAC1E,IAAIA,UAAUV,WAAW;QACvB,OAAOA;IACT;IACA,OAAOE,MAAMC,OAAO,CAACO,SAASA,QAAQ;QAACA;KAAM;AAC/C"}