// eslint-disable-next-line deprecation/deprecation
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    Alert: function() {
        return _Alert.Alert;
    },
    alertClassNames: function() {
        return _Alert.alertClassNames;
    },
    renderAlert_unstable: function() {
        return _Alert.renderAlert_unstable;
    },
    useAlertStyles_unstable: function() {
        return _Alert.useAlertStyles_unstable;
    },
    useAlert_unstable: function() {
        return _Alert.useAlert_unstable;
    }
});
const _Alert = require("./Alert");
