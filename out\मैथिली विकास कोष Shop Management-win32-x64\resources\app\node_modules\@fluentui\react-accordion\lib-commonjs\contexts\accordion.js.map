{"version": 3, "sources": ["../src/contexts/accordion.ts"], "sourcesContent": ["import { createContext, ContextSelector, useContextSelector } from '@fluentui/react-context-selector';\nimport type { Context } from '@fluentui/react-context-selector';\nimport { AccordionItemValue } from '../AccordionItem';\nimport { AccordionToggleData, AccordionToggleEvent } from '../Accordion';\n\nexport type AccordionRequestToggleData<Value = AccordionItemValue> = { event: AccordionToggleEvent } & Pick<\n  AccordionToggleData<Value>,\n  'value'\n>;\n\nexport type AccordionContextValue<Value = AccordionItemValue> = {\n  /**\n   * The list of opened panels by index\n   */\n  openItems: AccordionItemValue[];\n  /**\n   * Callback used by AccordionItem to request a change on it's own opened state\n   * Should be used to toggle AccordionItem\n   */\n  requestToggle: (data: AccordionRequestToggleData<Value>) => void;\n  collapsible: boolean;\n  multiple: boolean;\n  navigation: 'linear' | 'circular' | undefined;\n};\n\nconst AccordionContext = createContext<AccordionContextValue | undefined>(undefined) as Context<AccordionContextValue>;\n\nconst accordionContextDefaultValue: AccordionContextValue = {\n  openItems: [],\n  collapsible: false,\n  multiple: false,\n  navigation: undefined,\n  requestToggle() {\n    /* noop */\n  },\n};\n\nexport const { Provider: AccordionProvider } = AccordionContext;\nexport const useAccordionContext_unstable = <T>(selector: ContextSelector<AccordionContextValue, T>): T =>\n  useContextSelector(AccordionContext, (ctx = accordionContextDefaultValue) => selector(ctx));\n"], "names": ["Accordi<PERSON><PERSON><PERSON><PERSON>", "useAccordionContext_unstable", "AccordionContext", "createContext", "undefined", "accordionContextDefaultValue", "openItems", "collapsible", "multiple", "navigation", "requestToggle", "Provider", "selector", "useContextSelector", "ctx"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAqCyBA,iBAAiB;eAAjBA;;IACZC,4BAAAA;eAAAA;;;sCAtCsD;AAyBnE,MAAMC,mBAAmBC,IAAAA,mCAAAA,EAAiDC;AAE1E,MAAMC,+BAAsD;IAC1DC,WAAW,EAAE;IACbC,aAAa;IACbC,UAAU;IACVC,YAAYL;IACZM;IACE,QAAQ,GACV;AACF;AAEO,MAAM,EAAEC,UAAUX,iBAAiB,EAAE,GAAGE;AACxC,MAAMD,+BAA+B,CAAIW,WAC9CC,IAAAA,wCAAAA,EAAmBX,kBAAkB,CAACY,MAAMT,4BAA4B,GAAKO,SAASE"}