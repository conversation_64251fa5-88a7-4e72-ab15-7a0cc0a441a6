# Change Log - @fluentui/react-accordion

This log was last generated on Fri, 27 Jun 2025 13:36:33 GMT and should not be manually modified.

<!-- Start content -->

## [9.7.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.7.3)

Fri, 27 Jun 2025 13:36:33 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.7.2..@fluentui/react-accordion_v9.7.3)

### Patches

- Bump @fluentui/react-aria to v9.15.3 ([PR #34734](https://github.com/microsoft/fluentui/pull/34734) by beachball)
- Bump @fluentui/react-tabster to v9.25.3 ([PR #34734](https://github.com/microsoft/fluentui/pull/34734) by beachball)

## [9.7.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.7.2)

<PERSON>hu, 26 Jun 2025 14:11:55 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.7.1..@fluentui/react-accordion_v9.7.2)

### Patches

- Bump @fluentui/react-aria to v9.15.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-context-selector to v9.2.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-motion to v9.9.0 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.6.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-tabster to v9.25.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-utilities to v9.22.0 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)

## [9.7.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.7.1)

Wed, 18 Jun 2025 17:34:00 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.7.0..@fluentui/react-accordion_v9.7.1)

### Patches

- Bump @fluentui/react-aria to v9.15.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-context-selector to v9.2.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-shared-contexts to v9.24.0 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-motion to v9.8.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.6.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-tabster to v9.25.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-utilities to v9.21.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)

## [9.7.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.7.0)

Thu, 12 Jun 2025 09:43:29 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.10..@fluentui/react-accordion_v9.7.0)

### Minor changes

- Bump @fluentui/react-aria to v9.15.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-context-selector to v9.2.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-motion to v9.8.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.6.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-tabster to v9.25.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-utilities to v9.21.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)

## [9.6.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.10)

Fri, 06 Jun 2025 13:15:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.9..@fluentui/react-accordion_v9.6.10)

### Patches

- Bump @fluentui/react-aria to v9.14.8 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-context-selector to v9.1.77 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.55 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-motion to v9.7.4 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.5.1 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-tabster to v9.24.8 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-utilities to v9.20.0 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)

## [9.6.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.9)

Wed, 14 May 2025 18:49:20 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.8..@fluentui/react-accordion_v9.6.9)

### Patches

- Bump @fluentui/react-aria to v9.14.7 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)
- Bump @fluentui/react-motion to v9.7.3 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.5.0 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)
- Bump @fluentui/react-tabster to v9.24.7 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)

## [9.6.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.8)

Thu, 24 Apr 2025 09:59:45 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.7..@fluentui/react-accordion_v9.6.8)

### Patches

- Bump @fluentui/react-aria to v9.14.6 ([PR #34315](https://github.com/microsoft/fluentui/pull/34315) by beachball)
- Bump @fluentui/react-tabster to v9.24.6 ([PR #34315](https://github.com/microsoft/fluentui/pull/34315) by beachball)

## [9.6.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.7)

Wed, 16 Apr 2025 19:42:18 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.6..@fluentui/react-accordion_v9.6.7)

### Patches

- Bump @fluentui/react-aria to v9.14.5 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-context-selector to v9.1.76 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.54 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-motion to v9.7.2 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.9 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-tabster to v9.24.5 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-utilities to v9.19.0 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)

## [9.6.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.6)

Tue, 01 Apr 2025 15:08:02 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.5..@fluentui/react-accordion_v9.6.6)

### Patches

- Bump @fluentui/react-aria to v9.14.4 ([PR #33909](https://github.com/microsoft/fluentui/pull/33909) by beachball)
- Bump @fluentui/react-tabster to v9.24.4 ([PR #33909](https://github.com/microsoft/fluentui/pull/33909) by beachball)

## [9.6.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.5)

Thu, 27 Mar 2025 21:12:51 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.4..@fluentui/react-accordion_v9.6.5)

### Patches

- Bump @fluentui/react-aria to v9.14.3 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-context-selector to v9.1.75 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.53 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-shared-contexts to v9.23.1 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-motion to v9.7.1 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.8 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-tabster to v9.24.3 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-utilities to v9.18.23 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)

## [9.6.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.4)

Wed, 26 Mar 2025 21:47:47 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.3..@fluentui/react-accordion_v9.6.4)

### Patches

- Bump @fluentui/react-motion to v9.7.0 ([PR #34096](https://github.com/microsoft/fluentui/pull/34096) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.7 ([PR #34096](https://github.com/microsoft/fluentui/pull/34096) by beachball)

## [9.6.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.3)

Thu, 20 Mar 2025 09:34:59 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.2..@fluentui/react-accordion_v9.6.3)

### Patches

- Bump @fluentui/react-motion to v9.6.10 ([PR #33994](https://github.com/microsoft/fluentui/pull/33994) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.6 ([PR #33994](https://github.com/microsoft/fluentui/pull/33994) by beachball)

## [9.6.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.2)

Wed, 19 Mar 2025 15:40:43 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.1..@fluentui/react-accordion_v9.6.2)

### Patches

- Bump @fluentui/react-aria to v9.14.2 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-context-selector to v9.1.74 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.52 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-shared-contexts to v9.23.0 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-motion to v9.6.9 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.5 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-tabster to v9.24.2 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-utilities to v9.18.22 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)

## [9.6.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.1)

Tue, 11 Mar 2025 18:58:54 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.6.0..@fluentui/react-accordion_v9.6.1)

### Patches

- Bump @fluentui/react-aria to v9.14.1 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-context-selector to v9.1.73 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.51 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-shared-contexts to v9.22.0 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-motion to v9.6.8 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.4 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-tabster to v9.24.1 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-utilities to v9.18.21 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)

## [9.6.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.6.0)

Fri, 21 Feb 2025 14:34:05 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.14..@fluentui/react-accordion_v9.6.0)

### Minor changes

- Bump @fluentui/react-aria to v9.14.0 ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by beachball)
- Bump @fluentui/react-tabster to v9.24.0 ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by beachball)

## [9.5.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.14)

Wed, 22 Jan 2025 14:00:14 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.13..@fluentui/react-accordion_v9.5.14)

### Patches

- Bump @fluentui/react-aria to v9.13.14 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-context-selector to v9.1.72 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.50 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-motion to v9.6.7 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.3 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-tabster to v9.23.3 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-utilities to v9.18.20 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)

## [9.5.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.13)

Wed, 08 Jan 2025 18:33:36 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.12..@fluentui/react-accordion_v9.5.13)

### Patches

- Bump @fluentui/react-aria to v9.13.13 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.49 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)
- Bump @fluentui/react-motion to v9.6.6 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.2 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)

## [9.5.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.12)

Mon, 16 Dec 2024 16:26:49 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.11..@fluentui/react-accordion_v9.5.12)

### Patches

- Bump @fluentui/react-aria to v9.13.12 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-context-selector to v9.1.71 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.48 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.2 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-motion to v9.6.5 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.1 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-tabster to v9.23.2 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-theme to v9.1.24 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-utilities to v9.18.19 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)

## [9.5.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.11)

Mon, 09 Dec 2024 17:38:07 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.10..@fluentui/react-accordion_v9.5.11)

### Patches

- chore: remove usage of "export *" ([PR #33384](https://github.com/microsoft/fluentui/pull/33384) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.11 ([PR #33431](https://github.com/microsoft/fluentui/pull/33431) by beachball)
- Bump @fluentui/react-motion to v9.6.4 ([PR #33431](https://github.com/microsoft/fluentui/pull/33431) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.4.0 ([PR #33431](https://github.com/microsoft/fluentui/pull/33431) by beachball)

## [9.5.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.10)

Fri, 06 Dec 2024 12:53:46 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.9..@fluentui/react-accordion_v9.5.10)

### Patches

- Bump @fluentui/react-aria to v9.13.10 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-context-selector to v9.1.70 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.47 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.1 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-motion to v9.6.3 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.3.2 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-tabster to v9.23.1 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-theme to v9.1.23 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-utilities to v9.18.18 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)

## [9.5.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.9)

Thu, 28 Nov 2024 09:30:54 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.8..@fluentui/react-accordion_v9.5.9)

### Patches

- Bump @fluentui/react-motion to v9.6.2 ([PR #33331](https://github.com/microsoft/fluentui/pull/33331) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.3.1 ([PR #33331](https://github.com/microsoft/fluentui/pull/33331) by beachball)

## [9.5.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.8)

Mon, 11 Nov 2024 10:01:04 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.7..@fluentui/react-accordion_v9.5.8)

### Patches

- chore: replace npm-scripts and just-scrtips with nx inferred tasks ([PR #33074](https://github.com/microsoft/fluentui/pull/33074) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.9 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-context-selector to v9.1.69 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.46 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.0 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-motion to v9.6.1 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.3.0 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-tabster to v9.23.0 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-theme to v9.1.22 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-utilities to v9.18.17 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)

## [9.5.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.7)

Tue, 15 Oct 2024 17:17:53 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.6..@fluentui/react-accordion_v9.5.7)

### Patches

- Bump @fluentui/react-aria to v9.13.8 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-context-selector to v9.1.68 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.45 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-shared-contexts to v9.20.2 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-motion to v9.6.0 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.2.0 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-tabster to v9.22.9 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-theme to v9.1.21 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-utilities to v9.18.16 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)

## [9.5.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.6)

Tue, 08 Oct 2024 22:05:59 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.5..@fluentui/react-accordion_v9.5.6)

### Patches

- Bump @fluentui/react-aria to v9.13.7 ([PR #33007](https://github.com/microsoft/fluentui/pull/33007) by beachball)
- Bump @fluentui/react-tabster to v9.22.8 ([PR #33007](https://github.com/microsoft/fluentui/pull/33007) by beachball)

## [9.5.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.5)

Mon, 23 Sep 2024 12:40:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.4..@fluentui/react-accordion_v9.5.5)

### Patches

- Bump @fluentui/react-aria to v9.13.6 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-context-selector to v9.1.67 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.44 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-shared-contexts to v9.20.1 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-motion to v9.5.2 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.1.4 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-tabster to v9.22.7 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-theme to v9.1.20 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-utilities to v9.18.15 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)

## [9.5.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.4)

Tue, 10 Sep 2024 10:19:12 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.3..@fluentui/react-accordion_v9.5.4)

### Patches

- Bump @fluentui/react-aria to v9.13.5 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-context-selector to v9.1.66 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.43 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-motion to v9.5.1 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.1.3 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-tabster to v9.22.6 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-utilities to v9.18.14 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)

## [9.5.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.3)

Fri, 16 Aug 2024 10:24:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.2..@fluentui/react-accordion_v9.5.3)

### Patches

- Bump @fluentui/react-motion to v9.5.0 ([PR #32316](https://github.com/microsoft/fluentui/pull/32316) by beachball)
- Bump @fluentui/react-motion-components-preview to v0.1.2 ([PR #32316](https://github.com/microsoft/fluentui/pull/32316) by beachball)

## [9.5.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.2)

Thu, 15 Aug 2024 13:49:46 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.1..@fluentui/react-accordion_v9.5.2)

### Patches

- Bump @fluentui/react-aria to v9.13.4 ([PR #32313](https://github.com/microsoft/fluentui/pull/32313) by beachball)
- Bump @fluentui/react-tabster to v9.22.5 ([PR #32313](https://github.com/microsoft/fluentui/pull/32313) by beachball)

## [9.5.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.1)

Thu, 15 Aug 2024 08:22:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.5.0..@fluentui/react-accordion_v9.5.1)

### Patches

- Bump @fluentui/react-aria to v9.13.3 ([PR #31885](https://github.com/microsoft/fluentui/pull/31885) by beachball)
- Bump @fluentui/react-tabster to v9.22.4 ([PR #31885](https://github.com/microsoft/fluentui/pull/31885) by beachball)

## [9.5.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.5.0)

Tue, 30 Jul 2024 18:47:31 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.4.4..@fluentui/react-accordion_v9.5.0)

### Minor changes

- feat: add collapse motion to Accordion ([PR #32086](https://github.com/microsoft/fluentui/pull/32086) by <EMAIL>)
- Bump @fluentui/react-motion to v9.4.0 ([PR #32157](https://github.com/microsoft/fluentui/pull/32157) by beachball)

## [9.4.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.4.4)

Tue, 23 Jul 2024 20:13:14 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.4.3..@fluentui/react-accordion_v9.4.4)

### Patches

- Bump @fluentui/react-aria to v9.13.2 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-context-selector to v9.1.65 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.42 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-shared-contexts to v9.20.0 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-tabster to v9.22.3 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-utilities to v9.18.13 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)

## [9.4.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.4.3)

Mon, 15 Jul 2024 17:25:50 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.4.2..@fluentui/react-accordion_v9.4.3)

### Patches

- fix: revert incorectly set npm versions in all packages ([PR #31937](https://github.com/microsoft/fluentui/pull/31937) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.1 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-context-selector to v9.1.64 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.41 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-shared-contexts to v9.19.1 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-tabster to v9.22.2 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-utilities to v9.18.12 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)

## [9.4.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.4.2)

Mon, 01 Jul 2024 20:30:40 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.4.1..@fluentui/react-accordion_v9.4.2)

### Patches

- chore: Update react-icons package to ^2.0.245 ([PR #31802](https://github.com/microsoft/fluentui/pull/31802) by <EMAIL>)
- chore: add eslint react-compiler ([PR #31457](https://github.com/microsoft/fluentui/pull/31457) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.0 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-context-selector to v9.1.63 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.40 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-tabster to v9.22.1 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-utilities to v9.18.11 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)

## [9.4.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.4.1)

Mon, 17 Jun 2024 07:34:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.4.0..@fluentui/react-accordion_v9.4.1)

### Patches

- Bump @fluentui/react-aria to v9.12.1 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)
- Bump @fluentui/react-context-selector to v9.1.62 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)
- Bump @fluentui/react-tabster to v9.22.0 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)

## [9.4.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.4.0)

Wed, 12 Jun 2024 13:17:15 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.57..@fluentui/react-accordion_v9.4.0)

### Minor changes

- Deprecated react-accordion navigation prop. Tab navigation only should be used according to W3 APG pattern. ([PR #31587](https://github.com/microsoft/fluentui/pull/31587) by <EMAIL>)

## [9.3.57](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.57)

Thu, 06 Jun 2024 15:26:29 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.56..@fluentui/react-accordion_v9.3.57)

### Patches

- chore: ensure only state or defaultState is provided on useControllableState hook invocation ([PR #31461](https://github.com/microsoft/fluentui/pull/31461) by <EMAIL>)
- Bump @fluentui/react-aria to v9.12.0 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-context-selector to v9.1.61 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.39 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-tabster to v9.21.5 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-utilities to v9.18.10 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)

## [9.3.56](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.56)

Thu, 23 May 2024 08:02:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.55..@fluentui/react-accordion_v9.3.56)

### Patches

- chore: replace usage of .shorthands() in styles ([PR #31449](https://github.com/microsoft/fluentui/pull/31449) by <EMAIL>)
- Bump @fluentui/react-aria to v9.11.4 ([commit](https://github.com/microsoft/fluentui/commit/03599d609e8310b08c57d1f871cffbf717d79207) by beachball)
- Bump @fluentui/react-tabster to v9.21.4 ([commit](https://github.com/microsoft/fluentui/commit/03599d609e8310b08c57d1f871cffbf717d79207) by beachball)

## [9.3.55](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.55)

Mon, 20 May 2024 12:44:51 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.54..@fluentui/react-accordion_v9.3.55)

### Patches

- chore: bump @griffel/react ([PR #31258](https://github.com/microsoft/fluentui/pull/31258) by <EMAIL>)
- Bump @fluentui/react-aria to v9.11.3 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-context-selector to v9.1.60 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.38 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-shared-contexts to v9.19.0 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-tabster to v9.21.3 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-utilities to v9.18.9 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)

## [9.3.54](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.54)

Thu, 16 May 2024 09:25:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.53..@fluentui/react-accordion_v9.3.54)

### Patches

- chore: Upgrade react-icons version to 2.0.239 to pick up provider export map fix. ([PR #31287](https://github.com/microsoft/fluentui/pull/31287) by <EMAIL>)

## [9.3.53](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.53)

Thu, 09 May 2024 19:35:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.52..@fluentui/react-accordion_v9.3.53)

### Patches

- Bump @fluentui/react-aria to v9.11.2 ([PR #31321](https://github.com/microsoft/fluentui/pull/31321) by beachball)
- Bump @fluentui/react-tabster to v9.21.2 ([PR #31321](https://github.com/microsoft/fluentui/pull/31321) by beachball)

## [9.3.52](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.52)

Mon, 06 May 2024 12:55:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.51..@fluentui/react-accordion_v9.3.52)

### Patches

- Bump @fluentui/react-aria to v9.11.1 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-context-selector to v9.1.59 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.37 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-shared-contexts to v9.18.0 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-tabster to v9.21.1 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-utilities to v9.18.8 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)

## [9.3.51](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.51)

Thu, 02 May 2024 11:36:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.50..@fluentui/react-accordion_v9.3.51)

### Patches

- chore: upgrade @fluentui/react-icons to 2.0.237. ([PR #31139](https://github.com/microsoft/fluentui/pull/31139) by <EMAIL>)
- Bump @fluentui/react-aria to v9.11.0 ([PR #31231](https://github.com/microsoft/fluentui/pull/31231) by beachball)
- Bump @fluentui/react-tabster to v9.21.0 ([PR #31231](https://github.com/microsoft/fluentui/pull/31231) by beachball)

## [9.3.50](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.50)

Tue, 23 Apr 2024 08:17:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.49..@fluentui/react-accordion_v9.3.50)

### Patches

- Bump @fluentui/react-aria to v9.10.5 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-context-selector to v9.1.58 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.36 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-shared-contexts to v9.17.0 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-tabster to v9.20.1 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-utilities to v9.18.7 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)

## [9.3.49](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.49)

Wed, 17 Apr 2024 21:53:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.48..@fluentui/react-accordion_v9.3.49)

### Patches

- chore: Update react-icons to 2.0.235 ([PR #31011](https://github.com/microsoft/fluentui/pull/31011) by <EMAIL>)
- Bump @fluentui/react-aria to v9.10.4 ([PR #31100](https://github.com/microsoft/fluentui/pull/31100) by beachball)
- Bump @fluentui/react-tabster to v9.20.0 ([PR #31100](https://github.com/microsoft/fluentui/pull/31100) by beachball)

## [9.3.48](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.48)

Thu, 04 Apr 2024 12:08:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.47..@fluentui/react-accordion_v9.3.48)

### Patches

- fix: remove border from high contrast mode to match styles and make focus more visible ([PR #30865](https://github.com/microsoft/fluentui/pull/30865) by <EMAIL>)

## [9.3.47](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.47)

Tue, 02 Apr 2024 09:48:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.46..@fluentui/react-accordion_v9.3.47)

### Patches

- Bump @fluentui/react-aria to v9.10.3 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-context-selector to v9.1.57 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.35 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-shared-contexts to v9.16.0 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-tabster to v9.19.6 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-utilities to v9.18.6 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)

## [9.3.46](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.46)

Mon, 18 Mar 2024 19:50:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.45..@fluentui/react-accordion_v9.3.46)

### Patches

- Bump @fluentui/react-aria to v9.10.2 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-context-selector to v9.1.56 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.34 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-shared-contexts to v9.15.2 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-tabster to v9.19.5 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-theme to v9.1.19 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-utilities to v9.18.5 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)

## [9.3.45](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.45)

Fri, 15 Mar 2024 21:43:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.44..@fluentui/react-accordion_v9.3.45)

### Patches

- Bump @fluentui/react-aria to v9.10.1 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-context-selector to v9.1.55 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.33 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-shared-contexts to v9.15.1 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-tabster to v9.19.4 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-theme to v9.1.18 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-utilities to v9.18.4 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)

## [9.3.44](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.44)

Thu, 07 Mar 2024 19:33:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.43..@fluentui/react-accordion_v9.3.44)

### Patches

- chore: remove invalid peerDependency on scheduler ([PR #30587](https://github.com/microsoft/fluentui/pull/30587) by <EMAIL>)
- Bump @fluentui/react-aria to v9.10.0 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-context-selector to v9.1.54 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.32 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-shared-contexts to v9.15.0 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-tabster to v9.19.3 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-theme to v9.1.17 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-utilities to v9.18.3 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)

## [9.3.43](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.43)

Wed, 28 Feb 2024 02:34:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.42..@fluentui/react-accordion_v9.3.43)

### Patches

- Bump @fluentui/react-aria to v9.9.1 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-context-selector to v9.1.53 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.31 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-shared-contexts to v9.14.1 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-tabster to v9.19.2 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-utilities to v9.18.2 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)

## [9.3.42](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.42)

Tue, 20 Feb 2024 14:22:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.41..@fluentui/react-accordion_v9.3.42)

### Patches

- Bump @fluentui/react-aria to v9.9.0 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-context-selector to v9.1.52 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.30 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-tabster to v9.19.1 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-utilities to v9.18.1 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)

## [9.3.41](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.41)

Tue, 06 Feb 2024 17:55:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.40..@fluentui/react-accordion_v9.3.41)

### Patches

- Bump @fluentui/react-aria to v9.8.2 ([PR #30392](https://github.com/microsoft/fluentui/pull/30392) by beachball)
- Bump @fluentui/react-tabster to v9.19.0 ([PR #30392](https://github.com/microsoft/fluentui/pull/30392) by beachball)

## [9.3.40](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.40)

Tue, 30 Jan 2024 23:16:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.39..@fluentui/react-accordion_v9.3.40)

### Patches

- Bump @fluentui/react-aria to v9.8.1 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-context-selector to v9.1.51 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.29 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-tabster to v9.18.0 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-utilities to v9.18.0 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)

## [9.3.39](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.39)

Tue, 23 Jan 2024 15:11:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.38..@fluentui/react-accordion_v9.3.39)

### Patches

- Bump @fluentui/react-aria to v9.8.0 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-context-selector to v9.1.50 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.28 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-tabster to v9.17.4 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-utilities to v9.17.0 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)

## [9.3.38](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.38)

Thu, 18 Jan 2024 14:25:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.37..@fluentui/react-accordion_v9.3.38)

### Patches

- Bump @fluentui/react-aria to v9.7.3 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-context-selector to v9.1.49 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.27 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-shared-contexts to v9.14.0 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-tabster to v9.17.3 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-utilities to v9.16.1 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)

## [9.3.37](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.37)

Wed, 17 Jan 2024 16:18:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.36..@fluentui/react-accordion_v9.3.37)

### Patches

- Bump @fluentui/react-aria to v9.7.2 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-context-selector to v9.1.48 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.26 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-tabster to v9.17.2 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-utilities to v9.16.0 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)

## [9.3.36](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.36)

Tue, 16 Jan 2024 13:14:13 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.35..@fluentui/react-accordion_v9.3.36)

### Patches

- fix: correct version of @types/react-dom peer dep that matches for 16.x ([PR #30259](https://github.com/microsoft/fluentui/pull/30259) by <EMAIL>)
- Bump @fluentui/react-aria to v9.7.1 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)
- Bump @fluentui/react-context-selector to v9.1.47 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.25 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)
- Bump @fluentui/react-tabster to v9.17.1 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)

## [9.3.35](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.35)

Thu, 11 Jan 2024 09:04:29 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.34..@fluentui/react-accordion_v9.3.35)

### Patches

- Bump @fluentui/react-aria to v9.7.0 ([PR #30259](https://github.com/microsoft/fluentui/pull/30259) by beachball)

## [9.3.34](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.34)

Mon, 08 Jan 2024 16:24:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.33..@fluentui/react-accordion_v9.3.34)

### Patches

- update @fluentui/react-icons to 2.0.224 ([PR #30078](https://github.com/microsoft/fluentui/pull/30078) by <EMAIL>)
- Bump @fluentui/react-aria to v9.6.2 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-context-selector to v9.1.46 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.24 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-tabster to v9.17.0 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-utilities to v9.15.6 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)

## [9.3.33](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.33)

Wed, 03 Jan 2024 09:26:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.32..@fluentui/react-accordion_v9.3.33)

### Patches

- Bump @fluentui/react-aria to v9.6.1 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-context-selector to v9.1.45 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.23 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-tabster to v9.16.1 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-utilities to v9.15.5 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)

## [9.3.32](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.32)

Mon, 18 Dec 2023 14:40:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.31..@fluentui/react-accordion_v9.3.32)

### Patches

- chore: adopts useARIAButtonProps instead of deprecated method useARIAButtonShorthand ([PR #29735](https://github.com/microsoft/fluentui/pull/29735) by <EMAIL>)
- Bump @fluentui/react-aria to v9.6.0 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-context-selector to v9.1.44 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.22 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-shared-contexts to v9.13.2 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-tabster to v9.16.0 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-utilities to v9.15.4 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)

## [9.3.31](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.31)

Thu, 14 Dec 2023 09:58:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.30..@fluentui/react-accordion_v9.3.31)

### Patches

- Bump @fluentui/react-aria to v9.5.0 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-context-selector to v9.1.43 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.21 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-shared-contexts to v9.13.1 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-tabster to v9.15.1 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-utilities to v9.15.3 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)

## [9.3.30](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.30)

Thu, 30 Nov 2023 13:42:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.29..@fluentui/react-accordion_v9.3.30)

### Patches

- Bump @fluentui/react-aria to v9.4.0 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.20 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)
- Bump @fluentui/react-tabster to v9.15.0 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)

## [9.3.29](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.29)

Mon, 20 Nov 2023 09:55:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.28..@fluentui/react-accordion_v9.3.29)

### Patches

- Bump @fluentui/react-shared-contexts to v9.13.0 ([PR #29878](https://github.com/microsoft/fluentui/pull/29878) by beachball)
- Bump @fluentui/react-tabster to v9.14.6 ([PR #29878](https://github.com/microsoft/fluentui/pull/29878) by beachball)

## [9.3.28](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.28)

Tue, 14 Nov 2023 17:51:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.27..@fluentui/react-accordion_v9.3.28)

### Patches

- Bump @fluentui/react-tabster to v9.14.5 ([PR #29835](https://github.com/microsoft/fluentui/pull/29835) by beachball)

## [9.3.27](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.27)

Thu, 09 Nov 2023 17:29:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.26..@fluentui/react-accordion_v9.3.27)

### Patches

- chore: use package.json#files setup instead of npmignore for all v9 libraries ([PR #29734](https://github.com/microsoft/fluentui/pull/29734) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.44 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-context-selector to v9.1.42 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.19 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-shared-contexts to v9.12.0 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-tabster to v9.14.4 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-theme to v9.1.16 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-utilities to v9.15.2 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)

## [9.3.26](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.26)

Wed, 01 Nov 2023 12:55:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.25..@fluentui/react-accordion_v9.3.26)

### Patches

- Bump @fluentui/react-shared-contexts to v9.11.1 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)
- Bump @fluentui/react-tabster to v9.14.3 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)
- Bump @fluentui/react-theme to v9.1.15 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)

## [9.3.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.25)

Sat, 28 Oct 2023 23:35:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.24..@fluentui/react-accordion_v9.3.25)

### Patches

- Bump @fluentui/react-shared-contexts to v9.11.0 ([commit](https://github.com/microsoft/fluentui/commit/555b0fae3ec7f052e765557ae243c58000514f92) by beachball)
- Bump @fluentui/react-tabster to v9.14.2 ([commit](https://github.com/microsoft/fluentui/commit/555b0fae3ec7f052e765557ae243c58000514f92) by beachball)

## [9.3.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.24)

Mon, 23 Oct 2023 09:51:57 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.23..@fluentui/react-accordion_v9.3.24)

### Patches

- Bump @fluentui/react-tabster to v9.14.1 ([commit](https://github.com/microsoft/fluentui/commit/e4ef1febe8a185dddc10f8936944d177d50bc396) by beachball)

## [9.3.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.23)

Wed, 18 Oct 2023 17:54:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.22..@fluentui/react-accordion_v9.3.23)

### Patches

- chore: migrate from getNativeElementProps to getIntrinsicElementProps ([PR #29499](https://github.com/microsoft/fluentui/pull/29499) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.43 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-context-selector to v9.1.41 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.18 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-tabster to v9.14.0 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-utilities to v9.15.1 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)

## [9.3.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.22)

Thu, 12 Oct 2023 14:55:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.21..@fluentui/react-accordion_v9.3.22)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.17 ([PR #29513](https://github.com/microsoft/fluentui/pull/29513) by beachball)

## [9.3.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.21)

Wed, 11 Oct 2023 13:54:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.20..@fluentui/react-accordion_v9.3.21)

### Patches

- Bump @fluentui/react-aria to v9.3.42 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-context-selector to v9.1.40 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.16 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-tabster to v9.13.6 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-utilities to v9.15.0 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)

## [9.3.20](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.20)

Mon, 09 Oct 2023 20:45:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.19..@fluentui/react-accordion_v9.3.20)

### Patches

- Bump @fluentui/react-aria to v9.3.41 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-context-selector to v9.1.39 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.15 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-shared-contexts to v9.10.0 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-tabster to v9.13.5 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-utilities to v9.14.2 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)

## [9.3.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.19)

Thu, 05 Oct 2023 15:25:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.18..@fluentui/react-accordion_v9.3.19)

### Patches

- Bump @fluentui/react-aria to v9.3.40 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-context-selector to v9.1.38 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.14 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-tabster to v9.13.4 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-utilities to v9.14.1 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)

## [9.3.18](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.18)

Wed, 04 Oct 2023 08:45:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.17..@fluentui/react-accordion_v9.3.18)

### Patches

- Bump @fluentui/react-aria to v9.3.39 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-context-selector to v9.1.37 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.13 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-tabster to v9.13.3 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-utilities to v9.14.0 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)

## [9.3.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.17)

Mon, 02 Oct 2023 08:56:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.16..@fluentui/react-accordion_v9.3.17)

### Patches

- Bump @fluentui/react-shared-contexts to v9.9.2 ([PR #29301](https://github.com/microsoft/fluentui/pull/29301) by beachball)
- Bump @fluentui/react-tabster to v9.13.2 ([PR #29301](https://github.com/microsoft/fluentui/pull/29301) by beachball)

## [9.3.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.16)

Tue, 26 Sep 2023 17:49:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.15..@fluentui/react-accordion_v9.3.16)

### Patches

- chore: trigger manual version bump after broken release ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.38 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-context-selector to v9.1.36 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.12 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-shared-contexts to v9.9.1 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-tabster to v9.13.1 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-theme to v9.1.14 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-utilities to v9.13.5 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)

## [9.3.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.15)

Tue, 26 Sep 2023 15:32:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.14..@fluentui/react-accordion_v9.3.15)

### Patches

- fix: bump swc core to mitigate transpilation memory leaks ([PR #29253](https://github.com/microsoft/fluentui/pull/29253) by <EMAIL>)
- chore: Update react-icons version to pick up IconDirectionContextProvider updated export ([PR #29151](https://github.com/microsoft/fluentui/pull/29151) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.37 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-context-selector to v9.1.35 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.11 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-shared-contexts to v9.9.0 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-tabster to v9.13.0 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-theme to v9.1.13 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-utilities to v9.13.4 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)

## [9.3.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.14)

Wed, 20 Sep 2023 17:47:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.13..@fluentui/react-accordion_v9.3.14)

### Patches

- chore: trigger manual version bump after broken release ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.10 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)
- Bump @fluentui/react-shared-contexts to v9.8.1 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)
- Bump @fluentui/react-tabster to v9.12.11 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)

## [9.3.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.13)

Wed, 20 Sep 2023 14:59:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.12..@fluentui/react-accordion_v9.3.13)

### Patches

- fix: fix memory leak caused by context assignment ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.9 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)
- Bump @fluentui/react-shared-contexts to v9.8.0 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)
- Bump @fluentui/react-tabster to v9.12.10 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)

## [9.3.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.12)

Thu, 14 Sep 2023 16:44:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.11..@fluentui/react-accordion_v9.3.12)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.8 ([PR #29145](https://github.com/microsoft/fluentui/pull/29145) by beachball)

## [9.3.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.11)

Tue, 12 Sep 2023 08:51:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.10..@fluentui/react-accordion_v9.3.11)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.7 ([PR #29129](https://github.com/microsoft/fluentui/pull/29129) by beachball)
- Bump @fluentui/react-tabster to v9.12.9 ([PR #29129](https://github.com/microsoft/fluentui/pull/29129) by beachball)

## [9.3.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.10)

Wed, 06 Sep 2023 13:31:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.9..@fluentui/react-accordion_v9.3.10)

### Patches

- Bump @fluentui/react-aria to v9.3.36 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-context-selector to v9.1.34 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.6 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-tabster to v9.12.8 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-utilities to v9.13.3 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)

## [9.3.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.9)

Tue, 05 Sep 2023 15:39:04 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.8..@fluentui/react-accordion_v9.3.9)

### Patches

- Bump @fluentui/react-aria to v9.3.35 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-context-selector to v9.1.33 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.5 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-tabster to v9.12.7 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-utilities to v9.13.2 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)

## [9.3.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.8)

Tue, 05 Sep 2023 13:29:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.7..@fluentui/react-accordion_v9.3.8)

### Patches

- chore: migrate package to use JSX importSource ([PR #28959](https://github.com/microsoft/fluentui/pull/28959) by <EMAIL>)
- bumps @swc/helpers version to 0.5.1 ([PR #28989](https://github.com/microsoft/fluentui/pull/28989) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.34 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-context-selector to v9.1.32 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.4 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.3 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-tabster to v9.12.6 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-theme to v9.1.12 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-utilities to v9.13.1 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)

## [9.3.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.7)

Tue, 29 Aug 2023 12:57:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.6..@fluentui/react-accordion_v9.3.7)

### Patches

- Bump @fluentui/react-aria to v9.3.33 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-context-selector to v9.1.31 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.3 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-tabster to v9.12.5 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-utilities to v9.13.0 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)

## [9.3.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.6)

Thu, 24 Aug 2023 10:26:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.5..@fluentui/react-accordion_v9.3.6)

### Patches

- Bump @fluentui/react-aria to v9.3.32 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-context-selector to v9.1.30 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.2 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-tabster to v9.12.4 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-utilities to v9.12.0 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)

## [9.3.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.5)

Wed, 23 Aug 2023 12:01:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.4..@fluentui/react-accordion_v9.3.5)

### Patches

- fix: added cursor style in AccordionHeader for AccordionItem disabled state ([PR #28850](https://github.com/microsoft/fluentui/pull/28850) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.31 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-context-selector to v9.1.29 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.1 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-tabster to v9.12.3 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-utilities to v9.11.2 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)

## [9.3.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.4)

Wed, 16 Aug 2023 17:41:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.3..@fluentui/react-accordion_v9.3.4)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.0 ([PR #28885](https://github.com/microsoft/fluentui/pull/28885) by beachball)

## [9.3.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.3)

Wed, 16 Aug 2023 11:38:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.2..@fluentui/react-accordion_v9.3.3)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.16 ([PR #28791](https://github.com/microsoft/fluentui/pull/28791) by beachball)

## [9.3.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.2)

Fri, 11 Aug 2023 12:14:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.1..@fluentui/react-accordion_v9.3.2)

### Patches

- Bump @fluentui/react-aria to v9.3.30 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-context-selector to v9.1.28 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.15 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-tabster to v9.12.2 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-utilities to v9.11.1 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)

## [9.3.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.1)

Wed, 09 Aug 2023 13:16:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.3.0..@fluentui/react-accordion_v9.3.1)

### Patches

- bugfix: Breaking Change, onHeaderClick erroneously removed ([PR #28764](https://github.com/microsoft/fluentui/pull/28764) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #28684](https://github.com/microsoft/fluentui/pull/28684) by <EMAIL>)
- chore(cxe-coastal): migrate to new slot API ([PR #28754](https://github.com/microsoft/fluentui/pull/28754) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.29 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.14 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.2 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-tabster to v9.12.1 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-theme to v9.1.11 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)

## [9.3.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.3.0)

Fri, 04 Aug 2023 08:52:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.2.0..@fluentui/react-accordion_v9.3.0)

### Minor changes

- feat: make AccordionItemValue generic ([PR #28665](https://github.com/microsoft/fluentui/pull/28665) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.28 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-context-selector to v9.1.27 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.13 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.1 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-tabster to v9.12.0 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-theme to v9.1.10 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-utilities to v9.11.0 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)

## [9.2.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.2.0)

Tue, 01 Aug 2023 10:17:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.25..@fluentui/react-accordion_v9.2.0)

### Minor changes

- Accordion - export AccordionHeaderContextProvider ([PR #28542](https://github.com/microsoft/fluentui/pull/28542) by <EMAIL>)
- Bump @fluentui/react-shared-contexts to v9.7.0 ([PR #28689](https://github.com/microsoft/fluentui/pull/28689) by beachball)
- Bump @fluentui/react-tabster to v9.11.1 ([PR #28689](https://github.com/microsoft/fluentui/pull/28689) by beachball)

## [9.1.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.25)

Tue, 25 Jul 2023 13:29:16 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.24..@fluentui/react-accordion_v9.1.25)

### Patches

- chore: Update react-icons version to pick up fowardref change. ([PR #28590](https://github.com/microsoft/fluentui/pull/28590) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.11.0 ([PR #28622](https://github.com/microsoft/fluentui/pull/28622) by beachball)

## [9.1.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.24)

Thu, 20 Jul 2023 18:27:25 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.23..@fluentui/react-accordion_v9.1.24)

### Patches

- Bump @fluentui/react-aria to v9.3.27 ([PR #26826](https://github.com/microsoft/fluentui/pull/26826) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.12 ([PR #26826](https://github.com/microsoft/fluentui/pull/26826) by beachball)

## [9.1.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.23)

Tue, 11 Jul 2023 18:46:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.22..@fluentui/react-accordion_v9.1.23)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.11 ([PR #28491](https://github.com/microsoft/fluentui/pull/28491) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0 ([PR #28491](https://github.com/microsoft/fluentui/pull/28491) by beachball)

## [9.1.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.22)

Mon, 03 Jul 2023 13:34:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.21..@fluentui/react-accordion_v9.1.22)

### Patches

- Bump @fluentui/react-tabster to v9.10.0 ([PR #28394](https://github.com/microsoft/fluentui/pull/28394) by beachball)

## [9.1.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.21)

Mon, 03 Jul 2023 11:57:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.20..@fluentui/react-accordion_v9.1.21)

### Patches

- Bump @fluentui/react-aria to v9.3.26 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)
- Bump @fluentui/react-context-selector to v9.1.26 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.10 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)
- Bump @fluentui/react-shared-contexts to v9.6.0 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)
- Bump @fluentui/react-tabster to v9.9.2 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)
- Bump @fluentui/react-utilities to v9.10.1 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)

## [9.1.20](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.20)

Wed, 28 Jun 2023 11:12:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.19..@fluentui/react-accordion_v9.1.20)

### Patches

- Bump @fluentui/react-aria to v9.3.25 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)
- Bump @fluentui/react-context-selector to v9.1.25 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.9 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)
- Bump @fluentui/react-tabster to v9.9.1 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)
- Bump @fluentui/react-utilities to v9.10.0 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)

## [9.1.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.19)

Tue, 27 Jun 2023 11:21:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.18..@fluentui/react-accordion_v9.1.19)

### Patches

- Bump @fluentui/react-tabster to v9.9.0 ([PR #28291](https://github.com/microsoft/fluentui/pull/28291) by beachball)

## [9.1.18](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.18)

Mon, 26 Jun 2023 09:53:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.17..@fluentui/react-accordion_v9.1.18)

### Patches

- Bump @fluentui/react-aria to v9.3.24 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)
- Bump @fluentui/react-context-selector to v9.1.24 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.8 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)
- Bump @fluentui/react-tabster to v9.8.1 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)
- Bump @fluentui/react-utilities to v9.9.4 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)

## [9.1.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.17)

Tue, 20 Jun 2023 12:38:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.16..@fluentui/react-accordion_v9.1.17)

### Patches

- chore: update @fluentui/react-icons to 2.0.203 ([PR #28203](https://github.com/microsoft/fluentui/pull/28203) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.23 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-context-selector to v9.1.23 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.7 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-shared-contexts to v9.5.1 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-tabster to v9.8.0 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-theme to v9.1.9 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-utilities to v9.9.3 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.23 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)

## [9.1.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.16)

Wed, 31 May 2023 06:46:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.15..@fluentui/react-accordion_v9.1.16)

### Patches

- chore: Update Griffel to v1.5.7. ([PR #27925](https://github.com/microsoft/fluentui/pull/27925) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.22 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)
- Bump @fluentui/react-context-selector to v9.1.22 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.6 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)
- Bump @fluentui/react-tabster to v9.7.5 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)
- Bump @fluentui/react-utilities to v9.9.2 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.22 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)

## [9.1.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.15)

Thu, 25 May 2023 10:00:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.14..@fluentui/react-accordion_v9.1.15)

### Patches

- Bump @fluentui/react-aria to v9.3.21 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)
- Bump @fluentui/react-context-selector to v9.1.21 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.5 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)
- Bump @fluentui/react-tabster to v9.7.4 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)
- Bump @fluentui/react-utilities to v9.9.1 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)

## [9.1.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.14)

Wed, 24 May 2023 20:45:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.13..@fluentui/react-accordion_v9.1.14)

### Patches

- chore: ensures AccordionHeader expandIcon supports null ([PR #27912](https://github.com/microsoft/fluentui/pull/27912) by <EMAIL>)
- Bump @fluentui/react-shared-contexts to v9.5.0 ([PR #27989](https://github.com/microsoft/fluentui/pull/27989) by beachball)
- Bump @fluentui/react-tabster to v9.7.3 ([PR #27989](https://github.com/microsoft/fluentui/pull/27989) by beachball)

## [9.1.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.13)

Thu, 18 May 2023 13:11:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.12..@fluentui/react-accordion_v9.1.13)

### Patches

- Bump @fluentui/react-tabster to v9.7.2 ([PR #27540](https://github.com/microsoft/fluentui/pull/27540) by beachball)

## [9.1.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.12)

Thu, 18 May 2023 00:39:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.11..@fluentui/react-accordion_v9.1.12)

### Patches

- Bump @fluentui/react-aria to v9.3.20 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)
- Bump @fluentui/react-context-selector to v9.1.20 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.4 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)
- Bump @fluentui/react-tabster to v9.7.1 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)
- Bump @fluentui/react-utilities to v9.9.0 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)

## [9.1.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.11)

Fri, 12 May 2023 20:28:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.10..@fluentui/react-accordion_v9.1.11)

### Patches

- chore: move makeStyles() calls to .styles.ts files ([PR #27708](https://github.com/microsoft/fluentui/pull/27708) by <EMAIL>)
- Update to use single hook selector ([PR #27491](https://github.com/microsoft/fluentui/pull/27491) by <EMAIL>)
- chore: exclude .swcrc from being published ([PR #27740](https://github.com/microsoft/fluentui/pull/27740) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.19 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-context-selector to v9.1.19 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.3 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-shared-contexts to v9.4.0 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-tabster to v9.7.0 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-theme to v9.1.8 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-utilities to v9.8.1 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.21 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)

## [9.1.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.10)

Mon, 24 Apr 2023 08:12:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.9..@fluentui/react-accordion_v9.1.10)

### Patches

- feat: adopt custom JSX pragma ([PR #27601](https://github.com/microsoft/fluentui/pull/27601) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.2 ([commit](https://github.com/microsoft/fluentui/commit/505433ac64f144c9cca456097413d6af4582e5ee) by beachball)

## [9.1.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.9)

Mon, 17 Apr 2023 17:54:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.8..@fluentui/react-accordion_v9.1.9)

### Patches

- Bump @fluentui/react-aria to v9.3.18 ([PR #27564](https://github.com/microsoft/fluentui/pull/27564) by beachball)
- Bump @fluentui/react-context-selector to v9.1.18 ([PR #27564](https://github.com/microsoft/fluentui/pull/27564) by beachball)
- Bump @fluentui/react-tabster to v9.6.5 ([PR #27564](https://github.com/microsoft/fluentui/pull/27564) by beachball)
- Bump @fluentui/react-utilities to v9.8.0 ([PR #27564](https://github.com/microsoft/fluentui/pull/27564) by beachball)

## [9.1.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.8)

Wed, 12 Apr 2023 09:31:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.7..@fluentui/react-accordion_v9.1.8)

### Patches

- Bump @fluentui/react-aria to v9.3.17 ([PR #27274](https://github.com/microsoft/fluentui/pull/27274) by beachball)
- Bump @fluentui/react-context-selector to v9.1.17 ([PR #27274](https://github.com/microsoft/fluentui/pull/27274) by beachball)
- Bump @fluentui/react-tabster to v9.6.4 ([PR #27274](https://github.com/microsoft/fluentui/pull/27274) by beachball)
- Bump @fluentui/react-utilities to v9.7.4 ([PR #27274](https://github.com/microsoft/fluentui/pull/27274) by beachball)

## [9.1.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.7)

Tue, 04 Apr 2023 18:44:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.5..@fluentui/react-accordion_v9.1.7)

### Patches

- Bump @fluentui/react-aria to v9.3.16 ([PR #27434](https://github.com/microsoft/fluentui/pull/27434) by beachball)
- Bump @fluentui/react-context-selector to v9.1.16 ([PR #27434](https://github.com/microsoft/fluentui/pull/27434) by beachball)
- Bump @fluentui/react-tabster to v9.6.3 ([PR #27434](https://github.com/microsoft/fluentui/pull/27434) by beachball)
- Bump @fluentui/react-utilities to v9.7.3 ([PR #27434](https://github.com/microsoft/fluentui/pull/27434) by beachball)

## [9.1.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.5)

Tue, 21 Mar 2023 21:23:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.4..@fluentui/react-accordion_v9.1.5)

### Patches

- fix: add node field to package.json exports map. ([PR #27154](https://github.com/microsoft/fluentui/pull/27154) by <EMAIL>)
- chore: Bumping version of @fluentui/react-icons to ^2.0.196. ([PR #27100](https://github.com/microsoft/fluentui/pull/27100) by <EMAIL>)
- chore: migrate to swc transpilation approach. ([PR #27250](https://github.com/microsoft/fluentui/pull/27250) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.15 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-context-selector to v9.1.15 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-shared-contexts to v9.3.2 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-tabster to v9.6.1 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-theme to v9.1.7 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-utilities to v9.7.2 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.20 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)

## [9.1.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.4)

Thu, 16 Mar 2023 14:36:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.3..@fluentui/react-accordion_v9.1.4)

### Patches

- Bump @fluentui/react-aria to v9.3.14 ([PR #27229](https://github.com/microsoft/fluentui/pull/27229) by beachball)
- Bump @fluentui/react-context-selector to v9.1.14 ([PR #27229](https://github.com/microsoft/fluentui/pull/27229) by beachball)
- Bump @fluentui/react-tabster to v9.6.0 ([PR #27229](https://github.com/microsoft/fluentui/pull/27229) by beachball)
- Bump @fluentui/react-utilities to v9.7.1 ([PR #27229](https://github.com/microsoft/fluentui/pull/27229) by beachball)

## [9.1.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.3)

Wed, 15 Mar 2023 10:19:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.2..@fluentui/react-accordion_v9.1.3)

### Patches

- Bump @fluentui/react-shared-contexts to v9.3.1 ([PR #27213](https://github.com/microsoft/fluentui/pull/27213) by beachball)
- Bump @fluentui/react-tabster to v9.5.7 ([PR #27213](https://github.com/microsoft/fluentui/pull/27213) by beachball)
- Bump @fluentui/react-theme to v9.1.6 ([PR #27213](https://github.com/microsoft/fluentui/pull/27213) by beachball)

## [9.1.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.2)

Mon, 13 Mar 2023 08:58:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.1..@fluentui/react-accordion_v9.1.2)

### Patches

- Bump @fluentui/react-aria to v9.3.13 ([PR #27161](https://github.com/microsoft/fluentui/pull/27161) by beachball)
- Bump @fluentui/react-context-selector to v9.1.13 ([PR #27161](https://github.com/microsoft/fluentui/pull/27161) by beachball)
- Bump @fluentui/react-tabster to v9.5.6 ([PR #27161](https://github.com/microsoft/fluentui/pull/27161) by beachball)
- Bump @fluentui/react-utilities to v9.7.0 ([PR #27161](https://github.com/microsoft/fluentui/pull/27161) by beachball)

## [9.1.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.1)

Fri, 10 Mar 2023 07:14:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.1.0..@fluentui/react-accordion_v9.1.1)

### Patches

- Bump @fluentui/react-aria to v9.3.12 ([PR #27157](https://github.com/microsoft/fluentui/pull/27157) by beachball)
- Bump @fluentui/react-context-selector to v9.1.12 ([PR #27157](https://github.com/microsoft/fluentui/pull/27157) by beachball)
- Bump @fluentui/react-tabster to v9.5.5 ([PR #27157](https://github.com/microsoft/fluentui/pull/27157) by beachball)
- Bump @fluentui/react-utilities to v9.6.2 ([PR #27157](https://github.com/microsoft/fluentui/pull/27157) by beachball)

## [9.1.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.1.0)

Wed, 08 Mar 2023 17:42:25 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.26..@fluentui/react-accordion_v9.1.0)

### Minor changes

- feat: Adding calls to custom style hooks derived from context. ([PR #27059](https://github.com/microsoft/fluentui/pull/27059) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.11 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)
- Bump @fluentui/react-context-selector to v9.1.11 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)
- Bump @fluentui/react-shared-contexts to v9.3.0 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)
- Bump @fluentui/react-tabster to v9.5.4 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)
- Bump @fluentui/react-utilities to v9.6.1 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)

## [9.0.26](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.26)

Wed, 22 Feb 2023 23:06:04 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.25..@fluentui/react-accordion_v9.0.26)

### Patches

- bugfix: uses min-height instead of height ([PR #26862](https://github.com/microsoft/fluentui/pull/26862) by <EMAIL>)

## [9.0.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.25)

Wed, 15 Feb 2023 11:44:52 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.24..@fluentui/react-accordion_v9.0.25)

### Patches

- Bump @fluentui/react-aria to v9.3.10 ([PR #26845](https://github.com/microsoft/fluentui/pull/26845) by beachball)
- Bump @fluentui/react-context-selector to v9.1.10 ([PR #26845](https://github.com/microsoft/fluentui/pull/26845) by beachball)
- Bump @fluentui/react-tabster to v9.5.3 ([PR #26845](https://github.com/microsoft/fluentui/pull/26845) by beachball)
- Bump @fluentui/react-utilities to v9.6.0 ([PR #26845](https://github.com/microsoft/fluentui/pull/26845) by beachball)

## [9.0.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.24)

Mon, 13 Feb 2023 23:43:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.23..@fluentui/react-accordion_v9.0.24)

### Patches

- Bump @fluentui/react-aria to v9.3.9 ([PR #26814](https://github.com/microsoft/fluentui/pull/26814) by beachball)
- Bump @fluentui/react-context-selector to v9.1.9 ([PR #26814](https://github.com/microsoft/fluentui/pull/26814) by beachball)
- Bump @fluentui/react-tabster to v9.5.2 ([PR #26814](https://github.com/microsoft/fluentui/pull/26814) by beachball)
- Bump @fluentui/react-utilities to v9.5.3 ([PR #26814](https://github.com/microsoft/fluentui/pull/26814) by beachball)

## [9.0.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.23)

Fri, 10 Feb 2023 08:50:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.22..@fluentui/react-accordion_v9.0.23)

### Patches

- Bump @fluentui/react-aria to v9.3.8 ([commit](https://github.com/microsoft/fluentui/commit/cc62f050f8231e8f21a2cf7dddf33003e0ba3931) by beachball)
- Bump @fluentui/react-context-selector to v9.1.8 ([commit](https://github.com/microsoft/fluentui/commit/cc62f050f8231e8f21a2cf7dddf33003e0ba3931) by beachball)
- Bump @fluentui/react-tabster to v9.5.1 ([commit](https://github.com/microsoft/fluentui/commit/cc62f050f8231e8f21a2cf7dddf33003e0ba3931) by beachball)
- Bump @fluentui/react-utilities to v9.5.2 ([commit](https://github.com/microsoft/fluentui/commit/cc62f050f8231e8f21a2cf7dddf33003e0ba3931) by beachball)

## [9.0.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.22)

Tue, 07 Feb 2023 14:13:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.21..@fluentui/react-accordion_v9.0.22)

### Patches

- Bump @fluentui/react-tabster to v9.5.0 ([PR #26732](https://github.com/microsoft/fluentui/pull/26732) by beachball)

## [9.0.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.21)

Tue, 31 Jan 2023 19:53:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.20..@fluentui/react-accordion_v9.0.21)

### Patches

- Bump @fluentui/react-aria to v9.3.7 ([PR #26557](https://github.com/microsoft/fluentui/pull/26557) by beachball)
- Bump @fluentui/react-context-selector to v9.1.7 ([PR #26557](https://github.com/microsoft/fluentui/pull/26557) by beachball)
- Bump @fluentui/react-tabster to v9.4.2 ([PR #26557](https://github.com/microsoft/fluentui/pull/26557) by beachball)
- Bump @fluentui/react-utilities to v9.5.1 ([PR #26557](https://github.com/microsoft/fluentui/pull/26557) by beachball)

## [9.0.20](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.20)

Thu, 26 Jan 2023 13:31:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.19..@fluentui/react-accordion_v9.0.20)

### Patches

- Bump @fluentui/react-aria to v9.3.6 ([PR #26496](https://github.com/microsoft/fluentui/pull/26496) by beachball)
- Bump @fluentui/react-context-selector to v9.1.6 ([PR #26496](https://github.com/microsoft/fluentui/pull/26496) by beachball)
- Bump @fluentui/react-shared-contexts to v9.2.0 ([PR #26496](https://github.com/microsoft/fluentui/pull/26496) by beachball)
- Bump @fluentui/react-tabster to v9.4.1 ([PR #26496](https://github.com/microsoft/fluentui/pull/26496) by beachball)
- Bump @fluentui/react-utilities to v9.5.0 ([PR #26496](https://github.com/microsoft/fluentui/pull/26496) by beachball)

## [9.0.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.19)

Wed, 18 Jan 2023 16:32:57 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.18..@fluentui/react-accordion_v9.0.19)

### Patches

- Bump @fluentui/react-tabster to v9.4.0 ([PR #26377](https://github.com/microsoft/fluentui/pull/26377) by beachball)

## [9.0.18](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.18)

Mon, 16 Jan 2023 08:38:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.17..@fluentui/react-accordion_v9.0.18)

### Patches

- Bump @fluentui/react-shared-contexts to v9.1.5 ([commit](https://github.com/microsoft/fluentui/commit/a870d8360e47f3ea03358c4e75e89e08a74845d7) by beachball)
- Bump @fluentui/react-tabster to v9.3.7 ([commit](https://github.com/microsoft/fluentui/commit/a870d8360e47f3ea03358c4e75e89e08a74845d7) by beachball)

## [9.0.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.17)

Mon, 09 Jan 2023 14:35:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.16..@fluentui/react-accordion_v9.0.17)

### Patches

- Bump @fluentui/react-aria to v9.3.5 ([PR #26255](https://github.com/microsoft/fluentui/pull/26255) by beachball)
- Bump @fluentui/react-context-selector to v9.1.5 ([PR #26255](https://github.com/microsoft/fluentui/pull/26255) by beachball)
- Bump @fluentui/react-tabster to v9.3.6 ([PR #26255](https://github.com/microsoft/fluentui/pull/26255) by beachball)
- Bump @fluentui/react-utilities to v9.4.0 ([PR #26255](https://github.com/microsoft/fluentui/pull/26255) by beachball)

## [9.0.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.16)

Wed, 04 Jan 2023 01:40:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.15..@fluentui/react-accordion_v9.0.16)

### Patches

- chore: Update Griffel to latest version ([PR #26045](https://github.com/microsoft/fluentui/pull/26045) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.4 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)
- Bump @fluentui/react-context-selector to v9.1.4 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)
- Bump @fluentui/react-tabster to v9.3.5 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)
- Bump @fluentui/react-utilities to v9.3.1 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.19 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)

## [9.0.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.15)

Wed, 21 Dec 2022 10:20:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.14..@fluentui/react-accordion_v9.0.15)

### Patches

- Bump @fluentui/react-shared-contexts to v9.1.4 ([commit](https://github.com/microsoft/fluentui/commit/66bf89f634cad4a275e957d7a2214c7e73ff8c2e) by beachball)
- Bump @fluentui/react-tabster to v9.3.4 ([commit](https://github.com/microsoft/fluentui/commit/66bf89f634cad4a275e957d7a2214c7e73ff8c2e) by beachball)
- Bump @fluentui/react-theme to v9.1.5 ([commit](https://github.com/microsoft/fluentui/commit/66bf89f634cad4a275e957d7a2214c7e73ff8c2e) by beachball)

## [9.0.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.14)

Tue, 20 Dec 2022 14:59:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.13..@fluentui/react-accordion_v9.0.14)

### Patches

- Bump @fluentui/react-aria to v9.3.3 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)
- Bump @fluentui/react-context-selector to v9.1.3 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)
- Bump @fluentui/react-shared-contexts to v9.1.3 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)
- Bump @fluentui/react-tabster to v9.3.3 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)
- Bump @fluentui/react-theme to v9.1.4 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)
- Bump @fluentui/react-utilities to v9.3.0 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)

## [9.0.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.13)

Mon, 05 Dec 2022 18:29:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.12..@fluentui/react-accordion_v9.0.13)

### Patches

- Bump @fluentui/react-shared-contexts to v9.1.2 ([PR #25798](https://github.com/microsoft/fluentui/pull/25798) by beachball)
- Bump @fluentui/react-tabster to v9.3.2 ([PR #25798](https://github.com/microsoft/fluentui/pull/25798) by beachball)
- Bump @fluentui/react-theme to v9.1.3 ([PR #25798](https://github.com/microsoft/fluentui/pull/25798) by beachball)

## [9.0.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.12)

Thu, 17 Nov 2022 23:05:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.11..@fluentui/react-accordion_v9.0.12)

### Patches

- Bump @fluentui/react-aria to v9.3.2 ([PR #25683](https://github.com/microsoft/fluentui/pull/25683) by beachball)
- Bump @fluentui/react-context-selector to v9.1.2 ([PR #25683](https://github.com/microsoft/fluentui/pull/25683) by beachball)
- Bump @fluentui/react-tabster to v9.3.1 ([PR #25683](https://github.com/microsoft/fluentui/pull/25683) by beachball)
- Bump @fluentui/react-utilities to v9.2.2 ([PR #25683](https://github.com/microsoft/fluentui/pull/25683) by beachball)

## [9.0.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.11)

Fri, 11 Nov 2022 14:57:51 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.10..@fluentui/react-accordion_v9.0.11)

### Patches

- Bump @fluentui/react-aria to v9.3.1 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-context-selector to v9.1.1 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-shared-contexts to v9.1.1 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-tabster to v9.3.0 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-theme to v9.1.2 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-utilities to v9.2.1 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.18 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)

## [9.0.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.10)

Wed, 02 Nov 2022 11:57:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.9..@fluentui/react-accordion_v9.0.10)

### Patches

- chore: Update Griffel to latest version ([PR #25412](https://github.com/microsoft/fluentui/pull/25412) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.0 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)
- Bump @fluentui/react-context-selector to v9.1.0 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)
- Bump @fluentui/react-shared-contexts to v9.1.0 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)
- Bump @fluentui/react-tabster to v9.2.1 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)
- Bump @fluentui/react-utilities to v9.2.0 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.17 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)

## [9.0.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.9)

Thu, 20 Oct 2022 08:39:56 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.8..@fluentui/react-accordion_v9.0.9)

### Patches

- chore: Migrate to new package structure. ([PR #25196](https://github.com/microsoft/fluentui/pull/25196) by <EMAIL>)
- chore: Bump peer deps to support React 18 ([PR #24972](https://github.com/microsoft/fluentui/pull/24972) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #25212](https://github.com/microsoft/fluentui/pull/25212) by <EMAIL>)
- Bump @fluentui/react-aria to v9.2.3 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-context-selector to v9.0.5 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-shared-contexts to v9.0.2 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-tabster to v9.2.0 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-theme to v9.1.1 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-utilities to v9.1.2 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.16 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)

## [9.0.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.8)

Thu, 13 Oct 2022 11:02:42 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.7..@fluentui/react-accordion_v9.0.8)

### Patches

- chore: Update Griffel to latest version ([PR #25075](https://github.com/microsoft/fluentui/pull/25075) by <EMAIL>)
- Bump @fluentui/react-aria to v9.2.2 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)
- Bump @fluentui/react-context-selector to v9.0.4 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)
- Bump @fluentui/react-tabster to v9.1.3 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)
- Bump @fluentui/react-utilities to v9.1.1 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.15 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)

## [9.0.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.7)

Mon, 03 Oct 2022 22:24:42 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.6..@fluentui/react-accordion_v9.0.7)

### Patches

- Bump @fluentui/react-aria to v9.2.1 ([PR #25055](https://github.com/microsoft/fluentui/pull/25055) by beachball)
- Bump @fluentui/react-tabster to v9.1.2 ([PR #25055](https://github.com/microsoft/fluentui/pull/25055) by beachball)

## [9.0.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.6)

Tue, 20 Sep 2022 20:55:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.5..@fluentui/react-accordion_v9.0.6)

### Patches

- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.14 ([PR #24869](https://github.com/microsoft/fluentui/pull/24869) by beachball)

## [9.0.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.5)

Thu, 15 Sep 2022 09:49:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.4..@fluentui/react-accordion_v9.0.5)

### Patches

- chore: fix no-context-default-value lint violations for cxe ([PR #24277](https://github.com/microsoft/fluentui/pull/24277) by <EMAIL>)
- fix(Refactor accordion expandIcon styling) ([PR #24597](https://github.com/microsoft/fluentui/pull/24597) by <EMAIL>)
- fix: add type=button to button slot ([PR #24327](https://github.com/microsoft/fluentui/pull/24327) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #24221](https://github.com/microsoft/fluentui/pull/24221) by <EMAIL>)
- Bump @fluentui/react-aria to v9.2.0 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-context-selector to v9.0.3 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-shared-contexts to v9.0.1 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-tabster to v9.1.1 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-theme to v9.1.0 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-utilities to v9.1.0 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.13 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)

## [9.0.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.4)

Wed, 03 Aug 2022 16:03:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.3..@fluentui/react-accordion_v9.0.4)

### Patches

- chore: Updates AccordionHeader to use useARIAButtonShorthand over useARIAButton ([PR #24032](https://github.com/microsoft/fluentui/pull/24032) by <EMAIL>)
- chore: Bump Griffel dependencies ([PR #24114](https://github.com/microsoft/fluentui/pull/24114) by <EMAIL>)
- Bump @fluentui/react-aria to v9.1.0 ([PR #24131](https://github.com/microsoft/fluentui/pull/24131) by beachball)
- Bump @fluentui/react-tabster to v9.1.0 ([PR #24131](https://github.com/microsoft/fluentui/pull/24131) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.12 ([PR #24131](https://github.com/microsoft/fluentui/pull/24131) by beachball)

## [9.0.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.3)

Thu, 14 Jul 2022 21:21:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.2..@fluentui/react-accordion_v9.0.3)

### Patches

- fix: Fixing bad version bump of @fluentui/react-utilities. ([PR #23920](https://github.com/microsoft/fluentui/pull/23920) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.2 ([PR #23918](https://github.com/microsoft/fluentui/pull/23918) by beachball)
- Bump @fluentui/react-context-selector to v9.0.2 ([PR #23918](https://github.com/microsoft/fluentui/pull/23918) by beachball)
- Bump @fluentui/react-tabster to v9.0.3 ([PR #23918](https://github.com/microsoft/fluentui/pull/23918) by beachball)
- Bump @fluentui/react-utilities to v9.0.2 ([PR #23918](https://github.com/microsoft/fluentui/pull/23918) by beachball)

## [9.0.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.2)

Thu, 14 Jul 2022 17:06:25 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.1..@fluentui/react-accordion_v9.0.2)

### Patches

- chore: Update @fluentui/react-icons dependency to v2.0.175 ([PR #23812](https://github.com/microsoft/fluentui/pull/23812) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.1 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)
- Bump @fluentui/react-context-selector to v9.0.1 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)
- Bump @fluentui/react-tabster to v9.0.2 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)
- Bump @fluentui/react-utilities to v9.0.1-0 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.11 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)

## [9.0.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.1)

Tue, 28 Jun 2022 17:39:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0..@fluentui/react-accordion_v9.0.1)

### Patches

- fix: Use caret dependency range for Griffel ([PR #23754](https://github.com/microsoft/fluentui/pull/23754) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.0.1 ([PR #23754](https://github.com/microsoft/fluentui/pull/23754) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.10 ([PR #23754](https://github.com/microsoft/fluentui/pull/23754) by beachball)

## [9.0.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0)

Tue, 28 Jun 2022 15:14:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.14..@fluentui/react-accordion_v9.0.0)

### Patches

- feat: Initial 9.0.0 release ([PR #23733](https://github.com/microsoft/fluentui/pull/23733) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-shared-contexts to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-tabster to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-theme to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-utilities to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.9 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)

### Changes

- Bump Griffel dependencies ([PR #23688](https://github.com/microsoft/fluentui/pull/23688) by <EMAIL>)
- chore: Adding scheduler as a peer dependency since this package has a dependency on @fluentui/react-context-selector. ([PR #23681](https://github.com/microsoft/fluentui/pull/23681) by <EMAIL>)
- chore: Update @fluentui/react-icons to latest version ([PR #23459](https://github.com/microsoft/fluentui/pull/23459) by <EMAIL>)
- Update 9.0.0-rc dependencies to use caret range ([PR #23732](https://github.com/microsoft/fluentui/pull/23732) by <EMAIL>)
- fix: Removing user-select: 'none' from AccordionHeader styles. ([PR #23713](https://github.com/microsoft/fluentui/pull/23713) by <EMAIL>)
- chore: updates AccordionHeader to follow design spec tokens ([PR #23295](https://github.com/microsoft/fluentui/pull/23295) by <EMAIL>)

## [9.0.0-rc.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.14)

Thu, 23 Jun 2022 14:25:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.13..@fluentui/react-accordion_v9.0.0-rc.14)

### Changes

- Bump @fluentui/react-shared-contexts to v9.0.0-rc.11 ([PR #23608](https://github.com/microsoft/fluentui/pull/23608) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.14 ([PR #23608](https://github.com/microsoft/fluentui/pull/23608) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.10 ([PR #23608](https://github.com/microsoft/fluentui/pull/23608) by beachball)

## [9.0.0-rc.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.13)

Tue, 31 May 2022 21:28:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.12..@fluentui/react-accordion_v9.0.0-rc.13)

### Changes

- chore: Update Griffel to latest version ([PR #23275](https://github.com/microsoft/fluentui/pull/23275) by <EMAIL>)
- updates import to react-shared-components ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.10 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-rc.10 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)
- Bump @fluentui/react-shared-contexts to v9.0.0-rc.10 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.13 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.10 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.8 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)

## [9.0.0-rc.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.12)

Thu, 26 May 2022 21:01:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.11..@fluentui/react-accordion_v9.0.0-rc.12)

### Changes

- Bump @fluentui/react-tabster to v9.0.0-rc.12 ([PR #23267](https://github.com/microsoft/fluentui/pull/23267) by beachball)

## [9.0.0-rc.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.11)

Mon, 23 May 2022 18:56:40 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.10..@fluentui/react-accordion_v9.0.0-rc.11)

### Changes

- BREAKING: stop exporting AccordionContext and AccordionItemContext ([PR #23092](https://github.com/microsoft/fluentui/pull/23092) by <EMAIL>)
- Removing <componentName>ClassName exports. ([PR #23092](https://github.com/microsoft/fluentui/pull/23092) by <EMAIL>)
- Bump @fluentui/react-shared-contexts to v9.0.0-rc.9 ([PR #23146](https://github.com/microsoft/fluentui/pull/23146) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.11 ([PR #23146](https://github.com/microsoft/fluentui/pull/23146) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.9 ([PR #23146](https://github.com/microsoft/fluentui/pull/23146) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.7 ([PR #23146](https://github.com/microsoft/fluentui/pull/23146) by beachball)

## [9.0.0-rc.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.10)

Mon, 23 May 2022 12:13:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.9..@fluentui/react-accordion_v9.0.0-rc.10)

### Changes

- chore: Update Griffel to latest version ([PR #23029](https://github.com/microsoft/fluentui/pull/23029) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #22894](https://github.com/microsoft/fluentui/pull/22894) by <EMAIL>)
- react-accordion: ship rolluped only dts. ([PR #23061](https://github.com/microsoft/fluentui/pull/23061) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.9 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-rc.9 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-shared-contexts to v9.0.0-rc.8 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.10 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.8 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.9 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.6 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)

## [9.0.0-rc.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.9)

Thu, 05 May 2022 18:26:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.8..@fluentui/react-accordion_v9.0.0-rc.9)

### Changes

- Bump @fluentui/react-aria to v9.0.0-rc.8 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-rc.8 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-shared-contexts to v9.0.0-rc.7 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.9 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.7 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.8 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.5 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)

## [9.0.0-rc.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.8)

Wed, 04 May 2022 13:26:37 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.7..@fluentui/react-accordion_v9.0.0-rc.8)

### Changes

- remove star exports ([PR #22682](https://github.com/microsoft/fluentui/pull/22682) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.7 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-rc.7 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-shared-contexts to v9.0.0-rc.6 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.8 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.6 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.7 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)

## [9.0.0-rc.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.7)

Mon, 25 Apr 2022 09:32:16 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.6..@fluentui/react-accordion_v9.0.0-rc.7)

### Changes

- Adjusting accordion keyboard navigation. ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.0.0-rc.7 ([PR #22601](https://github.com/microsoft/fluentui/pull/22601) by beachball)

## [9.0.0-rc.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.6)

Tue, 19 Apr 2022 19:17:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.5..@fluentui/react-accordion_v9.0.0-rc.6)

### Changes

- Add static classnames to Accordion ([PR #21960](https://github.com/microsoft/fluentui/pull/21960) by <EMAIL>)
- add missing dependencies ([PR #21924](https://github.com/microsoft/fluentui/pull/21924) by <EMAIL>)
- update react-icons version to ^2.0.166-rc.3 from ^2.0.159-beta.10 ([PR #22512](https://github.com/microsoft/fluentui/pull/22512) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #21976](https://github.com/microsoft/fluentui/pull/21976) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.6 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-rc.6 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-shared-contexts to v9.0.0-rc.5 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.6 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.5 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.6 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.4 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)

## [9.0.0-rc.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.5)

Fri, 04 Mar 2022 05:17:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.4..@fluentui/react-accordion_v9.0.0-rc.5)

### Changes

- Adding explicit export maps on all consumer packages for FUIR 8 and 9. ([PR #21508](https://github.com/microsoft/fluentui/pull/21508) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.5 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-rc.5 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.5 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.4 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.5 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)

## [9.0.0-rc.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.4)

Tue, 01 Mar 2022 02:17:40 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.3..@fluentui/react-accordion_v9.0.0-rc.4)

### Changes

- Bump @fluentui/react-aria to v9.0.0-rc.4 ([PR #21884](https://github.com/microsoft/fluentui/pull/21884) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-rc.4 ([PR #21884](https://github.com/microsoft/fluentui/pull/21884) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.4 ([PR #21884](https://github.com/microsoft/fluentui/pull/21884) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.4 ([PR #21884](https://github.com/microsoft/fluentui/pull/21884) by beachball)

## [9.0.0-rc.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.3)

Fri, 18 Feb 2022 13:35:37 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-rc.1..@fluentui/react-accordion_v9.0.0-rc.3)

### Changes

- Updates initial open items value to be empty on every case ([PR #21728](https://github.com/microsoft/fluentui/pull/21728) by <EMAIL>)
- fix: Source maps contain original source code ([PR #21690](https://github.com/microsoft/fluentui/pull/21690) by <EMAIL>)
- Breaking change: navigable becomes navigation ([PR #21729](https://github.com/microsoft/fluentui/pull/21729) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)

## [9.0.0-rc.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-rc.1)

Thu, 10 Feb 2022 08:52:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-beta.5..@fluentui/react-accordion_v9.0.0-rc.1)

### Changes

- update @fluentui/react-icons package ([PR #21498](https://github.com/microsoft/fluentui/pull/21498) by <EMAIL>)
- update styles to not use CSS shorthands ([PR #20800](https://github.com/microsoft/fluentui/pull/20800) by <EMAIL>)
- remove export of commons types ([PR #21660](https://github.com/microsoft/fluentui/pull/21660) by <EMAIL>)
- Refactor component Slot typings ([PR #21518](https://github.com/microsoft/fluentui/pull/21518) by <EMAIL>)
- Allow React 17 in peerDependencies. ([PR #21544](https://github.com/microsoft/fluentui/pull/21544) by <EMAIL>)
- Remove component's shorthandProps array ([PR #21134](https://github.com/microsoft/fluentui/pull/21134) by <EMAIL>)
- use Griffel packages ([PR #21394](https://github.com/microsoft/fluentui/pull/21394) by <EMAIL>)
- Using ComponentSlotProps instead of ObjectShorthandProps. ([PR #20890](https://github.com/microsoft/fluentui/pull/20890) by <EMAIL>)
- Removes children as a slot from AccordionHeader ([PR #21285](https://github.com/microsoft/fluentui/pull/21285) by <EMAIL>)
- Bump Fluent UI packages to 9.0.0-rc ([PR #21623](https://github.com/microsoft/fluentui/pull/21623) by <EMAIL>)
- update semantic elements and ARIA roles in react-accordion, add heading level story ([PR #21401](https://github.com/microsoft/fluentui/pull/21401) by <EMAIL>)
- Update react-icons usage to resizable icons ([PR #21074](https://github.com/microsoft/fluentui/pull/21074) by <EMAIL>)
- BREAKING: Rename component hooks add the suffix \_unstable, as their API has not been finalized yet ([PR #21365](https://github.com/microsoft/fluentui/pull/21365) by <EMAIL>)
- Updating based on changes to composition types. ([PR #20891](https://github.com/microsoft/fluentui/pull/20891) by <EMAIL>)
- Updating packages based on changes to focusIndicator functions to remove functions from makeStyles in @fluentui/react-tabster. ([PR #21035](https://github.com/microsoft/fluentui/pull/21035) by <EMAIL>)
- Replacing use of functions in makeStyles with direct use of tokens. ([PR #21036](https://github.com/microsoft/fluentui/pull/21036) by <EMAIL>)
- Remove AccordionHeaderExpandIcon, and use ChevronRightRegular from @fluentui/react-icons instead ([PR #21218](https://github.com/microsoft/fluentui/pull/21218) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)

## [9.0.0-beta.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-beta.5)

Thu, 25 Nov 2021 08:34:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-beta.4..@fluentui/react-accordion_v9.0.0-beta.5)

### Changes

- Bump @fluentui/react-aria to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-beta.5 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-theme to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)

## [9.0.0-beta.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-beta.4)

Fri, 12 Nov 2021 13:25:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-beta.3..@fluentui/react-accordion_v9.0.0-beta.4)

### Changes

- export static classes for components ([PR #20451](https://github.com/microsoft/fluentui/pull/20451) by <EMAIL>)
- Updating AccordionHeader tests to match changes in useARIAButton. ([PR #20342](https://github.com/microsoft/fluentui/pull/20342) by <EMAIL>)
- Updated beta and RC components to ES2019 ([PR #20405](https://github.com/microsoft/fluentui/pull/20405) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-beta.4 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-theme to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)

## [9.0.0-beta.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-beta.3)

Wed, 27 Oct 2021 12:14:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-beta.2..@fluentui/react-accordion_v9.0.0-beta.3)

### Changes

- added styling of documentation ([PR #20193](https://github.com/microsoft/fluentui/pull/20193) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-beta.3 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-theme to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)

## [9.0.0-beta.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-beta.2)

Tue, 12 Oct 2021 19:45:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-beta.1..@fluentui/react-accordion_v9.0.0-beta.2)

### Changes

- Fix a11y errors on aria-expanded and disabled state ([PR #20132](https://github.com/microsoft/fluentui/pull/20132) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.0.0-beta.2 ([PR #20132](https://github.com/microsoft/fluentui/pull/20132) by beachball)

## [9.0.0-beta.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-beta.1)

Wed, 06 Oct 2021 10:37:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.90..@fluentui/react-accordion_v9.0.0-beta.1)

### Changes

- Bump all v9 components to beta prerelease tag ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-theme to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)

## [9.0.0-alpha.90](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.90)

Tue, 05 Oct 2021 12:47:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.89..@fluentui/react-accordion_v9.0.0-alpha.90)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.45 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.78 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.73 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.60 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.53 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.22 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)

## [9.0.0-alpha.89](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.89)

Tue, 05 Oct 2021 09:28:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.88..@fluentui/react-accordion_v9.0.0-alpha.89)

### Changes

- Adds ForwardRefComponent to components declaration ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-alpha.44 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-alpha.39 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.77 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.72 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.56 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.59 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.52 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.21 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)

## [9.0.0-alpha.88](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.88)

Fri, 01 Oct 2021 14:13:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.86..@fluentui/react-accordion_v9.0.0-alpha.88)

### Changes

- Bump v9 prerelease versions to rerelease ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-alpha.43 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-alpha.38 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.76 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.71 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-theme to v9.0.0-alpha.26 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.55 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.58 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.51 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.20 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)

## [9.0.0-alpha.86](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.86)

Fri, 01 Oct 2021 12:30:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.85..@fluentui/react-accordion_v9.0.0-alpha.86)

### Changes

- Updating API to reflect aria button changes. ([PR #18814](https://github.com/microsoft/fluentui/pull/18814) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-alpha.41 ([PR #18814](https://github.com/microsoft/fluentui/pull/18814) by beachball)

## [9.0.0-alpha.85](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.85)

Fri, 01 Oct 2021 09:44:56 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.84..@fluentui/react-accordion_v9.0.0-alpha.85)

### Changes

- Use new default pseudo element focus outline style ([PR #19990](https://github.com/microsoft/fluentui/pull/19990) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.0.0-alpha.69 ([PR #19990](https://github.com/microsoft/fluentui/pull/19990) by beachball)

## [9.0.0-alpha.84](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.84)

Wed, 29 Sep 2021 08:06:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.83..@fluentui/react-accordion_v9.0.0-alpha.84)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.40 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.74 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.68 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/react-theme to v9.0.0-alpha.24 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.56 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.49 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.18 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)

## [9.0.0-alpha.83](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.83)

Mon, 27 Sep 2021 08:06:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.82..@fluentui/react-accordion_v9.0.0-alpha.83)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.39 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-alpha.36 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.73 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.67 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.53 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.55 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.48 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.17 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)

## [9.0.0-alpha.82](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.82)

Fri, 24 Sep 2021 09:17:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.81..@fluentui/react-accordion_v9.0.0-alpha.82)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.38 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-alpha.35 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.72 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.66 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.52 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.54 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.47 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.16 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)

## [9.0.0-alpha.81](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.81)

Thu, 23 Sep 2021 08:21:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.80..@fluentui/react-accordion_v9.0.0-alpha.81)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.37 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-alpha.34 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.71 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.65 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.51 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.53 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.46 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.15 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)

## [9.0.0-alpha.80](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.80)

Wed, 22 Sep 2021 10:10:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.79..@fluentui/react-accordion_v9.0.0-alpha.80)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.36 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-context-selector to v9.0.0-alpha.33 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.70 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.64 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.50 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.52 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.45 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.14 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)

## [9.0.0-alpha.79](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.79)

Tue, 21 Sep 2021 07:42:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.78..@fluentui/react-accordion_v9.0.0-alpha.79)

### Changes

- Updating to types over interfaces ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-alpha.35 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.69 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.63 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/react-theme to v9.0.0-alpha.23 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.51 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.44 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.13 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)

## [9.0.0-alpha.78](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.78)

Mon, 20 Sep 2021 07:36:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.77..@fluentui/react-accordion_v9.0.0-alpha.78)

### Changes

- Bump @fluentui/react-accordion to v9.0.0-alpha.78 ([PR #19844](https://github.com/microsoft/fluentui/pull/19844) by <EMAIL>)

## [9.0.0-alpha.77](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.77)

Fri, 17 Sep 2021 07:35:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.76..@fluentui/react-accordion_v9.0.0-alpha.77)

### Changes

- Bump @fluentui/react-accordion to v9.0.0-alpha.77 ([PR #19840](https://github.com/microsoft/fluentui/pull/19840) by <EMAIL>)

## [9.0.0-alpha.76](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.76)

Thu, 16 Sep 2021 07:38:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.75..@fluentui/react-accordion_v9.0.0-alpha.76)

### Changes

- Fix typings in React.forwardRef ([PR #19815](https://github.com/microsoft/fluentui/pull/19815) by <EMAIL>)
- Bump @fluentui/react-accordion to v9.0.0-alpha.76 ([PR #19815](https://github.com/microsoft/fluentui/pull/19815) by <EMAIL>)

## [9.0.0-alpha.75](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.75)

Tue, 14 Sep 2021 20:09:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.74..@fluentui/react-accordion_v9.0.0-alpha.75)

### Changes

- Bump @fluentui/react-accordion to v9.0.0-alpha.75 ([PR #19155](https://github.com/microsoft/fluentui/pull/19155) by <EMAIL>)

## [9.0.0-alpha.74](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.74)

Tue, 14 Sep 2021 07:38:18 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.73..@fluentui/react-accordion_v9.0.0-alpha.74)

### Changes

- Bump @fluentui/react-accordion to v9.0.0-alpha.74 ([PR #19605](https://github.com/microsoft/fluentui/pull/19605) by <EMAIL>)

## [9.0.0-alpha.73](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.73)

Fri, 10 Sep 2021 16:31:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.72..@fluentui/react-accordion_v9.0.0-alpha.73)

### Changes

- chore(v9): Move all internal v9 dependencies from caret to fixed version ([PR #19748](https://github.com/microsoft/fluentui/pull/19748) by <EMAIL>)
- Bump @fluentui/react-accordion to v9.0.0-alpha.73 ([PR #19748](https://github.com/microsoft/fluentui/pull/19748) by <EMAIL>)

## [9.0.0-alpha.72](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.72)

Fri, 10 Sep 2021 07:39:51 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.71..@fluentui/react-accordion_v9.0.0-alpha.72)

### Changes

- Refactor ObjectShorthandProps into IntrinsicShorthandProps ([PR #19642](https://github.com/microsoft/fluentui/pull/19642) by <EMAIL>)

## [9.0.0-alpha.71](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.71)

Mon, 06 Sep 2021 07:34:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.70..@fluentui/react-accordion_v9.0.0-alpha.71)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.44 ([PR #19640](https://github.com/microsoft/fluentui/pull/19640) by <EMAIL>)

## [9.0.0-alpha.70](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.70)

Thu, 02 Sep 2021 07:36:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.69..@fluentui/react-accordion_v9.0.0-alpha.70)

### Patches

- Bump @fluentui/react-conformance to v0.4.5 ([PR #19590](https://github.com/microsoft/fluentui/pull/19590) by <EMAIL>)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.43 ([PR #19065](https://github.com/microsoft/fluentui/pull/19065) by <EMAIL>)

## [9.0.0-alpha.69](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.69)

Wed, 01 Sep 2021 07:39:56 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.68..@fluentui/react-accordion_v9.0.0-alpha.69)

### Changes

- Updates react-accordion to use root as slot ([PR #19483](https://github.com/microsoft/fluentui/pull/19483) by <EMAIL>)

## [9.0.0-alpha.68](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.68)

Tue, 31 Aug 2021 07:37:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.67..@fluentui/react-accordion_v9.0.0-alpha.68)

### Changes

- Bump @fluentui/react-tabster to v9.0.0-alpha.54 ([PR #19534](https://github.com/microsoft/fluentui/pull/19534) by <EMAIL>)

## [9.0.0-alpha.67](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.67)

Mon, 30 Aug 2021 07:35:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.66..@fluentui/react-accordion_v9.0.0-alpha.67)

### Changes

- Updating TypeScript type-only imports/exports to use import/export type syntax. ([PR #19485](https://github.com/microsoft/fluentui/pull/19485) by <EMAIL>)

## [9.0.0-alpha.66](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.66)

Fri, 27 Aug 2021 07:33:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.65..@fluentui/react-accordion_v9.0.0-alpha.66)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.40 ([PR #19462](https://github.com/microsoft/fluentui/pull/19462) by <EMAIL>)

## [9.0.0-alpha.65](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.65)

Thu, 26 Aug 2021 07:35:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.64..@fluentui/react-accordion_v9.0.0-alpha.65)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.39 ([PR #19486](https://github.com/microsoft/fluentui/pull/19486) by <EMAIL>)

## [9.0.0-alpha.64](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.64)

Fri, 20 Aug 2021 07:37:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.63..@fluentui/react-accordion_v9.0.0-alpha.64)

### Changes

- Update .npmignore ([PR #19441](https://github.com/microsoft/fluentui/pull/19441) by <EMAIL>)

## [9.0.0-alpha.63](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.63)

Thu, 19 Aug 2021 07:41:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.62..@fluentui/react-accordion_v9.0.0-alpha.63)

### Changes

- Updates react-accordion on slot null rendering ([PR #19273](https://github.com/microsoft/fluentui/pull/19273) by <EMAIL>)

## [9.0.0-alpha.62](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.62)

Fri, 13 Aug 2021 07:36:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.61..@fluentui/react-accordion_v9.0.0-alpha.62)

### Changes

- chore(Accordion): implement useContextValues() pattern ([PR #19339](https://github.com/microsoft/fluentui/pull/19339) by <EMAIL>)

## [9.0.0-alpha.61](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.61)

Wed, 11 Aug 2021 07:34:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.60..@fluentui/react-accordion_v9.0.0-alpha.61)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.35 ([PR #19256](https://github.com/microsoft/fluentui/pull/19256) by <EMAIL>)

## [9.0.0-alpha.60](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.60)

Fri, 06 Aug 2021 07:35:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.59..@fluentui/react-accordion_v9.0.0-alpha.60)

### Changes

- Removes descendants API in favor of explicit values ([PR #19189](https://github.com/microsoft/fluentui/pull/19189) by <EMAIL>)

## [9.0.0-alpha.59](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.59)

Tue, 03 Aug 2021 07:39:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.58..@fluentui/react-accordion_v9.0.0-alpha.59)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.3 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.4 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.33 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)

## [9.0.0-alpha.58](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.58)

Mon, 02 Aug 2021 07:36:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.57..@fluentui/react-accordion_v9.0.0-alpha.58)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.36 ([PR #19204](https://github.com/microsoft/fluentui/pull/19204) by <EMAIL>)

## [9.0.0-alpha.57](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.57)

Mon, 26 Jul 2021 07:37:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.56..@fluentui/react-accordion_v9.0.0-alpha.57)

### Changes

- Migrate to useControllableState hook ([PR #19094](https://github.com/microsoft/fluentui/pull/19094) by <EMAIL>)

## [9.0.0-alpha.56](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.56)

Fri, 23 Jul 2021 07:38:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.55..@fluentui/react-accordion_v9.0.0-alpha.56)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.30 ([PR #19041](https://github.com/microsoft/fluentui/pull/19041) by <EMAIL>)

## [9.0.0-alpha.55](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.55)

Thu, 22 Jul 2021 07:36:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.54..@fluentui/react-accordion_v9.0.0-alpha.55)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.29 ([PR #19038](https://github.com/microsoft/fluentui/pull/19038) by <EMAIL>)

## [9.0.0-alpha.54](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.54)

Tue, 20 Jul 2021 22:23:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.53..@fluentui/react-accordion_v9.0.0-alpha.54)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.28 ([PR #18998](https://github.com/microsoft/fluentui/pull/18998) by <EMAIL>)

## [9.0.0-alpha.53](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.53)

Fri, 16 Jul 2021 22:53:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.52..@fluentui/react-accordion_v9.0.0-alpha.53)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.26 ([PR #18973](https://github.com/microsoft/fluentui/pull/18973) by <EMAIL>)

## [9.0.0-alpha.52](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.52)

Thu, 15 Jul 2021 07:36:18 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.51..@fluentui/react-accordion_v9.0.0-alpha.52)

### Changes

- Fix react-accordion due to changes on slots typings ([PR #18861](https://github.com/microsoft/fluentui/pull/18861) by <EMAIL>)

## [9.0.0-alpha.51](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.51)

Tue, 13 Jul 2021 22:32:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.50..@fluentui/react-accordion_v9.0.0-alpha.51)

### Patches

- Bump @fluentui/react-conformance to v0.4.3 ([PR #18925](https://github.com/microsoft/fluentui/pull/18925) by <EMAIL>)

## [9.0.0-alpha.50](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.50)

Tue, 13 Jul 2021 07:35:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.49..@fluentui/react-accordion_v9.0.0-alpha.50)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.24 ([PR #18560](https://github.com/microsoft/fluentui/pull/18560) by <EMAIL>)

## [9.0.0-alpha.49](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.49)

Mon, 12 Jul 2021 07:33:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.48..@fluentui/react-accordion_v9.0.0-alpha.49)

### Changes

- Forces children prop with Fragment in useAccordionHeader ([PR #18879](https://github.com/microsoft/fluentui/pull/18879) by <EMAIL>)
- Refactor Accordion component internals ([PR #18873](https://github.com/microsoft/fluentui/pull/18873) by <EMAIL>)
- Update prop merging mechanism ([PR #18813](https://github.com/microsoft/fluentui/pull/18813) by <EMAIL>)

## [9.0.0-alpha.48](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.48)

Fri, 09 Jul 2021 07:39:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.47..@fluentui/react-accordion_v9.0.0-alpha.48)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.2 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.2 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.23 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)

## [9.0.0-alpha.47](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.47)

Fri, 02 Jul 2021 23:15:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.46..@fluentui/react-accordion_v9.0.0-alpha.47)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.22 ([PR #18816](https://github.com/microsoft/fluentui/pull/18816) by <EMAIL>)

## [9.0.0-alpha.46](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.46)

Fri, 02 Jul 2021 07:37:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.45..@fluentui/react-accordion_v9.0.0-alpha.46)

### Changes

- Rename typings and getSlots to have the Compat Suffix ([PR #18796](https://github.com/microsoft/fluentui/pull/18796) by <EMAIL>)

## [9.0.0-alpha.45](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.45)

Thu, 01 Jul 2021 07:35:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.44..@fluentui/react-accordion_v9.0.0-alpha.45)

### Changes

- remove redundant dependency on react-theme-provider ([PR #18710](https://github.com/microsoft/fluentui/pull/18710) by <EMAIL>)
- Fixing bug in start script of converged packages. ([PR #18768](https://github.com/microsoft/fluentui/pull/18768) by <EMAIL>)

## [9.0.0-alpha.44](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.44)

Wed, 30 Jun 2021 07:38:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.43..@fluentui/react-accordion_v9.0.0-alpha.44)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.19 ([PR #18695](https://github.com/microsoft/fluentui/pull/18695) by <EMAIL>)

## [9.0.0-alpha.43](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.43)

Tue, 29 Jun 2021 07:33:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.42..@fluentui/react-accordion_v9.0.0-alpha.43)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.18 ([PR #18169](https://github.com/microsoft/fluentui/pull/18169) by <EMAIL>)

## [9.0.0-alpha.42](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.42)

Tue, 22 Jun 2021 07:35:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.41..@fluentui/react-accordion_v9.0.0-alpha.42)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.17 ([PR #18397](https://github.com/microsoft/fluentui/pull/18397) by <EMAIL>)

## [9.0.0-alpha.41](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.41)

Mon, 21 Jun 2021 07:34:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.40..@fluentui/react-accordion_v9.0.0-alpha.41)

### Changes

- Adds react-aria ([PR #18597](https://github.com/microsoft/fluentui/pull/18597) by <EMAIL>)

## [9.0.0-alpha.40](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.40)

Wed, 16 Jun 2021 07:34:24 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.39..@fluentui/react-accordion_v9.0.0-alpha.40)

### Changes

- Refactor from makeMergePropsCompat to makeMergeProps ([PR #18567](https://github.com/microsoft/fluentui/pull/18567) by <EMAIL>)

## [9.0.0-alpha.39](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.39)

Tue, 15 Jun 2021 07:40:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.38..@fluentui/react-accordion_v9.0.0-alpha.39)

### Changes

- Migrates react-accordion ([PR #18553](https://github.com/microsoft/fluentui/pull/18553) by <EMAIL>)

## [9.0.0-alpha.38](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.38)

Wed, 09 Jun 2021 07:33:38 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.37..@fluentui/react-accordion_v9.0.0-alpha.38)

### Changes

- Adds focus indicator style rule ([PR #18419](https://github.com/microsoft/fluentui/pull/18419) by <EMAIL>)

## [9.0.0-alpha.37](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.37)

Mon, 07 Jun 2021 07:38:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.36..@fluentui/react-accordion_v9.0.0-alpha.37)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.1 ([PR #18437](https://github.com/microsoft/fluentui/pull/18437) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.1 ([PR #18437](https://github.com/microsoft/fluentui/pull/18437) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #18437](https://github.com/microsoft/fluentui/pull/18437) by <EMAIL>)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.15 ([PR #18437](https://github.com/microsoft/fluentui/pull/18437) by <EMAIL>)

## [9.0.0-alpha.36](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.36)

Fri, 04 Jun 2021 07:37:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.35..@fluentui/react-accordion_v9.0.0-alpha.36)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.16 ([PR #18168](https://github.com/microsoft/fluentui/pull/18168) by <EMAIL>)

## [9.0.0-alpha.35](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.35)

Thu, 03 Jun 2021 07:36:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.34..@fluentui/react-accordion_v9.0.0-alpha.35)

### Changes

- Bump @fluentui/react-tabster to v9.0.0-alpha.31 ([PR #18401](https://github.com/microsoft/fluentui/pull/18401) by <EMAIL>)

## [9.0.0-alpha.34](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.34)

Wed, 02 Jun 2021 07:37:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.33..@fluentui/react-accordion_v9.0.0-alpha.34)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.12 ([PR #18404](https://github.com/microsoft/fluentui/pull/18404) by <EMAIL>)

## [9.0.0-alpha.33](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.33)

Wed, 26 May 2021 07:35:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.32..@fluentui/react-accordion_v9.0.0-alpha.33)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.25 ([PR #18323](https://github.com/microsoft/fluentui/pull/18323) by <EMAIL>)

## [9.0.0-alpha.32](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.32)

Fri, 21 May 2021 07:34:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.31..@fluentui/react-accordion_v9.0.0-alpha.32)

### Changes

- Bump @fluentui/react-tabster to v9.0.0-alpha.27 ([PR #18238](https://github.com/microsoft/fluentui/pull/18238) by <EMAIL>)

## [9.0.0-alpha.31](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.31)

Thu, 20 May 2021 07:41:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.30..@fluentui/react-accordion_v9.0.0-alpha.31)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.0 ([PR #18024](https://github.com/microsoft/fluentui/pull/18024) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.0 ([PR #17577](https://github.com/microsoft/fluentui/pull/17577) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #18024](https://github.com/microsoft/fluentui/pull/18024) by <EMAIL>)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.10 ([PR #18024](https://github.com/microsoft/fluentui/pull/18024) by <EMAIL>)

## [9.0.0-alpha.30](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.30)

Wed, 19 May 2021 07:34:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.29..@fluentui/react-accordion_v9.0.0-alpha.30)

### Patches

- Bump @fluentui/react-conformance to v0.3.1 ([PR #18194](https://github.com/microsoft/fluentui/pull/18194) by <EMAIL>)

### Changes

- chore: add more Babel plugins ([PR #18037](https://github.com/microsoft/fluentui/pull/18037) by <EMAIL>)

## [9.0.0-alpha.29](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.29)

Tue, 18 May 2021 07:34:38 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.28..@fluentui/react-accordion_v9.0.0-alpha.29)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.8 ([PR #18171](https://github.com/microsoft/fluentui/pull/18171) by <EMAIL>)

## [9.0.0-alpha.28](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.28)

Thu, 13 May 2021 07:36:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.27..@fluentui/react-accordion_v9.0.0-alpha.28)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.7 ([PR #18039](https://github.com/microsoft/fluentui/pull/18039) by <EMAIL>)

## [9.0.0-alpha.27](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.27)

Wed, 12 May 2021 07:36:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.26..@fluentui/react-accordion_v9.0.0-alpha.27)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.6 ([PR #18097](https://github.com/microsoft/fluentui/pull/18097) by <EMAIL>)

## [9.0.0-alpha.26](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.26)

Mon, 10 May 2021 07:36:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.25..@fluentui/react-accordion_v9.0.0-alpha.26)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.5 ([PR #18095](https://github.com/microsoft/fluentui/pull/18095) by <EMAIL>)

## [9.0.0-alpha.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.25)

Wed, 05 May 2021 07:36:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.24..@fluentui/react-accordion_v9.0.0-alpha.25)

### Changes

- transform styles with Babel plugin ([PR #16534](https://github.com/microsoft/fluentui/pull/16534) by <EMAIL>)

## [9.0.0-alpha.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.24)

Mon, 03 May 2021 07:45:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.23..@fluentui/react-accordion_v9.0.0-alpha.24)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.21 ([PR #18005](https://github.com/microsoft/fluentui/pull/18005) by <EMAIL>)

## [9.0.0-alpha.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.23)

Fri, 30 Apr 2021 07:42:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.22..@fluentui/react-accordion_v9.0.0-alpha.23)

### Patches

- Bump @fluentui/eslint-plugin to v1.2.0 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.3.0 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)

### Changes

- update snapshots ([PR #17924](https://github.com/microsoft/fluentui/pull/17924) by <EMAIL>)
- Upgrade to typescript 4.1.5 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)

## [9.0.0-alpha.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.22)

Wed, 28 Apr 2021 07:32:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.21..@fluentui/react-accordion_v9.0.0-alpha.22)

### Changes

- Bump @fluentui/react-tabster to v9.0.0-alpha.20 ([PR #17971](https://github.com/microsoft/fluentui/pull/17971) by <EMAIL>)

## [9.0.0-alpha.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.21)

Tue, 27 Apr 2021 07:34:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.20..@fluentui/react-accordion_v9.0.0-alpha.21)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.5 ([PR #17922](https://github.com/microsoft/fluentui/pull/17922) by <EMAIL>)

## [9.0.0-alpha.20](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.20)

Mon, 26 Apr 2021 07:34:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.19..@fluentui/react-accordion_v9.0.0-alpha.20)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.4 ([PR #17936](https://github.com/microsoft/fluentui/pull/17936) by <EMAIL>)

## [9.0.0-alpha.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.19)

Fri, 23 Apr 2021 07:37:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.18..@fluentui/react-accordion_v9.0.0-alpha.19)

### Patches

- Bump @fluentui/eslint-plugin to v1.1.1 ([PR #17894](https://github.com/microsoft/fluentui/pull/17894) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.2.6 ([PR #17894](https://github.com/microsoft/fluentui/pull/17894) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #17894](https://github.com/microsoft/fluentui/pull/17894) by <EMAIL>)

### Changes

- Invoke useTabster to ensure Tabster exists ([PR #17885](https://github.com/microsoft/fluentui/pull/17885) by <EMAIL>)

## [9.0.0-alpha.18](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.18)

Thu, 22 Apr 2021 07:33:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.17..@fluentui/react-accordion_v9.0.0-alpha.18)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.2 ([PR #17826](https://github.com/microsoft/fluentui/pull/17826) by <EMAIL>)

## [9.0.0-alpha.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.17)

Wed, 21 Apr 2021 07:31:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.16..@fluentui/react-accordion_v9.0.0-alpha.17)

### Changes

- Rename ax() to mergeClasses() ([PR #17875](https://github.com/microsoft/fluentui/pull/17875) by <EMAIL>)

## [9.0.0-alpha.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.16)

Tue, 20 Apr 2021 07:31:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.15..@fluentui/react-accordion_v9.0.0-alpha.16)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.23 ([PR #17855](https://github.com/microsoft/fluentui/pull/17855) by <EMAIL>)

## [9.0.0-alpha.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.15)

Fri, 16 Apr 2021 18:08:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.14..@fluentui/react-accordion_v9.0.0-alpha.15)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.22 ([PR #17840](https://github.com/microsoft/fluentui/pull/17840) by <EMAIL>)

## [9.0.0-alpha.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.14)

Wed, 14 Apr 2021 07:34:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.13..@fluentui/react-accordion_v9.0.0-alpha.14)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.21 ([PR #17707](https://github.com/microsoft/fluentui/pull/17707) by <EMAIL>)

## [9.0.0-alpha.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.13)

Fri, 09 Apr 2021 23:42:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.12..@fluentui/react-accordion_v9.0.0-alpha.13)

### Changes

- Add A11y implementation ([PR #17668](https://github.com/microsoft/fluentui/pull/17668) by <EMAIL>)
- Adds styling to disabled state in react-accordion ([PR #17745](https://github.com/microsoft/fluentui/pull/17745) by <EMAIL>)

## [9.0.0-alpha.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.12)

Fri, 09 Apr 2021 07:31:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.11..@fluentui/react-accordion_v9.0.0-alpha.12)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.20 ([PR #17670](https://github.com/microsoft/fluentui/pull/17670) by <EMAIL>)

## [9.0.0-alpha.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.11)

Thu, 08 Apr 2021 07:33:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.10..@fluentui/react-accordion_v9.0.0-alpha.11)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.19 ([PR #17713](https://github.com/microsoft/fluentui/pull/17713) by <EMAIL>)

## [9.0.0-alpha.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.10)

Thu, 01 Apr 2021 20:13:37 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.9..@fluentui/react-accordion_v9.0.0-alpha.10)

### Changes

- Change react-focus-management to react-tabster ([PR #17651](https://github.com/microsoft/fluentui/pull/17651) by <EMAIL>)

## [9.0.0-alpha.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.9)

Thu, 01 Apr 2021 07:33:24 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.8..@fluentui/react-accordion_v9.0.0-alpha.9)

### Changes

- Rollback makeStyles usage in AccordionHeader style for a single hook ([PR #17647](https://github.com/microsoft/fluentui/pull/17647) by <EMAIL>)
- Adds Icon slot ([PR #17645](https://github.com/microsoft/fluentui/pull/17645) by <EMAIL>)

## [9.0.0-alpha.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.8)

Wed, 31 Mar 2021 00:53:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.7..@fluentui/react-accordion_v9.0.0-alpha.8)

### Patches

- Bump @fluentui/eslint-plugin to v1.1.0 ([PR #17568](https://github.com/microsoft/fluentui/pull/17568) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.2.5 ([PR #17568](https://github.com/microsoft/fluentui/pull/17568) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #17568](https://github.com/microsoft/fluentui/pull/17568) by <EMAIL>)

### Changes

- mergeProps was updated to improve type checking; use compat layer until type errors can be fixed ([PR #17508](https://github.com/microsoft/fluentui/pull/17508) by <EMAIL>)
- Adds disabled state in AccordionItem ([PR #17543](https://github.com/microsoft/fluentui/pull/17543) by <EMAIL>)

## [9.0.0-alpha.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.7)

Tue, 30 Mar 2021 07:34:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.6..@fluentui/react-accordion_v9.0.0-alpha.7)

### Changes

- chore: restore "sideEffects" to enable treeshaking ([PR #17584](https://github.com/microsoft/fluentui/pull/17584) by <EMAIL>)
- Bump react-context-selector to v9 ([PR #17350](https://github.com/microsoft/fluentui/pull/17350) by <EMAIL>)

## [9.0.0-alpha.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.6)

Fri, 26 Mar 2021 07:32:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.5..@fluentui/react-accordion_v9.0.0-alpha.6)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.12 ([PR #17524](https://github.com/microsoft/fluentui/pull/17524) by <EMAIL>)

## [9.0.0-alpha.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.5)

Thu, 25 Mar 2021 07:33:24 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.4..@fluentui/react-accordion_v9.0.0-alpha.5)

### Changes

- Adds id control for AccordionHeader and AccordionPanel ([PR #17547](https://github.com/microsoft/fluentui/pull/17547) by <EMAIL>)
- Move descendants to react-utilities ([PR #17528](https://github.com/microsoft/fluentui/pull/17528) by <EMAIL>)

## [9.0.0-alpha.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.4)

Tue, 23 Mar 2021 07:31:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.3..@fluentui/react-accordion_v9.0.0-alpha.4)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.10 ([PR #17339](https://github.com/microsoft/fluentui/pull/17339) by <EMAIL>)

## [9.0.0-alpha.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.3)

Thu, 18 Mar 2021 20:15:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.2..@fluentui/react-accordion_v9.0.0-alpha.3)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.9 ([PR #17387](https://github.com/microsoft/fluentui/pull/17387) by <EMAIL>)

## [9.0.0-alpha.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.2)

Tue, 16 Mar 2021 07:32:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-accordion_v9.0.0-alpha.1..@fluentui/react-accordion_v9.0.0-alpha.2)

### Patches

- Bump @fluentui/react-conformance to v0.2.4 ([PR #17418](https://github.com/microsoft/fluentui/pull/17418) by <EMAIL>)

### Changes

- Updates styles from makeStylesCompat to makeStyles ([PR #17395](https://github.com/microsoft/fluentui/pull/17395) by <EMAIL>)

## [9.0.0-alpha.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-accordion_v9.0.0-alpha.1)

Fri, 12 Mar 2021 20:04:27 GMT

### Patches

- Bump @fluentui/react-conformance to v0.2.3 ([PR #17161](https://github.com/microsoft/fluentui/pull/17161) by <EMAIL>)

### Changes

- Move `@types/node` and `@types/webpack-env` devDependencies to root, and remove unneeded references ([PR #17373](https://github.com/microsoft/fluentui/pull/17373) by <EMAIL>)
- Initial release ([PR #16969](https://github.com/microsoft/fluentui/pull/16969) by <EMAIL>)
