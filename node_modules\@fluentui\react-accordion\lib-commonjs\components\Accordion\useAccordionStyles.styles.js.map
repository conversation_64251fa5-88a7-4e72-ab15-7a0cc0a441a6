{"version": 3, "sources": ["useAccordionStyles.styles.js"], "sourcesContent": ["import { mergeClasses } from '@griffel/react';\nexport const accordionClassNames = {\n    root: 'fui-Accordion'\n};\nexport const useAccordionStyles_unstable = (state)=>{\n    'use no memo';\n    state.root.className = mergeClasses(accordionClassNames.root, state.root.className);\n    return state;\n};\n"], "names": ["accordionClassNames", "useAccordionStyles_unstable", "root", "state", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IACaA,mBAAmB;eAAnBA;;IAGAC,2BAA2B;eAA3BA;;;uBAJgB;AACtB,MAAMD,sBAAsB;IAC/BE,MAAM;AACV;AACO,MAAMD,8BAA+BE,CAAAA;IACxC;IACAA,MAAMD,IAAI,CAACE,SAAS,GAAGC,IAAAA,mBAAY,EAACL,oBAAoBE,IAAI,EAAEC,MAAMD,IAAI,CAACE,SAAS;IAClF,OAAOD;AACX"}