{"version": 3, "sources": ["../src/components/PresenceBadge/usePresenceBadge.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { slot } from '@fluentui/react-utilities';\nimport {\n  presenceAvailableFilled,\n  presenceAvailableRegular,\n  presenceAwayFilled,\n  presenceBlockedRegular,\n  presenceBusyFilled,\n  presenceDndFilled,\n  presenceDndRegular,\n  presenceOfflineRegular,\n  presenceOofRegular,\n  presenceUnknownRegular,\n} from './presenceIcons';\nimport { useBadge_unstable } from '../Badge/index';\nimport type { PresenceBadgeProps, PresenceBadgeState } from './PresenceBadge.types';\n\nconst iconMap = (status: PresenceBadgeState['status'], outOfOffice: boolean, size: PresenceBadgeState['size']) => {\n  switch (status) {\n    case 'available':\n      return outOfOffice ? presenceAvailableRegular[size] : presenceAvailableFilled[size];\n    case 'away':\n      return outOfOffice ? presenceOofRegular[size] : presenceAwayFilled[size];\n    case 'blocked':\n      return presenceBlockedRegular[size];\n    case 'busy':\n      return outOfOffice ? presenceUnknownRegular[size] : presenceBusyFilled[size];\n    case 'do-not-disturb':\n      return outOfOffice ? presenceDndRegular[size] : presenceDndFilled[size];\n    case 'offline':\n      return outOfOffice ? presenceOofRegular[size] : presenceOfflineRegular[size];\n    case 'out-of-office':\n      return presenceOofRegular[size];\n    case 'unknown':\n      return presenceUnknownRegular[size];\n  }\n};\n\nconst DEFAULT_STRINGS = {\n  busy: 'busy',\n  'out-of-office': 'out of office',\n  away: 'away',\n  available: 'available',\n  offline: 'offline',\n  'do-not-disturb': 'do not disturb',\n  unknown: 'unknown',\n  blocked: 'blocked',\n};\n\n/**\n * Returns the props and state required to render the component\n */\nexport const usePresenceBadge_unstable = (\n  props: PresenceBadgeProps,\n  ref: React.Ref<HTMLElement>,\n): PresenceBadgeState => {\n  const { size = 'medium', status = 'available', outOfOffice = false } = props;\n\n  const statusText = DEFAULT_STRINGS[status];\n  const oofText = props.outOfOffice && props.status !== 'out-of-office' ? ` ${DEFAULT_STRINGS['out-of-office']}` : '';\n\n  const IconElement = iconMap(status, outOfOffice, size);\n\n  const state: PresenceBadgeState = {\n    ...useBadge_unstable(\n      {\n        'aria-label': statusText + oofText,\n        role: 'img',\n        ...props,\n        size,\n        icon: slot.optional(props.icon, {\n          defaultProps: {\n            children: IconElement ? <IconElement /> : null,\n          },\n          renderByDefault: true,\n          elementType: 'span',\n        }),\n      },\n      ref,\n    ),\n    status,\n    outOfOffice,\n  };\n\n  return state;\n};\n"], "names": ["usePresenceBadge_unstable", "iconMap", "status", "outOfOffice", "size", "presenceAvailableRegular", "presenceAvailableFilled", "presenceOofRegular", "presenceAwayFilled", "presenceBlockedRegular", "presenceUnknownRegular", "presenceBusyFilled", "presenceDndRegular", "presenceDndFilled", "presenceOfflineRegular", "DEFAULT_STRINGS", "busy", "away", "available", "offline", "unknown", "blocked", "props", "ref", "statusText", "oofText", "IconElement", "state", "useBadge_unstable", "role", "icon", "slot", "optional", "defaultProps", "children", "React", "createElement", "renderByDefault", "elementType"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAoDaA;;;eAAAA;;;;iEApDU;gCACF;+BAYd;uBAC2B;AAGlC,MAAMC,UAAU,CAACC,QAAsCC,aAAsBC;IAC3E,OAAQF;QACN,KAAK;YACH,OAAOC,cAAcE,uCAAwB,CAACD,KAAK,GAAGE,sCAAuB,CAACF,KAAK;QACrF,KAAK;YACH,OAAOD,cAAcI,iCAAkB,CAACH,KAAK,GAAGI,iCAAkB,CAACJ,KAAK;QAC1E,KAAK;YACH,OAAOK,qCAAsB,CAACL,KAAK;QACrC,KAAK;YACH,OAAOD,cAAcO,qCAAsB,CAACN,KAAK,GAAGO,iCAAkB,CAACP,KAAK;QAC9E,KAAK;YACH,OAAOD,cAAcS,iCAAkB,CAACR,KAAK,GAAGS,gCAAiB,CAACT,KAAK;QACzE,KAAK;YACH,OAAOD,cAAcI,iCAAkB,CAACH,KAAK,GAAGU,qCAAsB,CAACV,KAAK;QAC9E,KAAK;YACH,OAAOG,iCAAkB,CAACH,KAAK;QACjC,KAAK;YACH,OAAOM,qCAAsB,CAACN,KAAK;IACvC;AACF;AAEA,MAAMW,kBAAkB;IACtBC,MAAM;IACN,iBAAiB;IACjBC,MAAM;IACNC,WAAW;IACXC,SAAS;IACT,kBAAkB;IAClBC,SAAS;IACTC,SAAS;AACX;AAKO,MAAMrB,4BAA4B,CACvCsB,OACAC;IAEA,MAAM,EAAEnB,OAAO,QAAQ,EAAEF,SAAS,WAAW,EAAEC,cAAc,KAAK,EAAE,GAAGmB;IAEvE,MAAME,aAAaT,eAAe,CAACb,OAAO;IAC1C,MAAMuB,UAAUH,MAAMnB,WAAW,IAAImB,MAAMpB,MAAM,KAAK,kBAAkB,CAAC,CAAC,EAAEa,eAAe,CAAC,gBAAgB,CAAC,CAAC,GAAG;IAEjH,MAAMW,cAAczB,QAAQC,QAAQC,aAAaC;IAEjD,MAAMuB,QAA4B;QAChC,GAAGC,IAAAA,wBAAAA,EACD;YACE,cAAcJ,aAAaC;YAC3BI,MAAM;YACN,GAAGP,KAAK;YACRlB;YACA0B,MAAMC,oBAAAA,CAAKC,QAAQ,CAACV,MAAMQ,IAAI,EAAE;gBAC9BG,cAAc;oBACZC,UAAUR,cAAAA,WAAAA,GAAcS,OAAAC,aAAA,CAACV,aAAAA,QAAiB;gBAC5C;gBACAW,iBAAiB;gBACjBC,aAAa;YACf;QACF,GACAf,IACD;QACDrB;QACAC;IACF;IAEA,OAAOwB;AACT"}