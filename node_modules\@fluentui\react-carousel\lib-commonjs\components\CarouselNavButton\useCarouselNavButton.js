"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useCarouselNavButton_unstable", {
    enumerable: true,
    get: function() {
        return useCarouselNavButton_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _reactaria = require("@fluentui/react-aria");
const _reacttabster = require("@fluentui/react-tabster");
const _reactutilities = require("@fluentui/react-utilities");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _CarouselContext = require("../CarouselContext");
const _CarouselNavContext = require("../CarouselNav/CarouselNavContext");
const _CarouselNavIndexContext = require("../CarouselNav/CarouselNavIndexContext");
const useCarouselNavButton_unstable = (props, ref)=>{
    const { onClick, as = 'button' } = props;
    const { appearance } = (0, _CarouselNavContext.useCarouselNavContext)();
    const index = (0, _CarouselNavIndexContext.useCarouselNavIndexContext)();
    const selectPageByIndex = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.selectPageByIndex);
    const selected = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.activeIndex === index);
    const subscribeForValues = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.subscribeForValues);
    const resetAutoplay = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.resetAutoplay);
    const handleClick = (0, _reactutilities.useEventCallback)((event)=>{
        onClick === null || onClick === void 0 ? void 0 : onClick(event);
        if (!event.defaultPrevented && (0, _reactutilities.isHTMLElement)(event.target)) {
            selectPageByIndex(event, index);
        }
        // Ensure any autoplay timers are extended/reset
        resetAutoplay();
    });
    const defaultTabProps = (0, _reacttabster.useTabsterAttributes)({
        focusable: {
            isDefault: selected
        }
    });
    const buttonRef = _react.useRef();
    const _carouselButton = _reactutilities.slot.always((0, _reactutilities.getIntrinsicElementProps)(as, (0, _reactaria.useARIAButtonProps)(props.as, props)), {
        elementType: 'button',
        defaultProps: {
            ref: (0, _reactutilities.useMergedRefs)(ref, buttonRef),
            role: 'tab',
            type: 'button',
            'aria-selected': selected,
            ...defaultTabProps
        }
    });
    (0, _reactutilities.useIsomorphicLayoutEffect)(()=>{
        return subscribeForValues((data)=>{
            var _data_groupIndexList;
            var _data_groupIndexList_index;
            const controlList = (_data_groupIndexList_index = (_data_groupIndexList = data.groupIndexList) === null || _data_groupIndexList === void 0 ? void 0 : _data_groupIndexList[index]) !== null && _data_groupIndexList_index !== void 0 ? _data_groupIndexList_index : [];
            const _controlledSlideIds = controlList.map((slideIndex)=>{
                return data.slideNodes[slideIndex].id;
            }).join(' ');
            if (buttonRef.current) {
                buttonRef.current.setAttribute('aria-controls', _controlledSlideIds);
            }
        });
    }, [
        index,
        subscribeForValues
    ]);
    // Override onClick
    _carouselButton.onClick = handleClick;
    const state = {
        selected,
        appearance,
        components: {
            root: 'button'
        },
        root: _carouselButton
    };
    return state;
};
