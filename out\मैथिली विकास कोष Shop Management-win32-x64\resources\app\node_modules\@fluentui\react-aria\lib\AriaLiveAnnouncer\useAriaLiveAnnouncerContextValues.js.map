{"version": 3, "sources": ["../src/AriaLiveAnnouncer/useAriaLiveAnnouncerContextValues.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { AriaLiveAnnouncerContextValues, AriaLiveAnnouncerState } from './AriaLiveAnnouncer.types';\n\nexport function useAriaLiveAnnouncerContextValues_unstable(\n  state: AriaLiveAnnouncerState,\n): AriaLiveAnnouncerContextValues {\n  const { announce } = state;\n\n  return React.useMemo(() => ({ announce: { announce } }), [announce]);\n}\n"], "names": ["React", "useAriaLiveAnnouncerContextValues_unstable", "state", "announce", "useMemo"], "rangeMappings": ";;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAG/B,OAAO,SAASC,2CACdC,KAA6B;IAE7B,MAAM,EAAEC,QAAQ,EAAE,GAAGD;IAErB,OAAOF,MAAMI,OAAO,CAAC,IAAO,CAAA;YAAED,UAAU;gBAAEA;YAAS;QAAE,CAAA,GAAI;QAACA;KAAS;AACrE"}