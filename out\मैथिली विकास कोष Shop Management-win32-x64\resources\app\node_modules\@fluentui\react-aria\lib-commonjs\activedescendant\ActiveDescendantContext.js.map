{"version": 3, "sources": ["../src/activedescendant/ActiveDescendantContext.ts"], "sourcesContent": ["import * as React from 'react';\nimport { ActiveDescendantImperativeRef } from './types';\n\nexport type ActiveDescendantContextValue = {\n  controller: ActiveDescendantImperativeRef;\n};\n\nconst noop = () => undefined;\n\nconst activeDescendantContextDefaultValue: ActiveDescendantContextValue = {\n  controller: {\n    active: noop,\n    blur: noop,\n    find: noop,\n    first: noop,\n    focus: noop,\n    focusLastActive: noop,\n    scrollActiveIntoView: noop,\n    last: noop,\n    next: noop,\n    prev: noop,\n    showAttributes: noop,\n    hideAttributes: noop,\n    showFocusVisibleAttributes: noop,\n    hideFocusVisibleAttributes: noop,\n  },\n};\n\nconst ActiveDescendantContext = React.createContext<ActiveDescendantContextValue | undefined>(undefined);\n\nexport const ActiveDescendantContextProvider = ActiveDescendantContext.Provider;\nexport const useActiveDescendantContext = () =>\n  React.useContext(ActiveDescendantContext) ?? activeDescendantContextDefaultValue;\nexport const useHasParentActiveDescendantContext = () => !!React.useContext(ActiveDescendantContext);\n"], "names": ["ActiveDescendantContextProvider", "useActiveDescendantContext", "useHasParentActiveDescendantContext", "noop", "undefined", "activeDescendantContextDefaultValue", "controller", "active", "blur", "find", "first", "focus", "focusLastActive", "scrollActiveIntoView", "last", "next", "prev", "showAttributes", "hideAttributes", "showFocusVisibleAttributes", "hideFocusVisibleAttributes", "ActiveDescendantContext", "React", "createContext", "Provider", "useContext"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IA8BaA,+BAA+B;eAA/BA;;IACAC,0BAA0B;eAA1BA;;IAEAC,mCAAmC;eAAnCA;;;;iEAjCU;AAOvB,MAAMC,OAAO,IAAMC;AAEnB,MAAMC,sCAAoE;IACxEC,YAAY;QACVC,QAAQJ;QACRK,MAAML;QACNM,MAAMN;QACNO,OAAOP;QACPQ,OAAOR;QACPS,iBAAiBT;QACjBU,sBAAsBV;QACtBW,MAAMX;QACNY,MAAMZ;QACNa,MAAMb;QACNc,gBAAgBd;QAChBe,gBAAgBf;QAChBgB,4BAA4BhB;QAC5BiB,4BAA4BjB;IAC9B;AACF;AAEA,MAAMkB,0BAA0BC,OAAMC,aAAa,CAA2CnB;AAEvF,MAAMJ,kCAAkCqB,wBAAwBG,QAAQ;AACxE,MAAMvB,6BAA6B;QACxCqB;WAAAA,CAAAA,oBAAAA,OAAMG,UAAU,CAACJ,sCAAjBC,+BAAAA,oBAA6CjB;AAAkC;AAC1E,MAAMH,sCAAsC,IAAM,CAAC,CAACoB,OAAMG,UAAU,CAACJ"}