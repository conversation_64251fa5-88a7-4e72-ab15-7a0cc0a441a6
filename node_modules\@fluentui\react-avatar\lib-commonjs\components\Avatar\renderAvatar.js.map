{"version": 3, "sources": ["../src/components/Avatar/renderAvatar.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { AvatarSlots, AvatarState } from './Avatar.types';\n\nexport const renderAvatar_unstable = (state: AvatarState) => {\n  assertSlots<AvatarSlots>(state);\n\n  return (\n    <state.root>\n      {state.initials && <state.initials />}\n      {state.icon && <state.icon />}\n      {state.image && <state.image />}\n      {state.badge && <state.badge />}\n      {state.activeAriaLabelElement}\n    </state.root>\n  );\n};\n"], "names": ["renderAvatar_unstable", "state", "assertSlots", "_jsxs", "root", "initials", "_jsx", "icon", "image", "badge", "activeAriaLabelElement"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAMaA;;;eAAAA;;;4BALb;gCAE4B;AAGrB,MAAMA,wBAAwB,CAACC;IACpCC,IAAAA,2BAAAA,EAAyBD;IAEzB,OAAA,WAAA,GACEE,IAAAA,gBAAA,EAACF,MAAMG,IAAI,EAAA;;YACRH,MAAMI,QAAQ,IAAA,WAAA,GAAIC,IAAAA,eAAA,EAACL,MAAMI,QAAQ,EAAA,CAAA;YACjCJ,MAAMM,IAAI,IAAA,WAAA,GAAID,IAAAA,eAAA,EAACL,MAAMM,IAAI,EAAA,CAAA;YACzBN,MAAMO,KAAK,IAAA,WAAA,GAAIF,IAAAA,eAAA,EAACL,MAAMO,KAAK,EAAA,CAAA;YAC3BP,MAAMQ,KAAK,IAAA,WAAA,GAAIH,IAAAA,eAAA,EAACL,MAAMQ,KAAK,EAAA,CAAA;YAC3BR,MAAMS,sBAAsB;;;AAGnC"}