"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    avatarGroupPopoverClassNames: function() {
        return avatarGroupPopoverClassNames;
    },
    useAvatarGroupPopoverStyles_unstable: function() {
        return useAvatarGroupPopoverStyles_unstable;
    }
});
const _react = require("@griffel/react");
const _useAvatarGroupItemStylesstyles = require("../AvatarGroupItem/useAvatarGroupItemStyles.styles");
const _useAvatarStylesstyles = require("../Avatar/useAvatarStyles.styles");
const avatarGroupPopoverClassNames = {
    root: 'fui-AvatarGroupPopover',
    content: 'fui-AvatarGroupPopover__content',
    popoverSurface: 'fui-AvatarGroupPopover__popoverSurface',
    tooltip: 'fui-AvatarGroupPopover__tooltip',
    triggerButton: 'fui-AvatarGroupPopover__triggerButton'
};
/**
 * Styles for the content slot.
 */ const useContentStyles = /*#__PURE__*/ (0, _react.__styles)({
    base: {
        dclx09: "ftrb29c",
        jrapky: 0,
        Frg6f3: 0,
        t21cq0: 0,
        B6of3ja: 0,
        B74szlk: "f1s184ao",
        Byoj8tv: 0,
        uwmqm3: 0,
        z189sj: 0,
        z8tnut: 0,
        B0ocmuz: "f1mk8lai",
        mc9l5x: "f22iagw",
        Beiy3e4: "f1vx9l62"
    }
}, {
    d: [
        ".ftrb29c{list-style-type:none;}",
        [
            ".f1s184ao{margin:0;}",
            {
                p: -1
            }
        ],
        [
            ".f1mk8lai{padding:0;}",
            {
                p: -1
            }
        ],
        ".f22iagw{display:flex;}",
        ".f1vx9l62{flex-direction:column;}"
    ]
});
/**
 * Styles for the popoverSurface slot.
 */ const usePopoverSurfaceStyles = /*#__PURE__*/ (0, _react.__styles)({
    base: {
        Bxyxcbc: "fopcw2o",
        sshi5w: "f1n5o1gx",
        B68tc82: 0,
        Bmxbyg5: 0,
        Bpg54ce: "f19r5mr9",
        Byoj8tv: 0,
        uwmqm3: 0,
        z189sj: 0,
        z8tnut: 0,
        B0ocmuz: "f1f5q0n8",
        a9b677: "f13dwy2t"
    }
}, {
    d: [
        ".fopcw2o{max-height:220px;}",
        ".f1n5o1gx{min-height:80px;}",
        [
            ".f19r5mr9{overflow:hidden scroll;}",
            {
                p: -1
            }
        ],
        [
            ".f1f5q0n8{padding:var(--spacingVerticalS) var(--spacingHorizontalS);}",
            {
                p: -1
            }
        ],
        ".f13dwy2t{width:220px;}"
    ]
});
/**
 * Styles for the triggerButton slot.
 */ const useTriggerButtonStyles = /*#__PURE__*/ (0, _react.__styles)({
    base: {
        mc9l5x: "ftuwxu6",
        qhf8xq: "f10pi13n",
        Bnnss6s: "fi64zpg",
        Brf1p80: "f4d9j23",
        Bt984gj: "f122n59",
        sj55zd: "f19n0e5",
        De3pzq: "fxugw4r",
        g2u3we: "fj3muxo",
        h3c5rm: [
            "f1akhkt",
            "f1lxtadh"
        ],
        B9xav0g: "f1aperda",
        zhjwy3: [
            "f1lxtadh",
            "f1akhkt"
        ],
        Beyfa6y: 0,
        Bbmb7ep: 0,
        Btl43ni: 0,
        B7oj6ja: 0,
        Dimara: "f44lkw9",
        icvyot: "fzkkow9",
        vrafjx: [
            "fcdblym",
            "fjik90z"
        ],
        oivjwe: "fg706s2",
        wvpqe5: [
            "fjik90z",
            "fcdblym"
        ],
        Byoj8tv: 0,
        uwmqm3: 0,
        z189sj: 0,
        z8tnut: 0,
        B0ocmuz: "f1mk8lai",
        Bjwas2f: "fw33nwi",
        Bn1d65q: [
            "f1ptkjjm",
            "fmzzjfk"
        ],
        Bxeuatn: "f15j0dln",
        n51gp8: [
            "fmzzjfk",
            "f1ptkjjm"
        ]
    },
    pie: {
        De3pzq: "f1c21dwh",
        g2u3we: "fghlq4f",
        h3c5rm: [
            "f1gn591s",
            "fjscplz"
        ],
        B9xav0g: "fb073pr",
        zhjwy3: [
            "fjscplz",
            "f1gn591s"
        ],
        sj55zd: "f44pa96"
    },
    focusIndicator: {
        Byu6kyc: 0,
        n8qw10: 0,
        Bbjhlyh: 0,
        i2cumq: 0,
        Bunx835: 0,
        Bdrgwmp: 0,
        mqozju: 0,
        lbo84a: 0,
        Bksnhdo: 0,
        Bci5o5g: 0,
        u5e7qz: 0,
        Bn40d3w: 0,
        B7b6zxw: 0,
        B8q5s1w: 0,
        B5gfjzb: 0,
        Bbcte9g: 0,
        Bqz3imu: "f1j9b7x8",
        g9k6zt: "f1nev41a"
    },
    states: {
        Bi91k9c: "feu1g3u",
        Jwef8y: "f1knas48",
        Bgoe8wy: "fvcxoqz",
        Bwzppfd: [
            "f1ub3y4t",
            "f1m52nbi"
        ],
        oetu4i: "f1xlaoq0",
        gg5e9n: [
            "f1m52nbi",
            "f1ub3y4t"
        ],
        lj723h: "f1g4hkjv",
        ecr2s2: "fb40n2d",
        B6oc9vd: "fvs00aa",
        ak43y8: [
            "f1assf6x",
            "f4ruux4"
        ],
        wmxk5l: "fumykes",
        B50zh58: [
            "f4ruux4",
            "f1assf6x"
        ]
    },
    selected: {
        sj55zd: "f14nttnl",
        De3pzq: "f1nfm20t",
        g2u3we: "f1ly1fcm",
        h3c5rm: [
            "fi8bssc",
            "fj6btzu"
        ],
        B9xav0g: "f1s9tnsa",
        zhjwy3: [
            "fj6btzu",
            "fi8bssc"
        ]
    },
    icon12: {
        Be2twd7: "f1ugzwwg"
    },
    icon16: {
        Be2twd7: "f4ybsrx"
    },
    icon20: {
        Be2twd7: "fe5j1ua"
    },
    icon24: {
        Be2twd7: "f1rt2boy"
    },
    icon28: {
        Be2twd7: "f24l1pt"
    },
    icon32: {
        Be2twd7: "ffl51b"
    },
    icon48: {
        Be2twd7: "f18m8u13"
    },
    caption2Strong: {
        Bahqtrf: "fk6fouc",
        Be2twd7: "f13mqy1h",
        Bhrd7zp: "fl43uef",
        Bg96gwp: "fcpl73t"
    },
    caption1Strong: {
        Bahqtrf: "fk6fouc",
        Be2twd7: "fy9rknc",
        Bhrd7zp: "fl43uef",
        Bg96gwp: "fwrc4pm"
    },
    body1Strong: {
        Bahqtrf: "fk6fouc",
        Be2twd7: "fkhj508",
        Bhrd7zp: "fl43uef",
        Bg96gwp: "f1i3iumi"
    },
    subtitle2: {
        Bahqtrf: "fk6fouc",
        Be2twd7: "fod5ikn",
        Bhrd7zp: "fl43uef",
        Bg96gwp: "faaz57k"
    },
    subtitle1: {
        Bahqtrf: "fk6fouc",
        Be2twd7: "f1pp30po",
        Bhrd7zp: "fl43uef",
        Bg96gwp: "f106mvju"
    },
    title3: {
        Bahqtrf: "fk6fouc",
        Be2twd7: "f1x0m3f5",
        Bhrd7zp: "fl43uef",
        Bg96gwp: "fb86gi6"
    },
    borderThin: {
        B4j52fo: "f192inf7",
        Bekrc4i: [
            "f5tn483",
            "f1ojsxk5"
        ],
        Bn0qgzm: "f1vxd6vx",
        ibv6hh: [
            "f1ojsxk5",
            "f5tn483"
        ]
    },
    borderThick: {
        B4j52fo: "f18zi460",
        Bekrc4i: [
            "f1wpluaz",
            "fsfsuhs"
        ],
        Bn0qgzm: "fmklw6v",
        ibv6hh: [
            "fsfsuhs",
            "f1wpluaz"
        ]
    },
    borderThicker: {
        B4j52fo: "fgx37oo",
        Bekrc4i: [
            "f130t4y6",
            "f1efpmoh"
        ],
        Bn0qgzm: "fv51ejd",
        ibv6hh: [
            "f1efpmoh",
            "f130t4y6"
        ]
    },
    borderThickest: {
        B4j52fo: "fwn6jck",
        Bekrc4i: [
            "figl7jc",
            "f1g0iy8l"
        ],
        Bn0qgzm: "f1b8shu7",
        ibv6hh: [
            "f1g0iy8l",
            "figl7jc"
        ]
    }
}, {
    d: [
        ".ftuwxu6{display:inline-flex;}",
        ".f10pi13n{position:relative;}",
        ".fi64zpg{flex-shrink:0;}",
        ".f4d9j23{justify-content:center;}",
        ".f122n59{align-items:center;}",
        ".f19n0e5{color:var(--colorNeutralForeground1);}",
        ".fxugw4r{background-color:var(--colorNeutralBackground1);}",
        ".fj3muxo{border-top-color:var(--colorNeutralStroke1);}",
        ".f1akhkt{border-right-color:var(--colorNeutralStroke1);}",
        ".f1lxtadh{border-left-color:var(--colorNeutralStroke1);}",
        ".f1aperda{border-bottom-color:var(--colorNeutralStroke1);}",
        [
            ".f44lkw9{border-radius:var(--borderRadiusCircular);}",
            {
                p: -1
            }
        ],
        ".fzkkow9{border-top-style:solid;}",
        ".fcdblym{border-right-style:solid;}",
        ".fjik90z{border-left-style:solid;}",
        ".fg706s2{border-bottom-style:solid;}",
        [
            ".f1mk8lai{padding:0;}",
            {
                p: -1
            }
        ],
        ".f1c21dwh{background-color:var(--colorTransparentBackground);}",
        ".fghlq4f{border-top-color:var(--colorTransparentStroke);}",
        ".f1gn591s{border-right-color:var(--colorTransparentStroke);}",
        ".fjscplz{border-left-color:var(--colorTransparentStroke);}",
        ".fb073pr{border-bottom-color:var(--colorTransparentStroke);}",
        ".f44pa96{color:transparent;}",
        [
            ".f1j9b7x8[data-fui-focus-visible]{border:var(--strokeWidthThick) solid var(--colorStrokeFocus2);}",
            {
                p: -2
            }
        ],
        ".f1nev41a[data-fui-focus-visible]{outline-style:none;}",
        ".f14nttnl{color:var(--colorNeutralForeground1Selected);}",
        ".f1nfm20t{background-color:var(--colorNeutralBackground1Selected);}",
        ".f1ly1fcm{border-top-color:var(--colorNeutralStroke1Selected);}",
        ".fi8bssc{border-right-color:var(--colorNeutralStroke1Selected);}",
        ".fj6btzu{border-left-color:var(--colorNeutralStroke1Selected);}",
        ".f1s9tnsa{border-bottom-color:var(--colorNeutralStroke1Selected);}",
        ".f1ugzwwg{font-size:12px;}",
        ".f4ybsrx{font-size:16px;}",
        ".fe5j1ua{font-size:20px;}",
        ".f1rt2boy{font-size:24px;}",
        ".f24l1pt{font-size:28px;}",
        ".ffl51b{font-size:32px;}",
        ".f18m8u13{font-size:48px;}",
        ".fk6fouc{font-family:var(--fontFamilyBase);}",
        ".f13mqy1h{font-size:var(--fontSizeBase100);}",
        ".fl43uef{font-weight:var(--fontWeightSemibold);}",
        ".fcpl73t{line-height:var(--lineHeightBase100);}",
        ".fy9rknc{font-size:var(--fontSizeBase200);}",
        ".fwrc4pm{line-height:var(--lineHeightBase200);}",
        ".fkhj508{font-size:var(--fontSizeBase300);}",
        ".f1i3iumi{line-height:var(--lineHeightBase300);}",
        ".fod5ikn{font-size:var(--fontSizeBase400);}",
        ".faaz57k{line-height:var(--lineHeightBase400);}",
        ".f1pp30po{font-size:var(--fontSizeBase500);}",
        ".f106mvju{line-height:var(--lineHeightBase500);}",
        ".f1x0m3f5{font-size:var(--fontSizeBase600);}",
        ".fb86gi6{line-height:var(--lineHeightBase600);}",
        ".f192inf7{border-top-width:var(--strokeWidthThin);}",
        ".f5tn483{border-right-width:var(--strokeWidthThin);}",
        ".f1ojsxk5{border-left-width:var(--strokeWidthThin);}",
        ".f1vxd6vx{border-bottom-width:var(--strokeWidthThin);}",
        ".f18zi460{border-top-width:var(--strokeWidthThick);}",
        ".f1wpluaz{border-right-width:var(--strokeWidthThick);}",
        ".fsfsuhs{border-left-width:var(--strokeWidthThick);}",
        ".fmklw6v{border-bottom-width:var(--strokeWidthThick);}",
        ".fgx37oo{border-top-width:var(--strokeWidthThicker);}",
        ".f130t4y6{border-right-width:var(--strokeWidthThicker);}",
        ".f1efpmoh{border-left-width:var(--strokeWidthThicker);}",
        ".fv51ejd{border-bottom-width:var(--strokeWidthThicker);}",
        ".fwn6jck{border-top-width:var(--strokeWidthThickest);}",
        ".figl7jc{border-right-width:var(--strokeWidthThickest);}",
        ".f1g0iy8l{border-left-width:var(--strokeWidthThickest);}",
        ".f1b8shu7{border-bottom-width:var(--strokeWidthThickest);}"
    ],
    m: [
        [
            "@media (forced-colors: active){.fw33nwi{border-top-color:CanvasText;}}",
            {
                m: "(forced-colors: active)"
            }
        ],
        [
            "@media (forced-colors: active){.f1ptkjjm{border-right-color:CanvasText;}.fmzzjfk{border-left-color:CanvasText;}}",
            {
                m: "(forced-colors: active)"
            }
        ],
        [
            "@media (forced-colors: active){.f15j0dln{border-bottom-color:CanvasText;}}",
            {
                m: "(forced-colors: active)"
            }
        ]
    ],
    h: [
        ".feu1g3u:hover{color:var(--colorNeutralForeground1Hover);}",
        ".f1knas48:hover{background-color:var(--colorNeutralBackground1Hover);}",
        ".fvcxoqz:hover{border-top-color:var(--colorNeutralStroke1Hover);}",
        ".f1ub3y4t:hover{border-right-color:var(--colorNeutralStroke1Hover);}",
        ".f1m52nbi:hover{border-left-color:var(--colorNeutralStroke1Hover);}",
        ".f1xlaoq0:hover{border-bottom-color:var(--colorNeutralStroke1Hover);}"
    ],
    a: [
        ".f1g4hkjv:active{color:var(--colorNeutralForeground1Pressed);}",
        ".fb40n2d:active{background-color:var(--colorNeutralBackground1Pressed);}",
        ".fvs00aa:active{border-top-color:var(--colorNeutralStroke1Pressed);}",
        ".f1assf6x:active{border-right-color:var(--colorNeutralStroke1Pressed);}",
        ".f4ruux4:active{border-left-color:var(--colorNeutralStroke1Pressed);}",
        ".fumykes:active{border-bottom-color:var(--colorNeutralStroke1Pressed);}"
    ]
});
const useAvatarGroupPopoverStyles_unstable = (state)=>{
    'use no memo';
    const { indicator, size, layout, popoverOpen } = state;
    const sizeStyles = (0, _useAvatarStylesstyles.useSizeStyles)();
    const triggerButtonStyles = useTriggerButtonStyles();
    const contentStyles = useContentStyles();
    const popoverSurfaceStyles = usePopoverSurfaceStyles();
    const groupChildClassName = (0, _useAvatarGroupItemStylesstyles.useGroupChildClassName)(layout, size);
    const triggerButtonClasses = [];
    if (size < 36) {
        triggerButtonClasses.push(triggerButtonStyles.borderThin);
    } else if (size < 56) {
        triggerButtonClasses.push(triggerButtonStyles.borderThick);
    } else if (size < 72) {
        triggerButtonClasses.push(triggerButtonStyles.borderThicker);
    } else {
        triggerButtonClasses.push(triggerButtonStyles.borderThickest);
    }
    if (indicator === 'count') {
        if (size <= 24) {
            triggerButtonClasses.push(triggerButtonStyles.caption2Strong);
        } else if (size <= 28) {
            triggerButtonClasses.push(triggerButtonStyles.caption1Strong);
        } else if (size <= 40) {
            triggerButtonClasses.push(triggerButtonStyles.body1Strong);
        } else if (size <= 56) {
            triggerButtonClasses.push(triggerButtonStyles.subtitle2);
        } else if (size <= 96) {
            triggerButtonClasses.push(triggerButtonStyles.subtitle1);
        } else {
            triggerButtonClasses.push(triggerButtonStyles.title3);
        }
    } else {
        if (size <= 16) {
            triggerButtonClasses.push(triggerButtonStyles.icon12);
        } else if (size <= 24) {
            triggerButtonClasses.push(triggerButtonStyles.icon16);
        } else if (size <= 40) {
            triggerButtonClasses.push(triggerButtonStyles.icon20);
        } else if (size <= 48) {
            triggerButtonClasses.push(triggerButtonStyles.icon24);
        } else if (size <= 56) {
            triggerButtonClasses.push(triggerButtonStyles.icon28);
        } else if (size <= 72) {
            triggerButtonClasses.push(triggerButtonStyles.icon32);
        } else {
            triggerButtonClasses.push(triggerButtonStyles.icon48);
        }
    }
    state.triggerButton.className = (0, _react.mergeClasses)(avatarGroupPopoverClassNames.triggerButton, groupChildClassName, sizeStyles[size], triggerButtonStyles.base, layout === 'pie' && triggerButtonStyles.pie, triggerButtonStyles.focusIndicator, layout !== 'pie' && triggerButtonStyles.states, layout !== 'pie' && popoverOpen && triggerButtonStyles.selected, ...triggerButtonClasses, state.triggerButton.className);
    state.content.className = (0, _react.mergeClasses)(avatarGroupPopoverClassNames.content, contentStyles.base, state.content.className);
    state.popoverSurface.className = (0, _react.mergeClasses)(avatarGroupPopoverClassNames.popoverSurface, popoverSurfaceStyles.base, state.popoverSurface.className);
    return state;
};
