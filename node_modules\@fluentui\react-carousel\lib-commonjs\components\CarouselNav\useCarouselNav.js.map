{"version": 3, "sources": ["../src/components/CarouselNav/useCarouselNav.ts"], "sourcesContent": ["import { useArrowNavigationGroup } from '@fluentui/react-tabster';\nimport { getIntrinsicElementProps, slot, useIsomorphicLayoutEffect } from '@fluentui/react-utilities';\nimport * as React from 'react';\n\nimport { useCarouselContext_unstable as useCarouselContext } from '../CarouselContext';\nimport type { CarouselNavProps, CarouselNavState } from './CarouselNav.types';\nimport { useControllableState } from '@fluentui/react-utilities';\n\n/**\n * Create the state required to render CarouselNav.\n *\n * The returned state can be modified with hooks such as useCarouselNavStyles_unstable,\n * before being passed to renderCarouselNav_unstable.\n *\n * @param props - props from this instance of CarouselNav\n * @param ref - reference to root HTMLDivElement of CarouselNav\n */\nexport const useCarouselNav_unstable = (props: CarouselNavProps, ref: React.Ref<HTMLDivElement>): CarouselNavState => {\n  const { appearance } = props;\n\n  const focusableGroupAttr = useArrowNavigationGroup({\n    circular: false,\n    axis: 'horizontal',\n    memorizeCurrent: false,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_hasDefault: true,\n  });\n\n  // Users can choose controlled or uncontrolled, if uncontrolled, the default is initialized by carousel context\n  const [totalSlides, setTotalSlides] = useControllableState({\n    state: props.totalSlides,\n    initialState: 0,\n  });\n\n  const subscribeForValues = useCarouselContext(ctx => ctx.subscribeForValues);\n\n  useIsomorphicLayoutEffect(() => {\n    return subscribeForValues(data => {\n      setTotalSlides(data.navItemsCount);\n    });\n  }, [subscribeForValues, setTotalSlides]);\n\n  return {\n    totalSlides,\n    appearance,\n    renderNavButton: props.children,\n    components: {\n      root: 'div',\n    },\n    root: slot.always(\n      getIntrinsicElementProps('div', {\n        ref,\n        role: 'tablist',\n        ...props,\n        ...focusableGroupAttr,\n        children: null,\n      }),\n      { elementType: 'div' },\n    ),\n  };\n};\n"], "names": ["useCarouselNav_unstable", "props", "ref", "appearance", "focusableGroupAttr", "useArrowNavigationGroup", "circular", "axis", "memorizeCurrent", "unstable_hasDefault", "totalSlides", "setTotalSlides", "useControllableState", "state", "initialState", "subscribeForValues", "useCarouselContext", "ctx", "useIsomorphicLayoutEffect", "data", "navItemsCount", "renderNavButton", "children", "components", "root", "slot", "always", "getIntrinsicElementProps", "role", "elementType"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAiBaA;;;eAAAA;;;;8BAjB2B;gCACkC;iEACnD;iCAE2C;AAa3D,MAAMA,0BAA0B,CAACC,OAAyBC;IAC/D,MAAM,EAAEC,UAAU,EAAE,GAAGF;IAEvB,MAAMG,qBAAqBC,IAAAA,qCAAAA,EAAwB;QACjDC,UAAU;QACVC,MAAM;QACNC,iBAAiB;QACjB,gEAAgE;QAChEC,qBAAqB;IACvB;IAEA,+GAA+G;IAC/G,MAAM,CAACC,aAAaC,eAAe,GAAGC,IAAAA,oCAAAA,EAAqB;QACzDC,OAAOZ,MAAMS,WAAW;QACxBI,cAAc;IAChB;IAEA,MAAMC,qBAAqBC,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIF,kBAAkB;IAE3EG,IAAAA,yCAAAA,EAA0B;QACxB,OAAOH,mBAAmBI,CAAAA;YACxBR,eAAeQ,KAAKC,aAAa;QACnC;IACF,GAAG;QAACL;QAAoBJ;KAAe;IAEvC,OAAO;QACLD;QACAP;QACAkB,iBAAiBpB,MAAMqB,QAAQ;QAC/BC,YAAY;YACVC,MAAM;QACR;QACAA,MAAMC,oBAAAA,CAAKC,MAAM,CACfC,IAAAA,wCAAAA,EAAyB,OAAO;YAC9BzB;YACA0B,MAAM;YACN,GAAG3B,KAAK;YACR,GAAGG,kBAAkB;YACrBkB,UAAU;QACZ,IACA;YAAEO,aAAa;QAAM;IAEzB;AACF"}