"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "renderAvatarGroup_unstable", {
    enumerable: true,
    get: function() {
        return renderAvatarGroup_unstable;
    }
});
const _jsxruntime = require("@fluentui/react-jsx-runtime/jsx-runtime");
const _reactutilities = require("@fluentui/react-utilities");
const _AvatarGroupContext = require("../../contexts/AvatarGroupContext");
const renderAvatarGroup_unstable = (state, contextValues)=>{
    (0, _reactutilities.assertSlots)(state);
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_AvatarGroupContext.AvatarGroupProvider, {
        value: contextValues.avatarGroup,
        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(state.root, {})
    });
};
