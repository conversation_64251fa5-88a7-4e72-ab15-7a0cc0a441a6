{"version": 3, "sources": ["../src/components/AvatarGroupPopover/renderAvatarGroupPopover.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\nimport { AvatarGroupProvider } from '../../contexts/AvatarGroupContext';\nimport { AvatarGroupContextValues } from '../AvatarGroup/AvatarGroup.types';\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport { PopoverTrigger } from '@fluentui/react-popover';\nimport type { AvatarGroupPopoverState, AvatarGroupPopoverSlots } from './AvatarGroupPopover.types';\n\n/**\n * Render the final JSX of AvatarGroupPopover\n */\nexport const renderAvatarGroupPopover_unstable = (\n  state: AvatarGroupPopoverState,\n  contextValues: AvatarGroupContextValues,\n) => {\n  assertSlots<AvatarGroupPopoverSlots>(state);\n\n  return (\n    <state.root>\n      <PopoverTrigger disableButtonEnhancement>\n        <state.tooltip>\n          <state.triggerButton />\n        </state.tooltip>\n      </PopoverTrigger>\n      <state.popoverSurface>\n        <AvatarGroupProvider value={contextValues.avatarGroup}>\n          <state.content />\n        </AvatarGroupProvider>\n      </state.popoverSurface>\n    </state.root>\n  );\n};\n"], "names": ["AvatarGroupProvider", "assertSlots", "PopoverTrigger", "renderAvatarGroupPopover_unstable", "state", "contextValues", "root", "disableButtonEnhancement", "tooltip", "trigger<PERSON>utton", "popoverSurface", "value", "avatarGroup", "content"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,0BAA0B,GAC1B,iDAAiD;AACjD,SAASA,mBAAmB,QAAQ,oCAAoC;AAGxE,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,cAAc,QAAQ,0BAA0B;AAGzD;;CAEC,GACD,OAAO,MAAMC,oCAAoC,CAC/CC,OACAC;IAEAJ,YAAqCG;IAErC,qBACE,MAACA,MAAME,IAAI;;0BACT,KAACJ;gBAAeK,wBAAwB;0BACtC,cAAA,KAACH,MAAMI,OAAO;8BACZ,cAAA,KAACJ,MAAMK,aAAa;;;0BAGxB,KAACL,MAAMM,cAAc;0BACnB,cAAA,KAACV;oBAAoBW,OAAON,cAAcO,WAAW;8BACnD,cAAA,KAACR,MAAMS,OAAO;;;;;AAKxB,EAAE"}