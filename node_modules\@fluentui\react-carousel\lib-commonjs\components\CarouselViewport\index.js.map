{"version": 3, "sources": ["../src/components/CarouselViewport/index.ts"], "sourcesContent": ["export { CarouselViewport } from './CarouselViewport';\nexport type { CarouselViewportProps, CarouselViewportSlots, CarouselViewportState } from './CarouselViewport.types';\nexport { renderCarouselViewport_unstable } from './renderCarouselViewport';\nexport { useCarouselViewport_unstable } from './useCarouselViewport';\nexport { carouselViewportClassNames, useCarouselViewportStyles_unstable } from './useCarouselViewportStyles.styles';\n"], "names": ["CarouselViewport", "carouselViewportClassNames", "renderCarouselViewport_unstable", "useCarouselViewportStyles_unstable", "useCarouselViewport_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAASA,gBAAgB;eAAhBA,kCAAgB;;IAIhBC,0BAA0B;eAA1BA,2DAA0B;;IAF1BC,+BAA+B;eAA/BA,uDAA+B;;IAEHC,kCAAkC;eAAlCA,mEAAkC;;IAD9DC,4BAA4B;eAA5BA,iDAA4B;;;kCAHJ;wCAEe;qCACH;iDACkC"}