{"version": 3, "sources": ["../src/components/AvatarGroup/index.ts"], "sourcesContent": ["export { AvatarGroup } from './AvatarGroup';\nexport type {\n  AvatarGroupContextValue,\n  AvatarGroupContextValues,\n  AvatarGroupProps,\n  AvatarGroupSlots,\n  AvatarGroupState,\n} from './AvatarGroup.types';\nexport { renderAvatarGroup_unstable } from './renderAvatarGroup';\nexport { defaultAvatarGroupSize, useAvatarGroup_unstable } from './useAvatarGroup';\nexport { avatarGroupClassNames, useAvatarGroupStyles_unstable } from './useAvatarGroupStyles.styles';\nexport { useAvatarGroupContextValues } from './useAvatarGroupContextValues';\n"], "names": ["AvatarGroup", "avatarGroupClassNames", "defaultAvatarGroupSize", "renderAvatarGroup_unstable", "useAvatarGroupContextValues", "useAvatarGroupStyles_unstable", "useAvatarGroup_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAASA,WAAW;eAAXA,wBAAW;;IAUXC,qBAAqB;eAArBA,iDAAqB;;IADrBC,sBAAsB;eAAtBA,sCAAsB;;IADtBC,0BAA0B;eAA1BA,6CAA0B;;IAG1BC,2BAA2B;eAA3BA,wDAA2B;;IADJC,6BAA6B;eAA7BA,yDAA6B;;IAD5BC,uBAAuB;eAAvBA,uCAAuB;;;6BAT5B;mCAQe;gCACqB;4CACK;6CACzB"}