"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    carouselNavContainerClassNames: function() {
        return carouselNavContainerClassNames;
    },
    useCarouselNavContainerStyles_unstable: function() {
        return useCarouselNavContainerStyles_unstable;
    }
});
const _react = require("@griffel/react");
const carouselNavContainerClassNames = {
    root: 'fui-CarouselNavContainer',
    next: 'fui-CarouselNavContainer__next',
    prev: 'fui-CarouselNavContainer__prev',
    autoplay: 'fui-CarouselNavContainer__autoplay',
    /* Tooltip classNames are listed for type compatibility only (cannot assign root className to portal)
  * Use 'content' slot to style Tooltip content instead
  */ nextTooltip: 'fui-CarouselNavContainer__nextTooltip',
    prevTooltip: 'fui-CarouselNavContainer__prevTooltip',
    autoplayTooltip: 'fui-CarouselNavContainer__autoplayTooltip'
};
/**
 * Styles for the root slot
 */ const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    root: {
        mc9l5x: "f22iagw",
        Beiy3e4: "f1063pyq",
        Brf1p80: "f4d9j23",
        a9b677: "fly5x3f",
        Bkecrkj: "f1aehjj5"
    },
    next: {},
    prev: {},
    autoplay: {},
    inline: {
        B6of3ja: "f1mevb6"
    },
    overlay: {
        qhf8xq: "f1euv43f",
        B5kzvoi: "f1bu4tuc",
        B7ck84d: "f1ewtqcl"
    },
    overlayWide: {
        B5kzvoi: "f1bu4tuc"
    },
    nextWide: {
        Frg6f3: [
            "fcgxt0o",
            "f1ujusj6"
        ]
    },
    prevWide: {
        t21cq0: [
            "f1ujusj6",
            "fcgxt0o"
        ]
    },
    nextOverlayWide: {
        t21cq0: [
            "fkujibs",
            "f199hnxi"
        ]
    },
    prevOverlayWide: {
        Frg6f3: [
            "f199hnxi",
            "fkujibs"
        ]
    },
    autoplayOverlayWide: {
        Frg6f3: [
            "f199hnxi",
            "fkujibs"
        ]
    },
    expanded: {
        a9b677: "fly5x3f",
        Bqenvij: "f1l02sjl",
        Bt984gj: "fgs5rwf",
        Brf1p80: "f4d9j23",
        B5kzvoi: "f1yab3r1",
        c7y7m3: "f1ok8cdc",
        yx0ijg: "fcq6x80",
        v4amzz: "f1gy9d85"
    },
    nextOverlayExpanded: {
        qhf8xq: "f1euv43f",
        j35jbq: [
            "f8b87gs",
            "fedtrts"
        ],
        Bhzewxz: "f1i1t8d1",
        Bz10aip: "f188r07x"
    },
    prevOverlayExpanded: {
        qhf8xq: "f1euv43f",
        oyh7mz: [
            "fedtrts",
            "f8b87gs"
        ],
        Bhzewxz: "f1i1t8d1",
        Bz10aip: "f188r07x"
    },
    autoplayExpanded: {
        qhf8xq: "f1euv43f",
        B5kzvoi: "f49tsn4",
        oyh7mz: [
            "fedtrts",
            "f8b87gs"
        ],
        jrapky: "f18zxyen"
    }
}, {
    d: [
        ".f22iagw{display:flex;}",
        ".f1063pyq{flex-direction:row;}",
        ".f4d9j23{justify-content:center;}",
        ".fly5x3f{width:100%;}",
        ".f1aehjj5{pointer-events:none;}",
        ".f1mevb6{margin-top:var(--spacingVerticalM);}",
        ".f1euv43f{position:absolute;}",
        ".f1bu4tuc{bottom:var(--spacingVerticalM);}",
        ".f1ewtqcl{box-sizing:border-box;}",
        ".fcgxt0o{margin-left:auto;}",
        ".f1ujusj6{margin-right:auto;}",
        ".fkujibs{margin-right:var(--spacingHorizontalM);}",
        ".f199hnxi{margin-left:var(--spacingHorizontalM);}",
        ".f1l02sjl{height:100%;}",
        ".fgs5rwf{align-items:flex-end;}",
        ".f1yab3r1{bottom:0;}",
        ".f1ok8cdc>div{position:relative;}",
        ".fcq6x80>div{bottom:var(--spacingVerticalL);}",
        ".f1gy9d85>div{margin-bottom:0;}",
        ".f8b87gs{right:var(--spacingHorizontalM);}",
        ".fedtrts{left:var(--spacingHorizontalM);}",
        ".f1i1t8d1{top:50%;}",
        ".f188r07x{transform:translateY(-50%);}",
        ".f49tsn4{bottom:-var(--spacingHorizontalXS);}",
        ".f18zxyen{margin-bottom:var(--spacingVerticalM);}"
    ]
});
const useCarouselNavContainerStyles_unstable = (state)=>{
    'use no memo';
    const { layout } = state;
    const isOverlay = layout === 'overlay' || layout === 'overlay-wide' || layout === 'overlay-expanded';
    const isWide = layout === 'inline-wide' || layout === 'overlay-wide';
    const styles = useStyles();
    state.root.className = (0, _react.mergeClasses)(carouselNavContainerClassNames.root, styles.root, isOverlay ? styles.overlay : styles.inline, isOverlay && isWide && styles.overlayWide, layout === 'overlay-expanded' && styles.expanded, state.root.className);
    if (state.next) {
        state.next.className = (0, _react.mergeClasses)(carouselNavContainerClassNames.next, styles.next, isWide && styles.nextWide, isWide && isOverlay && styles.nextOverlayWide, layout === 'overlay-expanded' && styles.nextOverlayExpanded, state.next.className);
    }
    if (state.prev) {
        state.prev.className = (0, _react.mergeClasses)(carouselNavContainerClassNames.prev, styles.prev, isWide && styles.prevWide, !state.autoplay && isWide && isOverlay && styles.prevOverlayWide, layout === 'overlay-expanded' && styles.prevOverlayExpanded, state.prev.className);
    }
    if (state.autoplay) {
        state.autoplay.className = (0, _react.mergeClasses)(carouselNavContainerClassNames.autoplay, styles.autoplay, layout === 'overlay-expanded' && styles.autoplayExpanded, isWide && isOverlay && styles.autoplayOverlayWide, state.autoplay.className);
    }
    return state;
};
