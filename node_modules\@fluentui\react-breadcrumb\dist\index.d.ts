/// <reference types="react" />

import { ButtonProps } from '@fluentui/react-button';
import { ButtonSlots } from '@fluentui/react-button';
import { ButtonState } from '@fluentui/react-button';
import type { ComponentProps } from '@fluentui/react-utilities';
import type { ComponentState } from '@fluentui/react-utilities';
import type { ForwardRefComponent } from '@fluentui/react-utilities';
import * as React_2 from 'react';
import type { Slot } from '@fluentui/react-utilities';
import type { SlotClassNames } from '@fluentui/react-utilities';

/**
 * Breadcrumb component - TODO: add more docs
 */
export declare const Breadcrumb: ForwardRefComponent<BreadcrumbProps>;

/**
 * A button component which is used inside the Breadcrumb.
 */
export declare const BreadcrumbButton: ForwardRefComponent<BreadcrumbButtonProps>;

/**
 * Static CSS class names used internally for the component slots.
 */
export declare const breadcrumbButtonClassNames: SlotClassNames<BreadcrumbButtonSlots>;

/**
 * BreadcrumbButton Props
 */
export declare type BreadcrumbButtonProps = ComponentProps<BreadcrumbButtonSlots> & Pick<BreadcrumbProps, 'size'> & Pick<ButtonProps, 'disabled' | 'disabledFocusable'> & {
    /**
     * Defines current sate of BreadcrumbButton.
     *
     * @default false
     */
    current?: boolean;
};

export declare type BreadcrumbButtonSlots = ButtonSlots;

/**
 * State used in rendering BreadcrumbButton
 */
export declare type BreadcrumbButtonState = ComponentState<BreadcrumbButtonSlots> & Omit<ButtonState, keyof ButtonSlots | 'components'> & Required<Pick<BreadcrumbButtonProps, 'current' | 'size'>>;

export declare const breadcrumbClassNames: SlotClassNames<BreadcrumbSlots>;

/**
 * Data shared between breadcrumb components
 */
export declare type BreadcrumbContextValues = Required<Pick<BreadcrumbProps, 'size'>>;

/**
 * A divider component which is used inside the Breadcrumb
 */
export declare const BreadcrumbDivider: ForwardRefComponent<BreadcrumbDividerProps>;

export declare const breadcrumbDividerClassNames: SlotClassNames<BreadcrumbDividerSlots>;

/**
 * BreadcrumbDivider Props
 */
export declare type BreadcrumbDividerProps = ComponentProps<BreadcrumbDividerSlots> & {};

export declare type BreadcrumbDividerSlots = {
    root: Slot<'li'>;
};

/**
 * State used in rendering BreadcrumbDivider
 */
export declare type BreadcrumbDividerState = ComponentState<BreadcrumbDividerSlots> & Pick<BreadcrumbProps, 'size'>;

/**
 * BreadcrumbItem component is a wrapper for BreadcrumbLink and BreadcrumbButton.
 * It can be used as a non-interactive item.
 */
export declare const BreadcrumbItem: ForwardRefComponent<BreadcrumbItemProps>;

export declare const breadcrumbItemClassNames: SlotClassNames<BreadcrumbItemSlots>;

/**
 * BreadcrumbItem Props
 */
export declare type BreadcrumbItemProps = ComponentProps<BreadcrumbItemSlots> & Pick<BreadcrumbProps, 'size'>;

export declare type BreadcrumbItemSlots = {
    root: Slot<'li'>;
};

/**
 * State used in rendering BreadcrumbItem
 */
export declare type BreadcrumbItemState = ComponentState<BreadcrumbItemSlots> & Required<Pick<BreadcrumbItemProps, 'size'>>;

/**
 * Breadcrumb Props
 */
export declare type BreadcrumbProps = ComponentProps<BreadcrumbSlots> & {
    /**
     * Sets the focus behavior for the Breadcrumb.
     *
     * `tab`
     * This behaviour will cycle through all elements inside of the Breadcrumb when pressing the Tab key and then release focus
     * after the last inner element.
     *
     * `arrow`
     * This behaviour will cycle through all elements inside of the Breadcrumb when pressing the Arrow key.
     *
     * @default 'tab'
     */
    focusMode?: 'arrow' | 'tab';
    /**
     * Controls size of Breadcrumb items and dividers.
     *
     * @default 'medium'
     */
    size?: 'small' | 'medium' | 'large';
};

/**
 * @internal
 */
export declare const BreadcrumbProvider: React_2.Provider<Required<Pick<BreadcrumbProps, "size">> | undefined>;

export declare type BreadcrumbSlots = {
    /**
     * Root element of the component.
     */
    root: Slot<'nav'>;
    /**
     * Ordered list which contains items.
     */
    list?: Slot<'ol'>;
};

/**
 * State used in rendering Breadcrumb
 */
export declare type BreadcrumbState = ComponentState<BreadcrumbSlots> & Required<Pick<BreadcrumbProps, 'size'>>;

export declare const isTruncatableBreadcrumbContent: (content: string, maxLength: number) => boolean;

export declare type PartitionBreadcrumbItems<T> = {
    startDisplayedItems: readonly T[];
    overflowItems?: readonly T[];
    endDisplayedItems?: readonly T[];
};

/**
 * Get the displayed items and overflowing items based on the array of BreadcrumbItems needed for Breadcrumb.
 *
 * @param options - Configure the partition options
 *
 * @returns Three arrays split into displayed items and overflow items based on maxDisplayedItems.
 */
export declare const partitionBreadcrumbItems: <T>(options: PartitionBreadcrumbItemsOptions<T>) => PartitionBreadcrumbItems<T>;

export declare type PartitionBreadcrumbItemsOptions<T> = {
    items: readonly T[];
    maxDisplayedItems?: number;
    overflowIndex?: number;
};

/**
 * Render the final JSX of Breadcrumb
 */
export declare const renderBreadcrumb_unstable: (state: BreadcrumbState, contextValues: BreadcrumbContextValues) => JSX.Element;

/**
 * Render the final JSX of BreadcrumbButton
 */
export declare const renderBreadcrumbButton_unstable: (state: BreadcrumbButtonState) => JSX.Element;

/**
 * Render the final JSX of BreadcrumbDivider
 */
export declare const renderBreadcrumbDivider_unstable: (state: BreadcrumbDividerState) => JSX.Element;

/**
 * Render the final JSX of BreadcrumbItem
 */
export declare const renderBreadcrumbItem_unstable: (state: BreadcrumbItemState) => JSX.Element;

export declare const truncateBreadcrumbLongName: (content: string, maxLength?: number) => string;

export declare const truncateBreadcrumLongTooltip: (content: string, maxLength?: number) => string;

/**
 * Create the state required to render Breadcrumb.
 *
 * The returned state can be modified with hooks such as useBreadcrumbStyles_unstable,
 * before being passed to renderBreadcrumb_unstable.
 *
 * @param props - props from this instance of Breadcrumb
 * @param ref - reference to root HTMLElement of Breadcrumb
 */
export declare const useBreadcrumb_unstable: (props: BreadcrumbProps, ref: React_2.Ref<HTMLElement>) => BreadcrumbState;

/**
 * Create the state required to render BreadcrumbButton.
 *
 * The returned state can be modified with hooks such as useBreadcrumbButtonStyles_unstable,
 * before being passed to renderBreadcrumbButton_unstable.
 *
 * @param props - props from this instance of BreadcrumbButton
 * @param ref - reference to root HTMLElement of BreadcrumbButton
 */
export declare const useBreadcrumbButton_unstable: (props: BreadcrumbButtonProps, ref: React_2.Ref<HTMLButtonElement | HTMLAnchorElement>) => BreadcrumbButtonState;

/**
 * Apply styling to the BreadcrumbButton slots based on the state
 */
export declare const useBreadcrumbButtonStyles_unstable: (state: BreadcrumbButtonState) => BreadcrumbButtonState;

/**
 * @internal
 */
export declare const useBreadcrumbContext_unstable: () => Required<Pick<BreadcrumbProps, "size">>;

/**
 * Create the state required to render BreadcrumbDivider.
 *
 * The returned state can be modified with hooks such as useBreadcrumbDividerStyles_unstable,
 * before being passed to renderBreadcrumbDivider_unstable.
 *
 * @param props - props from this instance of BreadcrumbDivider
 * @param ref - reference to root HTMLElement of BreadcrumbDivider
 */
export declare const useBreadcrumbDivider_unstable: (props: BreadcrumbDividerProps, ref: React_2.Ref<HTMLLIElement>) => BreadcrumbDividerState;

/**
 * Apply styling to the BreadcrumbDivider slots based on the state
 */
export declare const useBreadcrumbDividerStyles_unstable: (state: BreadcrumbDividerState) => BreadcrumbDividerState;

/**
 * Create the state required to render BreadcrumbItem.
 *
 * The returned state can be modified with hooks such as useBreadcrumbItemStyles_unstable,
 * before being passed to renderBreadcrumbItem_unstable.
 *
 * @param props - props from this instance of BreadcrumbItem
 * @param ref - reference to root HTMLElement of BreadcrumbItem
 */
export declare const useBreadcrumbItem_unstable: (props: BreadcrumbItemProps, ref: React_2.Ref<HTMLLIElement>) => BreadcrumbItemState;

/**
 * Apply styling to the BreadcrumbItem slots based on the state
 */
export declare const useBreadcrumbItemStyles_unstable: (state: BreadcrumbItemState) => BreadcrumbItemState;

/**
 * Apply styling to the Breadcrumb slots based on the state
 */
export declare const useBreadcrumbStyles_unstable: (state: BreadcrumbState) => BreadcrumbState;

export { }
