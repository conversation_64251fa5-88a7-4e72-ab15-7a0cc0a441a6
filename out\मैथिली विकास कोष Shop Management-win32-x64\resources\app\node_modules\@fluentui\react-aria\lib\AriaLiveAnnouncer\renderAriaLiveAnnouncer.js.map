{"version": 3, "sources": ["../src/AriaLiveAnnouncer/renderAriaLiveAnnouncer.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { AnnounceProvider } from '@fluentui/react-shared-contexts';\n\nimport type { AriaLiveAnnouncerContextValues, AriaLiveAnnouncerState } from './AriaLiveAnnouncer.types';\n\nexport const renderAriaLiveAnnouncer_unstable = (\n  state: AriaLiveAnnouncerState,\n  contextValues: AriaLiveAnnouncerContextValues,\n) => {\n  return <AnnounceProvider value={contextValues.announce}>{state.children}</AnnounceProvider>;\n};\n"], "names": ["React", "Ann<PERSON><PERSON><PERSON><PERSON><PERSON>", "renderAriaLiveAnnouncer_unstable", "state", "contextValues", "value", "announce", "children"], "rangeMappings": ";;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,gBAAgB,QAAQ,kCAAkC;AAInE,OAAO,MAAMC,mCAAmC,CAC9CC,OACAC;IAEA,qBAAO,oBAACH;QAAiBI,OAAOD,cAAcE,QAAQ;OAAGH,MAAMI,QAAQ;AACzE,EAAE"}