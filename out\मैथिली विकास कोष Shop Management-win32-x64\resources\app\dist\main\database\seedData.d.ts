/**
 * Seed sample data for Maithili Vikas Kosh Shop Management System
 */
export declare class SeedDataService {
    /**
     * Seed all sample data
     */
    seedAll(): Promise<void>;
    /**
     * Seed users
     */
    private seedUsers;
    /**
     * Seed categories
     */
    private seedCategories;
    /**
     * Seed artists
     */
    private seedArtists;
    /**
     * Seed products
     */
    private seedProducts;
    /**
     * Seed customers
     */
    private seedCustomers;
    /**
     * Seed inventory
     */
    private seedInventory;
    /**
     * Check if data seeding is needed
     */
    isDataSeedingNeeded(): Promise<boolean>;
}
export declare const seedDataService: SeedDataService;
//# sourceMappingURL=seedData.d.ts.map