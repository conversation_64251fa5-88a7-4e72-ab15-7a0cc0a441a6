{"version": 3, "sources": ["../src/components/AccordionItem/useAccordionItem.ts"], "sourcesContent": ["import * as React from 'react';\nimport { getIntrinsicElementProps, slot, useEventCallback } from '@fluentui/react-utilities';\nimport type { AccordionItemProps, AccordionItemState } from './AccordionItem.types';\nimport type { AccordionToggleEvent } from '../Accordion/Accordion.types';\nimport { useAccordionContext_unstable } from '../../contexts/accordion';\n\n/**\n * Returns the props and state required to render the component\n * @param props - AccordionItem properties\n * @param ref - reference to root HTMLElement of AccordionItem\n */\nexport const useAccordionItem_unstable = (\n  props: AccordionItemProps,\n  ref: React.Ref<HTMLElement>,\n): AccordionItemState => {\n  const { value, disabled = false } = props;\n\n  const requestToggle = useAccordionContext_unstable(ctx => ctx.requestToggle);\n  const open = useAccordionContext_unstable(ctx => ctx.openItems.includes(value));\n  const onAccordionHeaderClick = useEventCallback((event: AccordionToggleEvent) => requestToggle({ event, value }));\n\n  return {\n    open,\n    value,\n    disabled,\n    onHeaderClick: onAccordionHeaderClick,\n    components: {\n      root: 'div',\n    },\n    root: slot.always(\n      getIntrinsicElementProps('div', {\n        // FIXME:\n        // `ref` is wrongly assigned to be `HTMLElement` instead of `HTMLDivElement`\n        // but since it would be a breaking change to fix it, we are casting ref to it's proper type\n        ref: ref as React.Ref<HTMLDivElement>,\n        ...props,\n      }),\n      { elementType: 'div' },\n    ),\n  };\n};\n"], "names": ["useAccordionItem_unstable", "props", "ref", "value", "disabled", "requestToggle", "useAccordionContext_unstable", "ctx", "open", "openItems", "includes", "onAccordionHeaderClick", "useEventCallback", "event", "onHeaderClick", "components", "root", "slot", "always", "getIntrinsicElementProps", "elementType"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAWaA;;;eAAAA;;;;iEAXU;gCAC0C;2BAGpB;AAOtC,MAAMA,4BAA4B,CACvCC,OACAC;IAEA,MAAM,EAAEC,KAAK,EAAEC,WAAW,KAAK,EAAE,GAAGH;IAEpC,MAAMI,gBAAgBC,IAAAA,uCAAAA,EAA6BC,CAAAA,MAAOA,IAAIF,aAAa;IAC3E,MAAMG,OAAOF,IAAAA,uCAAAA,EAA6BC,CAAAA,MAAOA,IAAIE,SAAS,CAACC,QAAQ,CAACP;IACxE,MAAMQ,yBAAyBC,IAAAA,gCAAAA,EAAiB,CAACC,QAAgCR,cAAc;YAAEQ;YAAOV;QAAM;IAE9G,OAAO;QACLK;QACAL;QACAC;QACAU,eAAeH;QACfI,YAAY;YACVC,MAAM;QACR;QACAA,MAAMC,oBAAAA,CAAKC,MAAM,CACfC,IAAAA,wCAAAA,EAAyB,OAAO;YAC9B,SAAS;YACT,4EAA4E;YAC5E,4FAA4F;YAC5FjB,KAAKA;YACL,GAAGD,KAAK;QACV,IACA;YAAEmB,aAAa;QAAM;IAEzB;AACF"}