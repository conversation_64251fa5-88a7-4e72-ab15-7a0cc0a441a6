{"version": 3, "names": ["createCustomFocusIndicatorStyle", "__styles", "mergeClasses", "shorthands", "tokens", "typographyStyles", "useGroupChildClassName", "useSizeStyles", "avatarGroupPopoverClassNames", "root", "content", "popoverSurface", "tooltip", "trigger<PERSON>utton", "useContentStyles", "base", "dclx09", "j<PERSON><PERSON>", "Frg6f3", "t21cq0", "B6of3ja", "B74szlk", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "mc9l5x", "Beiy3e4", "d", "p", "usePopoverSurfaceStyles", "Bxyxcbc", "sshi5w", "B68tc82", "Bmxbyg5", "Bpg54ce", "a9b677", "useTriggerButtonStyles", "qhf8xq", "Bnnss6s", "Brf1p80", "Bt984gj", "sj55zd", "De3pzq", "g2u3we", "h3c5rm", "B9xav0g", "zhjwy3", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "<PERSON><PERSON><PERSON><PERSON>", "vrafjx", "oivjwe", "wvpqe5", "Bjwas2f", "Bn1d65q", "Bxeuatn", "n51gp8", "pie", "focusIndicator", "Byu6kyc", "n8qw10", "Bbjhlyh", "i2cumq", "Bunx835", "Bdrgwmp", "mqozju", "lbo84a", "Bksnhdo", "Bci5o5g", "u5e7qz", "Bn40d3w", "B7b6zxw", "B8q5s1w", "B5gfjzb", "Bbcte9g", "Bqz3imu", "g9k6zt", "states", "Bi91k9c", "Jwef8y", "Bgoe8wy", "Bwzppfd", "oetu4i", "gg5e9n", "lj723h", "ecr2s2", "B6oc9vd", "ak43y8", "wmxk5l", "B50zh58", "selected", "icon12", "Be2twd7", "icon16", "icon20", "icon24", "icon28", "icon32", "icon48", "caption2Strong", "Bahqtrf", "Bhrd7zp", "Bg96gwp", "caption1Strong", "body1Strong", "subtitle2", "subtitle1", "title3", "borderThin", "B4j52fo", "Bekrc4i", "Bn0qgzm", "ibv6hh", "borderThick", "borderThicker", "borderThickest", "m", "h", "a", "useAvatarGroupPopoverStyles_unstable", "state", "indicator", "size", "layout", "popoverOpen", "sizeStyles", "triggerButtonStyles", "contentStyles", "popoverSurfaceStyles", "groupChildClassName", "triggerButtonClasses", "push", "className"], "sources": ["useAvatarGroupPopoverStyles.styles.js"], "sourcesContent": ["import { createCustomFocusIndicatorStyle } from '@fluentui/react-tabster';\nimport { makeStyles, mergeClasses, shorthands } from '@griffel/react';\nimport { tokens, typographyStyles } from '@fluentui/react-theme';\nimport { useGroupChildClassName } from '../AvatarGroupItem/useAvatarGroupItemStyles.styles';\nimport { useSizeStyles } from '../Avatar/useAvatarStyles.styles';\nexport const avatarGroupPopoverClassNames = {\n    root: 'fui-AvatarGroupPopover',\n    content: 'fui-AvatarGroupPopover__content',\n    popoverSurface: 'fui-AvatarGroupPopover__popoverSurface',\n    tooltip: 'fui-AvatarGroupPopover__tooltip',\n    triggerButton: 'fui-AvatarGroupPopover__triggerButton'\n};\n/**\n * Styles for the content slot.\n */ const useContentStyles = makeStyles({\n    base: {\n        listStyleType: 'none',\n        margin: '0',\n        padding: '0',\n        display: 'flex',\n        flexDirection: 'column'\n    }\n});\n/**\n * Styles for the popoverSurface slot.\n */ const usePopoverSurfaceStyles = makeStyles({\n    base: {\n        maxHeight: '220px',\n        minHeight: '80px',\n        overflow: 'hidden scroll',\n        padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalS}`,\n        width: '220px'\n    }\n});\n/**\n * Styles for the triggerButton slot.\n */ const useTriggerButtonStyles = makeStyles({\n    base: {\n        display: 'inline-flex',\n        position: 'relative',\n        flexShrink: 0,\n        justifyContent: 'center',\n        alignItems: 'center',\n        color: tokens.colorNeutralForeground1,\n        backgroundColor: tokens.colorNeutralBackground1,\n        ...shorthands.borderColor(tokens.colorNeutralStroke1),\n        borderRadius: tokens.borderRadiusCircular,\n        ...shorthands.borderStyle('solid'),\n        padding: '0',\n        // Match color to Avatar's outline color.\n        '@media (forced-colors: active)': {\n            ...shorthands.borderColor('CanvasText')\n        }\n    },\n    pie: {\n        backgroundColor: tokens.colorTransparentBackground,\n        ...shorthands.borderColor(tokens.colorTransparentStroke),\n        color: 'transparent'\n    },\n    focusIndicator: createCustomFocusIndicatorStyle({\n        border: `${tokens.strokeWidthThick} solid ${tokens.colorStrokeFocus2}`,\n        outlineStyle: 'none'\n    }),\n    states: {\n        '&:hover': {\n            color: tokens.colorNeutralForeground1Hover,\n            backgroundColor: tokens.colorNeutralBackground1Hover,\n            ...shorthands.borderColor(tokens.colorNeutralStroke1Hover)\n        },\n        '&:active': {\n            color: tokens.colorNeutralForeground1Pressed,\n            backgroundColor: tokens.colorNeutralBackground1Pressed,\n            ...shorthands.borderColor(tokens.colorNeutralStroke1Pressed)\n        }\n    },\n    selected: {\n        color: tokens.colorNeutralForeground1Selected,\n        backgroundColor: tokens.colorNeutralBackground1Selected,\n        ...shorthands.borderColor(tokens.colorNeutralStroke1Selected)\n    },\n    icon12: {\n        fontSize: '12px'\n    },\n    icon16: {\n        fontSize: '16px'\n    },\n    icon20: {\n        fontSize: '20px'\n    },\n    icon24: {\n        fontSize: '24px'\n    },\n    icon28: {\n        fontSize: '28px'\n    },\n    icon32: {\n        fontSize: '32px'\n    },\n    icon48: {\n        fontSize: '48px'\n    },\n    caption2Strong: {\n        ...typographyStyles.caption2Strong\n    },\n    caption1Strong: {\n        ...typographyStyles.caption1Strong\n    },\n    body1Strong: {\n        ...typographyStyles.body1Strong\n    },\n    subtitle2: {\n        ...typographyStyles.subtitle2\n    },\n    subtitle1: {\n        ...typographyStyles.subtitle1\n    },\n    title3: {\n        ...typographyStyles.title3\n    },\n    borderThin: {\n        ...shorthands.borderWidth(tokens.strokeWidthThin)\n    },\n    borderThick: {\n        ...shorthands.borderWidth(tokens.strokeWidthThick)\n    },\n    borderThicker: {\n        ...shorthands.borderWidth(tokens.strokeWidthThicker)\n    },\n    borderThickest: {\n        ...shorthands.borderWidth(tokens.strokeWidthThickest)\n    }\n});\n/**\n * Apply styling to the AvatarGroupPopover slots based on the state\n */ export const useAvatarGroupPopoverStyles_unstable = (state)=>{\n    'use no memo';\n    const { indicator, size, layout, popoverOpen } = state;\n    const sizeStyles = useSizeStyles();\n    const triggerButtonStyles = useTriggerButtonStyles();\n    const contentStyles = useContentStyles();\n    const popoverSurfaceStyles = usePopoverSurfaceStyles();\n    const groupChildClassName = useGroupChildClassName(layout, size);\n    const triggerButtonClasses = [];\n    if (size < 36) {\n        triggerButtonClasses.push(triggerButtonStyles.borderThin);\n    } else if (size < 56) {\n        triggerButtonClasses.push(triggerButtonStyles.borderThick);\n    } else if (size < 72) {\n        triggerButtonClasses.push(triggerButtonStyles.borderThicker);\n    } else {\n        triggerButtonClasses.push(triggerButtonStyles.borderThickest);\n    }\n    if (indicator === 'count') {\n        if (size <= 24) {\n            triggerButtonClasses.push(triggerButtonStyles.caption2Strong);\n        } else if (size <= 28) {\n            triggerButtonClasses.push(triggerButtonStyles.caption1Strong);\n        } else if (size <= 40) {\n            triggerButtonClasses.push(triggerButtonStyles.body1Strong);\n        } else if (size <= 56) {\n            triggerButtonClasses.push(triggerButtonStyles.subtitle2);\n        } else if (size <= 96) {\n            triggerButtonClasses.push(triggerButtonStyles.subtitle1);\n        } else {\n            triggerButtonClasses.push(triggerButtonStyles.title3);\n        }\n    } else {\n        if (size <= 16) {\n            triggerButtonClasses.push(triggerButtonStyles.icon12);\n        } else if (size <= 24) {\n            triggerButtonClasses.push(triggerButtonStyles.icon16);\n        } else if (size <= 40) {\n            triggerButtonClasses.push(triggerButtonStyles.icon20);\n        } else if (size <= 48) {\n            triggerButtonClasses.push(triggerButtonStyles.icon24);\n        } else if (size <= 56) {\n            triggerButtonClasses.push(triggerButtonStyles.icon28);\n        } else if (size <= 72) {\n            triggerButtonClasses.push(triggerButtonStyles.icon32);\n        } else {\n            triggerButtonClasses.push(triggerButtonStyles.icon48);\n        }\n    }\n    state.triggerButton.className = mergeClasses(avatarGroupPopoverClassNames.triggerButton, groupChildClassName, sizeStyles[size], triggerButtonStyles.base, layout === 'pie' && triggerButtonStyles.pie, triggerButtonStyles.focusIndicator, layout !== 'pie' && triggerButtonStyles.states, layout !== 'pie' && popoverOpen && triggerButtonStyles.selected, ...triggerButtonClasses, state.triggerButton.className);\n    state.content.className = mergeClasses(avatarGroupPopoverClassNames.content, contentStyles.base, state.content.className);\n    state.popoverSurface.className = mergeClasses(avatarGroupPopoverClassNames.popoverSurface, popoverSurfaceStyles.base, state.popoverSurface.className);\n    return state;\n};\n"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,yBAAyB;AACzE,SAAAC,QAAA,EAAqBC,YAAY,EAAEC,UAAU,QAAQ,gBAAgB;AACrE,SAASC,MAAM,EAAEC,gBAAgB,QAAQ,uBAAuB;AAChE,SAASC,sBAAsB,QAAQ,oDAAoD;AAC3F,SAASC,aAAa,QAAQ,kCAAkC;AAChE,OAAO,MAAMC,4BAA4B,GAAG;EACxCC,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,EAAE,iCAAiC;EAC1CC,cAAc,EAAE,wCAAwC;EACxDC,OAAO,EAAE,iCAAiC;EAC1CC,aAAa,EAAE;AACnB,CAAC;AACD;AACA;AACA;AAAI,MAAMC,gBAAgB,gBAAGb,QAAA;EAAAc,IAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;AAAA;EAAAC,CAAA;IAAAC,CAAA;EAAA;IAAAA,CAAA;EAAA;AAAA,CAQ5B,CAAC;AACF;AACA;AACA;AAAI,MAAMC,uBAAuB,gBAAG9B,QAAA;EAAAc,IAAA;IAAAiB,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAd,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAW,MAAA;EAAA;AAAA;EAAAR,CAAA;IAAAC,CAAA;EAAA;IAAAA,CAAA;EAAA;AAAA,CAQnC,CAAC;AACF;AACA;AACA;AAAI,MAAMQ,sBAAsB,gBAAGrC,QAAA;EAAAc,IAAA;IAAAY,MAAA;IAAAY,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAnC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAgC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAC,GAAA;IAAAlB,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAL,MAAA;EAAA;EAAAoB,cAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;EAAAC,QAAA;IAAApD,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAgD,MAAA;IAAAC,OAAA;EAAA;EAAAC,MAAA;IAAAD,OAAA;EAAA;EAAAE,MAAA;IAAAF,OAAA;EAAA;EAAAG,MAAA;IAAAH,OAAA;EAAA;EAAAI,MAAA;IAAAJ,OAAA;EAAA;EAAAK,MAAA;IAAAL,OAAA;EAAA;EAAAM,MAAA;IAAAN,OAAA;EAAA;EAAAO,cAAA;IAAAC,OAAA;IAAAR,OAAA;IAAAS,OAAA;IAAAC,OAAA;EAAA;EAAAC,cAAA;IAAAH,OAAA;IAAAR,OAAA;IAAAS,OAAA;IAAAC,OAAA;EAAA;EAAAE,WAAA;IAAAJ,OAAA;IAAAR,OAAA;IAAAS,OAAA;IAAAC,OAAA;EAAA;EAAAG,SAAA;IAAAL,OAAA;IAAAR,OAAA;IAAAS,OAAA;IAAAC,OAAA;EAAA;EAAAI,SAAA;IAAAN,OAAA;IAAAR,OAAA;IAAAS,OAAA;IAAAC,OAAA;EAAA;EAAAK,MAAA;IAAAP,OAAA;IAAAR,OAAA;IAAAS,OAAA;IAAAC,OAAA;EAAA;EAAAM,UAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAC,WAAA;IAAAJ,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAE,aAAA;IAAAL,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAG,cAAA;IAAAN,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;AAAA;EAAAxF,CAAA;IAAAC,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;EAAA2F,CAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;EAAAC,CAAA;EAAAC,CAAA;AAAA,CA+FlC,CAAC;AACF;AACA;AACA;AAAI,OAAO,MAAMC,oCAAoC,GAAIC,KAAK,IAAG;EAC7D,aAAa;;EACb,MAAM;IAAEC,SAAS;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAY,CAAC,GAAGJ,KAAK;EACtD,MAAMK,UAAU,GAAG3H,aAAa,CAAC,CAAC;EAClC,MAAM4H,mBAAmB,GAAG7F,sBAAsB,CAAC,CAAC;EACpD,MAAM8F,aAAa,GAAGtH,gBAAgB,CAAC,CAAC;EACxC,MAAMuH,oBAAoB,GAAGtG,uBAAuB,CAAC,CAAC;EACtD,MAAMuG,mBAAmB,GAAGhI,sBAAsB,CAAC0H,MAAM,EAAED,IAAI,CAAC;EAChE,MAAMQ,oBAAoB,GAAG,EAAE;EAC/B,IAAIR,IAAI,GAAG,EAAE,EAAE;IACXQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAAClB,UAAU,CAAC;EAC7D,CAAC,MAAM,IAAIc,IAAI,GAAG,EAAE,EAAE;IAClBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACb,WAAW,CAAC;EAC9D,CAAC,MAAM,IAAIS,IAAI,GAAG,EAAE,EAAE;IAClBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACZ,aAAa,CAAC;EAChE,CAAC,MAAM;IACHgB,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACX,cAAc,CAAC;EACjE;EACA,IAAIM,SAAS,KAAK,OAAO,EAAE;IACvB,IAAIC,IAAI,IAAI,EAAE,EAAE;MACZQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAAC3B,cAAc,CAAC;IACjE,CAAC,MAAM,IAAIuB,IAAI,IAAI,EAAE,EAAE;MACnBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACvB,cAAc,CAAC;IACjE,CAAC,MAAM,IAAImB,IAAI,IAAI,EAAE,EAAE;MACnBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACtB,WAAW,CAAC;IAC9D,CAAC,MAAM,IAAIkB,IAAI,IAAI,EAAE,EAAE;MACnBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACrB,SAAS,CAAC;IAC5D,CAAC,MAAM,IAAIiB,IAAI,IAAI,EAAE,EAAE;MACnBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACpB,SAAS,CAAC;IAC5D,CAAC,MAAM;MACHwB,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACnB,MAAM,CAAC;IACzD;EACJ,CAAC,MAAM;IACH,IAAIe,IAAI,IAAI,EAAE,EAAE;MACZQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACnC,MAAM,CAAC;IACzD,CAAC,MAAM,IAAI+B,IAAI,IAAI,EAAE,EAAE;MACnBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAACjC,MAAM,CAAC;IACzD,CAAC,MAAM,IAAI6B,IAAI,IAAI,EAAE,EAAE;MACnBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAAChC,MAAM,CAAC;IACzD,CAAC,MAAM,IAAI4B,IAAI,IAAI,EAAE,EAAE;MACnBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAAC/B,MAAM,CAAC;IACzD,CAAC,MAAM,IAAI2B,IAAI,IAAI,EAAE,EAAE;MACnBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAAC9B,MAAM,CAAC;IACzD,CAAC,MAAM,IAAI0B,IAAI,IAAI,EAAE,EAAE;MACnBQ,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAAC7B,MAAM,CAAC;IACzD,CAAC,MAAM;MACHiC,oBAAoB,CAACC,IAAI,CAACL,mBAAmB,CAAC5B,MAAM,CAAC;IACzD;EACJ;EACAsB,KAAK,CAAChH,aAAa,CAAC4H,SAAS,GAAGvI,YAAY,CAACM,4BAA4B,CAACK,aAAa,EAAEyH,mBAAmB,EAAEJ,UAAU,CAACH,IAAI,CAAC,EAAEI,mBAAmB,CAACpH,IAAI,EAAEiH,MAAM,KAAK,KAAK,IAAIG,mBAAmB,CAACrE,GAAG,EAAEqE,mBAAmB,CAACpE,cAAc,EAAEiE,MAAM,KAAK,KAAK,IAAIG,mBAAmB,CAACjD,MAAM,EAAE8C,MAAM,KAAK,KAAK,IAAIC,WAAW,IAAIE,mBAAmB,CAACpC,QAAQ,EAAE,GAAGwC,oBAAoB,EAAEV,KAAK,CAAChH,aAAa,CAAC4H,SAAS,CAAC;EACnZZ,KAAK,CAACnH,OAAO,CAAC+H,SAAS,GAAGvI,YAAY,CAACM,4BAA4B,CAACE,OAAO,EAAE0H,aAAa,CAACrH,IAAI,EAAE8G,KAAK,CAACnH,OAAO,CAAC+H,SAAS,CAAC;EACzHZ,KAAK,CAAClH,cAAc,CAAC8H,SAAS,GAAGvI,YAAY,CAACM,4BAA4B,CAACG,cAAc,EAAE0H,oBAAoB,CAACtH,IAAI,EAAE8G,KAAK,CAAClH,cAAc,CAAC8H,SAAS,CAAC;EACrJ,OAAOZ,KAAK;AAChB,CAAC", "ignoreList": []}