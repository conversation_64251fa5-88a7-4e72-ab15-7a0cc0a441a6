# Change Log - @fluentui/react-breadcrumb

This log was last generated on Fri, 27 Jun 2025 13:36:33 GMT and should not be manually modified.

<!-- Start content -->

## [9.2.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.2.5)

Fri, 27 Jun 2025 13:36:33 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.2.4..@fluentui/react-breadcrumb_v9.2.5)

### Patches

- Bump @fluentui/react-aria to v9.15.3 ([PR #34734](https://github.com/microsoft/fluentui/pull/34734) by beachball)
- Bump @fluentui/react-button to v9.5.3 ([PR #34734](https://github.com/microsoft/fluentui/pull/34734) by beachball)
- Bump @fluentui/react-link to v9.5.3 ([PR #34734](https://github.com/microsoft/fluentui/pull/34734) by beachball)
- Bump @fluentui/react-tabster to v9.25.3 ([PR #34734](https://github.com/microsoft/fluentui/pull/34734) by beachball)

## [9.2.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.2.4)

Thu, 26 Jun 2025 14:11:55 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.2.2..@fluentui/react-breadcrumb_v9.2.4)

### Patches

- Bump @fluentui/react-aria to v9.15.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-button to v9.5.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-link to v9.5.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-tabster to v9.25.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-utilities to v9.22.0 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)

## [9.2.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.2.2)

Wed, 18 Jun 2025 17:34:00 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.2.0..@fluentui/react-breadcrumb_v9.2.2)

### Patches

- Bump @fluentui/react-aria to v9.15.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-button to v9.5.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-link to v9.5.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-shared-contexts to v9.24.0 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-tabster to v9.25.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-utilities to v9.21.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)

## [9.2.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.2.0)

Thu, 12 Jun 2025 09:43:33 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.1.8..@fluentui/react-breadcrumb_v9.2.0)

### Minor changes

- Bump @fluentui/react-aria to v9.15.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-button to v9.5.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-link to v9.5.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-tabster to v9.25.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-utilities to v9.21.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)

## [9.1.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.1.8)

Fri, 06 Jun 2025 13:15:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.1.7..@fluentui/react-breadcrumb_v9.1.8)

### Patches

- Bump @fluentui/react-aria to v9.14.8 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-button to v9.4.8 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-link to v9.4.8 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-tabster to v9.24.8 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-utilities to v9.20.0 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.55 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)

## [9.1.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.1.7)

Wed, 14 May 2025 18:49:20 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.1.6..@fluentui/react-breadcrumb_v9.1.7)

### Patches

- Bump @fluentui/react-aria to v9.14.7 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)
- Bump @fluentui/react-button to v9.4.7 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)
- Bump @fluentui/react-link to v9.4.7 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)
- Bump @fluentui/react-tabster to v9.24.7 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)

## [9.1.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.1.6)

Thu, 24 Apr 2025 09:59:45 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.1.5..@fluentui/react-breadcrumb_v9.1.6)

### Patches

- Bump @fluentui/react-aria to v9.14.6 ([PR #34315](https://github.com/microsoft/fluentui/pull/34315) by beachball)
- Bump @fluentui/react-button to v9.4.6 ([PR #34315](https://github.com/microsoft/fluentui/pull/34315) by beachball)
- Bump @fluentui/react-link to v9.4.6 ([PR #34315](https://github.com/microsoft/fluentui/pull/34315) by beachball)
- Bump @fluentui/react-tabster to v9.24.6 ([PR #34315](https://github.com/microsoft/fluentui/pull/34315) by beachball)

## [9.1.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.1.5)

Wed, 16 Apr 2025 19:42:18 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.1.4..@fluentui/react-breadcrumb_v9.1.5)

### Patches

- Bump @fluentui/react-aria to v9.14.5 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-button to v9.4.5 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-link to v9.4.5 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-tabster to v9.24.5 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-utilities to v9.19.0 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.54 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)

## [9.1.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.1.4)

Tue, 01 Apr 2025 15:08:02 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.1.3..@fluentui/react-breadcrumb_v9.1.4)

### Patches

- Bump @fluentui/react-aria to v9.14.4 ([PR #33909](https://github.com/microsoft/fluentui/pull/33909) by beachball)
- Bump @fluentui/react-button to v9.4.4 ([PR #33909](https://github.com/microsoft/fluentui/pull/33909) by beachball)
- Bump @fluentui/react-link to v9.4.4 ([PR #33909](https://github.com/microsoft/fluentui/pull/33909) by beachball)
- Bump @fluentui/react-tabster to v9.24.4 ([PR #33909](https://github.com/microsoft/fluentui/pull/33909) by beachball)

## [9.1.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.1.3)

Thu, 27 Mar 2025 21:12:51 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.1.2..@fluentui/react-breadcrumb_v9.1.3)

### Patches

- Bump @fluentui/react-aria to v9.14.3 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-button to v9.4.3 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-link to v9.4.3 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-shared-contexts to v9.23.1 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-tabster to v9.24.3 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-utilities to v9.18.23 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.53 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)

## [9.1.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.1.2)

Wed, 19 Mar 2025 15:40:43 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.1.1..@fluentui/react-breadcrumb_v9.1.2)

### Patches

- Bump @fluentui/react-aria to v9.14.2 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-button to v9.4.2 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-link to v9.4.2 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-shared-contexts to v9.23.0 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-tabster to v9.24.2 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-utilities to v9.18.22 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.52 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)

## [9.1.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.1.1)

Tue, 11 Mar 2025 18:58:54 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.1.0..@fluentui/react-breadcrumb_v9.1.1)

### Patches

- Bump @fluentui/react-aria to v9.14.1 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-button to v9.4.1 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-link to v9.4.1 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-shared-contexts to v9.22.0 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-tabster to v9.24.1 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-utilities to v9.18.21 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.51 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)

## [9.1.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.1.0)

Fri, 21 Feb 2025 14:34:05 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.52..@fluentui/react-breadcrumb_v9.1.0)

### Minor changes

- Bump @fluentui/react-aria to v9.14.0 ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by beachball)
- Bump @fluentui/react-button to v9.4.0 ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by beachball)
- Bump @fluentui/react-link to v9.4.0 ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by beachball)
- Bump @fluentui/react-tabster to v9.24.0 ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by beachball)

## [9.0.52](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.52)

Fri, 07 Feb 2025 10:42:12 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.51..@fluentui/react-breadcrumb_v9.0.52)

### Patches

- Bump @fluentui/react-button to v9.3.102 ([PR #33797](https://github.com/microsoft/fluentui/pull/33797) by beachball)

## [9.0.51](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.51)

Tue, 28 Jan 2025 21:26:35 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.49..@fluentui/react-breadcrumb_v9.0.51)

### Patches

- Bump @fluentui/react-button to v9.3.101 ([PR #33736](https://github.com/microsoft/fluentui/pull/33736) by beachball)

## [9.0.49](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.49)

Wed, 22 Jan 2025 14:00:21 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.48..@fluentui/react-breadcrumb_v9.0.49)

### Patches

- Bump @fluentui/react-aria to v9.13.14 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-button to v9.3.100 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-link to v9.3.7 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-tabster to v9.23.3 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-utilities to v9.18.20 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.50 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)

## [9.0.48](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.48)

Wed, 08 Jan 2025 18:33:36 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.46..@fluentui/react-breadcrumb_v9.0.48)

### Patches

- Bump @fluentui/react-aria to v9.13.13 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)
- Bump @fluentui/react-button to v9.3.99 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)
- Bump @fluentui/react-link to v9.3.6 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.49 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)

## [9.0.46](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.46)

Mon, 16 Dec 2024 16:26:49 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.45..@fluentui/react-breadcrumb_v9.0.46)

### Patches

- Bump @fluentui/react-aria to v9.13.12 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-button to v9.3.98 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-link to v9.3.5 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.2 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-tabster to v9.23.2 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-theme to v9.1.24 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-utilities to v9.18.19 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.48 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)

## [9.0.45](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.45)

Mon, 09 Dec 2024 17:38:09 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.44..@fluentui/react-breadcrumb_v9.0.45)

### Patches

- chore: remove usage of "export *" ([PR #33384](https://github.com/microsoft/fluentui/pull/33384) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.11 ([PR #33431](https://github.com/microsoft/fluentui/pull/33431) by beachball)
- Bump @fluentui/react-button to v9.3.97 ([PR #33431](https://github.com/microsoft/fluentui/pull/33431) by beachball)
- Bump @fluentui/react-link to v9.3.4 ([PR #33431](https://github.com/microsoft/fluentui/pull/33431) by beachball)

## [9.0.44](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.44)

Fri, 06 Dec 2024 12:53:38 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.42..@fluentui/react-breadcrumb_v9.0.44)

### Patches

- Updated README.md ([PR #33385](https://github.com/microsoft/fluentui/pull/33385) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.10 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-button to v9.3.96 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-link to v9.3.3 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.1 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-tabster to v9.23.1 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-theme to v9.1.23 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-utilities to v9.18.18 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.47 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)

## [9.0.42](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.42)

Mon, 11 Nov 2024 10:01:06 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.41..@fluentui/react-breadcrumb_v9.0.42)

### Patches

- chore: replace npm-scripts and just-scrtips with nx inferred tasks ([PR #33074](https://github.com/microsoft/fluentui/pull/33074) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.9 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-button to v9.3.95 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-link to v9.3.2 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.0 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-tabster to v9.23.0 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-theme to v9.1.22 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-utilities to v9.18.17 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.46 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)

## [9.0.41](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.41)

Tue, 15 Oct 2024 17:17:53 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.40..@fluentui/react-breadcrumb_v9.0.41)

### Patches

- Bump @fluentui/react-aria to v9.13.8 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-button to v9.3.94 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-link to v9.3.1 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-shared-contexts to v9.20.2 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-tabster to v9.22.9 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-theme to v9.1.21 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-utilities to v9.18.16 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.45 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)

## [9.0.40](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.40)

Tue, 08 Oct 2024 22:05:59 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.39..@fluentui/react-breadcrumb_v9.0.40)

### Patches

- Bump @fluentui/react-aria to v9.13.7 ([PR #33007](https://github.com/microsoft/fluentui/pull/33007) by beachball)
- Bump @fluentui/react-button to v9.3.93 ([PR #33007](https://github.com/microsoft/fluentui/pull/33007) by beachball)
- Bump @fluentui/react-link to v9.3.0 ([PR #33007](https://github.com/microsoft/fluentui/pull/33007) by beachball)
- Bump @fluentui/react-tabster to v9.22.8 ([PR #33007](https://github.com/microsoft/fluentui/pull/33007) by beachball)

## [9.0.39](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.39)

Thu, 26 Sep 2024 14:15:28 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.38..@fluentui/react-breadcrumb_v9.0.39)

### Patches

- Bump @fluentui/react-button to v9.3.92 ([PR #32924](https://github.com/microsoft/fluentui/pull/32924) by beachball)

## [9.0.38](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.38)

Mon, 23 Sep 2024 12:40:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.37..@fluentui/react-breadcrumb_v9.0.38)

### Patches

- Bump @fluentui/react-aria to v9.13.6 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-button to v9.3.91 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-link to v9.2.32 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-shared-contexts to v9.20.1 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-tabster to v9.22.7 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-theme to v9.1.20 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-utilities to v9.18.15 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.44 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)

## [9.0.37](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.37)

Tue, 10 Sep 2024 10:19:12 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.36..@fluentui/react-breadcrumb_v9.0.37)

### Patches

- Bump @fluentui/react-aria to v9.13.5 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-button to v9.3.90 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-link to v9.2.31 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-tabster to v9.22.6 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-utilities to v9.18.14 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.43 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)

## [9.0.36](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.36)

Thu, 15 Aug 2024 13:49:46 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.35..@fluentui/react-breadcrumb_v9.0.36)

### Patches

- Bump @fluentui/react-aria to v9.13.4 ([PR #32313](https://github.com/microsoft/fluentui/pull/32313) by beachball)
- Bump @fluentui/react-button to v9.3.89 ([PR #32313](https://github.com/microsoft/fluentui/pull/32313) by beachball)
- Bump @fluentui/react-link to v9.2.30 ([PR #32313](https://github.com/microsoft/fluentui/pull/32313) by beachball)
- Bump @fluentui/react-tabster to v9.22.5 ([PR #32313](https://github.com/microsoft/fluentui/pull/32313) by beachball)

## [9.0.35](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.35)

Thu, 15 Aug 2024 08:22:08 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.33..@fluentui/react-breadcrumb_v9.0.35)

### Patches

- Bump @fluentui/react-aria to v9.13.3 ([PR #31885](https://github.com/microsoft/fluentui/pull/31885) by beachball)
- Bump @fluentui/react-button to v9.3.88 ([PR #31885](https://github.com/microsoft/fluentui/pull/31885) by beachball)
- Bump @fluentui/react-link to v9.2.29 ([PR #31885](https://github.com/microsoft/fluentui/pull/31885) by beachball)
- Bump @fluentui/react-tabster to v9.22.4 ([PR #31885](https://github.com/microsoft/fluentui/pull/31885) by beachball)

## [9.0.33](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.33)

Tue, 23 Jul 2024 20:13:14 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.32..@fluentui/react-breadcrumb_v9.0.33)

### Patches

- Bump @fluentui/react-aria to v9.13.2 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-button to v9.3.87 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-link to v9.2.28 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-shared-contexts to v9.20.0 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-tabster to v9.22.3 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-utilities to v9.18.13 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.42 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)

## [9.0.32](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.32)

Mon, 15 Jul 2024 17:25:53 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.31..@fluentui/react-breadcrumb_v9.0.32)

### Patches

- fix: revert incorectly set npm versions in all packages ([PR #31937](https://github.com/microsoft/fluentui/pull/31937) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.1 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-button to v9.3.86 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-link to v9.2.27 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-shared-contexts to v9.19.1 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-tabster to v9.22.2 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-utilities to v9.18.12 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.41 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)

## [9.0.31](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.31)

Mon, 01 Jul 2024 20:30:42 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.30..@fluentui/react-breadcrumb_v9.0.31)

### Patches

- chore: Update react-icons package to ^2.0.245 ([PR #31802](https://github.com/microsoft/fluentui/pull/31802) by <EMAIL>)
- chore: add eslint react-compiler ([PR #31457](https://github.com/microsoft/fluentui/pull/31457) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.0 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-button to v9.3.85 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-link to v9.2.26 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-tabster to v9.22.1 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-utilities to v9.18.11 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.40 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)

## [9.0.30](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.30)

Mon, 17 Jun 2024 07:34:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.29..@fluentui/react-breadcrumb_v9.0.30)

### Patches

- Bump @fluentui/react-aria to v9.12.1 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)
- Bump @fluentui/react-button to v9.3.84 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)
- Bump @fluentui/react-link to v9.2.25 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)
- Bump @fluentui/react-tabster to v9.22.0 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)

## [9.0.29](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.29)

Thu, 06 Jun 2024 15:26:30 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.28..@fluentui/react-breadcrumb_v9.0.29)

### Patches

- Bump @fluentui/react-aria to v9.12.0 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-button to v9.3.83 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-link to v9.2.24 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-tabster to v9.21.5 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-utilities to v9.18.10 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.39 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)

## [9.0.28](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.28)

Thu, 23 May 2024 08:02:40 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.27..@fluentui/react-breadcrumb_v9.0.28)

### Patches

- chore: replace usage of .shorthands() in styles ([PR #31448](https://github.com/microsoft/fluentui/pull/31448) by <EMAIL>)
- Bump @fluentui/react-aria to v9.11.4 ([commit](https://github.com/microsoft/fluentui/commit/03599d609e8310b08c57d1f871cffbf717d79207) by beachball)
- Bump @fluentui/react-button to v9.3.82 ([commit](https://github.com/microsoft/fluentui/commit/03599d609e8310b08c57d1f871cffbf717d79207) by beachball)
- Bump @fluentui/react-link to v9.2.23 ([commit](https://github.com/microsoft/fluentui/commit/03599d609e8310b08c57d1f871cffbf717d79207) by beachball)
- Bump @fluentui/react-tabster to v9.21.4 ([commit](https://github.com/microsoft/fluentui/commit/03599d609e8310b08c57d1f871cffbf717d79207) by beachball)

## [9.0.27](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.27)

Mon, 20 May 2024 12:44:52 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.26..@fluentui/react-breadcrumb_v9.0.27)

### Patches

- chore: bump @griffel/react ([PR #31258](https://github.com/microsoft/fluentui/pull/31258) by <EMAIL>)
- Bump @fluentui/react-aria to v9.11.3 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-button to v9.3.81 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-link to v9.2.22 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-shared-contexts to v9.19.0 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-tabster to v9.21.3 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-utilities to v9.18.9 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.38 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)

## [9.0.26](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.26)

Thu, 16 May 2024 09:25:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.25..@fluentui/react-breadcrumb_v9.0.26)

### Patches

- chore: Upgrade react-icons version to 2.0.239 to pick up provider export map fix. ([PR #31287](https://github.com/microsoft/fluentui/pull/31287) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.80 ([PR #31390](https://github.com/microsoft/fluentui/pull/31390) by beachball)

## [9.0.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.25)

Thu, 09 May 2024 19:35:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.24..@fluentui/react-breadcrumb_v9.0.25)

### Patches

- Bump @fluentui/react-aria to v9.11.2 ([PR #31321](https://github.com/microsoft/fluentui/pull/31321) by beachball)
- Bump @fluentui/react-button to v9.3.79 ([PR #31321](https://github.com/microsoft/fluentui/pull/31321) by beachball)
- Bump @fluentui/react-link to v9.2.21 ([PR #31321](https://github.com/microsoft/fluentui/pull/31321) by beachball)
- Bump @fluentui/react-tabster to v9.21.2 ([PR #31321](https://github.com/microsoft/fluentui/pull/31321) by beachball)

## [9.0.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.24)

Mon, 06 May 2024 12:55:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.23..@fluentui/react-breadcrumb_v9.0.24)

### Patches

- Bump @fluentui/react-aria to v9.11.1 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-button to v9.3.78 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-link to v9.2.20 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-shared-contexts to v9.18.0 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-tabster to v9.21.1 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-utilities to v9.18.8 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.37 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)

## [9.0.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.23)

Thu, 02 May 2024 11:36:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.22..@fluentui/react-breadcrumb_v9.0.23)

### Patches

- chore: upgrade @fluentui/react-icons to 2.0.237. ([PR #31139](https://github.com/microsoft/fluentui/pull/31139) by <EMAIL>)
- Bump @fluentui/react-aria to v9.11.0 ([PR #31231](https://github.com/microsoft/fluentui/pull/31231) by beachball)
- Bump @fluentui/react-button to v9.3.77 ([PR #31231](https://github.com/microsoft/fluentui/pull/31231) by beachball)
- Bump @fluentui/react-link to v9.2.19 ([PR #31231](https://github.com/microsoft/fluentui/pull/31231) by beachball)
- Bump @fluentui/react-tabster to v9.21.0 ([PR #31231](https://github.com/microsoft/fluentui/pull/31231) by beachball)

## [9.0.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.22)

Tue, 23 Apr 2024 08:17:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.21..@fluentui/react-breadcrumb_v9.0.22)

### Patches

- Bump @fluentui/react-aria to v9.10.5 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-button to v9.3.76 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-link to v9.2.18 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-shared-contexts to v9.17.0 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-tabster to v9.20.1 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-utilities to v9.18.7 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.36 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)

## [9.0.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.21)

Wed, 17 Apr 2024 21:53:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.20..@fluentui/react-breadcrumb_v9.0.21)

### Patches

- chore: Update react-icons version to 2.0.235 ([PR #31011](https://github.com/microsoft/fluentui/pull/31011) by <EMAIL>)
- Bump @fluentui/react-aria to v9.10.4 ([PR #31100](https://github.com/microsoft/fluentui/pull/31100) by beachball)
- Bump @fluentui/react-button to v9.3.75 ([PR #31100](https://github.com/microsoft/fluentui/pull/31100) by beachball)
- Bump @fluentui/react-link to v9.2.17 ([PR #31100](https://github.com/microsoft/fluentui/pull/31100) by beachball)
- Bump @fluentui/react-tabster to v9.20.0 ([PR #31100](https://github.com/microsoft/fluentui/pull/31100) by beachball)

## [9.0.20](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.20)

Tue, 02 Apr 2024 09:48:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.19..@fluentui/react-breadcrumb_v9.0.20)

### Patches

- Bump @fluentui/react-aria to v9.10.3 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-button to v9.3.74 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-link to v9.2.16 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-shared-contexts to v9.16.0 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-tabster to v9.19.6 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-utilities to v9.18.6 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.35 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)

## [9.0.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.19)

Mon, 18 Mar 2024 19:50:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.18..@fluentui/react-breadcrumb_v9.0.19)

### Patches

- Bump @fluentui/react-aria to v9.10.2 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-button to v9.3.73 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-link to v9.2.15 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-shared-contexts to v9.15.2 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-tabster to v9.19.5 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-theme to v9.1.19 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-utilities to v9.18.5 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.34 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)

## [9.0.18](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.18)

Fri, 15 Mar 2024 21:43:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.17..@fluentui/react-breadcrumb_v9.0.18)

### Patches

- Bump @fluentui/react-aria to v9.10.1 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-button to v9.3.72 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-link to v9.2.14 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-shared-contexts to v9.15.1 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-tabster to v9.19.4 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-theme to v9.1.18 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-utilities to v9.18.4 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.33 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)

## [9.0.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.17)

Thu, 07 Mar 2024 19:33:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.16..@fluentui/react-breadcrumb_v9.0.17)

### Patches

- Bump @fluentui/react-aria to v9.10.0 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-button to v9.3.71 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-link to v9.2.13 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-shared-contexts to v9.15.0 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-tabster to v9.19.3 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-theme to v9.1.17 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-utilities to v9.18.3 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.32 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)

## [9.0.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.16)

Wed, 28 Feb 2024 02:34:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.15..@fluentui/react-breadcrumb_v9.0.16)

### Patches

- Bump @fluentui/react-aria to v9.9.1 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-button to v9.3.70 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-link to v9.2.12 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-shared-contexts to v9.14.1 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-tabster to v9.19.2 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-utilities to v9.18.2 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.31 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)

## [9.0.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.15)

Tue, 20 Feb 2024 14:22:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.14..@fluentui/react-breadcrumb_v9.0.15)

### Patches

- fix: moved sized icons to styling ([PR #30551](https://github.com/microsoft/fluentui/pull/30551) by <EMAIL>)
- Bump @fluentui/react-aria to v9.9.0 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-button to v9.3.69 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-link to v9.2.11 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-tabster to v9.19.1 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-utilities to v9.18.1 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.30 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)

## [9.0.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.14)

Tue, 06 Feb 2024 17:55:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.13..@fluentui/react-breadcrumb_v9.0.14)

### Patches

- Bump @fluentui/react-aria to v9.8.2 ([PR #30392](https://github.com/microsoft/fluentui/pull/30392) by beachball)
- Bump @fluentui/react-button to v9.3.68 ([PR #30392](https://github.com/microsoft/fluentui/pull/30392) by beachball)
- Bump @fluentui/react-link to v9.2.10 ([PR #30392](https://github.com/microsoft/fluentui/pull/30392) by beachball)
- Bump @fluentui/react-tabster to v9.19.0 ([PR #30392](https://github.com/microsoft/fluentui/pull/30392) by beachball)

## [9.0.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.13)

Tue, 30 Jan 2024 23:16:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.12..@fluentui/react-breadcrumb_v9.0.13)

### Patches

- Bump @fluentui/react-aria to v9.8.1 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-button to v9.3.67 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-link to v9.2.9 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-tabster to v9.18.0 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-utilities to v9.18.0 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.29 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)

## [9.0.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.12)

Tue, 23 Jan 2024 15:11:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.11..@fluentui/react-breadcrumb_v9.0.12)

### Patches

- Bump @fluentui/react-aria to v9.8.0 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-button to v9.3.66 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-link to v9.2.8 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-tabster to v9.17.4 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-utilities to v9.17.0 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.28 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)

## [9.0.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.11)

Thu, 18 Jan 2024 14:25:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.10..@fluentui/react-breadcrumb_v9.0.11)

### Patches

- Bump @fluentui/react-aria to v9.7.3 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-button to v9.3.65 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-link to v9.2.7 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-shared-contexts to v9.14.0 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-tabster to v9.17.3 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-utilities to v9.16.1 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.27 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)

## [9.0.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.10)

Wed, 17 Jan 2024 16:18:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.9..@fluentui/react-breadcrumb_v9.0.10)

### Patches

- Bump @fluentui/react-aria to v9.7.2 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-button to v9.3.64 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-link to v9.2.6 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-tabster to v9.17.2 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-utilities to v9.16.0 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.26 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)

## [9.0.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.9)

Tue, 16 Jan 2024 13:14:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.8..@fluentui/react-breadcrumb_v9.0.9)

### Patches

- fix: correct version of @types/react-dom peer dep that matches for 16.x ([PR #30259](https://github.com/microsoft/fluentui/pull/30259) by <EMAIL>)
- Bump @fluentui/react-aria to v9.7.1 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)
- Bump @fluentui/react-button to v9.3.63 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)
- Bump @fluentui/react-link to v9.2.5 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)
- Bump @fluentui/react-tabster to v9.17.1 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.25 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)

## [9.0.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.8)

Thu, 11 Jan 2024 09:04:29 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.7..@fluentui/react-breadcrumb_v9.0.8)

### Patches

- Bump @fluentui/react-aria to v9.7.0 ([PR #30259](https://github.com/microsoft/fluentui/pull/30259) by beachball)
- Bump @fluentui/react-button to v9.3.62 ([PR #30259](https://github.com/microsoft/fluentui/pull/30259) by beachball)

## [9.0.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.7)

Mon, 08 Jan 2024 16:24:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.6..@fluentui/react-breadcrumb_v9.0.7)

### Patches

- Update version of @fluentui/react-icons to 2.0.224 ([PR #30078](https://github.com/microsoft/fluentui/pull/30078) by <EMAIL>)
- Bump @fluentui/react-aria to v9.6.2 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-button to v9.3.61 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-link to v9.2.4 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-tabster to v9.17.0 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-utilities to v9.15.6 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.24 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)

## [9.0.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.6)

Wed, 03 Jan 2024 09:26:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.5..@fluentui/react-breadcrumb_v9.0.6)

### Patches

- Bump @fluentui/react-aria to v9.6.1 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-button to v9.3.60 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-link to v9.2.3 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-tabster to v9.16.1 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-utilities to v9.15.5 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.23 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)

## [9.0.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.5)

Thu, 21 Dec 2023 17:00:40 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.4..@fluentui/react-breadcrumb_v9.0.5)

### Patches

- fix(breadcrumb): BreadcrumbButton focus border has incorrent width and transition ([PR #30135](https://github.com/microsoft/fluentui/pull/30135) by <EMAIL>)

## [9.0.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.4)

Mon, 18 Dec 2023 14:40:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.3..@fluentui/react-breadcrumb_v9.0.4)

### Patches

- partitionBreadcrumbItems miscalculated endDisplayedItems when endDisplayedItems is incorrect when numberOfItemsToHide <= 0 ([PR #30005](https://github.com/microsoft/fluentui/pull/30005) by <EMAIL>)
- Bump @fluentui/react-aria to v9.6.0 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-button to v9.3.59 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-link to v9.2.2 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-shared-contexts to v9.13.2 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-tabster to v9.16.0 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-utilities to v9.15.4 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.22 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)

## [9.0.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.3)

Thu, 14 Dec 2023 09:58:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.2..@fluentui/react-breadcrumb_v9.0.3)

### Patches

- Bump @fluentui/react-aria to v9.5.0 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-button to v9.3.58 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-link to v9.2.1 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-shared-contexts to v9.13.1 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-tabster to v9.15.1 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-utilities to v9.15.3 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.21 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)

## [9.0.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.2)

Thu, 30 Nov 2023 13:42:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.1..@fluentui/react-breadcrumb_v9.0.2)

### Patches

- Bump @fluentui/react-aria to v9.4.0 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)
- Bump @fluentui/react-button to v9.3.57 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)
- Bump @fluentui/react-link to v9.2.0 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)
- Bump @fluentui/react-tabster to v9.15.0 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.20 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)

## [9.0.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.1)

Mon, 20 Nov 2023 09:55:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb_v9.0.0..@fluentui/react-breadcrumb_v9.0.1)

### Patches

- Bump @fluentui/react-button to v9.3.56 ([PR #29878](https://github.com/microsoft/fluentui/pull/29878) by beachball)
- Bump @fluentui/react-link to v9.1.35 ([PR #29878](https://github.com/microsoft/fluentui/pull/29878) by beachball)
- Bump @fluentui/react-shared-contexts to v9.13.0 ([PR #29878](https://github.com/microsoft/fluentui/pull/29878) by beachball)
- Bump @fluentui/react-tabster to v9.14.6 ([PR #29878](https://github.com/microsoft/fluentui/pull/29878) by beachball)

## [9.0.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb_v9.0.0)

Tue, 14 Nov 2023 17:51:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v2.1.2..@fluentui/react-breadcrumb_v9.0.0)

### Minor changes

- feat: release stable ([PR #29826](https://github.com/microsoft/fluentui/pull/29826) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.55 ([PR #29835](https://github.com/microsoft/fluentui/pull/29835) by beachball)
- Bump @fluentui/react-link to v9.1.34 ([PR #29835](https://github.com/microsoft/fluentui/pull/29835) by beachball)
- Bump @fluentui/react-tabster to v9.14.5 ([PR #29835](https://github.com/microsoft/fluentui/pull/29835) by beachball)

### Patches

- docs(breadcrumb): removed preview from all the links ([PR #29838](https://github.com/microsoft/fluentui/pull/29838) by <EMAIL>)

## [2.1.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v2.1.2)

Fri, 10 Nov 2023 13:46:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v2.1.1..@fluentui/react-breadcrumb-preview_v2.1.2)

### Patches

- fix: use button when no href is defined ([PR #29803](https://github.com/microsoft/fluentui/pull/29803) by <EMAIL>)

## [2.1.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v2.1.1)

Thu, 09 Nov 2023 17:29:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v2.1.0..@fluentui/react-breadcrumb-preview_v2.1.1)

### Patches

- chore: use package.json#files setup instead of npmignore for all v9 libraries ([PR #29734](https://github.com/microsoft/fluentui/pull/29734) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.54 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-link to v9.1.33 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-shared-contexts to v9.12.0 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-tabster to v9.14.4 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-theme to v9.1.16 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-utilities to v9.15.2 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.19 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)

## [2.1.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v2.1.0)

Wed, 01 Nov 2023 19:15:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v2.0.1..@fluentui/react-breadcrumb-preview_v2.1.0)

### Minor changes

- feat: made BreadcrumbButton semantically as a link ([PR #29679](https://github.com/microsoft/fluentui/pull/29679) by <EMAIL>)

## [2.0.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v2.0.1)

Wed, 01 Nov 2023 12:55:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v2.0.0..@fluentui/react-breadcrumb-preview_v2.0.1)

### Patches

- Bump @fluentui/react-button to v9.3.53 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)
- Bump @fluentui/react-link to v9.1.32 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)
- Bump @fluentui/react-shared-contexts to v9.11.1 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)
- Bump @fluentui/react-tabster to v9.14.3 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)
- Bump @fluentui/react-theme to v9.1.15 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)

## [2.0.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v2.0.0)

Sat, 28 Oct 2023 23:35:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.2.5..@fluentui/react-breadcrumb-preview_v2.0.0)

### Major changes

- BREAKING CHANGE: Removed non-interactive item and `slash` divider ([PR #29623](https://github.com/microsoft/fluentui/pull/29623) by <EMAIL>)
- BREAKING CHANGE: remove `appearance` prop and set `current` prop for BreadcrumbButton last item ([PR #29554](https://github.com/microsoft/fluentui/pull/29554) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.52 ([commit](https://github.com/microsoft/fluentui/commit/555b0fae3ec7f052e765557ae243c58000514f92) by beachball)
- Bump @fluentui/react-link to v9.1.31 ([commit](https://github.com/microsoft/fluentui/commit/555b0fae3ec7f052e765557ae243c58000514f92) by beachball)
- Bump @fluentui/react-shared-contexts to v9.11.0 ([commit](https://github.com/microsoft/fluentui/commit/555b0fae3ec7f052e765557ae243c58000514f92) by beachball)
- Bump @fluentui/react-tabster to v9.14.2 ([commit](https://github.com/microsoft/fluentui/commit/555b0fae3ec7f052e765557ae243c58000514f92) by beachball)

## [1.2.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.2.5)

Mon, 23 Oct 2023 09:51:57 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.2.4..@fluentui/react-breadcrumb-preview_v1.2.5)

### Patches

- Bump @fluentui/react-button to v9.3.51 ([commit](https://github.com/microsoft/fluentui/commit/e4ef1febe8a185dddc10f8936944d177d50bc396) by beachball)
- Bump @fluentui/react-link to v9.1.30 ([commit](https://github.com/microsoft/fluentui/commit/e4ef1febe8a185dddc10f8936944d177d50bc396) by beachball)
- Bump @fluentui/react-tabster to v9.14.1 ([commit](https://github.com/microsoft/fluentui/commit/e4ef1febe8a185dddc10f8936944d177d50bc396) by beachball)

## [1.2.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.2.4)

Wed, 18 Oct 2023 17:54:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.2.3..@fluentui/react-breadcrumb-preview_v1.2.4)

### Patches

- Bump @fluentui/react-button to v9.3.50 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-link to v9.1.29 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-tabster to v9.14.0 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-utilities to v9.15.1 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.18 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)

## [1.2.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.2.3)

Thu, 12 Oct 2023 14:55:42 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.2.2..@fluentui/react-breadcrumb-preview_v1.2.3)

### Patches

- chore: migrate from getNativeElementProps for getIntrinsicElementProps ([PR #29498](https://github.com/microsoft/fluentui/pull/29498) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.49 ([PR #29513](https://github.com/microsoft/fluentui/pull/29513) by beachball)
- Bump @fluentui/react-link to v9.1.28 ([PR #29513](https://github.com/microsoft/fluentui/pull/29513) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.17 ([PR #29513](https://github.com/microsoft/fluentui/pull/29513) by beachball)

## [1.2.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.2.2)

Wed, 11 Oct 2023 13:54:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.2.1..@fluentui/react-breadcrumb-preview_v1.2.2)

### Patches

- Bump @fluentui/react-button to v9.3.48 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-link to v9.1.27 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-tabster to v9.13.6 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-utilities to v9.15.0 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.16 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)

## [1.2.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.2.1)

Mon, 09 Oct 2023 20:45:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.2.0..@fluentui/react-breadcrumb-preview_v1.2.1)

### Patches

- Bump @fluentui/react-button to v9.3.47 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-link to v9.1.26 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-shared-contexts to v9.10.0 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-tabster to v9.13.5 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-utilities to v9.14.2 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.15 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)

## [1.2.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.2.0)

Thu, 05 Oct 2023 15:25:29 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.1.1..@fluentui/react-breadcrumb-preview_v1.2.0)

### Minor changes

- feat: register items via context ([PR #29393](https://github.com/microsoft/fluentui/pull/29393) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.46 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-link to v9.1.25 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-tabster to v9.13.4 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-utilities to v9.14.1 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.14 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)

## [1.1.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.1.1)

Wed, 04 Oct 2023 08:45:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.1.0..@fluentui/react-breadcrumb-preview_v1.1.1)

### Patches

- Bump @fluentui/react-button to v9.3.45 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-link to v9.1.24 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-tabster to v9.13.3 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-utilities to v9.14.0 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.13 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)

## [1.1.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.1.0)

Mon, 02 Oct 2023 08:56:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.0.5..@fluentui/react-breadcrumb-preview_v1.1.0)

### Minor changes

- fix: exported BreadcrumbProvider and useBreadcrumbContext ([PR #29315](https://github.com/microsoft/fluentui/pull/29315) by <EMAIL>)
- feat: add custom styling hooks for Breadcrumb component and its sub-components ([PR #29318](https://github.com/microsoft/fluentui/pull/29318) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.44 ([PR #29301](https://github.com/microsoft/fluentui/pull/29301) by beachball)
- Bump @fluentui/react-link to v9.1.23 ([PR #29301](https://github.com/microsoft/fluentui/pull/29301) by beachball)
- Bump @fluentui/react-shared-contexts to v9.9.2 ([PR #29301](https://github.com/microsoft/fluentui/pull/29301) by beachball)
- Bump @fluentui/react-tabster to v9.13.2 ([PR #29301](https://github.com/microsoft/fluentui/pull/29301) by beachball)

## [1.0.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.0.5)

Tue, 26 Sep 2023 17:49:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.0.4..@fluentui/react-breadcrumb-preview_v1.0.5)

### Patches

- chore: trigger manual version bump after broken release ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.43 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-link to v9.1.22 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-shared-contexts to v9.9.1 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-tabster to v9.13.1 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-theme to v9.1.14 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-utilities to v9.13.5 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.12 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)

## [1.0.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.0.4)

Tue, 26 Sep 2023 15:32:04 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.0.3..@fluentui/react-breadcrumb-preview_v1.0.4)

### Patches

- fix: used makeResetStyles instead of makeStyles ([PR #29278](https://github.com/microsoft/fluentui/pull/29278) by <EMAIL>)
- fix: bump swc core to mitigate transpilation memory leaks ([PR #29253](https://github.com/microsoft/fluentui/pull/29253) by <EMAIL>)
- chore: Update react-icons version to pick up IconDirectionContextProvider updated export ([PR #29151](https://github.com/microsoft/fluentui/pull/29151) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.42 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-link to v9.1.21 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-shared-contexts to v9.9.0 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-tabster to v9.13.0 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-theme to v9.1.13 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-utilities to v9.13.4 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.11 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)

## [1.0.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.0.3)

Wed, 20 Sep 2023 17:47:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.0.2..@fluentui/react-breadcrumb-preview_v1.0.3)

### Patches

- chore: trigger manual version bump after broken release ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.41 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)
- Bump @fluentui/react-link to v9.1.20 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)
- Bump @fluentui/react-shared-contexts to v9.8.1 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)
- Bump @fluentui/react-tabster to v9.12.11 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.10 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)

## [1.0.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.0.2)

Wed, 20 Sep 2023 14:59:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.0.1..@fluentui/react-breadcrumb-preview_v1.0.2)

### Patches

- fix/added CSS variables to BreadcrumbItem icon ([PR #29158](https://github.com/microsoft/fluentui/pull/29158) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.40 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)
- Bump @fluentui/react-link to v9.1.19 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)
- Bump @fluentui/react-shared-contexts to v9.8.0 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)
- Bump @fluentui/react-tabster to v9.12.10 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.9 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)

## [1.0.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.0.1)

Thu, 14 Sep 2023 16:44:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v1.0.0..@fluentui/react-breadcrumb-preview_v1.0.1)

### Patches

- Bump @fluentui/react-button to v9.3.39 ([PR #29145](https://github.com/microsoft/fluentui/pull/29145) by beachball)
- Bump @fluentui/react-link to v9.1.18 ([PR #29145](https://github.com/microsoft/fluentui/pull/29145) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.8 ([PR #29145](https://github.com/microsoft/fluentui/pull/29145) by beachball)

## [1.0.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v1.0.0)

Tue, 12 Sep 2023 08:51:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.3.8..@fluentui/react-breadcrumb-preview_v1.0.0)

### Major changes

- feat: removed breadcrumb-link component ([PR #29112](https://github.com/microsoft/fluentui/pull/29112) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.38 ([PR #29129](https://github.com/microsoft/fluentui/pull/29129) by beachball)
- Bump @fluentui/react-link to v9.1.17 ([PR #29129](https://github.com/microsoft/fluentui/pull/29129) by beachball)
- Bump @fluentui/react-tabster to v9.12.9 ([PR #29129](https://github.com/microsoft/fluentui/pull/29129) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.7 ([PR #29129](https://github.com/microsoft/fluentui/pull/29129) by beachball)

### Patches

- fix: icon spacing in Breadcrumb ([PR #29096](https://github.com/microsoft/fluentui/pull/29096) by <EMAIL>)

## [0.3.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.3.8)

Wed, 06 Sep 2023 13:31:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.3.7..@fluentui/react-breadcrumb-preview_v0.3.8)

### Patches

- fix: styling for BreadcrumbItem ([PR #29059](https://github.com/microsoft/fluentui/pull/29059) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.37 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-link to v9.1.16 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-tabster to v9.12.8 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-utilities to v9.13.3 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.6 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)

## [0.3.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.3.7)

Tue, 05 Sep 2023 15:39:04 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.3.6..@fluentui/react-breadcrumb-preview_v0.3.7)

### Patches

- Bump @fluentui/react-button to v9.3.36 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-link to v9.1.15 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-tabster to v9.12.7 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-utilities to v9.13.2 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.5 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)

## [0.3.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.3.6)

Tue, 05 Sep 2023 13:29:16 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.3.5..@fluentui/react-breadcrumb-preview_v0.3.6)

### Patches

- bumps @swc/helpers version to 0.5.1 ([PR #28989](https://github.com/microsoft/fluentui/pull/28989) by <EMAIL>)
- chore: migrate package to use JSX importSource ([PR #28959](https://github.com/microsoft/fluentui/pull/28959) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.35 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-link to v9.1.14 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.3 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-tabster to v9.12.6 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-theme to v9.1.12 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-utilities to v9.13.1 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.4 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)

## [0.3.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.3.5)

Tue, 29 Aug 2023 12:57:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.3.4..@fluentui/react-breadcrumb-preview_v0.3.5)

### Patches

- Bump @fluentui/react-button to v9.3.34 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-link to v9.1.13 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-tabster to v9.12.5 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-utilities to v9.13.0 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.3 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)

## [0.3.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.3.4)

Thu, 24 Aug 2023 10:26:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.3.3..@fluentui/react-breadcrumb-preview_v0.3.4)

### Patches

- Bump @fluentui/react-button to v9.3.33 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-link to v9.1.12 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-tabster to v9.12.4 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-utilities to v9.12.0 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.2 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)

## [0.3.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.3.3)

Wed, 23 Aug 2023 12:01:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.3.2..@fluentui/react-breadcrumb-preview_v0.3.3)

### Patches

- Bump @fluentui/react-button to v9.3.32 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-link to v9.1.11 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-tabster to v9.12.3 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-utilities to v9.11.2 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.1 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)

## [0.3.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.3.2)

Mon, 21 Aug 2023 11:38:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.3.1..@fluentui/react-breadcrumb-preview_v0.3.2)

### Patches

- fix: breadcrumb spacing ([PR #28883](https://github.com/microsoft/fluentui/pull/28883) by <EMAIL>)

## [0.3.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.3.1)

Wed, 16 Aug 2023 17:41:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.3.0..@fluentui/react-breadcrumb-preview_v0.3.1)

### Patches

- Bump @fluentui/react-button to v9.3.31 ([PR #28885](https://github.com/microsoft/fluentui/pull/28885) by beachball)
- Bump @fluentui/react-link to v9.1.10 ([PR #28885](https://github.com/microsoft/fluentui/pull/28885) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0 ([PR #28885](https://github.com/microsoft/fluentui/pull/28885) by beachball)

## [0.3.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.3.0)

Wed, 16 Aug 2023 11:38:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.2.2..@fluentui/react-breadcrumb-preview_v0.3.0)

### Minor changes

- removed iconPosition prop from the Breadcrumb ([PR #28846](https://github.com/microsoft/fluentui/pull/28846) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.30 ([PR #28791](https://github.com/microsoft/fluentui/pull/28791) by beachball)
- Bump @fluentui/react-link to v9.1.9 ([PR #28791](https://github.com/microsoft/fluentui/pull/28791) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.16 ([PR #28791](https://github.com/microsoft/fluentui/pull/28791) by beachball)

### Patches

- Added CY tests for Breacrumb with Menu and Overflow ([PR #28790](https://github.com/microsoft/fluentui/pull/28790) by <EMAIL>)

## [0.2.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.2.2)

Fri, 11 Aug 2023 12:14:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.2.1..@fluentui/react-breadcrumb-preview_v0.2.2)

### Patches

- Bump @fluentui/react-button to v9.3.29 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-link to v9.1.8 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-tabster to v9.12.2 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-utilities to v9.11.1 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.15 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)

## [0.2.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.2.1)

Wed, 09 Aug 2023 13:16:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.2.0..@fluentui/react-breadcrumb-preview_v0.2.1)

### Patches

- chore: Update Griffel to latest version ([PR #28684](https://github.com/microsoft/fluentui/pull/28684) by <EMAIL>)
- fix: disabled BreadcrumbLink shouldn't be focusable ([PR #28720](https://github.com/microsoft/fluentui/pull/28720) by <EMAIL>)
- chore(cxe-prg): migrate to new slot API ([PR #28752](https://github.com/microsoft/fluentui/pull/28752) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.28 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-link to v9.1.7 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.2 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-tabster to v9.12.1 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-theme to v9.1.11 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.14 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)

## [0.2.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.2.0)

Fri, 04 Aug 2023 08:52:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.1.3..@fluentui/react-breadcrumb-preview_v0.2.0)

### Minor changes

- feat: Added an icon for BreadcrumbItem ([PR #28682](https://github.com/microsoft/fluentui/pull/28682) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.27 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-link to v9.1.6 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.1 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-tabster to v9.12.0 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-theme to v9.1.10 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-utilities to v9.11.0 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.13 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)

### Patches

- fix: made BreadcrumbButton consistent with other items ([PR #28672](https://github.com/microsoft/fluentui/pull/28672) by <EMAIL>)

## [0.1.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.1.3)

Tue, 01 Aug 2023 10:17:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.1.2..@fluentui/react-breadcrumb-preview_v0.1.3)

### Patches

- Bump @fluentui/react-button to v9.3.26 ([PR #28689](https://github.com/microsoft/fluentui/pull/28689) by beachball)
- Bump @fluentui/react-link to v9.1.5 ([PR #28689](https://github.com/microsoft/fluentui/pull/28689) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.0 ([PR #28689](https://github.com/microsoft/fluentui/pull/28689) by beachball)
- Bump @fluentui/react-tabster to v9.11.1 ([PR #28689](https://github.com/microsoft/fluentui/pull/28689) by beachball)

## [0.1.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.1.2)

Tue, 25 Jul 2023 13:29:16 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.1.1..@fluentui/react-breadcrumb-preview_v0.1.2)

### Patches

- chore: Update react-icons version to pick up fowardref change. ([PR #28590](https://github.com/microsoft/fluentui/pull/28590) by <EMAIL>)
- fix: updated readme, replaced MenuItem with MenuItemLink ([PR #28626](https://github.com/microsoft/fluentui/pull/28626) by <EMAIL>)
- fix: spacing in BreadcrumbButton and Link, added more examples to the stories ([PR #28578](https://github.com/microsoft/fluentui/pull/28578) by <EMAIL>)
- Bump @fluentui/react-button to v9.3.25 ([PR #28622](https://github.com/microsoft/fluentui/pull/28622) by beachball)
- Bump @fluentui/react-link to v9.1.4 ([PR #28622](https://github.com/microsoft/fluentui/pull/28622) by beachball)
- Bump @fluentui/react-tabster to v9.11.0 ([PR #28622](https://github.com/microsoft/fluentui/pull/28622) by beachball)

## [0.1.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.1.1)

Thu, 20 Jul 2023 18:27:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-breadcrumb-preview_v0.1.0..@fluentui/react-breadcrumb-preview_v0.1.1)

### Patches

- Bump @fluentui/react-button to v9.3.24 ([PR #26826](https://github.com/microsoft/fluentui/pull/26826) by beachball)
- Bump @fluentui/react-link to v9.1.3 ([PR #26826](https://github.com/microsoft/fluentui/pull/26826) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.12 ([PR #26826](https://github.com/microsoft/fluentui/pull/26826) by beachball)

## [0.1.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-breadcrumb-preview_v0.1.0)

Mon, 17 Jul 2023 21:27:34 GMT

### Minor changes

- feat: release react-breadcrumb to unstable ([PR #28402](https://github.com/microsoft/fluentui/pull/28402) by <EMAIL>)

### Patches

- made breadcrumb package public ([PR #28549](https://github.com/microsoft/fluentui/pull/28549) by <EMAIL>)
