/**
 * Encryption service for Maithili Vikas Kosh Shop Management System
 * Provides AES-256 encryption for sensitive data and database files
 */
export declare class EncryptionService {
    private static instance;
    private encryptionKey;
    private readonly algorithm;
    private readonly keyFile;
    constructor();
    static getInstance(): EncryptionService;
    /**
     * Initialize encryption service
     */
    initialize(): Promise<void>;
    /**
     * Load existing encryption key or create a new one
     */
    private loadOrCreateEncryptionKey;
    /**
     * Encrypt data using AES-256-GCM
     */
    encrypt(data: string): {
        encrypted: string;
        iv: string;
        tag: string;
    };
    /**
     * Decrypt data using AES-256-GCM
     */
    decrypt(encryptedData: {
        encrypted: string;
        iv: string;
        tag: string;
    }): string;
    /**
     * Hash password with salt using PBKDF2
     */
    hashPassword(password: string, salt?: string): {
        hash: string;
        salt: string;
    };
    /**
     * Verify password against hash
     */
    verifyPassword(password: string, hash: string, salt: string): boolean;
    /**
     * Generate secure random token
     */
    generateToken(length?: number): string;
    /**
     * Encrypt file
     */
    encryptFile(inputPath: string, outputPath: string): Promise<void>;
    /**
     * Decrypt file
     */
    decryptFile(inputPath: string, outputPath: string): Promise<void>;
    /**
     * Get encryption status
     */
    isInitialized(): boolean;
}
export declare const encryptionService: EncryptionService;
//# sourceMappingURL=encryption.d.ts.map