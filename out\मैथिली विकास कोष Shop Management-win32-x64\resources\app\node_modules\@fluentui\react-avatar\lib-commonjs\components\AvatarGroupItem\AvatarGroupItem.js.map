{"version": 3, "sources": ["../src/components/AvatarGroupItem/AvatarGroupItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAvatarGroupItem_unstable } from './renderAvatarGroupItem';\nimport { useAvatarGroupItem_unstable } from './useAvatarGroupItem';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAvatarGroupItemStyles_unstable } from './useAvatarGroupItemStyles.styles';\nimport type { AvatarGroupItemProps } from './AvatarGroupItem.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\n/**\n * The AvatarGroupItem component represents a single person or entity.\n * AvatarGroupItem should only be used in an AvatarGroup component.\n */\nexport const AvatarGroupItem: ForwardRefComponent<AvatarGroupItemProps> = React.forwardRef((props, ref) => {\n  const state = useAvatarGroupItem_unstable(props, ref);\n\n  useAvatarGroupItemStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAvatarGroupItemStyles_unstable')(state);\n\n  return renderAvatarGroupItem_unstable(state);\n});\n\nAvatarGroupItem.displayName = 'AvatarGroupItem';\n"], "names": ["AvatarGroupItem", "React", "forwardRef", "props", "ref", "state", "useAvatarGroupItem_unstable", "useAvatarGroupItemStyles_unstable", "useCustomStyleHook_unstable", "renderAvatarGroupItem_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAYaA;;;eAAAA;;;;iEAZU;uCACwB;oCACH;qCACA;gDACM;AAQ3C,MAAMA,kBAAAA,WAAAA,GAA6DC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IACjG,MAAMC,QAAQC,IAAAA,+CAAAA,EAA4BH,OAAOC;IAEjDG,IAAAA,iEAAAA,EAAkCF;IAElCG,IAAAA,gDAAAA,EAA4B,qCAAqCH;IAEjE,OAAOI,IAAAA,qDAAAA,EAA+BJ;AACxC;AAEAL,gBAAgBU,WAAW,GAAG"}