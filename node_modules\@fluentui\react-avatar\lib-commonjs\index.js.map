{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["export {\n  Avatar,\n  avatarClassNames,\n  renderAvatar_unstable,\n  useAvatarStyles_unstable,\n  useAvatar_unstable,\n} from './Avatar';\nexport type {\n  AvatarNamedColor,\n  AvatarProps,\n  AvatarSlots,\n  AvatarState,\n  AvatarShape,\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  AvatarSizes,\n  AvatarSize,\n} from './Avatar';\nexport { getInitials, partitionAvatarGroupItems } from './utils/index';\nexport type { PartitionAvatarGroupItems, PartitionAvatarGroupItemsOptions } from './utils/index';\nexport {\n  AvatarGroup,\n  avatarGroupClassNames,\n  renderAvatarGroup_unstable,\n  useAvatarGroupContextValues,\n  useAvatarGroupStyles_unstable,\n  useAvatarGroup_unstable,\n} from './AvatarGroup';\nexport type {\n  AvatarGroupProps,\n  AvatarGroupSlots,\n  AvatarGroupState,\n  AvatarGroupContextValue,\n  AvatarGroupContextValues,\n} from './AvatarGroup';\nexport {\n  AvatarGroupItem,\n  avatarGroupItemClassNames,\n  renderAvatarGroupItem_unstable,\n  useAvatarGroupItemStyles_unstable,\n  useAvatarGroupItem_unstable,\n} from './AvatarGroupItem';\nexport type { AvatarGroupItemProps, AvatarGroupItemSlots, AvatarGroupItemState } from './AvatarGroupItem';\nexport {\n  AvatarGroupPopover,\n  avatarGroupPopoverClassNames,\n  renderAvatarGroupPopover_unstable,\n  useAvatarGroupPopover_unstable,\n  useAvatarGroupPopoverContextValues_unstable,\n  useAvatarGroupPopoverStyles_unstable,\n} from './AvatarGroupPopover';\nexport type { AvatarGroupPopoverProps, AvatarGroupPopoverSlots, AvatarGroupPopoverState } from './AvatarGroupPopover';\nexport {\n  AvatarContextProvider,\n  AvatarGroupProvider,\n  useAvatarContext,\n  useAvatarGroupContext_unstable,\n} from './contexts/index';\nexport type { AvatarContextValue } from './contexts/index';\n"], "names": ["Avatar", "AvatarContextProvider", "AvatarGroup", "AvatarGroupItem", "AvatarGroupPopover", "AvatarGroupProvider", "avatarClassNames", "avatarGroupClassNames", "avatarGroupItemClassNames", "avatarGroupPopoverClassNames", "getInitials", "partitionAvatarGroupItems", "renderAvatarGroupItem_unstable", "renderAvatarGroupPopover_unstable", "renderAvatarGroup_unstable", "renderAvatar_unstable", "useAvatarContext", "useAvatarGroupContextValues", "useAvatarGroupContext_unstable", "useAvatarGroupItemStyles_unstable", "useAvatarGroupItem_unstable", "useAvatarGroupPopoverContextValues_unstable", "useAvatarGroupPopoverStyles_unstable", "useAvatarGroupPopover_unstable", "useAvatarGroupStyles_unstable", "useAvatarGroup_unstable", "useAvatarStyles_unstable", "useAvatar_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IACEA,MAAM;eAANA,cAAM;;IAmDNC,qBAAqB;eAArBA,6BAAqB;;IAhCrBC,WAAW;eAAXA,wBAAW;;IAeXC,eAAe;eAAfA,gCAAe;;IAQfC,kBAAkB;eAAlBA,sCAAkB;;IAUlBC,mBAAmB;eAAnBA,2BAAmB;;IAnDnBC,gBAAgB;eAAhBA,wBAAgB;;IAmBhBC,qBAAqB;eAArBA,kCAAqB;;IAerBC,yBAAyB;eAAzBA,0CAAyB;;IAQzBC,4BAA4B;eAA5BA,gDAA4B;;IA3BrBC,WAAW;eAAXA,kBAAW;;IAAEC,yBAAyB;eAAzBA,gCAAyB;;IAoB7CC,8BAA8B;eAA9BA,+CAA8B;;IAQ9BC,iCAAiC;eAAjCA,qDAAiC;;IAvBjCC,0BAA0B;eAA1BA,uCAA0B;;IAnB1BC,qBAAqB;eAArBA,6BAAqB;;IAmDrBC,gBAAgB;eAAhBA,wBAAgB;;IA/BhBC,2BAA2B;eAA3BA,wCAA2B;;IAgC3BC,8BAA8B;eAA9BA,sCAA8B;;IAjB9BC,iCAAiC;eAAjCA,kDAAiC;;IACjCC,2BAA2B;eAA3BA,4CAA2B;;IAQ3BC,2CAA2C;eAA3CA,+DAA2C;;IAC3CC,oCAAoC;eAApCA,wDAAoC;;IAFpCC,8BAA8B;eAA9BA,kDAA8B;;IAtB9BC,6BAA6B;eAA7BA,0CAA6B;;IAC7BC,uBAAuB;eAAvBA,oCAAuB;;IArBvBC,wBAAwB;eAAxBA,gCAAwB;;IACxBC,kBAAkB;eAAlBA,0BAAkB;;;wBACb;uBAWgD;6BAShD;iCAcA;oCASA;wBAOA"}