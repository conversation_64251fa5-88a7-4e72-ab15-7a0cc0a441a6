{"version": 3, "sources": ["../src/AriaLiveAnnouncer/index.ts"], "sourcesContent": ["export { AriaLiveAnnouncer } from './AriaLiveAnnouncer';\nexport type { AriaLiveAnnouncerProps, AriaLiveAnnouncerState } from './AriaLiveAnnouncer.types';\nexport { renderAriaLiveAnnouncer_unstable } from './renderAriaLiveAnnouncer';\nexport { useAriaLiveAnnouncer_unstable } from './useAriaLiveAnnouncer';\nexport { useAriaLiveAnnouncerContextValues_unstable } from './useAriaLiveAnnouncerContextValues';\n"], "names": ["AriaLiveAnnouncer", "renderAriaLiveAnnouncer_unstable", "useAriaLiveAnnouncerContextValues_unstable", "useAriaLiveAnnouncer_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAASA,iBAAiB;eAAjBA,oCAAiB;;IAEjBC,gCAAgC;eAAhCA,yDAAgC;;IAEhCC,0CAA0C;eAA1CA,6EAA0C;;IAD1CC,6BAA6B;eAA7BA,mDAA6B;;;mCAHJ;yCAEe;sCACH;mDACa"}