{"version": 3, "sources": ["../src/components/AvatarGroupPopover/useAvatarGroupPopover.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useAvatarGroupContext_unstable } from '../../contexts/AvatarGroupContext';\nimport { defaultAvatarGroupSize } from '../AvatarGroup/useAvatarGroup';\nimport { useControllableState, slot } from '@fluentui/react-utilities';\nimport { MoreHorizontalRegular } from '@fluentui/react-icons';\nimport { OnOpenChangeData, OpenPopoverEvents, Popover, PopoverSurface } from '@fluentui/react-popover';\nimport type { AvatarGroupPopoverProps, AvatarGroupPopoverState } from './AvatarGroupPopover.types';\nimport { Tooltip } from '@fluentui/react-tooltip';\n\n/**\n * Create the state required to render AvatarGroupPopover.\n *\n * The returned state can be modified with hooks such as useAvatarGroupPopoverStyles_unstable,\n * before being passed to renderAvatarGroupPopover_unstable.\n *\n * @param props - props from this instance of AvatarGroupPopover\n */\nexport const useAvatarGroupPopover_unstable = (props: AvatarGroupPopoverProps): AvatarGroupPopoverState => {\n  const size = useAvatarGroupContext_unstable(ctx => ctx.size) ?? defaultAvatarGroupSize;\n  const layout = useAvatarGroupContext_unstable(ctx => ctx.layout);\n  const {\n    indicator = size < 24 ? 'icon' : 'count',\n    count = React.Children.count(props.children),\n    children,\n    ...restOfProps\n  } = props;\n\n  const [popoverOpen, setPopoverOpen] = useControllableState({\n    state: props.open,\n    defaultState: props.defaultOpen,\n    initialState: false,\n  });\n\n  const handleOnPopoverChange = (e: OpenPopoverEvents, data: OnOpenChangeData) => {\n    restOfProps.onOpenChange?.(e, data);\n    setPopoverOpen(data.open);\n  };\n\n  let triggerButtonChildren;\n  if (layout === 'pie') {\n    triggerButtonChildren = null;\n  } else if (indicator === 'icon') {\n    triggerButtonChildren = <MoreHorizontalRegular />;\n  } else {\n    triggerButtonChildren = count > 99 ? '99+' : `+${count}`;\n  }\n\n  return {\n    count,\n    indicator,\n    layout,\n    popoverOpen,\n    size,\n\n    components: {\n      root: Popover,\n      triggerButton: 'button',\n      content: 'ul',\n      popoverSurface: PopoverSurface,\n      tooltip: Tooltip,\n    },\n    root: slot.always(\n      {\n        // Popover expects a child for its children. The children are added in the renderAvatarGroupPopover.\n        children: <></>,\n        size: 'small',\n        trapFocus: true,\n        ...restOfProps,\n        open: popoverOpen,\n        onOpenChange: handleOnPopoverChange,\n      },\n      { elementType: Popover },\n    ),\n    triggerButton: slot.always(props.triggerButton, {\n      defaultProps: {\n        children: triggerButtonChildren,\n        type: 'button',\n      },\n      elementType: 'button',\n    }),\n    content: slot.always(props.content, {\n      defaultProps: {\n        children,\n        role: 'list',\n      },\n      elementType: 'ul',\n    }),\n    popoverSurface: slot.always(props.popoverSurface, {\n      defaultProps: {\n        'aria-label': 'Overflow',\n        tabIndex: 0,\n      },\n      elementType: PopoverSurface,\n    }),\n    tooltip: slot.always(props.tooltip, {\n      defaultProps: {\n        content: 'View more people.',\n        relationship: 'label',\n      },\n      elementType: Tooltip,\n    }),\n  };\n};\n"], "names": ["React", "useAvatarGroupContext_unstable", "defaultAvatarGroupSize", "useControllableState", "slot", "MoreHorizontalRegular", "Popover", "PopoverSurface", "<PERSON><PERSON><PERSON>", "useAvatarGroupPopover_unstable", "props", "size", "ctx", "layout", "indicator", "count", "Children", "children", "restOfProps", "popoverOpen", "setPopoverOpen", "state", "open", "defaultState", "defaultOpen", "initialState", "handleOnPopoverChange", "e", "data", "onOpenChange", "triggerButtonChildren", "components", "root", "trigger<PERSON>utton", "content", "popoverSurface", "tooltip", "always", "trapFocus", "elementType", "defaultProps", "type", "role", "tabIndex", "relationship"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,8BAA8B,QAAQ,oCAAoC;AACnF,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,oBAAoB,EAAEC,IAAI,QAAQ,4BAA4B;AACvE,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAA8CC,OAAO,EAAEC,cAAc,QAAQ,0BAA0B;AAEvG,SAASC,OAAO,QAAQ,0BAA0B;AAElD;;;;;;;CAOC,GACD,OAAO,MAAMC,iCAAiC,CAACC;QAChCT;IAAb,MAAMU,OAAOV,CAAAA,kCAAAA,+BAA+BW,CAAAA,MAAOA,IAAID,IAAI,eAA9CV,6CAAAA,kCAAmDC;IAChE,MAAMW,SAASZ,+BAA+BW,CAAAA,MAAOA,IAAIC,MAAM;IAC/D,MAAM,EACJC,YAAYH,OAAO,KAAK,SAAS,OAAO,EACxCI,QAAQf,MAAMgB,QAAQ,CAACD,KAAK,CAACL,MAAMO,QAAQ,CAAC,EAC5CA,QAAQ,EACR,GAAGC,aACJ,GAAGR;IAEJ,MAAM,CAACS,aAAaC,eAAe,GAAGjB,qBAAqB;QACzDkB,OAAOX,MAAMY,IAAI;QACjBC,cAAcb,MAAMc,WAAW;QAC/BC,cAAc;IAChB;IAEA,MAAMC,wBAAwB,CAACC,GAAsBC;YACnDV;SAAAA,4BAAAA,YAAYW,YAAY,cAAxBX,gDAAAA,+BAAAA,aAA2BS,GAAGC;QAC9BR,eAAeQ,KAAKN,IAAI;IAC1B;IAEA,IAAIQ;IACJ,IAAIjB,WAAW,OAAO;QACpBiB,wBAAwB;IAC1B,OAAO,IAAIhB,cAAc,QAAQ;QAC/BgB,sCAAwB,oBAACzB;IAC3B,OAAO;QACLyB,wBAAwBf,QAAQ,KAAK,QAAQ,CAAC,CAAC,EAAEA,MAAM,CAAC;IAC1D;IAEA,OAAO;QACLA;QACAD;QACAD;QACAM;QACAR;QAEAoB,YAAY;YACVC,MAAM1B;YACN2B,eAAe;YACfC,SAAS;YACTC,gBAAgB5B;YAChB6B,SAAS5B;QACX;QACAwB,MAAM5B,KAAKiC,MAAM,CACf;YACE,oGAAoG;YACpGpB,wBAAU;YACVN,MAAM;YACN2B,WAAW;YACX,GAAGpB,WAAW;YACdI,MAAMH;YACNU,cAAcH;QAChB,GACA;YAAEa,aAAajC;QAAQ;QAEzB2B,eAAe7B,KAAKiC,MAAM,CAAC3B,MAAMuB,aAAa,EAAE;YAC9CO,cAAc;gBACZvB,UAAUa;gBACVW,MAAM;YACR;YACAF,aAAa;QACf;QACAL,SAAS9B,KAAKiC,MAAM,CAAC3B,MAAMwB,OAAO,EAAE;YAClCM,cAAc;gBACZvB;gBACAyB,MAAM;YACR;YACAH,aAAa;QACf;QACAJ,gBAAgB/B,KAAKiC,MAAM,CAAC3B,MAAMyB,cAAc,EAAE;YAChDK,cAAc;gBACZ,cAAc;gBACdG,UAAU;YACZ;YACAJ,aAAahC;QACf;QACA6B,SAAShC,KAAKiC,MAAM,CAAC3B,MAAM0B,OAAO,EAAE;YAClCI,cAAc;gBACZN,SAAS;gBACTU,cAAc;YAChB;YACAL,aAAa/B;QACf;IACF;AACF,EAAE"}