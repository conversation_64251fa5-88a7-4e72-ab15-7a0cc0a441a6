{"version": 3, "sources": ["../src/PresenceBadge.ts"], "sourcesContent": ["export type { PresenceBadgeProps, PresenceBadgeState, PresenceBadgeStatus } from './components/PresenceBadge/index';\nexport {\n  PresenceBadge,\n  presenceAvailableFilled,\n  presenceAvailableRegular,\n  presenceAwayFilled,\n  presenceAwayRegular,\n  presenceBadgeClassNames,\n  presenceBlockedRegular,\n  presenceBusyFilled,\n  presenceDndFilled,\n  presenceDndRegular,\n  presenceOfflineRegular,\n  presenceOofRegular,\n  presenceUnknownRegular,\n  usePresenceBadgeStyles_unstable,\n  usePresenceBadge_unstable,\n} from './components/PresenceBadge/index';\n"], "names": ["PresenceBadge", "presenceAvailableFilled", "presenceAvailableRegular", "presenceAwayFilled", "presenceAwayRegular", "presenceBadgeClassNames", "presenceBlockedRegular", "presenceBusyFilled", "presenceDndFilled", "presenceDndRegular", "presenceOfflineRegular", "presenceOofRegular", "presenceUnknownRegular", "usePresenceBadgeStyles_unstable", "usePresenceBadge_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAEEA,aAAa;eAAbA,oBAAa;;IACbC,uBAAuB;eAAvBA,8BAAuB;;IACvBC,wBAAwB;eAAxBA,+BAAwB;;IACxBC,kBAAkB;eAAlBA,yBAAkB;;IAClBC,mBAAmB;eAAnBA,0BAAmB;;IACnBC,uBAAuB;eAAvBA,8BAAuB;;IACvBC,sBAAsB;eAAtBA,6BAAsB;;IACtBC,kBAAkB;eAAlBA,yBAAkB;;IAClBC,iBAAiB;eAAjBA,wBAAiB;;IACjBC,kBAAkB;eAAlBA,yBAAkB;;IAClBC,sBAAsB;eAAtBA,6BAAsB;;IACtBC,kBAAkB;eAAlBA,yBAAkB;;IAClBC,sBAAsB;eAAtBA,6BAAsB;;IACtBC,+BAA+B;eAA/BA,sCAA+B;;IAC/BC,yBAAyB;eAAzBA,gCAAyB;;;uBACpB"}