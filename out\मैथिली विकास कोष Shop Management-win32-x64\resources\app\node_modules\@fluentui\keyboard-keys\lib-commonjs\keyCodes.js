"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    A: function() {
        return A;
    },
    Accept: function() {
        return Accept;
    },
    Alt: function() {
        return Alt;
    },
    AltGraph: function() {
        return AltGraph;
    },
    Ampersand: function() {
        return Ampersand;
    },
    ArrowDown: function() {
        return ArrowDown;
    },
    ArrowLeft: function() {
        return ArrowLeft;
    },
    ArrowRight: function() {
        return ArrowRight;
    },
    ArrowUp: function() {
        return ArrowUp;
    },
    AtSign: function() {
        return AtSign;
    },
    Attn: function() {
        return Attn;
    },
    B: function() {
        return B;
    },
    BackSlash: function() {
        return BackSlash;
    },
    Backspace: function() {
        return Backspace;
    },
    C: function() {
        return C;
    },
    Cancel: function() {
        return Cancel;
    },
    CapsLock: function() {
        return CapsLock;
    },
    Caret: function() {
        return Caret;
    },
    Clear: function() {
        return Clear;
    },
    Comma: function() {
        return Comma;
    },
    ContextMenu: function() {
        return ContextMenu;
    },
    Control: function() {
        return Control;
    },
    Convert: function() {
        return Convert;
    },
    CrSel: function() {
        return CrSel;
    },
    D: function() {
        return D;
    },
    Decimal: function() {
        return Decimal;
    },
    Delete: function() {
        return Delete;
    },
    Digit0: function() {
        return Digit0;
    },
    Digit1: function() {
        return Digit1;
    },
    Digit2: function() {
        return Digit2;
    },
    Digit3: function() {
        return Digit3;
    },
    Digit4: function() {
        return Digit4;
    },
    Digit5: function() {
        return Digit5;
    },
    Digit6: function() {
        return Digit6;
    },
    Digit7: function() {
        return Digit7;
    },
    Digit8: function() {
        return Digit8;
    },
    Digit9: function() {
        return Digit9;
    },
    DollarSign: function() {
        return DollarSign;
    },
    DoubleQuote: function() {
        return DoubleQuote;
    },
    E: function() {
        return E;
    },
    End: function() {
        return End;
    },
    Enter: function() {
        return Enter;
    },
    EqualsSign: function() {
        return EqualsSign;
    },
    EraseEof: function() {
        return EraseEof;
    },
    Escape: function() {
        return Escape;
    },
    ExSel: function() {
        return ExSel;
    },
    ExclamationPoint: function() {
        return ExclamationPoint;
    },
    Execute: function() {
        return Execute;
    },
    F: function() {
        return F;
    },
    F1: function() {
        return F1;
    },
    F10: function() {
        return F10;
    },
    F11: function() {
        return F11;
    },
    F12: function() {
        return F12;
    },
    F13: function() {
        return F13;
    },
    F14: function() {
        return F14;
    },
    F15: function() {
        return F15;
    },
    F16: function() {
        return F16;
    },
    F17: function() {
        return F17;
    },
    F18: function() {
        return F18;
    },
    F19: function() {
        return F19;
    },
    F2: function() {
        return F2;
    },
    F20: function() {
        return F20;
    },
    F21: function() {
        return F21;
    },
    F22: function() {
        return F22;
    },
    F23: function() {
        return F23;
    },
    F24: function() {
        return F24;
    },
    F3: function() {
        return F3;
    },
    F4: function() {
        return F4;
    },
    F5: function() {
        return F5;
    },
    F6: function() {
        return F6;
    },
    F7: function() {
        return F7;
    },
    F8: function() {
        return F8;
    },
    F9: function() {
        return F9;
    },
    ForwardSlash: function() {
        return ForwardSlash;
    },
    G: function() {
        return G;
    },
    GraveAccent: function() {
        return GraveAccent;
    },
    H: function() {
        return H;
    },
    Help: function() {
        return Help;
    },
    Home: function() {
        return Home;
    },
    I: function() {
        return I;
    },
    Insert: function() {
        return Insert;
    },
    J: function() {
        return J;
    },
    K: function() {
        return K;
    },
    L: function() {
        return L;
    },
    LeftAngleBracket: function() {
        return LeftAngleBracket;
    },
    LeftCurlyBrace: function() {
        return LeftCurlyBrace;
    },
    LeftParenthesis: function() {
        return LeftParenthesis;
    },
    LeftSquareBracket: function() {
        return LeftSquareBracket;
    },
    M: function() {
        return M;
    },
    Meta: function() {
        return Meta;
    },
    MinusSign: function() {
        return MinusSign;
    },
    ModeChange: function() {
        return ModeChange;
    },
    MultiplicationSign: function() {
        return MultiplicationSign;
    },
    N: function() {
        return N;
    },
    NonConvert: function() {
        return NonConvert;
    },
    NumLock: function() {
        return NumLock;
    },
    O: function() {
        return O;
    },
    OS: function() {
        return OS;
    },
    P: function() {
        return P;
    },
    PageDown: function() {
        return PageDown;
    },
    PageUp: function() {
        return PageUp;
    },
    Pause: function() {
        return Pause;
    },
    PercentSign: function() {
        return PercentSign;
    },
    Pipe: function() {
        return Pipe;
    },
    Play: function() {
        return Play;
    },
    PlusSign: function() {
        return PlusSign;
    },
    PoundSign: function() {
        return PoundSign;
    },
    Print: function() {
        return Print;
    },
    PrintScreen: function() {
        return PrintScreen;
    },
    Q: function() {
        return Q;
    },
    QuestionMark: function() {
        return QuestionMark;
    },
    R: function() {
        return R;
    },
    RightAngleBracket: function() {
        return RightAngleBracket;
    },
    RightCurlyBrace: function() {
        return RightCurlyBrace;
    },
    RightParenthesis: function() {
        return RightParenthesis;
    },
    RightSquareBracket: function() {
        return RightSquareBracket;
    },
    S: function() {
        return S;
    },
    ScrollLock: function() {
        return ScrollLock;
    },
    Select: function() {
        return Select;
    },
    Semicolon: function() {
        return Semicolon;
    },
    Shift: function() {
        return Shift;
    },
    SingleQuote: function() {
        return SingleQuote;
    },
    Space: function() {
        return Space;
    },
    T: function() {
        return T;
    },
    Tab: function() {
        return Tab;
    },
    Tilde: function() {
        return Tilde;
    },
    U: function() {
        return U;
    },
    Underscore: function() {
        return Underscore;
    },
    V: function() {
        return V;
    },
    VolumeDown: function() {
        return VolumeDown;
    },
    VolumeMute: function() {
        return VolumeMute;
    },
    VolumeUp: function() {
        return VolumeUp;
    },
    W: function() {
        return W;
    },
    X: function() {
        return X;
    },
    Y: function() {
        return Y;
    },
    Z: function() {
        return Z;
    },
    ZoomOut: function() {
        return ZoomOut;
    },
    a: function() {
        return a;
    },
    b: function() {
        return b;
    },
    c: function() {
        return c;
    },
    d: function() {
        return d;
    },
    e: function() {
        return e;
    },
    f: function() {
        return f;
    },
    g: function() {
        return g;
    },
    h: function() {
        return h;
    },
    i: function() {
        return i;
    },
    j: function() {
        return j;
    },
    k: function() {
        return k;
    },
    l: function() {
        return l;
    },
    m: function() {
        return m;
    },
    n: function() {
        return n;
    },
    o: function() {
        return o;
    },
    p: function() {
        return p;
    },
    q: function() {
        return q;
    },
    r: function() {
        return r;
    },
    s: function() {
        return s;
    },
    t: function() {
        return t;
    },
    u: function() {
        return u;
    },
    v: function() {
        return v;
    },
    w: function() {
        return w;
    },
    x: function() {
        return x;
    },
    y: function() {
        return y;
    },
    z: function() {
        return z;
    }
});
const Cancel = 3;
const Help = 6;
const Backspace = 8;
const Tab = 9;
const Clear = 12;
const Enter = 13;
const Shift = 16;
const Control = 17;
const Alt = 18;
const Pause = 19;
const CapsLock = 20;
const Escape = 27;
const Convert = 28;
const NonConvert = 29;
const Accept = 30;
const ModeChange = 31;
const Space = 32;
const PageUp = 33;
const PageDown = 34;
const End = 35;
const Home = 36;
const ArrowLeft = 37;
const ArrowUp = 38;
const ArrowRight = 39;
const ArrowDown = 40;
const Select = 41;
const Print = 42;
const Execute = 43;
const PrintScreen = 44;
const Insert = 45;
const Delete = 46;
const Digit0 = 48;
const RightParenthesis = 48;
const Digit1 = 49;
const ExclamationPoint = 49;
const Digit2 = 50;
const AtSign = 50;
const Digit3 = 51;
const PoundSign = 51;
const Digit4 = 52;
const DollarSign = 52;
const Digit5 = 53;
const PercentSign = 53;
const Digit6 = 54;
const Caret = 54;
const Digit7 = 55;
const Ampersand = 55;
const Digit8 = 56;
const MultiplicationSign = 56;
const Digit9 = 57;
const LeftParenthesis = 57;
const a = 65;
const A = 65;
const b = 66;
const B = 66;
const c = 67;
const C = 67;
const d = 68;
const D = 68;
const e = 69;
const E = 69;
const f = 70;
const F = 70;
const g = 71;
const G = 71;
const h = 72;
const H = 72;
const i = 73;
const I = 73;
const j = 74;
const J = 74;
const k = 75;
const K = 75;
const l = 76;
const L = 76;
const m = 77;
const M = 77;
const n = 78;
const N = 78;
const o = 79;
const O = 79;
const p = 80;
const P = 80;
const q = 81;
const Q = 81;
const r = 82;
const R = 82;
const s = 83;
const S = 83;
const t = 84;
const T = 84;
const u = 85;
const U = 85;
const v = 86;
const V = 86;
const w = 87;
const W = 87;
const x = 88;
const X = 88;
const y = 89;
const Y = 89;
const z = 90;
const Z = 90;
const OS = 91;
const ContextMenu = 93;
const F1 = 112;
const F2 = 113;
const F3 = 114;
const F4 = 115;
const F5 = 116;
const F6 = 117;
const F7 = 118;
const F8 = 119;
const F9 = 120;
const F10 = 121;
const F11 = 122;
const F12 = 123;
const F13 = 124;
const F14 = 125;
const F15 = 126;
const F16 = 127;
const F17 = 128;
const F18 = 129;
const F19 = 130;
const F20 = 131;
const F21 = 132;
const F22 = 133;
const F23 = 134;
const F24 = 135;
const NumLock = 144;
const ScrollLock = 145;
const VolumeMute = 181;
const VolumeDown = 182;
const VolumeUp = 183;
const Semicolon = 186;
const EqualsSign = 187;
const PlusSign = 187;
const Comma = 188;
const LeftAngleBracket = 188;
const MinusSign = 189;
const Underscore = 189;
const Decimal = 190;
const RightAngleBracket = 190;
const ForwardSlash = 191;
const QuestionMark = 191;
const GraveAccent = 192;
const Tilde = 192;
const LeftSquareBracket = 219;
const LeftCurlyBrace = 219;
const BackSlash = 220;
const Pipe = 220;
const RightSquareBracket = 221;
const RightCurlyBrace = 221;
const SingleQuote = 222;
const DoubleQuote = 222;
const Meta = 224;
const AltGraph = 225;
const Attn = 246;
const CrSel = 247;
const ExSel = 248;
const EraseEof = 249;
const Play = 250;
const ZoomOut = 251;
