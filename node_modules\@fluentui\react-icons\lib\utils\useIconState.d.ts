/// <reference types="react" />
import { FluentIconsProps } from "./FluentIconsProps.types";
export declare type UseIconStateOptions = {
    flipInRtl?: boolean;
};
export declare const useIconState: <TBaseAttributes extends import("react").HTMLAttributes<HTMLElement> | import("react").SVGAttributes<SVGElement> = import("react").SVGAttributes<SVGElement>, TRefType extends HTMLElement | SVGSVGElement = SVGSVGElement>(props: FluentIconsProps<TBaseAttributes, TRefType>, options?: UseIconStateOptions | undefined) => Pick<FluentIconsProps<TBaseAttributes, TRefType>, "title" | "filled" | "ref" | "key" | "className" | Exclude<keyof TBaseAttributes, "primaryFill">>;
