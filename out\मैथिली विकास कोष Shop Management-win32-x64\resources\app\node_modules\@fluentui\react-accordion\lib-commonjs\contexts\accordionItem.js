"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AccordionItemProvider: function() {
        return AccordionItemProvider;
    },
    useAccordionItemContext_unstable: function() {
        return useAccordionItemContext_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const AccordionItemContext = /*#__PURE__*/ _react.createContext(undefined);
const accordionItemContextDefaultValue = {
    open: false,
    disabled: false,
    value: undefined,
    onHeaderClick () {
    /* noop */ }
};
const { Provider: AccordionItemProvider } = AccordionItemContext;
const useAccordionItemContext_unstable = ()=>{
    var _React_useContext;
    return (_React_useContext = _react.useContext(AccordionItemContext)) !== null && _React_useContext !== void 0 ? _React_useContext : accordionItemContextDefaultValue;
};
