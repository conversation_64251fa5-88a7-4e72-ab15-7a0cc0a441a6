{"version": 3, "sources": ["../src/components/CarouselNavContainer/CarouselNavContainer.types.ts"], "sourcesContent": ["import type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\nimport { CarouselAutoplayButton } from '../CarouselAutoplayButton/CarouselAutoplayButton';\nimport { CarouselButtonProps } from '../CarouselButton/CarouselButton.types';\nimport { TooltipProps } from '@fluentui/react-tooltip';\n\nexport type CarouselNavContainerSlots = {\n  root: Slot<'div'>;\n  next?: Slot<CarouselButtonProps>;\n  nextTooltip?: Slot<TooltipProps>;\n  prev?: Slot<CarouselButtonProps>;\n  prevTooltip?: Slot<TooltipProps>;\n  autoplay?: Slot<typeof CarouselAutoplayButton>;\n  autoplayTooltip?: Slot<TooltipProps>;\n};\n\n/**\n * CarouselNavContainer Props\n */\nexport type CarouselNavContainerProps = ComponentProps<CarouselNavContainerSlots> & {\n  /**\n   * Default: 'inline'\n   * Defines the nav container layout:\n   *\n   * 'inline' - Default controls inline with carousel view\n   *\n   *  inline-wide - Similar to inline but places nav buttons on far left/right\n   *\n   * 'overlay' - Controls overlaid on bottom of carousel viewport,\n   *\n   * 'overlay-wide' - Controls overlaid on bottom of carousel viewport with prev+autoplay/next buttons on far side\n   *\n   * 'overlay-expanded' - Controls overlaid on bottom of carousel viewport, with prev/next buttons on sides vertically centered\n   */\n  layout?: 'inline' | 'inline-wide' | 'overlay' | 'overlay-wide' | 'overlay-expanded';\n};\n\n/**\n * State used in rendering CarouselNavContainer\n */\nexport type CarouselNavContainerState = ComponentState<CarouselNavContainerSlots> &\n  Pick<CarouselNavContainerProps, 'layout'>;\n"], "names": [], "rangeMappings": ";;", "mappings": "AAoCA;;CAEC"}