{"version": 3, "names": ["tokens", "__resetStyles", "__styles", "mergeClasses", "avatarClassNames", "root", "image", "initials", "icon", "badge", "vars", "badgeRadius", "badgeGap", "badgeAlign", "ringWidth", "useRootClassName", "r", "s", "useImageClassName", "useIconInitialsClassName", "badgeMask", "margin", "centerOffset", "innerRadius", "outerRadius", "useStyles", "textCaption2Strong", "Be2twd7", "textCaption1Strong", "textSubtitle2", "textSubtitle1", "textTitle3", "squareSmall", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "squareMedium", "squareLarge", "squareXLarge", "activeOrInactive", "Bz10aip", "Bmy1vo4", "B3o57yi", "Bkqvd7p", "Hwfdqs", "ring", "Ftih45", "ringBadgeCutout", "f4a502", "ringThick", "of393c", "<PERSON><PERSON><PERSON><PERSON>", "ringThickest", "shadow", "Bsft5z2", "shadow4", "Be6vj1x", "shadow8", "shadow16", "shadow28", "inactive", "abs64n", "Bfgortx", "Bnvr3x9", "b2tv09", "Bucmhp4", "iayac2", "b6ubon", "Bqinb2h", "qhf8xq", "B5kzvoi", "j35jbq", "badgeCutout", "btxmck", "Dnlfbu", "tiny", "Bdjeniz", "niu6jh", "small", "medium", "large", "icon12", "icon16", "icon20", "icon24", "icon28", "icon32", "icon48", "d", "p", "m", "useSizeStyles", "a9b677", "Bqenvij", "useColorStyles", "neutral", "sj55zd", "De3pzq", "brand", "cranberry", "red", "pumpkin", "peach", "marigold", "gold", "brass", "brown", "forest", "seafoam", "teal", "steel", "blue", "cornflower", "navy", "lavender", "purple", "grape", "lilac", "pink", "magenta", "plum", "beige", "mink", "platinum", "anchor", "useRingColorStyles", "Bic5iru", "useAvatarStyles_unstable", "state", "size", "shape", "active", "activeAppearance", "color", "rootClassName", "imageClassName", "iconInitialsClassName", "styles", "sizeStyles", "colorStyles", "ringColorStyles", "rootClasses", "push", "className", "iconSizeClass"], "sources": ["useAvatarStyles.styles.js"], "sourcesContent": ["import { tokens } from '@fluentui/react-theme';\nimport { makeResetStyles, makeStyles, mergeClasses } from '@griffel/react';\nexport const avatarClassNames = {\n    root: 'fui-Avatar',\n    image: 'fui-Avatar__image',\n    initials: 'fui-Avatar__initials',\n    icon: 'fui-Avatar__icon',\n    badge: 'fui-Avatar__badge'\n};\n// CSS variables used internally in Avatar's styles\nconst vars = {\n    badgeRadius: '--fui-Avatar-badgeRadius',\n    badgeGap: '--fui-Avatar-badgeGap',\n    badgeAlign: '--fui-Avatar-badgeAlign',\n    ringWidth: '--fui-Avatar-ringWidth'\n};\nconst useRootClassName = makeResetStyles({\n    display: 'inline-block',\n    flexShrink: 0,\n    position: 'relative',\n    verticalAlign: 'middle',\n    borderRadius: tokens.borderRadiusCircular,\n    fontFamily: tokens.fontFamilyBase,\n    fontWeight: tokens.fontWeightSemibold,\n    fontSize: tokens.fontSizeBase300,\n    width: '32px',\n    height: '32px',\n    // ::before is the ring, and ::after is the shadow.\n    // These are not displayed by default; the ring and shadow clases set content: \"\" to display them when appropriate.\n    '::before,::after': {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        bottom: 0,\n        right: 0,\n        zIndex: -1,\n        margin: `calc(-2 * var(${vars.ringWidth}, 0px))`,\n        borderRadius: 'inherit',\n        transitionProperty: 'margin, opacity',\n        transitionTimingFunction: `${tokens.curveEasyEaseMax}, ${tokens.curveLinear}`,\n        transitionDuration: `${tokens.durationUltraSlow}, ${tokens.durationSlower}`,\n        '@media screen and (prefers-reduced-motion: reduce)': {\n            transitionDuration: '0.01ms'\n        }\n    },\n    '::before': {\n        borderStyle: 'solid',\n        borderWidth: `var(${vars.ringWidth})`\n    }\n});\nconst useImageClassName = makeResetStyles({\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    borderRadius: 'inherit',\n    objectFit: 'cover',\n    verticalAlign: 'top'\n});\nconst useIconInitialsClassName = makeResetStyles({\n    position: 'absolute',\n    boxSizing: 'border-box',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    lineHeight: '1',\n    border: `${tokens.strokeWidthThin} solid ${tokens.colorTransparentStroke}`,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    verticalAlign: 'center',\n    textAlign: 'center',\n    userSelect: 'none',\n    borderRadius: 'inherit'\n});\n/**\n * Helper to create a maskImage that punches out a circle larger than the badge by `badgeGap`.\n * This creates a transparent gap between the badge and Avatar.\n *\n * Used by the icon, initials, and image slots, as well as the ring ::before pseudo-element.\n */ const badgeMask = (margin)=>{\n    // Center the cutout at the badge's radius away from the edge.\n    // The ring (::before) also has a 2 * ringWidth margin that also needs to be offset.\n    const centerOffset = margin ? `calc(var(${vars.badgeRadius}) + ${margin})` : `var(${vars.badgeRadius})`;\n    // radial-gradient does not have anti-aliasing, so the transparent and opaque gradient stops are offset by +/- 0.25px\n    // to \"fade\" from transparent to opaque over a half-pixel and ease the transition.\n    const innerRadius = `calc(var(${vars.badgeRadius}) + var(${vars.badgeGap}) - 0.25px)`;\n    const outerRadius = `calc(var(${vars.badgeRadius}) + var(${vars.badgeGap}) + 0.25px)`;\n    return `radial-gradient(circle at bottom ${centerOffset} var(${vars.badgeAlign}) ${centerOffset}, ` + `transparent ${innerRadius}, white ${outerRadius})`;\n};\nconst useStyles = makeStyles({\n    textCaption2Strong: {\n        fontSize: tokens.fontSizeBase100\n    },\n    textCaption1Strong: {\n        fontSize: tokens.fontSizeBase200\n    },\n    textSubtitle2: {\n        fontSize: tokens.fontSizeBase400\n    },\n    textSubtitle1: {\n        fontSize: tokens.fontSizeBase500\n    },\n    textTitle3: {\n        fontSize: tokens.fontSizeBase600\n    },\n    squareSmall: {\n        borderRadius: tokens.borderRadiusSmall\n    },\n    squareMedium: {\n        borderRadius: tokens.borderRadiusMedium\n    },\n    squareLarge: {\n        borderRadius: tokens.borderRadiusLarge\n    },\n    squareXLarge: {\n        borderRadius: tokens.borderRadiusXLarge\n    },\n    activeOrInactive: {\n        transform: 'perspective(1px)',\n        transitionProperty: 'transform, opacity',\n        transitionDuration: `${tokens.durationUltraSlow}, ${tokens.durationFaster}`,\n        transitionTimingFunction: `${tokens.curveEasyEaseMax}, ${tokens.curveLinear}`,\n        '@media screen and (prefers-reduced-motion: reduce)': {\n            transitionDuration: '0.01ms'\n        }\n    },\n    ring: {\n        // Show the ::before pseudo-element, which is the ring\n        '::before': {\n            content: '\"\"'\n        }\n    },\n    ringBadgeCutout: {\n        '::before': {\n            maskImage: badgeMask(/*margin =*/ `2 * var(${vars.ringWidth})`)\n        }\n    },\n    ringThick: {\n        [vars.ringWidth]: tokens.strokeWidthThick\n    },\n    ringThicker: {\n        [vars.ringWidth]: tokens.strokeWidthThicker\n    },\n    ringThickest: {\n        [vars.ringWidth]: tokens.strokeWidthThickest\n    },\n    shadow: {\n        // Show the ::after pseudo-element, which is the shadow\n        '::after': {\n            content: '\"\"'\n        }\n    },\n    shadow4: {\n        '::after': {\n            boxShadow: tokens.shadow4\n        }\n    },\n    shadow8: {\n        '::after': {\n            boxShadow: tokens.shadow8\n        }\n    },\n    shadow16: {\n        '::after': {\n            boxShadow: tokens.shadow16\n        }\n    },\n    shadow28: {\n        '::after': {\n            boxShadow: tokens.shadow28\n        }\n    },\n    inactive: {\n        opacity: '0.8',\n        transform: 'scale(0.875)',\n        transitionTimingFunction: `${tokens.curveDecelerateMin}, ${tokens.curveLinear}`,\n        '::before,::after': {\n            margin: 0,\n            opacity: 0,\n            transitionTimingFunction: `${tokens.curveDecelerateMin}, ${tokens.curveLinear}`\n        }\n    },\n    // Applied to the badge slot\n    badge: {\n        position: 'absolute',\n        bottom: 0,\n        right: 0\n    },\n    // Applied to the image, initials, or icon slot when there is a badge\n    badgeCutout: {\n        maskImage: badgeMask()\n    },\n    // Applied to the root when there is a badge\n    badgeAlign: {\n        // Griffel won't auto-flip the \"right\" alignment to \"left\" in RTL if it is inline in the maskImage,\n        // so split it out into a css variable that will auto-flip.\n        [vars.badgeAlign]: 'right'\n    },\n    // Badge size: applied to root when there is a badge\n    tiny: {\n        [vars.badgeRadius]: '3px',\n        [vars.badgeGap]: tokens.strokeWidthThin\n    },\n    'extra-small': {\n        [vars.badgeRadius]: '5px',\n        [vars.badgeGap]: tokens.strokeWidthThin\n    },\n    small: {\n        [vars.badgeRadius]: '6px',\n        [vars.badgeGap]: tokens.strokeWidthThin\n    },\n    medium: {\n        [vars.badgeRadius]: '8px',\n        [vars.badgeGap]: tokens.strokeWidthThin\n    },\n    large: {\n        [vars.badgeRadius]: '10px',\n        [vars.badgeGap]: tokens.strokeWidthThick\n    },\n    'extra-large': {\n        [vars.badgeRadius]: '14px',\n        [vars.badgeGap]: tokens.strokeWidthThick\n    },\n    icon12: {\n        fontSize: '12px'\n    },\n    icon16: {\n        fontSize: '16px'\n    },\n    icon20: {\n        fontSize: '20px'\n    },\n    icon24: {\n        fontSize: '24px'\n    },\n    icon28: {\n        fontSize: '28px'\n    },\n    icon32: {\n        fontSize: '32px'\n    },\n    icon48: {\n        fontSize: '48px'\n    }\n});\nexport const useSizeStyles = makeStyles({\n    16: {\n        width: '16px',\n        height: '16px'\n    },\n    20: {\n        width: '20px',\n        height: '20px'\n    },\n    24: {\n        width: '24px',\n        height: '24px'\n    },\n    28: {\n        width: '28px',\n        height: '28px'\n    },\n    32: {\n        width: '32px',\n        height: '32px'\n    },\n    36: {\n        width: '36px',\n        height: '36px'\n    },\n    40: {\n        width: '40px',\n        height: '40px'\n    },\n    48: {\n        width: '48px',\n        height: '48px'\n    },\n    56: {\n        width: '56px',\n        height: '56px'\n    },\n    64: {\n        width: '64px',\n        height: '64px'\n    },\n    72: {\n        width: '72px',\n        height: '72px'\n    },\n    96: {\n        width: '96px',\n        height: '96px'\n    },\n    120: {\n        width: '120px',\n        height: '120px'\n    },\n    128: {\n        width: '128px',\n        height: '128px'\n    }\n});\nconst useColorStyles = makeStyles({\n    neutral: {\n        color: tokens.colorNeutralForeground3,\n        backgroundColor: tokens.colorNeutralBackground6\n    },\n    brand: {\n        color: tokens.colorNeutralForegroundStaticInverted,\n        backgroundColor: tokens.colorBrandBackgroundStatic\n    },\n    'dark-red': {\n        color: tokens.colorPaletteDarkRedForeground2,\n        backgroundColor: tokens.colorPaletteDarkRedBackground2\n    },\n    cranberry: {\n        color: tokens.colorPaletteCranberryForeground2,\n        backgroundColor: tokens.colorPaletteCranberryBackground2\n    },\n    red: {\n        color: tokens.colorPaletteRedForeground2,\n        backgroundColor: tokens.colorPaletteRedBackground2\n    },\n    pumpkin: {\n        color: tokens.colorPalettePumpkinForeground2,\n        backgroundColor: tokens.colorPalettePumpkinBackground2\n    },\n    peach: {\n        color: tokens.colorPalettePeachForeground2,\n        backgroundColor: tokens.colorPalettePeachBackground2\n    },\n    marigold: {\n        color: tokens.colorPaletteMarigoldForeground2,\n        backgroundColor: tokens.colorPaletteMarigoldBackground2\n    },\n    gold: {\n        color: tokens.colorPaletteGoldForeground2,\n        backgroundColor: tokens.colorPaletteGoldBackground2\n    },\n    brass: {\n        color: tokens.colorPaletteBrassForeground2,\n        backgroundColor: tokens.colorPaletteBrassBackground2\n    },\n    brown: {\n        color: tokens.colorPaletteBrownForeground2,\n        backgroundColor: tokens.colorPaletteBrownBackground2\n    },\n    forest: {\n        color: tokens.colorPaletteForestForeground2,\n        backgroundColor: tokens.colorPaletteForestBackground2\n    },\n    seafoam: {\n        color: tokens.colorPaletteSeafoamForeground2,\n        backgroundColor: tokens.colorPaletteSeafoamBackground2\n    },\n    'dark-green': {\n        color: tokens.colorPaletteDarkGreenForeground2,\n        backgroundColor: tokens.colorPaletteDarkGreenBackground2\n    },\n    'light-teal': {\n        color: tokens.colorPaletteLightTealForeground2,\n        backgroundColor: tokens.colorPaletteLightTealBackground2\n    },\n    teal: {\n        color: tokens.colorPaletteTealForeground2,\n        backgroundColor: tokens.colorPaletteTealBackground2\n    },\n    steel: {\n        color: tokens.colorPaletteSteelForeground2,\n        backgroundColor: tokens.colorPaletteSteelBackground2\n    },\n    blue: {\n        color: tokens.colorPaletteBlueForeground2,\n        backgroundColor: tokens.colorPaletteBlueBackground2\n    },\n    'royal-blue': {\n        color: tokens.colorPaletteRoyalBlueForeground2,\n        backgroundColor: tokens.colorPaletteRoyalBlueBackground2\n    },\n    cornflower: {\n        color: tokens.colorPaletteCornflowerForeground2,\n        backgroundColor: tokens.colorPaletteCornflowerBackground2\n    },\n    navy: {\n        color: tokens.colorPaletteNavyForeground2,\n        backgroundColor: tokens.colorPaletteNavyBackground2\n    },\n    lavender: {\n        color: tokens.colorPaletteLavenderForeground2,\n        backgroundColor: tokens.colorPaletteLavenderBackground2\n    },\n    purple: {\n        color: tokens.colorPalettePurpleForeground2,\n        backgroundColor: tokens.colorPalettePurpleBackground2\n    },\n    grape: {\n        color: tokens.colorPaletteGrapeForeground2,\n        backgroundColor: tokens.colorPaletteGrapeBackground2\n    },\n    lilac: {\n        color: tokens.colorPaletteLilacForeground2,\n        backgroundColor: tokens.colorPaletteLilacBackground2\n    },\n    pink: {\n        color: tokens.colorPalettePinkForeground2,\n        backgroundColor: tokens.colorPalettePinkBackground2\n    },\n    magenta: {\n        color: tokens.colorPaletteMagentaForeground2,\n        backgroundColor: tokens.colorPaletteMagentaBackground2\n    },\n    plum: {\n        color: tokens.colorPalettePlumForeground2,\n        backgroundColor: tokens.colorPalettePlumBackground2\n    },\n    beige: {\n        color: tokens.colorPaletteBeigeForeground2,\n        backgroundColor: tokens.colorPaletteBeigeBackground2\n    },\n    mink: {\n        color: tokens.colorPaletteMinkForeground2,\n        backgroundColor: tokens.colorPaletteMinkBackground2\n    },\n    platinum: {\n        color: tokens.colorPalettePlatinumForeground2,\n        backgroundColor: tokens.colorPalettePlatinumBackground2\n    },\n    anchor: {\n        color: tokens.colorPaletteAnchorForeground2,\n        backgroundColor: tokens.colorPaletteAnchorBackground2\n    }\n});\nconst useRingColorStyles = makeStyles({\n    neutral: {\n        '::before': {\n            color: tokens.colorBrandStroke1\n        }\n    },\n    brand: {\n        '::before': {\n            color: tokens.colorBrandStroke1\n        }\n    },\n    'dark-red': {\n        '::before': {\n            color: tokens.colorPaletteDarkRedBorderActive\n        }\n    },\n    cranberry: {\n        '::before': {\n            color: tokens.colorPaletteCranberryBorderActive\n        }\n    },\n    red: {\n        '::before': {\n            color: tokens.colorPaletteRedBorderActive\n        }\n    },\n    pumpkin: {\n        '::before': {\n            color: tokens.colorPalettePumpkinBorderActive\n        }\n    },\n    peach: {\n        '::before': {\n            color: tokens.colorPalettePeachBorderActive\n        }\n    },\n    marigold: {\n        '::before': {\n            color: tokens.colorPaletteMarigoldBorderActive\n        }\n    },\n    gold: {\n        '::before': {\n            color: tokens.colorPaletteGoldBorderActive\n        }\n    },\n    brass: {\n        '::before': {\n            color: tokens.colorPaletteBrassBorderActive\n        }\n    },\n    brown: {\n        '::before': {\n            color: tokens.colorPaletteBrownBorderActive\n        }\n    },\n    forest: {\n        '::before': {\n            color: tokens.colorPaletteForestBorderActive\n        }\n    },\n    seafoam: {\n        '::before': {\n            color: tokens.colorPaletteSeafoamBorderActive\n        }\n    },\n    'dark-green': {\n        '::before': {\n            color: tokens.colorPaletteDarkGreenBorderActive\n        }\n    },\n    'light-teal': {\n        '::before': {\n            color: tokens.colorPaletteLightTealBorderActive\n        }\n    },\n    teal: {\n        '::before': {\n            color: tokens.colorPaletteTealBorderActive\n        }\n    },\n    steel: {\n        '::before': {\n            color: tokens.colorPaletteSteelBorderActive\n        }\n    },\n    blue: {\n        '::before': {\n            color: tokens.colorPaletteBlueBorderActive\n        }\n    },\n    'royal-blue': {\n        '::before': {\n            color: tokens.colorPaletteRoyalBlueBorderActive\n        }\n    },\n    cornflower: {\n        '::before': {\n            color: tokens.colorPaletteCornflowerBorderActive\n        }\n    },\n    navy: {\n        '::before': {\n            color: tokens.colorPaletteNavyBorderActive\n        }\n    },\n    lavender: {\n        '::before': {\n            color: tokens.colorPaletteLavenderBorderActive\n        }\n    },\n    purple: {\n        '::before': {\n            color: tokens.colorPalettePurpleBorderActive\n        }\n    },\n    grape: {\n        '::before': {\n            color: tokens.colorPaletteGrapeBorderActive\n        }\n    },\n    lilac: {\n        '::before': {\n            color: tokens.colorPaletteLilacBorderActive\n        }\n    },\n    pink: {\n        '::before': {\n            color: tokens.colorPalettePinkBorderActive\n        }\n    },\n    magenta: {\n        '::before': {\n            color: tokens.colorPaletteMagentaBorderActive\n        }\n    },\n    plum: {\n        '::before': {\n            color: tokens.colorPalettePlumBorderActive\n        }\n    },\n    beige: {\n        '::before': {\n            color: tokens.colorPaletteBeigeBorderActive\n        }\n    },\n    mink: {\n        '::before': {\n            color: tokens.colorPaletteMinkBorderActive\n        }\n    },\n    platinum: {\n        '::before': {\n            color: tokens.colorPalettePlatinumBorderActive\n        }\n    },\n    anchor: {\n        '::before': {\n            color: tokens.colorPaletteAnchorBorderActive\n        }\n    }\n});\nexport const useAvatarStyles_unstable = (state)=>{\n    'use no memo';\n    const { size, shape, active, activeAppearance, color } = state;\n    const rootClassName = useRootClassName();\n    const imageClassName = useImageClassName();\n    const iconInitialsClassName = useIconInitialsClassName();\n    const styles = useStyles();\n    const sizeStyles = useSizeStyles();\n    const colorStyles = useColorStyles();\n    const ringColorStyles = useRingColorStyles();\n    const rootClasses = [\n        rootClassName,\n        size !== 32 && sizeStyles[size]\n    ];\n    if (state.badge) {\n        rootClasses.push(styles.badgeAlign, styles[state.badge.size || 'medium']);\n    }\n    if (size <= 24) {\n        rootClasses.push(styles.textCaption2Strong);\n    } else if (size <= 28) {\n        rootClasses.push(styles.textCaption1Strong);\n    } else if (size <= 40) {\n    // Default text size included in useRootClassName\n    } else if (size <= 56) {\n        rootClasses.push(styles.textSubtitle2);\n    } else if (size <= 96) {\n        rootClasses.push(styles.textSubtitle1);\n    } else {\n        rootClasses.push(styles.textTitle3);\n    }\n    if (shape === 'square') {\n        if (size <= 24) {\n            rootClasses.push(styles.squareSmall);\n        } else if (size <= 48) {\n            rootClasses.push(styles.squareMedium);\n        } else if (size <= 72) {\n            rootClasses.push(styles.squareLarge);\n        } else {\n            rootClasses.push(styles.squareXLarge);\n        }\n    }\n    if (active === 'active' || active === 'inactive') {\n        rootClasses.push(styles.activeOrInactive);\n        if (activeAppearance === 'ring' || activeAppearance === 'ring-shadow') {\n            rootClasses.push(styles.ring, ringColorStyles[color]);\n            if (state.badge) {\n                rootClasses.push(styles.ringBadgeCutout);\n            }\n            if (size <= 48) {\n                rootClasses.push(styles.ringThick);\n            } else if (size <= 64) {\n                rootClasses.push(styles.ringThicker);\n            } else {\n                rootClasses.push(styles.ringThickest);\n            }\n        }\n        if (activeAppearance === 'shadow' || activeAppearance === 'ring-shadow') {\n            rootClasses.push(styles.shadow);\n            if (size <= 28) {\n                rootClasses.push(styles.shadow4);\n            } else if (size <= 48) {\n                rootClasses.push(styles.shadow8);\n            } else if (size <= 64) {\n                rootClasses.push(styles.shadow16);\n            } else {\n                rootClasses.push(styles.shadow28);\n            }\n        }\n        // Note: The inactive style overrides some of the activeAppearance styles and must be applied after them\n        if (active === 'inactive') {\n            rootClasses.push(styles.inactive);\n        }\n    }\n    state.root.className = mergeClasses(avatarClassNames.root, ...rootClasses, state.root.className);\n    if (state.badge) {\n        state.badge.className = mergeClasses(avatarClassNames.badge, styles.badge, state.badge.className);\n    }\n    if (state.image) {\n        state.image.className = mergeClasses(avatarClassNames.image, imageClassName, colorStyles[color], state.badge && styles.badgeCutout, state.image.className);\n    }\n    if (state.initials) {\n        state.initials.className = mergeClasses(avatarClassNames.initials, iconInitialsClassName, colorStyles[color], state.badge && styles.badgeCutout, state.initials.className);\n    }\n    if (state.icon) {\n        let iconSizeClass;\n        if (size <= 16) {\n            iconSizeClass = styles.icon12;\n        } else if (size <= 24) {\n            iconSizeClass = styles.icon16;\n        } else if (size <= 40) {\n            iconSizeClass = styles.icon20;\n        } else if (size <= 48) {\n            iconSizeClass = styles.icon24;\n        } else if (size <= 56) {\n            iconSizeClass = styles.icon28;\n        } else if (size <= 72) {\n            iconSizeClass = styles.icon32;\n        } else {\n            iconSizeClass = styles.icon48;\n        }\n        state.icon.className = mergeClasses(avatarClassNames.icon, iconInitialsClassName, iconSizeClass, colorStyles[color], state.badge && styles.badgeCutout, state.icon.className);\n    }\n    return state;\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,SAAAC,aAAA,EAAAC,QAAA,EAAsCC,YAAY,QAAQ,gBAAgB;AAC1E,OAAO,MAAMC,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE,mBAAmB;EAC1BC,QAAQ,EAAE,sBAAsB;EAChCC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,IAAI,GAAG;EACTC,WAAW,EAAE,0BAA0B;EACvCC,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,yBAAyB;EACrCC,SAAS,EAAE;AACf,CAAC;AACD,MAAMC,gBAAgB,gBAAGd,aAAA;EAAAe,CAAA;EAAAC,CAAA;AAAA,CAiCxB,CAAC;AACF,MAAMC,iBAAiB,gBAAGjB,aAAA,sRASzB,CAAC;AACF,MAAMkB,wBAAwB,gBAAGlB,aAAA,otBAgBhC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AAAI,MAAMmB,SAAS,GAAIC,MAAM,IAAG;EAC5B;EACA;EACA,MAAMC,YAAY,GAAGD,MAAM,GAAG,YAAYX,IAAI,CAACC,WAAW,OAAOU,MAAM,GAAG,GAAG,OAAOX,IAAI,CAACC,WAAW,GAAG;EACvG;EACA;EACA,MAAMY,WAAW,GAAG,YAAYb,IAAI,CAACC,WAAW,WAAWD,IAAI,CAACE,QAAQ,aAAa;EACrF,MAAMY,WAAW,GAAG,YAAYd,IAAI,CAACC,WAAW,WAAWD,IAAI,CAACE,QAAQ,aAAa;EACrF,OAAO,oCAAoCU,YAAY,QAAQZ,IAAI,CAACG,UAAU,KAAKS,YAAY,IAAI,GAAG,eAAeC,WAAW,WAAWC,WAAW,GAAG;AAC7J,CAAC;AACD,MAAMC,SAAS,gBAAGvB,QAAA;EAAAwB,kBAAA;IAAAC,OAAA;EAAA;EAAAC,kBAAA;IAAAD,OAAA;EAAA;EAAAE,aAAA;IAAAF,OAAA;EAAA;EAAAG,aAAA;IAAAH,OAAA;EAAA;EAAAI,UAAA;IAAAJ,OAAA;EAAA;EAAAK,WAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAC,YAAA;IAAAL,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAE,WAAA;IAAAN,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAG,YAAA;IAAAP,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAI,gBAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAC,IAAA;IAAAC,MAAA;EAAA;EAAAC,eAAA;IAAAC,MAAA;EAAA;EAAAC,SAAA;IAAAC,MAAA;EAAA;EAAAC,WAAA;IAAAD,MAAA;EAAA;EAAAE,YAAA;IAAAF,MAAA;EAAA;EAAAG,MAAA;IAAAC,OAAA;EAAA;EAAAC,OAAA;IAAAC,OAAA;EAAA;EAAAC,OAAA;IAAAD,OAAA;EAAA;EAAAE,QAAA;IAAAF,OAAA;EAAA;EAAAG,QAAA;IAAAH,OAAA;EAAA;EAAAI,QAAA;IAAAC,MAAA;IAAArB,OAAA;IAAAG,OAAA;IAAAmB,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;EAAA7D,KAAA;IAAA8D,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAC,WAAA;IAAAC,MAAA;EAAA;EAAA9D,UAAA;IAAA+D,MAAA;EAAA;EAAAC,IAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,OAAA;IAAAC,MAAA;EAAA;EAAAC,KAAA;IAAAF,OAAA;IAAAC,MAAA;EAAA;EAAAE,MAAA;IAAAH,OAAA;IAAAC,MAAA;EAAA;EAAAG,KAAA;IAAAJ,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,OAAA;IAAAC,MAAA;EAAA;EAAAI,MAAA;IAAAxD,OAAA;EAAA;EAAAyD,MAAA;IAAAzD,OAAA;EAAA;EAAA0D,MAAA;IAAA1D,OAAA;EAAA;EAAA2D,MAAA;IAAA3D,OAAA;EAAA;EAAA4D,MAAA;IAAA5D,OAAA;EAAA;EAAA6D,MAAA;IAAA7D,OAAA;EAAA;EAAA8D,MAAA;IAAA9D,OAAA;EAAA;AAAA;EAAA+D,CAAA;IAAAC,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;EAAAC,CAAA;IAAAA,CAAA;EAAA;AAAA,CA2JjB,CAAC;AACF,OAAO,MAAMC,aAAa,gBAAG3F,QAAA;EAAA;IAAA4F,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,OAAA;EAAA;AAAA;EAAAL,CAAA;AAAA,CAyD5B,CAAC;AACF,MAAMM,cAAc,gBAAG9F,QAAA;EAAA+F,OAAA;IAAAC,MAAA;IAAAC,MAAA;EAAA;EAAAC,KAAA;IAAAF,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAAE,SAAA;IAAAH,MAAA;IAAAC,MAAA;EAAA;EAAAG,GAAA;IAAAJ,MAAA;IAAAC,MAAA;EAAA;EAAAI,OAAA;IAAAL,MAAA;IAAAC,MAAA;EAAA;EAAAK,KAAA;IAAAN,MAAA;IAAAC,MAAA;EAAA;EAAAM,QAAA;IAAAP,MAAA;IAAAC,MAAA;EAAA;EAAAO,IAAA;IAAAR,MAAA;IAAAC,MAAA;EAAA;EAAAQ,KAAA;IAAAT,MAAA;IAAAC,MAAA;EAAA;EAAAS,KAAA;IAAAV,MAAA;IAAAC,MAAA;EAAA;EAAAU,MAAA;IAAAX,MAAA;IAAAC,MAAA;EAAA;EAAAW,OAAA;IAAAZ,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAAY,IAAA;IAAAb,MAAA;IAAAC,MAAA;EAAA;EAAAa,KAAA;IAAAd,MAAA;IAAAC,MAAA;EAAA;EAAAc,IAAA;IAAAf,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAAe,UAAA;IAAAhB,MAAA;IAAAC,MAAA;EAAA;EAAAgB,IAAA;IAAAjB,MAAA;IAAAC,MAAA;EAAA;EAAAiB,QAAA;IAAAlB,MAAA;IAAAC,MAAA;EAAA;EAAAkB,MAAA;IAAAnB,MAAA;IAAAC,MAAA;EAAA;EAAAmB,KAAA;IAAApB,MAAA;IAAAC,MAAA;EAAA;EAAAoB,KAAA;IAAArB,MAAA;IAAAC,MAAA;EAAA;EAAAqB,IAAA;IAAAtB,MAAA;IAAAC,MAAA;EAAA;EAAAsB,OAAA;IAAAvB,MAAA;IAAAC,MAAA;EAAA;EAAAuB,IAAA;IAAAxB,MAAA;IAAAC,MAAA;EAAA;EAAAwB,KAAA;IAAAzB,MAAA;IAAAC,MAAA;EAAA;EAAAyB,IAAA;IAAA1B,MAAA;IAAAC,MAAA;EAAA;EAAA0B,QAAA;IAAA3B,MAAA;IAAAC,MAAA;EAAA;EAAA2B,MAAA;IAAA5B,MAAA;IAAAC,MAAA;EAAA;AAAA;EAAAT,CAAA;AAAA,CAiItB,CAAC;AACF,MAAMqC,kBAAkB,gBAAG7H,QAAA;EAAA+F,OAAA;IAAA+B,OAAA;EAAA;EAAA5B,KAAA;IAAA4B,OAAA;EAAA;EAAA;IAAAA,OAAA;EAAA;EAAA3B,SAAA;IAAA2B,OAAA;EAAA;EAAA1B,GAAA;IAAA0B,OAAA;EAAA;EAAAzB,OAAA;IAAAyB,OAAA;EAAA;EAAAxB,KAAA;IAAAwB,OAAA;EAAA;EAAAvB,QAAA;IAAAuB,OAAA;EAAA;EAAAtB,IAAA;IAAAsB,OAAA;EAAA;EAAArB,KAAA;IAAAqB,OAAA;EAAA;EAAApB,KAAA;IAAAoB,OAAA;EAAA;EAAAnB,MAAA;IAAAmB,OAAA;EAAA;EAAAlB,OAAA;IAAAkB,OAAA;EAAA;EAAA;IAAAA,OAAA;EAAA;EAAA;IAAAA,OAAA;EAAA;EAAAjB,IAAA;IAAAiB,OAAA;EAAA;EAAAhB,KAAA;IAAAgB,OAAA;EAAA;EAAAf,IAAA;IAAAe,OAAA;EAAA;EAAA;IAAAA,OAAA;EAAA;EAAAd,UAAA;IAAAc,OAAA;EAAA;EAAAb,IAAA;IAAAa,OAAA;EAAA;EAAAZ,QAAA;IAAAY,OAAA;EAAA;EAAAX,MAAA;IAAAW,OAAA;EAAA;EAAAV,KAAA;IAAAU,OAAA;EAAA;EAAAT,KAAA;IAAAS,OAAA;EAAA;EAAAR,IAAA;IAAAQ,OAAA;EAAA;EAAAP,OAAA;IAAAO,OAAA;EAAA;EAAAN,IAAA;IAAAM,OAAA;EAAA;EAAAL,KAAA;IAAAK,OAAA;EAAA;EAAAJ,IAAA;IAAAI,OAAA;EAAA;EAAAH,QAAA;IAAAG,OAAA;EAAA;EAAAF,MAAA;IAAAE,OAAA;EAAA;AAAA;EAAAtC,CAAA;AAAA,CAiK1B,CAAC;AACF,OAAO,MAAMuC,wBAAwB,GAAIC,KAAK,IAAG;EAC7C,aAAa;;EACb,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,MAAM;IAAEC,gBAAgB;IAAEC;EAAM,CAAC,GAAGL,KAAK;EAC9D,MAAMM,aAAa,GAAGzH,gBAAgB,CAAC,CAAC;EACxC,MAAM0H,cAAc,GAAGvH,iBAAiB,CAAC,CAAC;EAC1C,MAAMwH,qBAAqB,GAAGvH,wBAAwB,CAAC,CAAC;EACxD,MAAMwH,MAAM,GAAGlH,SAAS,CAAC,CAAC;EAC1B,MAAMmH,UAAU,GAAG/C,aAAa,CAAC,CAAC;EAClC,MAAMgD,WAAW,GAAG7C,cAAc,CAAC,CAAC;EACpC,MAAM8C,eAAe,GAAGf,kBAAkB,CAAC,CAAC;EAC5C,MAAMgB,WAAW,GAAG,CAChBP,aAAa,EACbL,IAAI,KAAK,EAAE,IAAIS,UAAU,CAACT,IAAI,CAAC,CAClC;EACD,IAAID,KAAK,CAACzH,KAAK,EAAE;IACbsI,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC9H,UAAU,EAAE8H,MAAM,CAACT,KAAK,CAACzH,KAAK,CAAC0H,IAAI,IAAI,QAAQ,CAAC,CAAC;EAC7E;EACA,IAAIA,IAAI,IAAI,EAAE,EAAE;IACZY,WAAW,CAACC,IAAI,CAACL,MAAM,CAACjH,kBAAkB,CAAC;EAC/C,CAAC,MAAM,IAAIyG,IAAI,IAAI,EAAE,EAAE;IACnBY,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC/G,kBAAkB,CAAC;EAC/C,CAAC,MAAM,IAAIuG,IAAI,IAAI,EAAE,EAAE;IACvB;EAAA,CACC,MAAM,IAAIA,IAAI,IAAI,EAAE,EAAE;IACnBY,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC9G,aAAa,CAAC;EAC1C,CAAC,MAAM,IAAIsG,IAAI,IAAI,EAAE,EAAE;IACnBY,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC7G,aAAa,CAAC;EAC1C,CAAC,MAAM;IACHiH,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC5G,UAAU,CAAC;EACvC;EACA,IAAIqG,KAAK,KAAK,QAAQ,EAAE;IACpB,IAAID,IAAI,IAAI,EAAE,EAAE;MACZY,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC3G,WAAW,CAAC;IACxC,CAAC,MAAM,IAAImG,IAAI,IAAI,EAAE,EAAE;MACnBY,WAAW,CAACC,IAAI,CAACL,MAAM,CAACrG,YAAY,CAAC;IACzC,CAAC,MAAM,IAAI6F,IAAI,IAAI,EAAE,EAAE;MACnBY,WAAW,CAACC,IAAI,CAACL,MAAM,CAACpG,WAAW,CAAC;IACxC,CAAC,MAAM;MACHwG,WAAW,CAACC,IAAI,CAACL,MAAM,CAACnG,YAAY,CAAC;IACzC;EACJ;EACA,IAAI6F,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,UAAU,EAAE;IAC9CU,WAAW,CAACC,IAAI,CAACL,MAAM,CAAClG,gBAAgB,CAAC;IACzC,IAAI6F,gBAAgB,KAAK,MAAM,IAAIA,gBAAgB,KAAK,aAAa,EAAE;MACnES,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC5F,IAAI,EAAE+F,eAAe,CAACP,KAAK,CAAC,CAAC;MACrD,IAAIL,KAAK,CAACzH,KAAK,EAAE;QACbsI,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC1F,eAAe,CAAC;MAC5C;MACA,IAAIkF,IAAI,IAAI,EAAE,EAAE;QACZY,WAAW,CAACC,IAAI,CAACL,MAAM,CAACxF,SAAS,CAAC;MACtC,CAAC,MAAM,IAAIgF,IAAI,IAAI,EAAE,EAAE;QACnBY,WAAW,CAACC,IAAI,CAACL,MAAM,CAACtF,WAAW,CAAC;MACxC,CAAC,MAAM;QACH0F,WAAW,CAACC,IAAI,CAACL,MAAM,CAACrF,YAAY,CAAC;MACzC;IACJ;IACA,IAAIgF,gBAAgB,KAAK,QAAQ,IAAIA,gBAAgB,KAAK,aAAa,EAAE;MACrES,WAAW,CAACC,IAAI,CAACL,MAAM,CAACpF,MAAM,CAAC;MAC/B,IAAI4E,IAAI,IAAI,EAAE,EAAE;QACZY,WAAW,CAACC,IAAI,CAACL,MAAM,CAAClF,OAAO,CAAC;MACpC,CAAC,MAAM,IAAI0E,IAAI,IAAI,EAAE,EAAE;QACnBY,WAAW,CAACC,IAAI,CAACL,MAAM,CAAChF,OAAO,CAAC;MACpC,CAAC,MAAM,IAAIwE,IAAI,IAAI,EAAE,EAAE;QACnBY,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC/E,QAAQ,CAAC;MACrC,CAAC,MAAM;QACHmF,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC9E,QAAQ,CAAC;MACrC;IACJ;IACA;IACA,IAAIwE,MAAM,KAAK,UAAU,EAAE;MACvBU,WAAW,CAACC,IAAI,CAACL,MAAM,CAAC7E,QAAQ,CAAC;IACrC;EACJ;EACAoE,KAAK,CAAC7H,IAAI,CAAC4I,SAAS,GAAG9I,YAAY,CAACC,gBAAgB,CAACC,IAAI,EAAE,GAAG0I,WAAW,EAAEb,KAAK,CAAC7H,IAAI,CAAC4I,SAAS,CAAC;EAChG,IAAIf,KAAK,CAACzH,KAAK,EAAE;IACbyH,KAAK,CAACzH,KAAK,CAACwI,SAAS,GAAG9I,YAAY,CAACC,gBAAgB,CAACK,KAAK,EAAEkI,MAAM,CAAClI,KAAK,EAAEyH,KAAK,CAACzH,KAAK,CAACwI,SAAS,CAAC;EACrG;EACA,IAAIf,KAAK,CAAC5H,KAAK,EAAE;IACb4H,KAAK,CAAC5H,KAAK,CAAC2I,SAAS,GAAG9I,YAAY,CAACC,gBAAgB,CAACE,KAAK,EAAEmI,cAAc,EAAEI,WAAW,CAACN,KAAK,CAAC,EAAEL,KAAK,CAACzH,KAAK,IAAIkI,MAAM,CAACjE,WAAW,EAAEwD,KAAK,CAAC5H,KAAK,CAAC2I,SAAS,CAAC;EAC9J;EACA,IAAIf,KAAK,CAAC3H,QAAQ,EAAE;IAChB2H,KAAK,CAAC3H,QAAQ,CAAC0I,SAAS,GAAG9I,YAAY,CAACC,gBAAgB,CAACG,QAAQ,EAAEmI,qBAAqB,EAAEG,WAAW,CAACN,KAAK,CAAC,EAAEL,KAAK,CAACzH,KAAK,IAAIkI,MAAM,CAACjE,WAAW,EAAEwD,KAAK,CAAC3H,QAAQ,CAAC0I,SAAS,CAAC;EAC9K;EACA,IAAIf,KAAK,CAAC1H,IAAI,EAAE;IACZ,IAAI0I,aAAa;IACjB,IAAIf,IAAI,IAAI,EAAE,EAAE;MACZe,aAAa,GAAGP,MAAM,CAACxD,MAAM;IACjC,CAAC,MAAM,IAAIgD,IAAI,IAAI,EAAE,EAAE;MACnBe,aAAa,GAAGP,MAAM,CAACvD,MAAM;IACjC,CAAC,MAAM,IAAI+C,IAAI,IAAI,EAAE,EAAE;MACnBe,aAAa,GAAGP,MAAM,CAACtD,MAAM;IACjC,CAAC,MAAM,IAAI8C,IAAI,IAAI,EAAE,EAAE;MACnBe,aAAa,GAAGP,MAAM,CAACrD,MAAM;IACjC,CAAC,MAAM,IAAI6C,IAAI,IAAI,EAAE,EAAE;MACnBe,aAAa,GAAGP,MAAM,CAACpD,MAAM;IACjC,CAAC,MAAM,IAAI4C,IAAI,IAAI,EAAE,EAAE;MACnBe,aAAa,GAAGP,MAAM,CAACnD,MAAM;IACjC,CAAC,MAAM;MACH0D,aAAa,GAAGP,MAAM,CAAClD,MAAM;IACjC;IACAyC,KAAK,CAAC1H,IAAI,CAACyI,SAAS,GAAG9I,YAAY,CAACC,gBAAgB,CAACI,IAAI,EAAEkI,qBAAqB,EAAEQ,aAAa,EAAEL,WAAW,CAACN,KAAK,CAAC,EAAEL,KAAK,CAACzH,KAAK,IAAIkI,MAAM,CAACjE,WAAW,EAAEwD,KAAK,CAAC1H,IAAI,CAACyI,SAAS,CAAC;EACjL;EACA,OAAOf,KAAK;AAChB,CAAC", "ignoreList": []}