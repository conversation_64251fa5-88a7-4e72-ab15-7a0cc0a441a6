{"version": 3, "sources": ["../src/components/AvatarGroupItem/AvatarGroupItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAvatarGroupItem_unstable } from './renderAvatarGroupItem';\nimport { useAvatarGroupItem_unstable } from './useAvatarGroupItem';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAvatarGroupItemStyles_unstable } from './useAvatarGroupItemStyles.styles';\nimport type { AvatarGroupItemProps } from './AvatarGroupItem.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\n/**\n * The AvatarGroupItem component represents a single person or entity.\n * AvatarGroupItem should only be used in an AvatarGroup component.\n */\nexport const AvatarGroupItem: ForwardRefComponent<AvatarGroupItemProps> = React.forwardRef((props, ref) => {\n  const state = useAvatarGroupItem_unstable(props, ref);\n\n  useAvatarGroupItemStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAvatarGroupItemStyles_unstable')(state);\n\n  return renderAvatarGroupItem_unstable(state);\n});\n\nAvatarGroupItem.displayName = 'AvatarGroupItem';\n"], "names": ["React", "renderAvatarGroupItem_unstable", "useAvatarGroupItem_unstable", "useCustomStyleHook_unstable", "useAvatarGroupItemStyles_unstable", "AvatarGroupItem", "forwardRef", "props", "ref", "state", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,8BAA8B,QAAQ,0BAA0B;AACzE,SAASC,2BAA2B,QAAQ,uBAAuB;AACnE,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,iCAAiC,QAAQ,oCAAoC;AAItF;;;CAGC,GACD,OAAO,MAAMC,gCAA6DL,MAAMM,UAAU,CAAC,CAACC,OAAOC;IACjG,MAAMC,QAAQP,4BAA4BK,OAAOC;IAEjDJ,kCAAkCK;IAElCN,4BAA4B,qCAAqCM;IAEjE,OAAOR,+BAA+BQ;AACxC,GAAG;AAEHJ,gBAAgBK,WAAW,GAAG"}