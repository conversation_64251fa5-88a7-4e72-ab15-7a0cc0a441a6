/// <reference types="react" />

import type { ComponentProps } from '@fluentui/react-utilities';
import type { ComponentState } from '@fluentui/react-utilities';
import type { ForwardRefComponent } from '@fluentui/react-utilities';
import * as React_2 from 'react';
import type { Slot } from '@fluentui/react-utilities';
import type { SlotClassNames } from '@fluentui/react-utilities';

/**
 * Define a styled Badge, using the `useBadge_unstable` hook.
 */
export declare const Badge: ForwardRefComponent<BadgeProps>;

export declare const badgeClassNames: SlotClassNames<BadgeSlots>;

export declare type BadgeProps = Omit<ComponentProps<BadgeSlots>, 'color'> & {
    /**
     * A Badge can be filled, outline, ghost, inverted
     * @defaultvalue filled
     */
    appearance?: 'filled' | 'ghost' | 'outline' | 'tint';
    /**
     * A Badge can be one of preset colors
     * @defaultvalue brand
     */
    color?: 'brand' | 'danger' | 'important' | 'informative' | 'severe' | 'subtle' | 'success' | 'warning';
    /**
     * A Badge can position the icon before or after the content.
     * @defaultvalue before
     */
    iconPosition?: 'before' | 'after';
    /**
     * A Badge can be square, circular or rounded.
     * @defaultvalue circular
     */
    shape?: 'circular' | 'rounded' | 'square';
    /**
     * A Badge can be on of several preset sizes.
     * @defaultvalue medium
     */
    size?: 'tiny' | 'extra-small' | 'small' | 'medium' | 'large' | 'extra-large';
};

export declare type BadgeSlots = {
    root: Slot<'div'>;
    icon?: Slot<'span'>;
};

export declare type BadgeState = ComponentState<BadgeSlots> & Required<Pick<BadgeProps, 'appearance' | 'color' | 'iconPosition' | 'shape' | 'size'>>;

/**
 * Define a styled CounterBadge, using the `useCounterBadge_unstable` hook.
 */
export declare const CounterBadge: ForwardRefComponent<CounterBadgeProps>;

export declare const counterBadgeClassNames: SlotClassNames<BadgeSlots>;

export declare type CounterBadgeProps = Omit<BadgeProps, 'appearance' | 'color' | 'shape'> & {
    /**
     * A Badge can have different appearances that emphasize certain parts of it:
     *  - filled: The default appearance if one is not specified.
     *    The badge background is filled with color with a contrasting foreground text to match.
     *  - ghost: The badge background is transparent, with the foreground text taking color to emphasize it.
     * @default filled
     */
    appearance?: 'filled' | 'ghost';
    /**
     * Semantic colors for a counter badge
     * @default brand
     */
    color?: Extract<BadgeProps['color'], 'brand' | 'danger' | 'important' | 'informative'>;
    /**
     * Value displayed by the Badge
     * @default 0
     */
    count?: number;
    /**
     * If a dot should be displayed without the count
     * @default false
     */
    dot?: boolean;
    /**
     * Max number to be displayed
     * @default 99
     */
    overflowCount?: number;
    /**
     * A Badge can be circular or rounded
     * @default circular
     */
    shape?: 'circular' | 'rounded';
    /**
     * If the badge should be shown when count is 0
     * @default false
     */
    showZero?: boolean;
};

export declare type CounterBadgeState = Omit<BadgeState, 'appearance' | 'color' | 'shape'> & Required<Pick<CounterBadgeProps, 'appearance' | 'color' | 'count' | 'dot' | 'shape' | 'showZero'>>;

export declare const presenceAvailableFilled: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const presenceAvailableRegular: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const presenceAwayFilled: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const presenceAwayRegular: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

/**
 * Define a styled Badge, using the `useBadge_unstable` hook.
 */
export declare const PresenceBadge: ForwardRefComponent<PresenceBadgeProps>;

export declare const presenceBadgeClassNames: SlotClassNames<BadgeSlots>;

export declare type PresenceBadgeProps = Omit<ComponentProps<Pick<BadgeSlots, 'root' | 'icon'>>, 'color'> & Pick<BadgeProps, 'size'> & {
    /**
     * Represents several status
     * @default available
     */
    status?: PresenceBadgeStatus;
    /**
     * Modifies the display to indicate that the user is out of office.
     * This can be combined with any status to display an out-of-office version of that status
     * @default false
     */
    outOfOffice?: boolean;
};

export declare type PresenceBadgeState = ComponentState<BadgeSlots> & BadgeState & Required<Pick<PresenceBadgeProps, 'status' | 'outOfOffice'>>;

export declare type PresenceBadgeStatus = 'busy' | 'out-of-office' | 'away' | 'available' | 'offline' | 'do-not-disturb' | 'unknown' | 'blocked';

export declare const presenceBlockedRegular: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const presenceBusyFilled: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const presenceDndFilled: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const presenceDndRegular: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const presenceOfflineRegular: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const presenceOofRegular: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const presenceUnknownRegular: Record<PresenceBadgeState['size'], React_2.FunctionComponent>;

export declare const renderBadge_unstable: (state: BadgeState) => JSX.Element;

/**
 * Returns the props and state required to render the component
 */
export declare const useBadge_unstable: (props: BadgeProps, ref: React_2.Ref<HTMLElement>) => BadgeState;

/**
 * Applies style classnames to slots
 */
export declare const useBadgeStyles_unstable: (state: BadgeState) => BadgeState;

/**
 * Returns the props and state required to render the component
 */
export declare const useCounterBadge_unstable: (props: CounterBadgeProps, ref: React_2.Ref<HTMLElement>) => CounterBadgeState;

/**
 * Applies style classnames to slots
 */
export declare const useCounterBadgeStyles_unstable: (state: CounterBadgeState) => CounterBadgeState;

/**
 * Returns the props and state required to render the component
 */
export declare const usePresenceBadge_unstable: (props: PresenceBadgeProps, ref: React_2.Ref<HTMLElement>) => PresenceBadgeState;

/**
 * Applies style classnames to slots
 */
export declare const usePresenceBadgeStyles_unstable: (state: PresenceBadgeState) => PresenceBadgeState;

export { }
