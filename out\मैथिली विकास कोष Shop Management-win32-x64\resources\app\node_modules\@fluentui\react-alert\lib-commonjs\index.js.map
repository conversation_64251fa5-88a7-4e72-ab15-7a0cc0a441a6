{"version": 3, "sources": ["index.js"], "sourcesContent": ["// eslint-disable-next-line deprecation/deprecation\nexport { Alert, alertClassNames, renderAlert_unstable, useAlertStyles_unstable, useAlert_unstable } from './Alert';\n"], "names": ["<PERSON><PERSON>", "alertClassNames", "renderAlert_unstable", "useAlertStyles_unstable", "useAlert_unstable"], "mappings": "AAAA,mDAAmD;;;;;;;;;;;;IAC1CA,KAAK;eAALA,YAAK;;IAAEC,eAAe;eAAfA,sBAAe;;IAAEC,oBAAoB;eAApBA,2BAAoB;;IAAEC,uBAAuB;eAAvBA,8BAAuB;;IAAEC,iBAAiB;eAAjBA,wBAAiB;;;uBAAQ"}