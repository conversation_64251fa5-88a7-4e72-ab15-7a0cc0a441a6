{"version": 3, "sources": ["useAlert.js"], "sourcesContent": ["import * as React from 'react';\nimport { Avatar } from '@fluentui/react-avatar';\nimport { But<PERSON> } from '@fluentui/react-button';\nimport { CheckmarkCircleFilled, DismissCircleFilled, InfoFilled, WarningFilled } from '@fluentui/react-icons';\nimport { getIntrinsicElementProps, slot } from '@fluentui/react-utilities';\n/**\n * @deprecated please use the Toast or MessageBar component\n * Create the state required to render Alert.\n *\n * The returned state can be modified with hooks such as useAlertStyles_unstable,\n * before being passed to renderAlert_unstable.\n *\n * @param props - props from this instance of Alert\n * @param ref - reference to root HTMLElement of Alert\n */ // eslint-disable-next-line deprecation/deprecation\nexport const useAlert_unstable = (props, ref)=>{\n    const { appearance = 'primary', intent } = props;\n    /** Determine the role and icon to render based on the intent */ let defaultIcon;\n    let defaultRole = 'status';\n    switch(intent){\n        case 'success':\n            defaultIcon = /*#__PURE__*/ React.createElement(CheckmarkCircleFilled, null);\n            break;\n        case 'error':\n            defaultIcon = /*#__PURE__*/ React.createElement(DismissCircleFilled, null);\n            defaultRole = 'alert';\n            break;\n        case 'warning':\n            defaultIcon = /*#__PURE__*/ React.createElement(WarningFilled, null);\n            defaultRole = 'alert';\n            break;\n        case 'info':\n            defaultIcon = /*#__PURE__*/ React.createElement(InfoFilled, null);\n            break;\n    }\n    const action = slot.optional(props.action, {\n        defaultProps: {\n            appearance: 'transparent'\n        },\n        elementType: Button\n    });\n    const avatar = slot.optional(props.avatar, {\n        elementType: Avatar\n    });\n    let icon;\n    /** Avatar prop takes precedence over the icon or intent prop */ if (!avatar) {\n        icon = slot.optional(props.icon, {\n            defaultProps: {\n                children: defaultIcon\n            },\n            renderByDefault: !!props.intent,\n            elementType: 'span'\n        });\n    }\n    return {\n        action,\n        appearance,\n        avatar,\n        components: {\n            root: 'div',\n            icon: 'span',\n            action: Button,\n            avatar: Avatar\n        },\n        icon,\n        intent,\n        root: slot.always(getIntrinsicElementProps('div', {\n            // FIXME:\n            // `ref` is wrongly assigned to be `HTMLElement` instead of `HTMLDivElement`\n            // but since it would be a breaking change to fix it, we are casting ref to it's proper type\n            ref: ref,\n            role: defaultRole,\n            children: props.children,\n            ...props\n        }), {\n            elementType: 'div'\n        })\n    };\n};\n"], "names": ["useAlert_unstable", "props", "ref", "appearance", "intent", "defaultIcon", "defaultRole", "React", "createElement", "CheckmarkCircleFilled", "DismissCircleFilled", "WarningFilled", "InfoFilled", "action", "slot", "optional", "defaultProps", "elementType", "<PERSON><PERSON>", "avatar", "Avatar", "icon", "children", "renderByDefault", "components", "root", "always", "getIntrinsicElementProps", "role"], "mappings": ";;;;+BAeaA;;;eAAAA;;;;iEAfU;6BACA;6BACA;4BAC+D;gCACvC;AAWxC,MAAMA,oBAAoB,CAACC,OAAOC;IACrC,MAAM,EAAEC,aAAa,SAAS,EAAEC,MAAM,EAAE,GAAGH;IAC3C,8DAA8D,GAAG,IAAII;IACrE,IAAIC,cAAc;IAClB,OAAOF;QACH,KAAK;YACDC,cAAc,WAAW,GAAGE,OAAMC,aAAa,CAACC,iCAAqB,EAAE;YACvE;QACJ,KAAK;YACDJ,cAAc,WAAW,GAAGE,OAAMC,aAAa,CAACE,+BAAmB,EAAE;YACrEJ,cAAc;YACd;QACJ,KAAK;YACDD,cAAc,WAAW,GAAGE,OAAMC,aAAa,CAACG,yBAAa,EAAE;YAC/DL,cAAc;YACd;QACJ,KAAK;YACDD,cAAc,WAAW,GAAGE,OAAMC,aAAa,CAACI,sBAAU,EAAE;YAC5D;IACR;IACA,MAAMC,SAASC,oBAAI,CAACC,QAAQ,CAACd,MAAMY,MAAM,EAAE;QACvCG,cAAc;YACVb,YAAY;QAChB;QACAc,aAAaC,mBAAM;IACvB;IACA,MAAMC,SAASL,oBAAI,CAACC,QAAQ,CAACd,MAAMkB,MAAM,EAAE;QACvCF,aAAaG,mBAAM;IACvB;IACA,IAAIC;IACJ,8DAA8D,GAAG,IAAI,CAACF,QAAQ;QAC1EE,OAAOP,oBAAI,CAACC,QAAQ,CAACd,MAAMoB,IAAI,EAAE;YAC7BL,cAAc;gBACVM,UAAUjB;YACd;YACAkB,iBAAiB,CAAC,CAACtB,MAAMG,MAAM;YAC/Ba,aAAa;QACjB;IACJ;IACA,OAAO;QACHJ;QACAV;QACAgB;QACAK,YAAY;YACRC,MAAM;YACNJ,MAAM;YACNR,QAAQK,mBAAM;YACdC,QAAQC,mBAAM;QAClB;QACAC;QACAjB;QACAqB,MAAMX,oBAAI,CAACY,MAAM,CAACC,IAAAA,wCAAwB,EAAC,OAAO;YAC9C,SAAS;YACT,4EAA4E;YAC5E,4FAA4F;YAC5FzB,KAAKA;YACL0B,MAAMtB;YACNgB,UAAUrB,MAAMqB,QAAQ;YACxB,GAAGrB,KAAK;QACZ,IAAI;YACAgB,aAAa;QACjB;IACJ;AACJ"}