{"version": 3, "sources": ["../src/components/Avatar/Avatar.types.ts"], "sourcesContent": ["import { PresenceBadge } from '@fluentui/react-badge';\nimport type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\n\n/**\n * Sizes for the avatar\n * @deprecated use AvatarSize instead\n */\nexport type AvatarSizes = AvatarSize;\n/**\n * Sizes for the avatar\n */\nexport type AvatarSize = 16 | 20 | 24 | 28 | 32 | 36 | 40 | 48 | 56 | 64 | 72 | 96 | 120 | 128;\n\n/**\n * Shape of the avatar\n */\nexport type AvatarShape = 'circular' | 'square';\n\nexport type AvatarSlots = {\n  root: Slot<'span'>;\n\n  /**\n   * The Avatar's image.\n   *\n   * Usage e.g.: `image={{ src: '...' }}`\n   */\n  image?: Slot<'img'>;\n\n  /**\n   * (optional) Custom initials.\n   *\n   * It is usually not necessary to specify custom initials; by default they will be derived from the `name` prop,\n   * using the `getInitials` function.\n   *\n   * The initials are displayed when there is no image (including while the image is loading).\n   */\n  initials?: Slot<'span'>;\n\n  /**\n   * Icon to be displayed when the avatar doesn't have an image or initials.\n   *\n   * @default `PersonRegular` (the default icon's size depends on the Avatar's size)\n   */\n  icon?: Slot<'span'>;\n\n  /**\n   * Badge to show the avatar's presence status.\n   */\n  badge?: Slot<typeof PresenceBadge>;\n};\n\n/**\n * A specific named color for the Avatar\n */\nexport type AvatarNamedColor =\n  | 'dark-red'\n  | 'cranberry'\n  | 'red'\n  | 'pumpkin'\n  | 'peach'\n  | 'marigold'\n  | 'gold'\n  | 'brass'\n  | 'brown'\n  | 'forest'\n  | 'seafoam'\n  | 'dark-green'\n  | 'light-teal'\n  | 'teal'\n  | 'steel'\n  | 'blue'\n  | 'royal-blue'\n  | 'cornflower'\n  | 'navy'\n  | 'lavender'\n  | 'purple'\n  | 'grape'\n  | 'lilac'\n  | 'pink'\n  | 'magenta'\n  | 'plum'\n  | 'beige'\n  | 'mink'\n  | 'platinum'\n  | 'anchor';\n\n/**\n * Properties for Avatar\n */\nexport type AvatarProps = Omit<ComponentProps<AvatarSlots>, 'color'> & {\n  /**\n   * Optional activity indicator\n   * * active: the avatar will be decorated according to activeAppearance\n   * * inactive: the avatar will be reduced in size and partially transparent\n   * * unset: normal display\n   *\n   * @default unset\n   */\n  active?: 'active' | 'inactive' | 'unset';\n\n  /**\n   * The appearance when `active=\"active\"`\n   *\n   * @default ring\n   */\n  activeAppearance?: 'ring' | 'shadow' | 'ring-shadow';\n\n  /**\n   * The color when displaying either an icon or initials.\n   * * neutral (default): gray\n   * * brand: color from the brand palette\n   * * colorful: picks a color from a set of pre-defined colors, based on a hash of the name (or idForColor if provided)\n   * * [AvatarNamedColor]: a specific color from the theme\n   *\n   * @default neutral\n   */\n  color?: 'neutral' | 'brand' | 'colorful' | AvatarNamedColor;\n\n  /**\n   * Specify a string to be used instead of the name, to determine which color to use when color=\"colorful\".\n   * Use this when a name is not available, but there is another unique identifier that can be used instead.\n   */\n  idForColor?: string | undefined;\n\n  /**\n   * The name of the person or entity represented by this Avatar. This should always be provided if it is available.\n   *\n   * The name is used to determine the initials displayed when there is no image. It is also provided to\n   * accessibility tools.\n   */\n  name?: string;\n\n  /**\n   * The avatar can have a circular or square shape.\n   * @default circular\n   */\n  shape?: AvatarShape;\n\n  /**\n   * Size of the avatar in pixels.\n   *\n   * Size is restricted to a limited set of supported values recommended for most uses (see `AvatarSizeValue`) and\n   * based on design guidelines for the Avatar control.\n   *\n   * Note: At size 16, if initials are displayed, only the first initial will be rendered.\n   *\n   * If a non-supported size is needed, set `size` to the next-smaller supported size, and set `width` and `height`\n   * to override the rendered size.\n   *\n   * For example, to set the avatar to 45px in size:\n   * `<Avatar size={40} style={{ width: '45px', height: '45px' }} />`\n   *\n   * @default 32\n   */\n  size?: AvatarSize;\n};\n\n/**\n * State used in rendering Avatar\n */\nexport type AvatarState = ComponentState<AvatarSlots> &\n  Required<Pick<AvatarProps, 'active' | 'activeAppearance' | 'shape' | 'size'>> & {\n    /**\n     * The Avatar's color, it matches props.color but with `'colorful'` resolved to a named color\n     */\n    color: NonNullable<Exclude<AvatarProps['color'], 'colorful'>>;\n\n    /**\n     * Hidden span to render the active state label for the purposes of including in the aria-labelledby, if needed.\n     */\n    activeAriaLabelElement?: JSX.Element;\n  };\n"], "names": [], "rangeMappings": ";;", "mappings": "AA6JA;;CAEC"}