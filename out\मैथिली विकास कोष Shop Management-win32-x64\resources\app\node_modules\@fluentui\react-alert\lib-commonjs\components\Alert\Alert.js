"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "Alert", {
    enumerable: true,
    get: function() {
        return Alert;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _renderAlert = require("./renderAlert");
const _useAlert = require("./useAlert");
const _useAlertStylesstyles = require("./useAlertStyles.styles");
const Alert = /*#__PURE__*/ _react.forwardRef((props, ref)=>{
    // eslint-disable-next-line deprecation/deprecation
    const state = (0, _useAlert.useAlert_unstable)(props, ref);
    // eslint-disable-next-line deprecation/deprecation
    (0, _useAlertStylesstyles.useAlertStyles_unstable)(state);
    // eslint-disable-next-line deprecation/deprecation
    return (0, _renderAlert.renderAlert_unstable)(state);
// eslint-disable-next-line deprecation/deprecation
});
// eslint-disable-next-line deprecation/deprecation
Alert.displayName = 'Alert';
