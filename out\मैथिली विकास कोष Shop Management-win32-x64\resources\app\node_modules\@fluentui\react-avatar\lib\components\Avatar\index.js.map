{"version": 3, "sources": ["../src/components/Avatar/index.ts"], "sourcesContent": ["export type {\n  AvatarNamedColor,\n  AvatarProps,\n  AvatarShape,\n  AvatarSize,\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  AvatarSizes,\n  AvatarSlots,\n  AvatarState,\n} from './Avatar.types';\nexport { Avatar } from './Avatar';\nexport { renderAvatar_unstable } from './renderAvatar';\nexport { DEFAULT_STRINGS, useAvatar_unstable } from './useAvatar';\nexport { avatarClassNames, useAvatarStyles_unstable, useSizeStyles } from './useAvatarStyles.styles';\n"], "names": ["Avatar", "renderAvatar_unstable", "DEFAULT_STRINGS", "useAvatar_unstable", "avatarClassNames", "useAvatarStyles_unstable", "useSizeStyles"], "rangeMappings": ";;;", "mappings": "AAUA,SAASA,MAAM,QAAQ,WAAW;AAClC,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,eAAe,EAAEC,kBAAkB,QAAQ,cAAc;AAClE,SAASC,gBAAgB,EAAEC,wBAAwB,EAAEC,aAAa,QAAQ,2BAA2B"}