{"version": 3, "sources": ["../src/button/useARIAButtonProps.ts"], "sourcesContent": ["import { Enter, Space } from '@fluentui/keyboard-keys';\nimport { useEventCallback } from '@fluentui/react-utilities';\nimport * as React from 'react';\nimport type { ARIAButtonElementIntersection, ARIAButtonProps, ARIAButtonResultProps, ARIAButtonType } from './types';\n\n/**\n * @internal\n *\n * Button keyboard handling, role, disabled and tabIndex implementation that ensures ARIA spec\n * for multiple scenarios of non native button elements. Ensuring 1st rule of ARIA for cases\n * where no attribute addition is required.\n *\n * @param type - the proper scenario to be interpreted by the hook.\n *  1. `button` - Minimal interference from the hook, as semantic button already supports most of the states\n *  2. `a` or `div` - Proper keyboard/mouse handling plus other support to ensure ARIA behavior\n * @param props - the props to be passed down the line to the desired element.\n * This hook will encapsulate proper properties, such as `onClick`, `onKeyDown`, `onKeyUp`, etc,.\n *\n * @example\n * ```tsx\n * const buttonProps = useARIAButtonProps('a', {\n *   href: './some-route'\n *   onClick: () => console.log('this should run both on click and Space and Enter')\n * })\n *\n * // ...\n *\n * return (\n *  <a {...buttonProps}>This anchor will behave as a proper button</a>\n * )\n * ```\n */\nexport function useARIAButtonProps<Type extends ARIAButtonType, Props extends ARIAButtonProps<Type>>(\n  type?: Type,\n  props?: Props,\n): ARIAButtonResultProps<Type, Props> {\n  const {\n    disabled,\n    disabledFocusable = false,\n    ['aria-disabled']: ariaDisabled,\n    onClick,\n    onKeyDown,\n    onKeyUp,\n    ...rest\n  } = props ?? {};\n\n  const normalizedARIADisabled = typeof ariaDisabled === 'string' ? ariaDisabled === 'true' : ariaDisabled;\n\n  const isDisabled = disabled || disabledFocusable || normalizedARIADisabled;\n\n  const handleClick = useEventCallback((ev: React.MouseEvent<ARIAButtonElementIntersection>) => {\n    if (isDisabled) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    } else {\n      onClick?.(ev);\n    }\n  });\n\n  const handleKeyDown = useEventCallback((ev: React.KeyboardEvent<ARIAButtonElementIntersection>) => {\n    onKeyDown?.(ev);\n\n    if (ev.isDefaultPrevented()) {\n      return;\n    }\n\n    const key = ev.key;\n\n    if (isDisabled && (key === Enter || key === Space)) {\n      ev.preventDefault();\n      ev.stopPropagation();\n      return;\n    }\n\n    if (key === Space) {\n      ev.preventDefault();\n      return;\n    }\n\n    // If enter is pressed, activate the button\n    else if (key === Enter) {\n      ev.preventDefault();\n      ev.currentTarget.click();\n    }\n  });\n\n  const handleKeyUp = useEventCallback((ev: React.KeyboardEvent<ARIAButtonElementIntersection>) => {\n    onKeyUp?.(ev);\n\n    if (ev.isDefaultPrevented()) {\n      return;\n    }\n\n    const key = ev.key;\n\n    if (isDisabled && (key === Enter || key === Space)) {\n      ev.preventDefault();\n      ev.stopPropagation();\n      return;\n    }\n\n    if (key === Space) {\n      ev.preventDefault();\n      ev.currentTarget.click();\n    }\n  });\n\n  // If a <button> tag is to be rendered we just need to set disabled and aria-disabled correctly\n  if (type === 'button' || type === undefined) {\n    return {\n      ...rest,\n      disabled: disabled && !disabledFocusable,\n      'aria-disabled': disabledFocusable ? true : normalizedARIADisabled,\n      // onclick should still use internal handler to ensure prevention if disabled\n      // if disabledFocusable then there's no requirement for handlers as those events should not be propagated\n      onClick: disabledFocusable ? undefined : handleClick,\n      onKeyUp: disabledFocusable ? undefined : onKeyUp,\n      onKeyDown: disabledFocusable ? undefined : onKeyDown,\n    } as ARIAButtonResultProps<Type, Props>;\n  }\n\n  // If an <a> or <div> tag is to be rendered we have to remove disabled and type,\n  // and set aria-disabled, role and tabIndex.\n  else {\n    // the role needs to be explicitly set if the href is undefined\n    const isLink = !!(rest as ARIAButtonResultProps<'a', Props>).href;\n    let roleOverride = isLink ? undefined : 'button';\n    if (!roleOverride && isDisabled) {\n      // need to set role=link explicitly for disabled links\n      roleOverride = 'link';\n    }\n\n    const resultProps = {\n      role: roleOverride,\n      tabIndex: disabledFocusable || (!isLink && !disabled) ? 0 : undefined,\n      ...rest,\n      // If it's not a <button> than listeners are required even with disabledFocusable\n      // Since you cannot assure the default behavior of the element\n      // E.g: <a> will redirect on click\n      onClick: handleClick,\n      onKeyUp: handleKeyUp,\n      onKeyDown: handleKeyDown,\n      'aria-disabled': isDisabled,\n    } as ARIAButtonResultProps<Type, Props>;\n\n    if (type === 'a' && isDisabled) {\n      (resultProps as ARIAButtonResultProps<'a', Props>).href = undefined;\n    }\n\n    return resultProps;\n  }\n}\n"], "names": ["Enter", "Space", "useEventCallback", "React", "useARIAButtonProps", "type", "props", "disabled", "disabledFocusable", "ariaDisabled", "onClick", "onKeyDown", "onKeyUp", "rest", "normalizedARIADisabled", "isDisabled", "handleClick", "ev", "preventDefault", "stopPropagation", "handleKeyDown", "isDefaultPrevented", "key", "currentTarget", "click", "handleKeyUp", "undefined", "isLink", "href", "roleOverride", "resultProps", "role", "tabIndex"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,SAASA,KAAK,EAAEC,KAAK,QAAQ,0BAA0B;AACvD,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,YAAYC,WAAW,QAAQ;AAG/B;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,OAAO,SAASC,mBACdC,IAAW,EACXC,KAAa;IAEb,MAAM,EACJC,QAAQ,EACRC,oBAAoB,KAAK,EACzB,CAAC,gBAAgB,EAAEC,YAAY,EAC/BC,OAAO,EACPC,SAAS,EACTC,OAAO,EACP,GAAGC,MACJ,GAAGP,kBAAAA,mBAAAA,QAAS,CAAC;IAEd,MAAMQ,yBAAyB,OAAOL,iBAAiB,WAAWA,iBAAiB,SAASA;IAE5F,MAAMM,aAAaR,YAAYC,qBAAqBM;IAEpD,MAAME,cAAcd,iBAAiB,CAACe;QACpC,IAAIF,YAAY;YACdE,GAAGC,cAAc;YACjBD,GAAGE,eAAe;QACpB,OAAO;YACLT,oBAAAA,8BAAAA,QAAUO;QACZ;IACF;IAEA,MAAMG,gBAAgBlB,iBAAiB,CAACe;QACtCN,sBAAAA,gCAAAA,UAAYM;QAEZ,IAAIA,GAAGI,kBAAkB,IAAI;YAC3B;QACF;QAEA,MAAMC,MAAML,GAAGK,GAAG;QAElB,IAAIP,cAAeO,CAAAA,QAAQtB,SAASsB,QAAQrB,KAAI,GAAI;YAClDgB,GAAGC,cAAc;YACjBD,GAAGE,eAAe;YAClB;QACF;QAEA,IAAIG,QAAQrB,OAAO;YACjBgB,GAAGC,cAAc;YACjB;QACF,OAGK,IAAII,QAAQtB,OAAO;YACtBiB,GAAGC,cAAc;YACjBD,GAAGM,aAAa,CAACC,KAAK;QACxB;IACF;IAEA,MAAMC,cAAcvB,iBAAiB,CAACe;QACpCL,oBAAAA,8BAAAA,QAAUK;QAEV,IAAIA,GAAGI,kBAAkB,IAAI;YAC3B;QACF;QAEA,MAAMC,MAAML,GAAGK,GAAG;QAElB,IAAIP,cAAeO,CAAAA,QAAQtB,SAASsB,QAAQrB,KAAI,GAAI;YAClDgB,GAAGC,cAAc;YACjBD,GAAGE,eAAe;YAClB;QACF;QAEA,IAAIG,QAAQrB,OAAO;YACjBgB,GAAGC,cAAc;YACjBD,GAAGM,aAAa,CAACC,KAAK;QACxB;IACF;IAEA,+FAA+F;IAC/F,IAAInB,SAAS,YAAYA,SAASqB,WAAW;QAC3C,OAAO;YACL,GAAGb,IAAI;YACPN,UAAUA,YAAY,CAACC;YACvB,iBAAiBA,oBAAoB,OAAOM;YAC5C,6EAA6E;YAC7E,yGAAyG;YACzGJ,SAASF,oBAAoBkB,YAAYV;YACzCJ,SAASJ,oBAAoBkB,YAAYd;YACzCD,WAAWH,oBAAoBkB,YAAYf;QAC7C;IACF,OAIK;QACH,+DAA+D;QAC/D,MAAMgB,SAAS,CAAC,CAAC,AAACd,KAA2Ce,IAAI;QACjE,IAAIC,eAAeF,SAASD,YAAY;QACxC,IAAI,CAACG,gBAAgBd,YAAY;YAC/B,sDAAsD;YACtDc,eAAe;QACjB;QAEA,MAAMC,cAAc;YAClBC,MAAMF;YACNG,UAAUxB,qBAAsB,CAACmB,UAAU,CAACpB,WAAY,IAAImB;YAC5D,GAAGb,IAAI;YACP,iFAAiF;YACjF,8DAA8D;YAC9D,kCAAkC;YAClCH,SAASM;YACTJ,SAASa;YACTd,WAAWS;YACX,iBAAiBL;QACnB;QAEA,IAAIV,SAAS,OAAOU,YAAY;YAC7Be,YAAkDF,IAAI,GAAGF;QAC5D;QAEA,OAAOI;IACT;AACF"}