{"version": 3, "sources": ["../src/components/AvatarGroupPopover/useAvatarGroupPopoverContextValues.ts"], "sourcesContent": ["import { AvatarGroupContextValue, AvatarGroupContextValues } from '../AvatarGroup/AvatarGroup.types';\nimport { AvatarGroupPopoverState } from './AvatarGroupPopover.types';\n\nexport const useAvatarGroupPopoverContextValues_unstable = (\n  state: AvatarGroupPopoverState,\n): AvatarGroupContextValues => {\n  const avatarGroup: AvatarGroupContextValue = {\n    isOverflow: true,\n    size: 24,\n  };\n\n  return { avatarGroup };\n};\n"], "names": ["useAvatarGroupPopoverContextValues_unstable", "state", "avatarGroup", "isOverflow", "size"], "rangeMappings": ";;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAGaA;;;eAAAA;;;AAAN,MAAMA,8CAA8C,CACzDC;IAEA,MAAMC,cAAuC;QAC3CC,YAAY;QACZC,MAAM;IACR;IAEA,OAAO;QAAEF;IAAY;AACvB"}