import type { AnnounceContextValue } from '@fluentui/react-shared-contexts';
import type { DistributiveOmit } from '@fluentui/react-utilities';
import type { ExtractSlotProps } from '@fluentui/react-utilities';
import * as React_2 from 'react';
import type { ResolveShorthandFunction } from '@fluentui/react-utilities';
import type { Slot } from '@fluentui/react-utilities';
import type { UnionToIntersection } from '@fluentui/react-utilities';

/**
 * Applied to the active descendant when the user is navigating with keyboard
 */
export declare const ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE = "data-activedescendant-focusvisible";

export declare type ActiveDescendantChangeEvent = CustomEvent<ActiveDescendantChangeEventDetail>;

declare interface ActiveDescendantChangeEventDetail {
    id: string;
    previousId: string | null;
}

export declare const ActiveDescendantContextProvider: React_2.Provider<ActiveDescendantContextValue | undefined>;

export declare type ActiveDescendantContextValue = {
    controller: ActiveDescendantImperativeRef;
};

export declare interface ActiveDescendantImperativeRef {
    first: (options?: IteratorOptions) => string | undefined;
    last: (options?: IteratorOptions) => string | undefined;
    next: (options?: IteratorOptions) => string | undefined;
    prev: (options?: IteratorOptions) => string | undefined;
    find: (predicate: (id: string) => boolean, options?: IteratorOptions & FindOptions) => string | undefined;
    blur: () => void;
    active: () => string | undefined;
    focus: (id: string) => void;
    /**
     * @deprecated This function is not used internally anymore and will be removed in the future
     */
    focusLastActive: () => void;
    /**
     * Scrolls the active option into view, if it still exists
     */
    scrollActiveIntoView: () => void;
    hideAttributes: () => void;
    showAttributes: () => void;
    hideFocusVisibleAttributes: () => void;
    showFocusVisibleAttributes: () => void;
}

export declare interface ActiveDescendantOptions {
    /**
     * @param el - HTML element to test
     * @returns whether the element can be an active descendant
     */
    matchOption: (el: HTMLElement) => boolean;
    /**
     * Forward imperative refs when exposing functionality from a React component
     */
    imperativeRef?: React_2.RefObject<ActiveDescendantImperativeRef>;
}

/**
 * Props that will be modified internally by `useARIAButtonProps` by each case.
 * This typing is to ensure a well specified return value for `useARIAbButtonProps`
 */
export declare type ARIAButtonAlteredProps<Type extends ARIAButtonType> = (Type extends 'button' ? Pick<JSX.IntrinsicElements['button'], 'onClick' | 'onKeyDown' | 'onKeyUp' | 'disabled' | 'aria-disabled' | 'tabIndex'> : never) | (Type extends 'a' ? Pick<JSX.IntrinsicElements['a'], 'onClick' | 'onKeyDown' | 'onKeyUp' | 'aria-disabled' | 'tabIndex' | 'role' | 'href'> : never) | (Type extends 'div' ? Pick<JSX.IntrinsicElements['div'], 'onClick' | 'onKeyDown' | 'onKeyUp' | 'aria-disabled' | 'tabIndex' | 'role'> : never);

export declare type ARIAButtonElement<AlternateAs extends 'a' | 'div' = 'a' | 'div'> = HTMLButtonElement | (AlternateAs extends 'a' ? HTMLAnchorElement : never) | (AlternateAs extends 'div' ? HTMLDivElement : never);

/**
 * @internal
 */
export declare type ARIAButtonElementIntersection<AlternateAs extends 'a' | 'div' = 'a' | 'div'> = UnionToIntersection<ARIAButtonElement<AlternateAs>>;

/**
 * Props expected by `useARIAButtonProps` hooks
 */
export declare type ARIAButtonProps<Type extends ARIAButtonType = ARIAButtonType> = DistributiveOmit<React_2.PropsWithRef<JSX.IntrinsicElements[Type]>, 'children'> & {
    disabled?: boolean;
    /**
     * When set, allows the button to be focusable even when it has been disabled.
     * This is used in scenarios where it is important to keep a consistent tab order
     * for screen reader and keyboard users. The primary example of this
     * pattern is when the disabled button is in a menu or a commandbar and is seldom used for standalone buttons.
     *
     * @default false
     */
    disabledFocusable?: boolean;
};

/**
 * Merge of props provided by the user and props provided internally.
 */
export declare type ARIAButtonResultProps<Type extends ARIAButtonType, Props> = Props & UnionToIntersection<ARIAButtonAlteredProps<Type>>;

export declare type ARIAButtonSlotProps<AlternateAs extends 'a' | 'div' = 'a' | 'div'> = ExtractSlotProps<Slot<'button', AlternateAs>> & Pick<ARIAButtonProps<ARIAButtonType>, 'disabled' | 'disabledFocusable'>;

export declare type ARIAButtonType = 'button' | 'a' | 'div';

declare type AriaLiveAnnounceFn = AnnounceContextValue['announce'];

/**
 * A sample implementation of a component that manages aria live announcements.
 */
export declare const AriaLiveAnnouncer: React_2.FC<AriaLiveAnnouncerProps>;

declare type AriaLiveAnnouncerContextValues = {
    announce: {
        announce: AriaLiveAnnounceFn;
    };
};

export declare type AriaLiveAnnouncerProps = {
    children?: React_2.ReactNode;
};

export declare type AriaLiveAnnouncerState = {
    announce: AriaLiveAnnounceFn;
    children?: React_2.ReactNode;
};

declare interface FindOptions {
    /**
     * Starts the search from a specific id
     */
    startFrom?: string;
}

declare interface IteratorOptions {
    /**
     * When passive, the active descendant is changed
     * @default false
     */
    passive?: boolean;
}

export declare const renderAriaLiveAnnouncer_unstable: (state: AriaLiveAnnouncerState, contextValues: AriaLiveAnnouncerContextValues) => JSX.Element;

export declare function useActiveDescendant<TActiveParentElement extends HTMLElement, TListboxElement extends HTMLElement>(options: ActiveDescendantOptions): UseActiveDescendantReturn<TActiveParentElement, TListboxElement>;

export declare const useActiveDescendantContext: () => ActiveDescendantContextValue;

declare interface UseActiveDescendantReturn<TActiveParentElement extends HTMLElement = HTMLElement, TListboxElement extends HTMLElement = HTMLElement> {
    /**
     * Attach this to the element that contains all active descendants
     */
    listboxRef: React_2.Ref<TListboxElement>;
    /**
     * Attach this to the element that has an active descendant
     */
    activeParentRef: React_2.Ref<TActiveParentElement>;
    /**
     * Imperative functions to manage active descendants within the listboxRef
     */
    controller: ActiveDescendantImperativeRef;
}

/**
 * @internal
 *
 * Button keyboard handling, role, disabled and tabIndex implementation that ensures ARIA spec
 * for multiple scenarios of non native button elements. Ensuring 1st rule of ARIA for cases
 * where no attribute addition is required.
 *
 * @param type - the proper scenario to be interpreted by the hook.
 *  1. `button` - Minimal interference from the hook, as semantic button already supports most of the states
 *  2. `a` or `div` - Proper keyboard/mouse handling plus other support to ensure ARIA behavior
 * @param props - the props to be passed down the line to the desired element.
 * This hook will encapsulate proper properties, such as `onClick`, `onKeyDown`, `onKeyUp`, etc,.
 *
 * @example
 * ```tsx
 * const buttonProps = useARIAButtonProps('a', {
 *   href: './some-route'
 *   onClick: () => console.log('this should run both on click and Space and Enter')
 * })
 *
 * // ...
 *
 * return (
 *  <a {...buttonProps}>This anchor will behave as a proper button</a>
 * )
 * ```
 */
export declare function useARIAButtonProps<Type extends ARIAButtonType, Props extends ARIAButtonProps<Type>>(type?: Type, props?: Props): ARIAButtonResultProps<Type, Props>;

/**
 * @internal
 *
 * @deprecated use useARIAButtonProps instead
 *
 * This function expects to receive a slot, if `as` property is not desired use `useARIAButtonProps` instead
 *
 * Button keyboard handling, role, disabled and tabIndex implementation that ensures ARIA spec
 * for multiple scenarios of shorthand properties. Ensuring 1st rule of ARIA for cases
 * where no attribute addition is required.
 */
export declare const useARIAButtonShorthand: ResolveShorthandFunction<ARIAButtonSlotProps>;

export declare const useAriaLiveAnnouncer_unstable: (props: AriaLiveAnnouncerProps) => AriaLiveAnnouncerState;

export declare function useAriaLiveAnnouncerContextValues_unstable(state: AriaLiveAnnouncerState): AriaLiveAnnouncerContextValues;

export declare const useHasParentActiveDescendantContext: () => boolean;

export { }
