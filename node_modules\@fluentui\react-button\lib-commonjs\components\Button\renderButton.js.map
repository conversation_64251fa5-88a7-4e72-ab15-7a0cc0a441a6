{"version": 3, "sources": ["../src/components/Button/renderButton.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { ButtonSlots, ButtonState } from './Button.types';\n\n/**\n * Renders a Button component by passing the state defined props to the appropriate slots.\n */\nexport const renderButton_unstable = (state: ButtonState) => {\n  assertSlots<ButtonSlots>(state);\n  const { iconOnly, iconPosition } = state;\n\n  return (\n    <state.root>\n      {iconPosition !== 'after' && state.icon && <state.icon />}\n      {!iconOnly && state.root.children}\n      {iconPosition === 'after' && state.icon && <state.icon />}\n    </state.root>\n  );\n};\n"], "names": ["renderButton_unstable", "state", "assertSlots", "iconOnly", "iconPosition", "_jsxs", "root", "icon", "_jsx", "children"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BASaA;;;eAAAA;;;4BARb;gCAE4B;AAMrB,MAAMA,wBAAwB,CAACC;IACpCC,IAAAA,2BAAAA,EAAyBD;IACzB,MAAM,EAAEE,QAAQ,EAAEC,YAAY,EAAE,GAAGH;IAEnC,OAAA,WAAA,GACEI,IAAAA,gBAAA,EAACJ,MAAMK,IAAI,EAAA;;YACRF,iBAAiB,WAAWH,MAAMM,IAAI,IAAA,WAAA,GAAIC,IAAAA,eAAA,EAACP,MAAMM,IAAI,EAAA,CAAA;YACrD,CAACJ,YAAYF,MAAMK,IAAI,CAACG,QAAQ;YAChCL,iBAAiB,WAAWH,MAAMM,IAAI,IAAA,WAAA,GAAIC,IAAAA,eAAA,EAACP,MAAMM,IAAI,EAAA,CAAA;;;AAG5D"}