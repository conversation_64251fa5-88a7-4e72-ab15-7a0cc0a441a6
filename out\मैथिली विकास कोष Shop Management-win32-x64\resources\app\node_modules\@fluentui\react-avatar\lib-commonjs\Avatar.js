"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    Avatar: function() {
        return _index.Avatar;
    },
    DEFAULT_STRINGS: function() {
        return _index.DEFAULT_STRINGS;
    },
    avatarClassNames: function() {
        return _index.avatarClassNames;
    },
    renderAvatar_unstable: function() {
        return _index.renderAvatar_unstable;
    },
    useAvatarStyles_unstable: function() {
        return _index.useAvatarStyles_unstable;
    },
    useAvatar_unstable: function() {
        return _index.useAvatar_unstable;
    },
    useSizeStyles: function() {
        return _index.useSizeStyles;
    }
});
const _index = require("./components/Avatar/index");
