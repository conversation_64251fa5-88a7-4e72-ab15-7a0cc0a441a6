{"version": 3, "sources": ["../src/components/CarouselSlider/renderCarouselSlider.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { CarouselSliderState, CarouselSliderSlots } from './CarouselSlider.types';\nimport { CarouselSliderContextProvider, CarouselSliderContextValues } from './CarouselSliderContext';\n\n/**\n * Render the final JSX of CarouselSlider\n */\nexport const renderCarouselSlider_unstable = (\n  state: CarouselSliderState,\n  contextValues: CarouselSliderContextValues,\n) => {\n  assertSlots<CarouselSliderSlots>(state);\n\n  return (\n    <CarouselSliderContextProvider value={contextValues.carouselSlider}>\n      <state.root />\n    </CarouselSliderContextProvider>\n  );\n};\n"], "names": ["renderCarouselSlider_unstable", "state", "contextValues", "assertSlots", "_jsx", "CarouselSliderContextProvider", "value", "carouselSlider", "root"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAUaA;;;eAAAA;;;4BATb;gCAE4B;uCAE+C;AAKpE,MAAMA,gCAAgC,CAC3CC,OACAC;IAEAC,IAAAA,2BAAAA,EAAiCF;IAEjC,OAAA,WAAA,GACEG,IAAAA,eAAA,EAACC,oDAAAA,EAAAA;QAA8BC,OAAOJ,cAAcK,cAAc;kBAChE,WAAA,GAAAH,IAAAA,eAAA,EAACH,MAAMO,IAAI,EAAA,CAAA;;AAGjB"}