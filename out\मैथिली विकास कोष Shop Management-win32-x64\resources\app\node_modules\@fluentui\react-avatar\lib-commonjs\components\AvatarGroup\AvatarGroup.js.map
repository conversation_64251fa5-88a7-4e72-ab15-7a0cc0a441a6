{"version": 3, "sources": ["../src/components/AvatarGroup/AvatarGroup.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAvatarGroup_unstable } from './renderAvatarGroup';\nimport { useAvatarGroup_unstable } from './useAvatarGroup';\nimport { useAvatarGroupContextValues } from './useAvatarGroupContextValues';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAvatarGroupStyles_unstable } from './useAvatarGroupStyles.styles';\nimport type { AvatarGroupProps } from './AvatarGroup.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\n/**\n * The AvatarGroup component represents a group of multiple people or entities by taking care of the arrangement\n * of individual Avatars in a spread, stack, or pie layout.\n */\nexport const AvatarGroup: ForwardRefComponent<AvatarGroupProps> = React.forwardRef((props, ref) => {\n  const state = useAvatarGroup_unstable(props, ref);\n  const contextValues = useAvatarGroupContextValues(state);\n\n  useAvatarGroupStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAvatarGroupStyles_unstable')(state);\n\n  return renderAvatarGroup_unstable(state, contextValues);\n});\n\nAvatarGroup.displayName = 'AvatarGroup';\n"], "names": ["AvatarGroup", "React", "forwardRef", "props", "ref", "state", "useAvatarGroup_unstable", "contextValues", "useAvatarGroupContextValues", "useAvatarGroupStyles_unstable", "useCustomStyleHook_unstable", "renderAvatarGroup_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAaaA;;;eAAAA;;;;iEAbU;mCACoB;gCACH;6CACI;qCACA;4CACE;AAQvC,MAAMA,cAAAA,WAAAA,GAAqDC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IACzF,MAAMC,QAAQC,IAAAA,uCAAAA,EAAwBH,OAAOC;IAC7C,MAAMG,gBAAgBC,IAAAA,wDAAAA,EAA4BH;IAElDI,IAAAA,yDAAAA,EAA8BJ;IAE9BK,IAAAA,gDAAAA,EAA4B,iCAAiCL;IAE7D,OAAOM,IAAAA,6CAAAA,EAA2BN,OAAOE;AAC3C;AAEAP,YAAYY,WAAW,GAAG"}