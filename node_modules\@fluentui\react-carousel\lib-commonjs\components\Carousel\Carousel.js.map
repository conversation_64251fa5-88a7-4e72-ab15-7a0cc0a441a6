{"version": 3, "sources": ["../src/components/Carousel/Carousel.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\nimport { useCarousel_unstable } from './useCarousel';\nimport { renderCarousel_unstable } from './renderCarousel';\nimport { useCarouselStyles_unstable } from './useCarouselStyles.styles';\nimport type { CarouselProps } from './Carousel.types';\nimport { useCarouselContextValues_unstable } from './useCarouselContextValues';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\n\n/**\n * Carousel is the context wrapper and container for all carousel content/controls,\n * it has no direct style or slot opinions.\n *\n * Carousel also provides API interfaces for callbacks that will occur on navigation events.\n */\nexport const Carousel: ForwardRefComponent<CarouselProps> = React.forwardRef((props, ref) => {\n  const state = useCarousel_unstable(props, ref);\n\n  useCarouselStyles_unstable(state);\n  useCustomStyleHook_unstable('useCarouselStyles_unstable')(state);\n\n  const contextValues = useCarouselContextValues_unstable(state);\n\n  return renderCarousel_unstable(state, contextValues);\n});\n\nCarousel.displayName = 'Carousel';\n"], "names": ["Carousel", "React", "forwardRef", "props", "ref", "state", "useCarousel_unstable", "useCarouselStyles_unstable", "useCustomStyleHook_unstable", "contextValues", "useCarouselContextValues_unstable", "renderCarousel_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAeaA;;;eAAAA;;;;iEAfU;6BAEc;gCACG;yCACG;0CAEO;qCACN;AAQrC,MAAMA,WAAAA,WAAAA,GAA+CC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IACnF,MAAMC,QAAQC,IAAAA,iCAAAA,EAAqBH,OAAOC;IAE1CG,IAAAA,mDAAAA,EAA2BF;IAC3BG,IAAAA,gDAAAA,EAA4B,8BAA8BH;IAE1D,MAAMI,gBAAgBC,IAAAA,2DAAAA,EAAkCL;IAExD,OAAOM,IAAAA,uCAAAA,EAAwBN,OAAOI;AACxC;AAEAT,SAASY,WAAW,GAAG"}