{"version": 3, "sources": ["../src/priorityQueue.ts"], "sourcesContent": ["export type PriorityQueueCompareFn<T> = (a: T, b: T) => number;\n\nexport interface PriorityQueue<T> {\n  all: () => T[];\n  clear: () => void;\n  contains: (item: T) => boolean;\n  dequeue: () => T;\n  enqueue: (item: T) => void;\n  peek: () => T | null;\n  remove: (item: T) => void;\n  size: () => number;\n}\n\n/**\n * @param compare - comparison function for items\n * @returns Priority queue implemented with a min heap\n */\nexport function createPriorityQueue<T>(compare: PriorityQueueCompareFn<T>): PriorityQueue<T> {\n  const arr: T[] = [];\n  let size = 0;\n\n  const left = (i: number) => {\n    return 2 * i + 1;\n  };\n\n  const right = (i: number) => {\n    return 2 * i + 2;\n  };\n\n  const parent = (i: number) => {\n    return Math.floor((i - 1) / 2);\n  };\n\n  const swap = (a: number, b: number) => {\n    const tmp = arr[a];\n    arr[a] = arr[b];\n    arr[b] = tmp;\n  };\n\n  const heapify = (i: number) => {\n    let smallest = i;\n    const l = left(i);\n    const r = right(i);\n\n    if (l < size && compare(arr[l], arr[smallest]) < 0) {\n      smallest = l;\n    }\n\n    if (r < size && compare(arr[r], arr[smallest]) < 0) {\n      smallest = r;\n    }\n\n    if (smallest !== i) {\n      swap(smallest, i);\n      heapify(smallest);\n    }\n  };\n\n  const dequeue = () => {\n    if (size === 0) {\n      throw new Error('Priority queue empty');\n    }\n\n    const res = arr[0];\n    arr[0] = arr[--size];\n    heapify(0);\n\n    return res;\n  };\n\n  const peek = () => {\n    if (size === 0) {\n      return null;\n    }\n\n    return arr[0];\n  };\n\n  const enqueue = (item: T) => {\n    arr[size++] = item;\n    let i = size - 1;\n    let p = parent(i);\n    while (i > 0 && compare(arr[p], arr[i]) > 0) {\n      swap(p, i);\n      i = p;\n      p = parent(i);\n    }\n  };\n\n  const contains = (item: T) => {\n    const index = arr.indexOf(item);\n    return index >= 0 && index < size;\n  };\n\n  const remove = (item: T) => {\n    const i = arr.indexOf(item);\n\n    if (i === -1 || i >= size) {\n      return;\n    }\n\n    arr[i] = arr[--size];\n    heapify(i);\n  };\n\n  const clear = () => {\n    size = 0;\n  };\n\n  const all = () => {\n    return arr.slice(0, size);\n  };\n\n  return {\n    all,\n    clear,\n    contains,\n    dequeue,\n    enqueue,\n    peek,\n    remove,\n    size: () => size,\n  };\n}\n"], "names": ["createPriorityQueue", "compare", "arr", "size", "left", "i", "right", "parent", "Math", "floor", "swap", "a", "b", "tmp", "heapify", "smallest", "l", "r", "dequeue", "Error", "res", "peek", "enqueue", "item", "p", "contains", "index", "indexOf", "remove", "clear", "all", "slice"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAaA;;;CAGC,GACD,OAAO,SAASA,oBAAuBC,OAAkC;IACvE,MAAMC,MAAW,EAAE;IACnB,IAAIC,OAAO;IAEX,MAAMC,OAAO,CAACC;QACZ,OAAO,IAAIA,IAAI;IACjB;IAEA,MAAMC,QAAQ,CAACD;QACb,OAAO,IAAIA,IAAI;IACjB;IAEA,MAAME,SAAS,CAACF;QACd,OAAOG,KAAKC,KAAK,CAAC,AAACJ,CAAAA,IAAI,CAAA,IAAK;IAC9B;IAEA,MAAMK,OAAO,CAACC,GAAWC;QACvB,MAAMC,MAAMX,GAAG,CAACS,EAAE;QAClBT,GAAG,CAACS,EAAE,GAAGT,GAAG,CAACU,EAAE;QACfV,GAAG,CAACU,EAAE,GAAGC;IACX;IAEA,MAAMC,UAAU,CAACT;QACf,IAAIU,WAAWV;QACf,MAAMW,IAAIZ,KAAKC;QACf,MAAMY,IAAIX,MAAMD;QAEhB,IAAIW,IAAIb,QAAQF,QAAQC,GAAG,CAACc,EAAE,EAAEd,GAAG,CAACa,SAAS,IAAI,GAAG;YAClDA,WAAWC;QACb;QAEA,IAAIC,IAAId,QAAQF,QAAQC,GAAG,CAACe,EAAE,EAAEf,GAAG,CAACa,SAAS,IAAI,GAAG;YAClDA,WAAWE;QACb;QAEA,IAAIF,aAAaV,GAAG;YAClBK,KAAKK,UAAUV;YACfS,QAAQC;QACV;IACF;IAEA,MAAMG,UAAU;QACd,IAAIf,SAAS,GAAG;YACd,MAAM,IAAIgB,MAAM;QAClB;QAEA,MAAMC,MAAMlB,GAAG,CAAC,EAAE;QAClBA,GAAG,CAAC,EAAE,GAAGA,GAAG,CAAC,EAAEC,KAAK;QACpBW,QAAQ;QAER,OAAOM;IACT;IAEA,MAAMC,OAAO;QACX,IAAIlB,SAAS,GAAG;YACd,OAAO;QACT;QAEA,OAAOD,GAAG,CAAC,EAAE;IACf;IAEA,MAAMoB,UAAU,CAACC;QACfrB,GAAG,CAACC,OAAO,GAAGoB;QACd,IAAIlB,IAAIF,OAAO;QACf,IAAIqB,IAAIjB,OAAOF;QACf,MAAOA,IAAI,KAAKJ,QAAQC,GAAG,CAACsB,EAAE,EAAEtB,GAAG,CAACG,EAAE,IAAI,EAAG;YAC3CK,KAAKc,GAAGnB;YACRA,IAAImB;YACJA,IAAIjB,OAAOF;QACb;IACF;IAEA,MAAMoB,WAAW,CAACF;QAChB,MAAMG,QAAQxB,IAAIyB,OAAO,CAACJ;QAC1B,OAAOG,SAAS,KAAKA,QAAQvB;IAC/B;IAEA,MAAMyB,SAAS,CAACL;QACd,MAAMlB,IAAIH,IAAIyB,OAAO,CAACJ;QAEtB,IAAIlB,MAAM,CAAC,KAAKA,KAAKF,MAAM;YACzB;QACF;QAEAD,GAAG,CAACG,EAAE,GAAGH,GAAG,CAAC,EAAEC,KAAK;QACpBW,QAAQT;IACV;IAEA,MAAMwB,QAAQ;QACZ1B,OAAO;IACT;IAEA,MAAM2B,MAAM;QACV,OAAO5B,IAAI6B,KAAK,CAAC,GAAG5B;IACtB;IAEA,OAAO;QACL2B;QACAD;QACAJ;QACAP;QACAI;QACAD;QACAO;QACAzB,MAAM,IAAMA;IACd;AACF"}