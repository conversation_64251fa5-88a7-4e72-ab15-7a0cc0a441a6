{"version": 3, "sources": ["../src/components/AvatarGroupPopover/useAvatarGroupPopover.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useAvatarGroupContext_unstable } from '../../contexts/AvatarGroupContext';\nimport { defaultAvatarGroupSize } from '../AvatarGroup/useAvatarGroup';\nimport { useControllableState, slot } from '@fluentui/react-utilities';\nimport { MoreHorizontalRegular } from '@fluentui/react-icons';\nimport { OnOpenChangeData, OpenPopoverEvents, Popover, PopoverSurface } from '@fluentui/react-popover';\nimport type { AvatarGroupPopoverProps, AvatarGroupPopoverState } from './AvatarGroupPopover.types';\nimport { Tooltip } from '@fluentui/react-tooltip';\n\n/**\n * Create the state required to render AvatarGroupPopover.\n *\n * The returned state can be modified with hooks such as useAvatarGroupPopoverStyles_unstable,\n * before being passed to renderAvatarGroupPopover_unstable.\n *\n * @param props - props from this instance of AvatarGroupPopover\n */\nexport const useAvatarGroupPopover_unstable = (props: AvatarGroupPopoverProps): AvatarGroupPopoverState => {\n  const size = useAvatarGroupContext_unstable(ctx => ctx.size) ?? defaultAvatarGroupSize;\n  const layout = useAvatarGroupContext_unstable(ctx => ctx.layout);\n  const {\n    indicator = size < 24 ? 'icon' : 'count',\n    count = React.Children.count(props.children),\n    children,\n    ...restOfProps\n  } = props;\n\n  const [popoverOpen, setPopoverOpen] = useControllableState({\n    state: props.open,\n    defaultState: props.defaultOpen,\n    initialState: false,\n  });\n\n  const handleOnPopoverChange = (e: OpenPopoverEvents, data: OnOpenChangeData) => {\n    restOfProps.onOpenChange?.(e, data);\n    setPopoverOpen(data.open);\n  };\n\n  let triggerButtonChildren;\n  if (layout === 'pie') {\n    triggerButtonChildren = null;\n  } else if (indicator === 'icon') {\n    triggerButtonChildren = <MoreHorizontalRegular />;\n  } else {\n    triggerButtonChildren = count > 99 ? '99+' : `+${count}`;\n  }\n\n  return {\n    count,\n    indicator,\n    layout,\n    popoverOpen,\n    size,\n\n    components: {\n      root: Popover,\n      triggerButton: 'button',\n      content: 'ul',\n      popoverSurface: PopoverSurface,\n      tooltip: Tooltip,\n    },\n    root: slot.always(\n      {\n        // Popover expects a child for its children. The children are added in the renderAvatarGroupPopover.\n        children: <></>,\n        size: 'small',\n        trapFocus: true,\n        ...restOfProps,\n        open: popoverOpen,\n        onOpenChange: handleOnPopoverChange,\n      },\n      { elementType: Popover },\n    ),\n    triggerButton: slot.always(props.triggerButton, {\n      defaultProps: {\n        children: triggerButtonChildren,\n        type: 'button',\n      },\n      elementType: 'button',\n    }),\n    content: slot.always(props.content, {\n      defaultProps: {\n        children,\n        role: 'list',\n      },\n      elementType: 'ul',\n    }),\n    popoverSurface: slot.always(props.popoverSurface, {\n      defaultProps: {\n        'aria-label': 'Overflow',\n        tabIndex: 0,\n      },\n      elementType: PopoverSurface,\n    }),\n    tooltip: slot.always(props.tooltip, {\n      defaultProps: {\n        content: 'View more people.',\n        relationship: 'label',\n      },\n      elementType: Tooltip,\n    }),\n  };\n};\n"], "names": ["useAvatarGroupPopover_unstable", "props", "useAvatarGroupContext_unstable", "size", "ctx", "defaultAvatarGroupSize", "layout", "indicator", "count", "React", "Children", "children", "restOfProps", "popoverOpen", "setPopoverOpen", "useControllableState", "state", "open", "defaultState", "defaultOpen", "initialState", "handleOnPopoverChange", "e", "data", "onOpenChange", "triggerButtonChildren", "createElement", "MoreHorizontalRegular", "components", "root", "Popover", "trigger<PERSON>utton", "content", "popoverSurface", "PopoverSurface", "tooltip", "<PERSON><PERSON><PERSON>", "slot", "always", "Fragment", "trapFocus", "elementType", "defaultProps", "type", "role", "tabIndex", "relationship"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAiBaA;;;eAAAA;;;;iEAjBU;oCACwB;gCACR;gCACI;4BACL;8BACuC;8BAErD;AAUjB,MAAMA,iCAAiC,CAACC;QAChCC;IAAb,MAAMC,OAAOD,CAAAA,kCAAAA,IAAAA,kDAAAA,EAA+BE,CAAAA,MAAOA,IAAID,IAAI,CAAA,MAAA,QAA9CD,oCAAAA,KAAAA,IAAAA,kCAAmDG,sCAAAA;IAChE,MAAMC,SAASJ,IAAAA,kDAAAA,EAA+BE,CAAAA,MAAOA,IAAIE,MAAM;IAC/D,MAAM,EACJC,YAAYJ,OAAO,KAAK,SAAS,OAAO,EACxCK,QAAQC,OAAMC,QAAQ,CAACF,KAAK,CAACP,MAAMU,QAAQ,CAAC,EAC5CA,QAAQ,EACR,GAAGC,aACJ,GAAGX;IAEJ,MAAM,CAACY,aAAaC,eAAe,GAAGC,IAAAA,oCAAAA,EAAqB;QACzDC,OAAOf,MAAMgB,IAAI;QACjBC,cAAcjB,MAAMkB,WAAW;QAC/BC,cAAc;IAChB;IAEA,MAAMC,wBAAwB,CAACC,GAAsBC;YACnDX;QAAAA,CAAAA,4BAAAA,YAAYY,YAAY,AAAZA,MAAY,QAAxBZ,8BAAAA,KAAAA,IAAAA,KAAAA,IAAAA,0BAAAA,IAAAA,CAAAA,aAA2BU,GAAGC;QAC9BT,eAAeS,KAAKN,IAAI;IAC1B;IAEA,IAAIQ;IACJ,IAAInB,WAAW,OAAO;QACpBmB,wBAAwB;IAC1B,OAAO,IAAIlB,cAAc,QAAQ;QAC/BkB,wBAAAA,WAAAA,GAAwBhB,OAAAiB,aAAA,CAACC,iCAAAA,EAAAA;IAC3B,OAAO;QACLF,wBAAwBjB,QAAQ,KAAK,QAAQ,CAAC,CAAC,EAAEA,MAAM,CAAC;IAC1D;IAEA,OAAO;QACLA;QACAD;QACAD;QACAO;QACAV;QAEAyB,YAAY;YACVC,MAAMC,qBAAAA;YACNC,eAAe;YACfC,SAAS;YACTC,gBAAgBC,4BAAAA;YAChBC,SAASC,qBAAAA;QACX;QACAP,MAAMQ,oBAAAA,CAAKC,MAAM,CACf;YACE,oGAAoG;YACpG3B,UAAAA,WAAAA,GAAUF,OAAAiB,aAAA,CAAAjB,OAAA8B,QAAA,EAAA;YACVpC,MAAM;YACNqC,WAAW;YACX,GAAG5B,WAAW;YACdK,MAAMJ;YACNW,cAAcH;QAChB,GACA;YAAEoB,aAAaX,qBAAAA;QAAQ;QAEzBC,eAAeM,oBAAAA,CAAKC,MAAM,CAACrC,MAAM8B,aAAa,EAAE;YAC9CW,cAAc;gBACZ/B,UAAUc;gBACVkB,MAAM;YACR;YACAF,aAAa;QACf;QACAT,SAASK,oBAAAA,CAAKC,MAAM,CAACrC,MAAM+B,OAAO,EAAE;YAClCU,cAAc;gBACZ/B;gBACAiC,MAAM;YACR;YACAH,aAAa;QACf;QACAR,gBAAgBI,oBAAAA,CAAKC,MAAM,CAACrC,MAAMgC,cAAc,EAAE;YAChDS,cAAc;gBACZ,cAAc;gBACdG,UAAU;YACZ;YACAJ,aAAaP,4BAAAA;QACf;QACAC,SAASE,oBAAAA,CAAKC,MAAM,CAACrC,MAAMkC,OAAO,EAAE;YAClCO,cAAc;gBACZV,SAAS;gBACTc,cAAc;YAChB;YACAL,aAAaL,qBAAAA;QACf;IACF;AACF"}