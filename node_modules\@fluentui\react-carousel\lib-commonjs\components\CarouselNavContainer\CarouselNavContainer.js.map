{"version": 3, "sources": ["../src/components/CarouselNavContainer/CarouselNavContainer.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\nimport { useCarouselNavContainer_unstable } from './useCarouselNavContainer';\nimport { renderCarouselNavContainer_unstable } from './renderCarouselNavContainer';\nimport { useCarouselNavContainerStyles_unstable } from './useCarouselNavContainerStyles.styles';\nimport type { CarouselNavContainerProps } from './CarouselNavContainer.types';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\n\n/**\n * CarouselNavContainer component - This container will provide multiple valid layout options for the underlying carousel controls\n */\nexport const CarouselNavContainer: ForwardRefComponent<CarouselNavContainerProps> = React.forwardRef((props, ref) => {\n  const state = useCarouselNavContainer_unstable(props, ref);\n\n  useCarouselNavContainerStyles_unstable(state);\n  useCustomStyleHook_unstable('useCarouselNavContainerStyles_unstable')(state);\n\n  return renderCarouselNavContainer_unstable(state);\n});\n\nCarouselNavContainer.displayName = 'CarouselNavContainer';\n"], "names": ["CarouselNavContainer", "React", "forwardRef", "props", "ref", "state", "useCarouselNavContainer_unstable", "useCarouselNavContainerStyles_unstable", "useCustomStyleHook_unstable", "renderCarouselNavContainer_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAWaA;;;eAAAA;;;;iEAXU;yCAE0B;4CACG;qDACG;qCAEX;AAKrC,MAAMA,uBAAAA,WAAAA,GAAuEC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IAC3G,MAAMC,QAAQC,IAAAA,yDAAAA,EAAiCH,OAAOC;IAEtDG,IAAAA,2EAAAA,EAAuCF;IACvCG,IAAAA,gDAAAA,EAA4B,0CAA0CH;IAEtE,OAAOI,IAAAA,+DAAAA,EAAoCJ;AAC7C;AAEAL,qBAAqBU,WAAW,GAAG"}