{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;;;;AAAA,qCAAmD;AACnD,wDAA0B;AAC1B,uCAAgD;AAChD,yCAAsC;AACtC,gDAAwB;AACxB,gEAAiC;AAGjC,0BAA0B;AAC1B,KAAK,UAAU,iBAAiB,CAAC,OAAgB;IAC/C,MAAM,KAAK,GAAG,CAAC,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IACzF,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;IACpD,KAAK,CAAC,KAAK,CAAC,CAAC;IACb,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC;AAED,SAAgB,SAAS,CAAC,IAAc;IACtC,MAAM,IAAI,GAAG,IAAA,sBAAK,EAAC,IAAI,EAAE;QACvB,OAAO,EAAE;YACP,KAAK;YACL,gBAAgB;YAChB,MAAM;YACN,WAAW;YACX,OAAO;YACP,OAAO;SACR;QACD,OAAO,EAAE;YACP,gBAAgB,EAAE,IAAI;YACtB,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,IAAI;SACZ;QACD,MAAM,EAAE;YACN,kBAAkB;YAClB,KAAK;SACN;KACF,CAAC,CAAC;IAEH,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtB,MAAM,eAAe,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;IACvD,MAAM,aAAa,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;IAEzD,IAAI,eAAe,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,CAAC;QACxF,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,UAAS,MAAM,EAAE,CAAC;YACrD,OAAO,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,IAAI,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC;QACpB,IAAA,gBAAO,EAAC,oEAAoE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;IAClB,CAAC;IAED,2EAA2E;IAE3E,2BAA2B;IAC3B,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,YAAY,KAAK,EAAE,CAAC;QACvD,IAAA,gBAAO,EAAC,6EAA6E,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACnG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,mCAAmC;IACnC,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC;QAChC,IAAA,gBAAO,EAAC,qFAAqF,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3G,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;SAAM,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,QAAQ,EAAE,CAAC;QACpD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC;YACxC,IAAA,gBAAO,EAAC,uGAAuG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/H,CAAC;aAAM,CAAC;YACN,oCAAoC;YACpC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,+BAA+B;IAC/B,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;QAC5B,IAAA,gBAAO,EAAC,iFAAiF,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACvG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;SAAM,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE,CAAC;QAChD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;YACpC,IAAA,gBAAO,EAAC,mGAAmG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3H,CAAC;aAAM,CAAC;YACN,oCAAoC;YACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5E,IAAA,gBAAO,EAAC,qFAAqF,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3G,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACzB,IAAA,gBAAO,EAAC,gIAAgI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACtJ,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,8BAA8B;IAC9B,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;QAC5B,IAAA,gBAAO,EAAC,uDAAuD,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7E,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AA9FD,8BA8FC;AAED,0BAA0B,CAAQ,KAAK,UAAU,GAAG,CAAC,IAAc;IACjE,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;IAE7B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;SAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACxB,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,CAAC,KAAK,CAAC,+FAA+F,CAAC,CAAC;QACjH,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAQ,GAAE,CAAC,CAAC;QACxB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;SAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QACrB,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,IAAA,qBAAe,GAAE,CAAC;IAElB,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAA0B,CAAC,CAAC;QAC5D,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,IAAA,aAAI,EAAC,uBAAuB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC;aAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,IAAA,aAAI,EAAC,qBAAqB,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,CAAU,CAAC;QAEvB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAlC0B,kBAkC1B"}