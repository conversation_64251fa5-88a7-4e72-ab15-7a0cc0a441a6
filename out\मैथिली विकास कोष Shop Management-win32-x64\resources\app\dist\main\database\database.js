"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseService = exports.DatabaseService = void 0;
const better_sqlite3_1 = __importDefault(require("better-sqlite3"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const electron_1 = require("electron");
const encryption_1 = require("../security/encryption");
/**
 * Database service for Maithili Vikas Kosh Shop Management System
 * Uses better-sqlite3 for high performance SQLite operations
 */
class DatabaseService {
    constructor() {
        this.db = null;
        this.isInitialized = false;
        // Set database path in user data directory
        const userDataPath = electron_1.app.getPath('userData');
        this.dbPath = path.join(userDataPath, 'maithili-shop.db');
        this.schemaPath = path.join(__dirname, 'schema.sql');
        console.log('Database path:', this.dbPath);
        console.log('Schema path:', this.schemaPath);
    }
    /**
     * Initialize the database connection and run migrations
     */
    async initialize() {
        try {
            // Initialize encryption service first
            await encryption_1.encryptionService.initialize();
            // Ensure user data directory exists
            const userDataPath = path.dirname(this.dbPath);
            if (!fs.existsSync(userDataPath)) {
                fs.mkdirSync(userDataPath, { recursive: true });
            }
            // Open database connection
            this.db = new better_sqlite3_1.default(this.dbPath);
            // Enable WAL mode for better performance
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('foreign_keys = ON');
            this.db.pragma('synchronous = NORMAL');
            this.db.pragma('cache_size = 1000');
            this.db.pragma('temp_store = MEMORY');
            // Run initial migration
            await this.runMigrations();
            this.isInitialized = true;
            console.log('✅ Database initialized successfully');
        }
        catch (error) {
            console.error('❌ Database initialization failed:', error);
            throw error;
        }
    }
    /**
     * Run database migrations
     */
    async runMigrations() {
        try {
            // Check if schema file exists
            if (!fs.existsSync(this.schemaPath)) {
                throw new Error(`Schema file not found: ${this.schemaPath}`);
            }
            // Read and execute schema
            const schema = fs.readFileSync(this.schemaPath, 'utf8');
            this.db.exec(schema);
            // Initialize database config if not exists
            const configExists = this.db.prepare('SELECT COUNT(*) as count FROM database_config').get();
            if (configExists.count === 0) {
                this.db.prepare(`
          INSERT INTO database_config (version, last_migration, encryption_enabled, backup_enabled)
          VALUES (1, datetime('now'), 1, 1)
        `).run();
            }
            console.log('✅ Database migrations completed');
        }
        catch (error) {
            console.error('❌ Migration failed:', error);
            throw error;
        }
    }
    /**
     * Get database connection (ensure initialized)
     */
    getDb() {
        if (!this.db || !this.isInitialized) {
            throw new Error('Database not initialized. Call initialize() first.');
        }
        return this.db;
    }
    /**
     * Execute a query and return results
     */
    query(sql, params = []) {
        try {
            const db = this.getDb();
            const stmt = db.prepare(sql);
            const result = stmt.all(...params);
            return {
                success: true,
                data: result,
                rowsAffected: result.length
            };
        }
        catch (error) {
            console.error('Query error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    /**
     * Execute a query and return first result
     */
    queryOne(sql, params = []) {
        try {
            const db = this.getDb();
            const stmt = db.prepare(sql);
            const result = stmt.get(...params);
            return {
                success: true,
                data: result
            };
        }
        catch (error) {
            console.error('QueryOne error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    /**
     * Execute an insert/update/delete query
     */
    execute(sql, params = []) {
        try {
            const db = this.getDb();
            const stmt = db.prepare(sql);
            const result = stmt.run(...params);
            return {
                success: true,
                data: result,
                rowsAffected: result.changes
            };
        }
        catch (error) {
            console.error('Execute error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    /**
     * Execute multiple queries in a transaction
     */
    transaction(queries) {
        try {
            const db = this.getDb();
            const transaction = db.transaction(() => {
                const results = [];
                for (const query of queries) {
                    const stmt = db.prepare(query.sql);
                    const result = stmt.run(...(query.params || []));
                    results.push(result);
                }
                return results;
            });
            const results = transaction();
            return {
                success: true,
                data: results,
                rowsAffected: results.reduce((sum, r) => sum + r.changes, 0)
            };
        }
        catch (error) {
            console.error('Transaction error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    /**
     * Get paginated results
     */
    paginate(sql, params = [], page = 1, limit = 50) {
        try {
            const offset = (page - 1) * limit;
            // Get total count
            const countSql = `SELECT COUNT(*) as total FROM (${sql})`;
            const countResult = this.queryOne(countSql, params);
            if (!countResult.success || !countResult.data) {
                throw new Error('Failed to get total count');
            }
            const total = countResult.data.total;
            const totalPages = Math.ceil(total / limit);
            // Get paginated data
            const paginatedSql = `${sql} LIMIT ? OFFSET ?`;
            const dataResult = this.query(paginatedSql, [...params, limit, offset]);
            if (!dataResult.success) {
                throw new Error(dataResult.error);
            }
            return {
                success: true,
                data: {
                    data: dataResult.data || [],
                    total,
                    page,
                    limit,
                    totalPages
                }
            };
        }
        catch (error) {
            console.error('Pagination error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    /**
     * Build WHERE clause from search filters
     */
    buildWhereClause(filters) {
        const conditions = [];
        const params = [];
        if (filters.query) {
            conditions.push('(name LIKE ? OR name_hindi LIKE ? OR description LIKE ?)');
            const searchTerm = `%${filters.query}%`;
            params.push(searchTerm, searchTerm, searchTerm);
        }
        if (filters.category) {
            conditions.push('category_id = ?');
            params.push(filters.category);
        }
        if (filters.artist) {
            conditions.push('artist_id = ?');
            params.push(filters.artist);
        }
        if (filters.priceMin !== undefined) {
            conditions.push('selling_price >= ?');
            params.push(filters.priceMin);
        }
        if (filters.priceMax !== undefined) {
            conditions.push('selling_price <= ?');
            params.push(filters.priceMax);
        }
        if (filters.isActive !== undefined) {
            conditions.push('is_active = ?');
            params.push(filters.isActive ? 1 : 0);
        }
        if (filters.dateFrom) {
            conditions.push('created_at >= ?');
            params.push(filters.dateFrom);
        }
        if (filters.dateTo) {
            conditions.push('created_at <= ?');
            params.push(filters.dateTo);
        }
        const where = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        return { where, params };
    }
    /**
     * Build ORDER BY clause from sort options
     */
    buildOrderClause(sort) {
        if (!sort) {
            return 'ORDER BY created_at DESC';
        }
        const direction = sort.direction.toUpperCase();
        return `ORDER BY ${sort.field} ${direction}`;
    }
    /**
     * Close database connection
     */
    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
            this.isInitialized = false;
            console.log('✅ Database connection closed');
        }
    }
    /**
     * Get database statistics
     */
    getStats() {
        var _a, _b, _c, _d, _e, _f;
        try {
            const stats = {
                users: ((_a = this.queryOne('SELECT COUNT(*) as count FROM users').data) === null || _a === void 0 ? void 0 : _a.count) || 0,
                categories: ((_b = this.queryOne('SELECT COUNT(*) as count FROM categories').data) === null || _b === void 0 ? void 0 : _b.count) || 0,
                artists: ((_c = this.queryOne('SELECT COUNT(*) as count FROM artists').data) === null || _c === void 0 ? void 0 : _c.count) || 0,
                products: ((_d = this.queryOne('SELECT COUNT(*) as count FROM products').data) === null || _d === void 0 ? void 0 : _d.count) || 0,
                customers: ((_e = this.queryOne('SELECT COUNT(*) as count FROM customers').data) === null || _e === void 0 ? void 0 : _e.count) || 0,
                orders: ((_f = this.queryOne('SELECT COUNT(*) as count FROM orders').data) === null || _f === void 0 ? void 0 : _f.count) || 0,
                dbSize: this.getDbSize()
            };
            return {
                success: true,
                data: stats
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    /**
     * Get database file size
     */
    getDbSize() {
        try {
            const stats = fs.statSync(this.dbPath);
            const sizeInBytes = stats.size;
            const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(2);
            return `${sizeInMB} MB`;
        }
        catch (_a) {
            return 'Unknown';
        }
    }
}
exports.DatabaseService = DatabaseService;
// Export singleton instance
exports.databaseService = new DatabaseService();
//# sourceMappingURL=database.js.map