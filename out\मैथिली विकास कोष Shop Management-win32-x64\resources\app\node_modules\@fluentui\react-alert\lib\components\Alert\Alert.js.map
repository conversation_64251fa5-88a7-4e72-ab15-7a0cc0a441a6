{"version": 3, "sources": ["Alert.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { renderAlert_unstable } from './renderAlert';\nimport { useAlert_unstable } from './useAlert';\nimport { useAlertStyles_unstable } from './useAlertStyles.styles';\n\nimport type { AlertProps } from './Alert.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\n/**\n * @deprecated please use the Toast or MessageBar component\n * An Alert component displays a brief, important message to attract a user's attention\n *  without interrupting their current task.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const Alert: ForwardRefComponent<AlertProps> = React.forwardRef((props, ref) => {\n  // eslint-disable-next-line deprecation/deprecation\n  const state = useAlert_unstable(props, ref);\n\n  // eslint-disable-next-line deprecation/deprecation\n  useAlertStyles_unstable(state);\n  // eslint-disable-next-line deprecation/deprecation\n  return renderAlert_unstable(state);\n  // eslint-disable-next-line deprecation/deprecation\n}) as ForwardRefComponent<AlertProps>;\n\n// eslint-disable-next-line deprecation/deprecation\nAlert.displayName = 'Alert';\n"], "names": ["React", "renderAlert_unstable", "useAlert_unstable", "useAlertStyles_unstable", "<PERSON><PERSON>", "forwardRef", "props", "ref", "state", "displayName"], "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAE/B,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,uBAAuB,QAAQ,0BAA0B;AAKlE;;;;CAIC,GACD,mDAAmD;AACnD,OAAO,MAAMC,sBAAyCJ,MAAMK,UAAU,CAAC,CAACC,OAAOC;IAC7E,mDAAmD;IACnD,MAAMC,QAAQN,kBAAkBI,OAAOC;IAEvC,mDAAmD;IACnDJ,wBAAwBK;IACxB,mDAAmD;IACnD,OAAOP,qBAAqBO;AAC5B,mDAAmD;AACrD,GAAsC;AAEtC,mDAAmD;AACnDJ,MAAMK,WAAW,GAAG"}