{"version": 3, "sources": ["../src/Avatar.ts"], "sourcesContent": ["export type {\n  AvatarNamedColor,\n  AvatarProps,\n  AvatarShape,\n  AvatarSize,\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  AvatarSizes,\n  AvatarSlots,\n  AvatarState,\n} from './components/Avatar/index';\nexport {\n  Avatar,\n  DEFAULT_STRINGS,\n  avatarClassNames,\n  renderAvatar_unstable,\n  useAvatarStyles_unstable,\n  useAvatar_unstable,\n  useSizeStyles,\n} from './components/Avatar/index';\n"], "names": ["Avatar", "DEFAULT_STRINGS", "avatarClassNames", "renderAvatar_unstable", "useAvatarStyles_unstable", "useAvatar_unstable", "useSizeStyles"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAWEA,MAAM;eAANA,aAAM;;IACNC,eAAe;eAAfA,sBAAe;;IACfC,gBAAgB;eAAhBA,uBAAgB;;IAChBC,qBAAqB;eAArBA,4BAAqB;;IACrBC,wBAAwB;eAAxBA,+BAAwB;;IACxBC,kBAAkB;eAAlBA,yBAAkB;;IAClBC,aAAa;eAAbA,oBAAa;;;uBACR"}