"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AvatarGroup", {
    enumerable: true,
    get: function() {
        return AvatarGroup;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _renderAvatarGroup = require("./renderAvatarGroup");
const _useAvatarGroup = require("./useAvatarGroup");
const _useAvatarGroupContextValues = require("./useAvatarGroupContextValues");
const _reactsharedcontexts = require("@fluentui/react-shared-contexts");
const _useAvatarGroupStylesstyles = require("./useAvatarGroupStyles.styles");
const AvatarGroup = /*#__PURE__*/ _react.forwardRef((props, ref)=>{
    const state = (0, _useAvatarGroup.useAvatarGroup_unstable)(props, ref);
    const contextValues = (0, _useAvatarGroupContextValues.useAvatarGroupContextValues)(state);
    (0, _useAvatarGroupStylesstyles.useAvatarGroupStyles_unstable)(state);
    (0, _reactsharedcontexts.useCustomStyleHook_unstable)('useAvatarGroupStyles_unstable')(state);
    return (0, _renderAvatarGroup.renderAvatarGroup_unstable)(state, contextValues);
});
AvatarGroup.displayName = 'AvatarGroup';
