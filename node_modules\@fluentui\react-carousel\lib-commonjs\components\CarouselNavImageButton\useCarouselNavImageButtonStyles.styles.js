"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    carouselNavImageButtonClassNames: function() {
        return carouselNavImageButtonClassNames;
    },
    useCarouselNavImageButtonStyles_unstable: function() {
        return useCarouselNavImageButtonStyles_unstable;
    }
});
const _react = require("@griffel/react");
const carouselNavImageButtonClassNames = {
    root: 'fui-CarouselNavImageButton',
    image: 'fui-CarouselNavImageButton__image'
};
const imageButtonSize = 40;
const selectedImageButtonSize = 48;
/**
 * Styles for the root slot
 */ const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    root: {
        B7ck84d: "f1e4lqlz",
        Byoj8tv: 0,
        uwmqm3: 0,
        z189sj: 0,
        z8tnut: 0,
        B0ocmuz: "f1c5fvqg",
        jrapky: 0,
        Frg6f3: 0,
        t21cq0: 0,
        B6of3ja: 0,
        B74szlk: "f186qr3g",
        g2u3we: "fghlq4f",
        h3c5rm: [
            "f1gn591s",
            "fjscplz"
        ],
        B9xav0g: "fb073pr",
        zhjwy3: [
            "fjscplz",
            "f1gn591s"
        ],
        Beyfa6y: 0,
        Bbmb7ep: 0,
        Btl43ni: 0,
        B7oj6ja: 0,
        Dimara: "fq9zq91",
        a9b677: "feqmc2u",
        Bqenvij: "fbhnoac",
        B68tc82: 0,
        Bmxbyg5: 0,
        Bpg54ce: "f1a3p1vp",
        eoavqd: "f8491dx"
    },
    image: {
        a9b677: "feqmc2u",
        Bqenvij: "fbhnoac",
        Beyfa6y: 0,
        Bbmb7ep: 0,
        Btl43ni: 0,
        B7oj6ja: 0,
        Dimara: "fq9zq91"
    },
    selected: {
        a9b677: "f124akge",
        Bqenvij: "ff2sm71"
    }
}, {
    d: [
        ".f1e4lqlz{box-sizing:content-box;}",
        [
            ".f1c5fvqg{padding:0px;}",
            {
                p: -1
            }
        ],
        [
            ".f186qr3g{margin:0 var(--spacingHorizontalXS);}",
            {
                p: -1
            }
        ],
        ".fghlq4f{border-top-color:var(--colorTransparentStroke);}",
        ".f1gn591s{border-right-color:var(--colorTransparentStroke);}",
        ".fjscplz{border-left-color:var(--colorTransparentStroke);}",
        ".fb073pr{border-bottom-color:var(--colorTransparentStroke);}",
        [
            ".fq9zq91{border-radius:var(--borderRadiusSmall);}",
            {
                p: -1
            }
        ],
        ".feqmc2u{width:40px;}",
        ".fbhnoac{height:40px;}",
        [
            ".f1a3p1vp{overflow:hidden;}",
            {
                p: -1
            }
        ],
        [
            ".fq9zq91{border-radius:var(--borderRadiusSmall);}",
            {
                p: -1
            }
        ],
        ".f124akge{width:48px;}",
        ".ff2sm71{height:48px;}"
    ],
    h: [
        ".f8491dx:hover{cursor:pointer;}"
    ]
});
const useCarouselNavImageButtonStyles_unstable = (state)=>{
    'use no memo';
    const { selected } = state;
    const styles = useStyles();
    state.root.className = (0, _react.mergeClasses)(carouselNavImageButtonClassNames.root, styles.root, selected && styles.selected, state.root.className);
    if (state.image) {
        var _state_image;
        state.image.className = (0, _react.mergeClasses)(carouselNavImageButtonClassNames.image, styles.image, selected && styles.selected, (_state_image = state.image) === null || _state_image === void 0 ? void 0 : _state_image.className);
    }
    return state;
};
