{"version": 3, "sources": ["../src/activedescendant/index.ts"], "sourcesContent": ["export type { ActiveDescendantContextValue } from './ActiveDescendantContext';\nexport {\n  ActiveDescendantContextProvider,\n  useActiveDescendantContext,\n  useHasParentActiveDescendantContext,\n} from './ActiveDescendantContext';\nexport type { ActiveDescendantChangeEvent } from './useActiveDescendant';\nexport { createActiveDescendantChangeEvent, useActiveDescendant } from './useActiveDescendant';\nexport { ACTIVEDESCENDANT_ATTRIBUTE, ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE } from './constants';\nexport type {\n  ActiveDescendantImperativeRef,\n  ActiveDescendantOptions,\n  FindOptions,\n  IteratorOptions,\n  UseActiveDescendantReturn,\n} from './types';\n"], "names": ["ACTIVEDESCENDANT_ATTRIBUTE", "ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE", "ActiveDescendantContextProvider", "createActiveDescendantChangeEvent", "useActiveDescendant", "useActiveDescendantContext", "useHasParentActiveDescendantContext"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAQSA,0BAA0B;eAA1BA,qCAA0B;;IAAEC,uCAAuC;eAAvCA,kDAAuC;;IAN1EC,+BAA+B;eAA/BA,wDAA+B;;IAKxBC,iCAAiC;eAAjCA,sDAAiC;;IAAEC,mBAAmB;eAAnBA,wCAAmB;;IAJ7DC,0BAA0B;eAA1BA,mDAA0B;;IAC1BC,mCAAmC;eAAnCA,4DAAmC;;;yCAC9B;qCAEgE;2BACa"}