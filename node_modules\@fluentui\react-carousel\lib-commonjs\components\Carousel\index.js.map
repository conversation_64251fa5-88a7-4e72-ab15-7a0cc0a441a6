{"version": 3, "sources": ["../src/components/Carousel/index.ts"], "sourcesContent": ["export { Carousel } from './Carousel';\nexport type {\n  CarouselAnnouncerFunction,\n  CarouselMotion,\n  CarouselProps,\n  CarouselSlots,\n  CarouselState,\n  CarouselUpdateData,\n  CarouselVisibilityChangeEvent,\n  CarouselVisibilityEventDetail,\n} from './Carousel.types';\nexport { renderCarousel_unstable } from './renderCarousel';\nexport { useCarousel_unstable } from './useCarousel';\nexport { carouselClassNames, useCarouselStyles_unstable } from './useCarouselStyles.styles';\n"], "names": ["Carousel", "carouselClassNames", "renderCarousel_unstable", "useCarouselStyles_unstable", "useCarousel_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAASA,QAAQ;eAARA,kBAAQ;;IAaRC,kBAAkB;eAAlBA,2CAAkB;;IAFlBC,uBAAuB;eAAvBA,uCAAuB;;IAEHC,0BAA0B;eAA1BA,mDAA0B;;IAD9CC,oBAAoB;eAApBA,iCAAoB;;;0BAZJ;gCAWe;6BACH;yCAC0B"}