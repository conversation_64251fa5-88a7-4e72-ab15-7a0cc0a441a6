"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useCarousel_unstable", {
    enumerable: true,
    get: function() {
        return useCarousel_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _reactsharedcontexts = require("@fluentui/react-shared-contexts");
const _reactutilities = require("@fluentui/react-utilities");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _useEmblaCarousel = require("../useEmblaCarousel");
function useCarousel_unstable(props, ref) {
    'use no memo';
    const { align = 'center', circular = false, onActiveIndexChange, groupSize = 'auto', draggable = false, whitespace = false, announcement, motion = 'slide', autoplayInterval = 4000 } = props;
    const { dir } = (0, _reactsharedcontexts.useFluent_unstable)();
    const { activeIndex, carouselApi, containerRef, viewportRef, subscribeForValues, enableAutoplay, resetAutoplay } = (0, _useEmblaCarousel.useEmblaCarousel)({
        align,
        direction: dir,
        loop: circular,
        slidesToScroll: groupSize,
        defaultActiveIndex: props.defaultActiveIndex,
        activeIndex: props.activeIndex,
        watchDrag: draggable,
        containScroll: whitespace ? false : 'keepSnaps',
        motion,
        onDragIndexChange: onActiveIndexChange,
        onAutoplayIndexChange: onActiveIndexChange,
        autoplayInterval
    });
    const selectPageByElement = (0, _reactutilities.useEventCallback)((event, element, jump)=>{
        const foundIndex = carouselApi.scrollToElement(element, jump);
        onActiveIndexChange === null || onActiveIndexChange === void 0 ? void 0 : onActiveIndexChange(event, {
            event,
            type: 'focus',
            index: foundIndex
        });
        return foundIndex;
    });
    const selectPageByIndex = (0, _reactutilities.useEventCallback)((event, index, jump)=>{
        carouselApi.scrollToIndex(index, jump);
        onActiveIndexChange === null || onActiveIndexChange === void 0 ? void 0 : onActiveIndexChange(event, {
            event,
            type: 'click',
            index
        });
    });
    const selectPageByDirection = (0, _reactutilities.useEventCallback)((event, direction)=>{
        const nextPageIndex = carouselApi.scrollInDirection(direction);
        onActiveIndexChange === null || onActiveIndexChange === void 0 ? void 0 : onActiveIndexChange(event, {
            event,
            type: 'click',
            index: nextPageIndex
        });
        return nextPageIndex;
    });
    const mergedContainerRef = (0, _reactutilities.useMergedRefs)(ref, containerRef);
    // Announce carousel updates
    const announcementTextRef = _react.useRef('');
    const totalNavLength = _react.useRef(0);
    const navGroupRef = _react.useRef([]);
    const { announce } = (0, _reactsharedcontexts.useAnnounce)();
    const updateAnnouncement = (0, _reactutilities.useEventCallback)(()=>{
        if (totalNavLength.current <= 0 || !announcement) {
            // Ignore announcements until slides discovered
            return;
        }
        const announcementText = announcement(activeIndex, totalNavLength.current, navGroupRef.current);
        if (announcementText !== announcementTextRef.current) {
            announcementTextRef.current = announcementText;
            announce(announcementText, {
                polite: true
            });
        }
    });
    (0, _reactutilities.useIsomorphicLayoutEffect)(()=>{
        // Subscribe to any non-index carousel state changes
        return subscribeForValues((data)=>{
            if (totalNavLength.current <= 0 && data.navItemsCount > 0 && announcement) {
                const announcementText = announcement(data.activeIndex, data.navItemsCount, data.groupIndexList);
                // Initialize our string to prevent updateAnnouncement from reading an initial load
                announcementTextRef.current = announcementText;
            }
            totalNavLength.current = data.navItemsCount;
            navGroupRef.current = data.groupIndexList;
            updateAnnouncement();
        });
    }, [
        subscribeForValues,
        updateAnnouncement,
        announcement
    ]);
    (0, _reactutilities.useIsomorphicLayoutEffect)(()=>{
        updateAnnouncement();
    }, [
        activeIndex,
        updateAnnouncement
    ]);
    return {
        components: {
            root: 'div'
        },
        root: _reactutilities.slot.always((0, _reactutilities.getIntrinsicElementProps)('div', {
            ref: mergedContainerRef,
            role: 'region',
            ...props
        }), {
            elementType: 'div'
        }),
        activeIndex,
        circular,
        containerRef: mergedContainerRef,
        viewportRef,
        selectPageByElement,
        selectPageByDirection,
        selectPageByIndex,
        subscribeForValues,
        enableAutoplay,
        resetAutoplay
    };
}
