{"version": 3, "sources": ["../src/components/Avatar/index.ts"], "sourcesContent": ["export type {\n  AvatarNamedColor,\n  AvatarProps,\n  AvatarShape,\n  AvatarSize,\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  AvatarSizes,\n  AvatarSlots,\n  AvatarState,\n} from './Avatar.types';\nexport { Avatar } from './Avatar';\nexport { renderAvatar_unstable } from './renderAvatar';\nexport { DEFAULT_STRINGS, useAvatar_unstable } from './useAvatar';\nexport { avatarClassNames, useAvatarStyles_unstable, useSizeStyles } from './useAvatarStyles.styles';\n"], "names": ["Avatar", "DEFAULT_STRINGS", "avatarClassNames", "renderAvatar_unstable", "useAvatarStyles_unstable", "useAvatar_unstable", "useSizeStyles"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAUSA,MAAM;eAANA,cAAM;;IAENC,eAAe;eAAfA,0BAAe;;IACfC,gBAAgB;eAAhBA,uCAAgB;;IAFhBC,qBAAqB;eAArBA,mCAAqB;;IAEHC,wBAAwB;eAAxBA,+CAAwB;;IADzBC,kBAAkB;eAAlBA,6BAAkB;;IACSC,aAAa;eAAbA,oCAAa;;;wBAH3C;8BACe;2BACc;uCACsB"}