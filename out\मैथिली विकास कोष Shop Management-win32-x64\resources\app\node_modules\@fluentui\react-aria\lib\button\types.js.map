{"version": 3, "sources": ["../src/button/types.ts"], "sourcesContent": ["import type { DistributiveOmit, ExtractSlotProps, Slot, UnionToIntersection } from '@fluentui/react-utilities';\nimport * as React from 'react';\n\nexport type ARIAButtonType = 'button' | 'a' | 'div';\n\nexport type ARIAButtonElement<AlternateAs extends 'a' | 'div' = 'a' | 'div'> =\n  | HTMLButtonElement\n  | (AlternateAs extends 'a' ? HTMLAnchorElement : never)\n  | (AlternateAs extends 'div' ? HTMLDivElement : never);\n\n/**\n * @internal\n */\nexport type ARIAButtonElementIntersection<AlternateAs extends 'a' | 'div' = 'a' | 'div'> = UnionToIntersection<\n  ARIAButtonElement<AlternateAs>\n>;\n\n/**\n * Props expected by `useARIAButtonProps` hooks\n */\nexport type ARIAButtonProps<Type extends ARIAButtonType = ARIAButtonType> = DistributiveOmit<\n  React.PropsWithRef<JSX.IntrinsicElements[Type]>,\n  'children'\n> & {\n  disabled?: boolean;\n  /**\n   * When set, allows the button to be focusable even when it has been disabled.\n   * This is used in scenarios where it is important to keep a consistent tab order\n   * for screen reader and keyboard users. The primary example of this\n   * pattern is when the disabled button is in a menu or a commandbar and is seldom used for standalone buttons.\n   *\n   * @default false\n   */\n  disabledFocusable?: boolean;\n};\n\nexport type ARIAButtonSlotProps<AlternateAs extends 'a' | 'div' = 'a' | 'div'> = ExtractSlotProps<\n  Slot<'button', AlternateAs>\n> &\n  Pick<ARIAButtonProps<ARIAButtonType>, 'disabled' | 'disabledFocusable'>;\n\n/**\n * Props that will be modified internally by `useARIAButtonProps` by each case.\n * This typing is to ensure a well specified return value for `useARIAbButtonProps`\n */\nexport type ARIAButtonAlteredProps<Type extends ARIAButtonType> =\n  | (Type extends 'button'\n      ? Pick<\n          JSX.IntrinsicElements['button'],\n          'onClick' | 'onKeyDown' | 'onKeyUp' | 'disabled' | 'aria-disabled' | 'tabIndex'\n        >\n      : never)\n  | (Type extends 'a'\n      ? Pick<\n          JSX.IntrinsicElements['a'],\n          'onClick' | 'onKeyDown' | 'onKeyUp' | 'aria-disabled' | 'tabIndex' | 'role' | 'href'\n        >\n      : never)\n  | (Type extends 'div'\n      ? Pick<JSX.IntrinsicElements['div'], 'onClick' | 'onKeyDown' | 'onKeyUp' | 'aria-disabled' | 'tabIndex' | 'role'>\n      : never);\n\n/**\n * Merge of props provided by the user and props provided internally.\n */\nexport type ARIAButtonResultProps<Type extends ARIAButtonType, Props> = Props &\n  UnionToIntersection<ARIAButtonAlteredProps<Type>>;\n"], "names": ["React"], "rangeMappings": "", "mappings": "AACA,YAAYA,WAAW,QAAQ"}