{"version": 3, "sources": ["../src/components/AccordionPanel/useAccordionPanel.ts"], "sourcesContent": ["import * as React from 'react';\nimport { getIntrinsicElementProps, slot } from '@fluentui/react-utilities';\nimport { useTabsterAttributes } from '@fluentui/react-tabster';\nimport { presenceMotionSlot } from '@fluentui/react-motion';\nimport { Collapse } from '@fluentui/react-motion-components-preview';\nimport { useAccordionContext_unstable } from '../../contexts/accordion';\nimport type { AccordionPanelProps, AccordionPanelState } from './AccordionPanel.types';\nimport { useAccordionItemContext_unstable } from '../../contexts/accordionItem';\n\n/**\n * Returns the props and state required to render the component\n * @param props - AccordionPanel properties\n * @param ref - reference to root HTMLElement of AccordionPanel\n */\nexport const useAccordionPanel_unstable = (\n  props: AccordionPanelProps,\n  ref: React.Ref<HTMLElement>,\n): AccordionPanelState => {\n  const { open } = useAccordionItemContext_unstable();\n  const focusableProps = useTabsterAttributes({ focusable: { excludeFromMover: true } });\n  const navigation = useAccordionContext_unstable(ctx => ctx.navigation);\n\n  return {\n    open,\n    components: {\n      root: 'div',\n      collapseMotion: Collapse,\n    },\n    root: slot.always(\n      getIntrinsicElementProps('div', {\n        // FIXME:\n        // `ref` is wrongly assigned to be `HTMLElement` instead of `HTMLDivElement`\n        // but since it would be a breaking change to fix it, we are casting ref to it's proper type\n        ref: ref as React.Ref<HTMLDivElement>,\n        ...props,\n        ...(navigation && focusableProps),\n      }),\n      { elementType: 'div' },\n    ),\n    collapseMotion: presenceMotionSlot(props.collapseMotion, {\n      elementType: Collapse,\n      defaultProps: {\n        visible: open,\n        unmountOnExit: true,\n      },\n    }),\n  };\n};\n"], "names": ["useAccordionPanel_unstable", "props", "ref", "open", "useAccordionItemContext_unstable", "focusableProps", "useTabsterAttributes", "focusable", "excludeFromMover", "navigation", "useAccordionContext_unstable", "ctx", "components", "root", "collapseMotion", "Collapse", "slot", "always", "getIntrinsicElementProps", "elementType", "presenceMotionSlot", "defaultProps", "visible", "unmountOnExit"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAcaA;;;eAAAA;;;;iEAdU;gCACwB;8BACV;6BACF;8CACV;2BACoB;+BAEI;AAO1C,MAAMA,6BAA6B,CACxCC,OACAC;IAEA,MAAM,EAAEC,IAAI,EAAE,GAAGC,IAAAA,+CAAAA;IACjB,MAAMC,iBAAiBC,IAAAA,kCAAAA,EAAqB;QAAEC,WAAW;YAAEC,kBAAkB;QAAK;IAAE;IACpF,MAAMC,aAAaC,IAAAA,uCAAAA,EAA6BC,CAAAA,MAAOA,IAAIF,UAAU;IAErE,OAAO;QACLN;QACAS,YAAY;YACVC,MAAM;YACNC,gBAAgBC,sCAAAA;QAClB;QACAF,MAAMG,oBAAAA,CAAKC,MAAM,CACfC,IAAAA,wCAAAA,EAAyB,OAAO;YAC9B,SAAS;YACT,4EAA4E;YAC5E,4FAA4F;YAC5FhB,KAAKA;YACL,GAAGD,KAAK;YACR,GAAIQ,cAAcJ,cAAc;QAClC,IACA;YAAEc,aAAa;QAAM;QAEvBL,gBAAgBM,IAAAA,+BAAAA,EAAmBnB,MAAMa,cAAc,EAAE;YACvDK,aAAaJ,sCAAAA;YACbM,cAAc;gBACZC,SAASnB;gBACToB,eAAe;YACjB;QACF;IACF;AACF"}