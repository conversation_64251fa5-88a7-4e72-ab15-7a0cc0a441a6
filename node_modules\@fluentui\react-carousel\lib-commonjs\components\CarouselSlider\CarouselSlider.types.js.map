{"version": 3, "sources": ["../src/components/CarouselSlider/CarouselSlider.types.ts"], "sourcesContent": ["import type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\n\nexport type CarouselSliderSlots = {\n  /**\n   * The root viewport/window of the carousel\n   */\n  root: Slot<'div'>;\n};\n\n/**\n * CarouselSlider Props\n */\nexport type CarouselSliderProps = Partial<ComponentProps<CarouselSliderSlots>> & {\n  /**\n   * cardFocus sets the carousel slider as a focus group,\n   * enabling left/right navigation of elements.\n   *\n   * This will also be passed into CarouselCards via context and set the appropriate focus attributes\n   *\n   * Defaults: false\n   */\n  cardFocus?: boolean;\n};\n\nexport type CarouselSliderContextValue = Pick<CarouselSliderProps, 'cardFocus'>;\n/**\n * State used in rendering CarouselSlider\n */\nexport type CarouselSliderState = ComponentState<CarouselSliderSlots> & CarouselSliderContextValue;\n"], "names": [], "rangeMappings": ";;", "mappings": "AAyBA;;CAEC"}