{"version": 3, "sources": ["Alert.js"], "sourcesContent": ["import * as React from 'react';\nimport { renderAlert_unstable } from './renderAlert';\nimport { useAlert_unstable } from './useAlert';\nimport { useAlertStyles_unstable } from './useAlertStyles.styles';\n/**\n * @deprecated please use the Toast or MessageBar component\n * An Alert component displays a brief, important message to attract a user's attention\n *  without interrupting their current task.\n */ // eslint-disable-next-line deprecation/deprecation\nexport const Alert = /*#__PURE__*/ React.forwardRef((props, ref)=>{\n    // eslint-disable-next-line deprecation/deprecation\n    const state = useAlert_unstable(props, ref);\n    // eslint-disable-next-line deprecation/deprecation\n    useAlertStyles_unstable(state);\n    // eslint-disable-next-line deprecation/deprecation\n    return renderAlert_unstable(state);\n// eslint-disable-next-line deprecation/deprecation\n});\n// eslint-disable-next-line deprecation/deprecation\nAlert.displayName = 'Alert';\n"], "names": ["<PERSON><PERSON>", "React", "forwardRef", "props", "ref", "state", "useAlert_unstable", "useAlertStyles_unstable", "renderAlert_unstable", "displayName"], "mappings": ";;;;+BASaA;;;eAAAA;;;;iEATU;6BACc;0BACH;sCACM;AAMjC,MAAMA,QAAQ,WAAW,GAAGC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IACxD,mDAAmD;IACnD,MAAMC,QAAQC,IAAAA,2BAAiB,EAACH,OAAOC;IACvC,mDAAmD;IACnDG,IAAAA,6CAAuB,EAACF;IACxB,mDAAmD;IACnD,OAAOG,IAAAA,iCAAoB,EAACH;AAChC,mDAAmD;AACnD;AACA,mDAAmD;AACnDL,MAAMS,WAAW,GAAG"}