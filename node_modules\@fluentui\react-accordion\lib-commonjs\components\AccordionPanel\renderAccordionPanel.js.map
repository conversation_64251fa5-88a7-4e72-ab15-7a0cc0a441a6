{"version": 3, "sources": ["../src/components/AccordionPanel/renderAccordionPanel.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { AccordionPanelState, AccordionPanelSlots } from './AccordionPanel.types';\n\n/**\n * Function that renders the final JSX of the component\n */\nexport const renderAccordionPanel_unstable = (state: AccordionPanelState) => {\n  assertSlots<AccordionPanelSlots>(state);\n  return state.collapseMotion ? (\n    <state.collapseMotion>\n      <state.root />\n    </state.collapseMotion>\n  ) : (\n    <state.root />\n  );\n};\n"], "names": ["renderAccordionPanel_unstable", "state", "assertSlots", "collapseMotion", "_jsx", "root"], "rangeMappings": ";;;;;;;;;;;;;;;;;", "mappings": ";;;;+BASaA;;;eAAAA;;;4BARb;gCAE4B;AAMrB,MAAMA,gCAAgC,CAACC;IAC5CC,IAAAA,2BAAAA,EAAiCD;IACjC,OAAOA,MAAME,cAAc,GAAA,WAAA,GACzBC,IAAAA,eAAA,EAACH,MAAME,cAAc,EAAA;kBACnB,WAAA,GAAAC,IAAAA,eAAA,EAACH,MAAMI,IAAI,EAAA,CAAA;uBAGbD,IAAAA,eAAA,EAACH,MAAMI,IAAI,EAAA,CAAA;AAEf"}