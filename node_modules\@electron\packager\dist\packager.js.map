{"version": 3, "file": "packager.js", "sourceRoot": "", "sources": ["../src/packager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qCAAgG;AAChG,+CAAqD;AACrD,yCAAuE;AACvE,wDAA0B;AAC1B,mCAAqD;AACrD,mCAAyC;AACzC,gDAAwB;AACxB,uCAAwF;AACxF,mCAA6C;AAC7C,2CAAkD;AAIlD,SAAS,aAAa;IACpB,IAAA,cAAK,EAAC,IAAA,iBAAQ,GAAE,CAAC,CAAC;AACpB,CAAC;AAED,MAAa,QAAQ;IAMnB,YAAY,IAAa;QALzB,sBAAiB,GAAwB,SAAS,CAAC;QAMjD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,IAAA,oBAAW,EAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,kBAAE,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAuB,EAAE,OAAe;QACxD,MAAM,kBAAE,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACrH,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAEjD,IAAI,CAAC;YACH,MAAM,kBAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAClC,MAAM,kBAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAChC,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,0BAA0B;YAC1B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QACjC,CAAC;gBAAS,CAAC;YACT,MAAM,kBAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACjD,CAAC;QAED,0BAA0B;QAC1B,OAAO,IAAI,CAAC,kCAAkC,CAAC,SAAS,CAAC,CAAC;IAC5D,CAAC;IAED,0BAA0B;IAC1B,kCAAkC,CAAC,SAAuB;QACxD,IAAA,aAAI,EAAC,qFAAqF,SAAS,CAAC,QAAQ,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1I,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc,EAAE,SAAuB,EAAE,OAAe;QAClF,IAAA,cAAK,EAAC,YAAY,MAAM,iCAAiC,CAAC,CAAC;QAC3D,MAAM,kBAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,SAAuB,EAAE,OAAe,EAAE,QAAgB;QACjF,IAAA,cAAK,EAAC,cAAc,OAAO,OAAO,QAAQ,EAAE,CAAC,CAAC;QAC9C,MAAM,IAAA,0BAAkB,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5C,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YAC3C,QAAQ;YACR,SAAS,CAAC,eAAe;YACzB,SAAS,CAAC,QAAQ;YAClB,SAAS,CAAC,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,QAAkC,EAAE,IAA0B;QAC3E,IAAI,cAAc,CAAC;QACnB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAClD,CAAC;QACD,MAAM,kBAAE,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAChC,OAAO,MAAM,kBAAE,CAAC,OAAO,CAAC,cAAI,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,QAAQ,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC;IACzF,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,SAAuB,EAAE,OAAe;QACtD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;QACzE,IAAA,aAAI,EAAC,8BAA8B,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,oBAAoB,SAAS,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzI,IAAA,cAAK,EAAC,YAAY,QAAQ,EAAE,CAAC,CAAC;QAC9B,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC7B,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,MAAM,EAAE,GAAG,yBAAa,mBAAS,CAAC,SAAS,CAAC,QAA4B,CAAC,uCAAC,CAAC;QAC3E,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAQ,CAAC;QACnD,OAAO,GAAG,CAAC,MAAM,EAAE,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,SAAuB,EAAE,OAAe;QAC3D,MAAM,SAAS,GAAG,IAAA,0BAAiB,EAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YACnE,CAAC;iBAAM,CAAC;gBACN,IAAA,aAAI,EAAC,YAAY,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,wDAAwD,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAChI,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,YAA6B;QACpD,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAC7B,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClD,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,EACxB,aAAa,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,IAAI,MAAM,CACtF,CAAC;gBACF,IAAI,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;oBAClC,MAAM,IAAI,KAAK,CAAC,mDAAmD,OAAO,EAAE,CAAC,CAAC;gBAChF,CAAC;gBAED,OAAO,OAAO,CAAC;YACjB,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,wDAAwD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QACtG,CAAC;aAAM,CAAC;YACN,OAAO,IAAA,8BAAmB,EAAC,YAAY,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iCAAiC,CAAC,SAAuB,EAAE,YAA6B;QAC5F,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAE5D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,IAAA,sBAAa,EAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,0BAA0B;YAC1B,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBACzC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC;iBAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,kCAAkC,CAAC,SAAS,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,YAA6B;QAC3D,+FAA+F;QAC/F,MAAM,SAAS,GAAiB;YAC9B,GAAG,IAAI,CAAC,IAAI;YACZ,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,QAAQ,EAAE,YAAY,CAAC,QAAQ;YAC/B,eAAe,EAAE,YAAY,CAAC,OAAO;SACtC,CAAC;QAEF,IAAI,IAAA,sBAAa,EAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YACxE,OAAO,IAAA,+BAAmB,EAAC,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjL,CAAC;QAED,OAAO,IAAI,CAAC,iCAAiC,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACzE,CAAC;CACF;AA/JD,4BA+JC;AAED,KAAK,UAAU,yBAAyB,CAAC,IAAa,EAAE,KAAsB,EAAE,SAA8B;IAC5G,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;IACpC,MAAM,QAAQ,CAAC,aAAa,EAAE,CAAC;IAC/B,OAAO,OAAO,CAAC,GAAG,CAAC,IAAA,+BAAoB,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,GAAG,CACjE,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC,YAAY,CAAC,CACjE,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACI,KAAK,UAAU,QAAQ,CAAC,IAAa;IAC1C,aAAa,EAAE,CAAC;IAEhB,IAAI,cAAK,CAAC,OAAO,EAAE,CAAC;QAClB,IAAA,cAAK,EAAC,qBAAqB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,KAAK,GAAG,IAAA,iCAAuB,EAAC,IAAI,EAAE,MAAM,CAA4B,CAAC;IAC/E,MAAM,SAAS,GAAG,IAAA,iCAAuB,EAAC,IAAI,EAAE,UAAU,CAAgC,CAAC;IAE3F,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QAC9B,OAAO,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAED,IAAA,cAAK,EAAC,qBAAqB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnD,IAAA,cAAK,EAAC,yBAAyB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEnD,MAAM,cAAc,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAE9E,MAAM,IAAA,kCAA0B,EAAC,SAAS,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;IAClE,IAAI,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;IAC3F,CAAC;IAED,IAAA,cAAK,EAAC,qBAAqB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IACxC,IAAA,cAAK,EAAC,4BAA4B,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;IAE1D,IAAA,kCAAoB,EAAC,IAAI,CAAC,CAAC;IAE3B,MAAM,IAAA,sBAAc,EAAC,IAAI,CAAC,2BAA2B,EAAE;QACrD,IAAA,iCAAuB,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;KAChG,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG,MAAM,yBAAyB,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IACzE,gDAAgD;IAChD,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,CAAa,CAAC;AACxF,CAAC;AAvCD,4BAuCC"}