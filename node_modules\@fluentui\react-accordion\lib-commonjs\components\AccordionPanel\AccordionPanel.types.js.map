{"version": 3, "sources": ["../src/components/AccordionPanel/AccordionPanel.types.ts"], "sourcesContent": ["import type { PresenceMotionSlotProps } from '@fluentui/react-motion';\nimport type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\n\nexport type AccordionPanelSlots = {\n  root: NonNullable<Slot<'div'>>;\n  collapseMotion?: Slot<PresenceMotionSlotProps>;\n};\n\nexport type AccordionPanelProps = ComponentProps<AccordionPanelSlots>;\n\nexport type AccordionPanelState = ComponentState<AccordionPanelSlots> & {\n  /**\n   * Internal open state, provided by context.\n   */\n  open: boolean;\n};\n"], "names": [], "rangeMappings": "", "mappings": ""}