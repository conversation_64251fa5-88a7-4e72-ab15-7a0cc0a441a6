/// <reference types="react" />

import type { ComponentProps } from '@fluentui/react-utilities';
import type { ComponentState } from '@fluentui/react-utilities';
import { ContextSelector } from '@fluentui/react-context-selector';
import { FC } from 'react';
import type { ForwardRefComponent } from '@fluentui/react-utilities';
import type { PopoverProps } from '@fluentui/react-popover';
import type { PopoverSurface } from '@fluentui/react-popover';
import { PresenceBadge } from '@fluentui/react-badge';
import { Provider } from 'react';
import { ProviderProps } from 'react';
import * as React_2 from 'react';
import type { Slot } from '@fluentui/react-utilities';
import type { SlotClassNames } from '@fluentui/react-utilities';
import type { TooltipProps } from '@fluentui/react-tooltip';

export declare const Avatar: ForwardRefComponent<AvatarProps>;

export declare const avatarClassNames: SlotClassNames<AvatarSlots>;

/**
 * @internal
 */
export declare const AvatarContextProvider: React_2.Provider<AvatarContextValue | undefined>;

/**
 * @internal
 */
export declare interface AvatarContextValue {
    shape?: AvatarShape;
    size?: AvatarSize;
}

/**
 * The AvatarGroup component represents a group of multiple people or entities by taking care of the arrangement
 * of individual Avatars in a spread, stack, or pie layout.
 */
export declare const AvatarGroup: ForwardRefComponent<AvatarGroupProps>;

export declare const avatarGroupClassNames: SlotClassNames<AvatarGroupSlots>;

export declare type AvatarGroupContextValue = Pick<AvatarGroupProps, 'size' | 'layout'> & {
    isOverflow?: boolean;
};

export declare type AvatarGroupContextValues = {
    avatarGroup: AvatarGroupContextValue;
};

/**
 * The AvatarGroupItem component represents a single person or entity.
 * AvatarGroupItem should only be used in an AvatarGroup component.
 */
export declare const AvatarGroupItem: ForwardRefComponent<AvatarGroupItemProps>;

export declare const avatarGroupItemClassNames: SlotClassNames<AvatarGroupItemSlots>;

/**
 * AvatarGroupItem Props
 */
export declare type AvatarGroupItemProps = Omit<ComponentProps<Partial<AvatarGroupItemSlots>, 'avatar'>, 'size' | 'shape'>;

export declare type AvatarGroupItemSlots = {
    root: NonNullable<Slot<'div', 'li'>>;
    /**
     * Avatar that represents a person or entity.
     */
    avatar: NonNullable<Slot<typeof Avatar>>;
    /**
     * Label used for the name of the AvatarGroupItem when rendered as an overflow item.
     * The content of the label, by default, is the `name` prop from the `avatar` slot.
     */
    overflowLabel: NonNullable<Slot<'span'>>;
};

/**
 * State used in rendering AvatarGroupItem
 */
export declare type AvatarGroupItemState = ComponentState<AvatarGroupItemSlots> & {
    /**
     * Whether the Avatar is an overflow item.
     *
     * @default false
     */
    isOverflowItem?: boolean;
    layout: AvatarGroupProps['layout'];
    size: AvatarSize;
};

/**
 * The AvatarGroupPopover component provides a button with a Popover containing the children provided.
 */
export declare const AvatarGroupPopover: React_2.FC<AvatarGroupPopoverProps>;

export declare const avatarGroupPopoverClassNames: SlotClassNames<AvatarGroupPopoverSlots>;

/**
 * AvatarGroupPopover Props
 */
export declare type AvatarGroupPopoverProps = Omit<ComponentProps<Partial<AvatarGroupPopoverSlots>>, 'children'> & {
    /**
     * Whether the triggerButton should render an icon instead of the number of overflowed AvatarGroupItems.
     * Note: The indicator will default to `icon` when the size is less than 24.
     * @default count
     */
    indicator?: 'count' | 'icon';
    /**
     * Number of AvatarGroupItems that will be rendered.
     *
     * Note: AvatarGroupPopover handles counting the number of children, but when using a react fragment to wrap the
     * children, this is not possible and therefore it has do be added manually.
     */
    count?: number;
    children: React_2.ReactNode;
};

export declare type AvatarGroupPopoverSlots = {
    root: NonNullable<Slot<PopoverProps>>;
    /**
     * Button that triggers the Popover.
     */
    triggerButton: NonNullable<Slot<'button'>>;
    /**
     * List that contains the overflowed AvatarGroupItems.
     */
    content: NonNullable<Slot<'ul'>>;
    /**
     * PopoverSurface that contains the content.
     */
    popoverSurface: NonNullable<Slot<typeof PopoverSurface>>;
    /**
     * Tooltip shown when triggerButton is hovered.
     */
    tooltip: NonNullable<Slot<TooltipProps>>;
};

/**
 * State used in rendering AvatarGroupPopover
 */
export declare type AvatarGroupPopoverState = ComponentState<AvatarGroupPopoverSlots> & Required<Pick<AvatarGroupPopoverProps, 'count' | 'indicator'>> & {
    popoverOpen: boolean;
    layout: AvatarGroupProps['layout'];
    size: AvatarSize;
};

/**
 * AvatarGroup Props
 */
export declare type AvatarGroupProps = ComponentProps<AvatarGroupSlots> & {
    /**
     * Layout the AvatarGroupItems should be displayed as.
     * @default spread
     */
    layout?: 'spread' | 'stack' | 'pie';
    /**
     * Size of the AvatarGroupItems.
     * @default 32
     */
    size?: AvatarSize;
};

export declare const AvatarGroupProvider: Provider<AvatarGroupContextValue> & FC<ProviderProps<AvatarGroupContextValue>>;

export declare type AvatarGroupSlots = {
    root: NonNullable<Slot<'div'>>;
};

/**
 * State used in rendering AvatarGroup
 */
export declare type AvatarGroupState = ComponentState<AvatarGroupSlots> & Required<Pick<AvatarGroupProps, 'layout' | 'size'>>;

/**
 * A specific named color for the Avatar
 */
export declare type AvatarNamedColor = 'dark-red' | 'cranberry' | 'red' | 'pumpkin' | 'peach' | 'marigold' | 'gold' | 'brass' | 'brown' | 'forest' | 'seafoam' | 'dark-green' | 'light-teal' | 'teal' | 'steel' | 'blue' | 'royal-blue' | 'cornflower' | 'navy' | 'lavender' | 'purple' | 'grape' | 'lilac' | 'pink' | 'magenta' | 'plum' | 'beige' | 'mink' | 'platinum' | 'anchor';

/**
 * Properties for Avatar
 */
export declare type AvatarProps = Omit<ComponentProps<AvatarSlots>, 'color'> & {
    /**
     * Optional activity indicator
     * * active: the avatar will be decorated according to activeAppearance
     * * inactive: the avatar will be reduced in size and partially transparent
     * * unset: normal display
     *
     * @default unset
     */
    active?: 'active' | 'inactive' | 'unset';
    /**
     * The appearance when `active="active"`
     *
     * @default ring
     */
    activeAppearance?: 'ring' | 'shadow' | 'ring-shadow';
    /**
     * The color when displaying either an icon or initials.
     * * neutral (default): gray
     * * brand: color from the brand palette
     * * colorful: picks a color from a set of pre-defined colors, based on a hash of the name (or idForColor if provided)
     * * [AvatarNamedColor]: a specific color from the theme
     *
     * @default neutral
     */
    color?: 'neutral' | 'brand' | 'colorful' | AvatarNamedColor;
    /**
     * Specify a string to be used instead of the name, to determine which color to use when color="colorful".
     * Use this when a name is not available, but there is another unique identifier that can be used instead.
     */
    idForColor?: string | undefined;
    /**
     * The name of the person or entity represented by this Avatar. This should always be provided if it is available.
     *
     * The name is used to determine the initials displayed when there is no image. It is also provided to
     * accessibility tools.
     */
    name?: string;
    /**
     * The avatar can have a circular or square shape.
     * @default circular
     */
    shape?: AvatarShape;
    /**
     * Size of the avatar in pixels.
     *
     * Size is restricted to a limited set of supported values recommended for most uses (see `AvatarSizeValue`) and
     * based on design guidelines for the Avatar control.
     *
     * Note: At size 16, if initials are displayed, only the first initial will be rendered.
     *
     * If a non-supported size is needed, set `size` to the next-smaller supported size, and set `width` and `height`
     * to override the rendered size.
     *
     * For example, to set the avatar to 45px in size:
     * `<Avatar size={40} style={{ width: '45px', height: '45px' }} />`
     *
     * @default 32
     */
    size?: AvatarSize;
};

/**
 * Shape of the avatar
 */
export declare type AvatarShape = 'circular' | 'square';

/**
 * Sizes for the avatar
 */
export declare type AvatarSize = 16 | 20 | 24 | 28 | 32 | 36 | 40 | 48 | 56 | 64 | 72 | 96 | 120 | 128;

/**
 * Sizes for the avatar
 * @deprecated use AvatarSize instead
 */
export declare type AvatarSizes = AvatarSize;

export declare type AvatarSlots = {
    root: Slot<'span'>;
    /**
     * The Avatar's image.
     *
     * Usage e.g.: `image={{ src: '...' }}`
     */
    image?: Slot<'img'>;
    /**
     * (optional) Custom initials.
     *
     * It is usually not necessary to specify custom initials; by default they will be derived from the `name` prop,
     * using the `getInitials` function.
     *
     * The initials are displayed when there is no image (including while the image is loading).
     */
    initials?: Slot<'span'>;
    /**
     * Icon to be displayed when the avatar doesn't have an image or initials.
     *
     * @default `PersonRegular` (the default icon's size depends on the Avatar's size)
     */
    icon?: Slot<'span'>;
    /**
     * Badge to show the avatar's presence status.
     */
    badge?: Slot<typeof PresenceBadge>;
};

/**
 * State used in rendering Avatar
 */
export declare type AvatarState = ComponentState<AvatarSlots> & Required<Pick<AvatarProps, 'active' | 'activeAppearance' | 'shape' | 'size'>> & {
    /**
     * The Avatar's color, it matches props.color but with `'colorful'` resolved to a named color
     */
    color: NonNullable<Exclude<AvatarProps['color'], 'colorful'>>;
    /**
     * Hidden span to render the active state label for the purposes of including in the aria-labelledby, if needed.
     */
    activeAriaLabelElement?: JSX.Element;
};

/**
 * Regular expressions matching characters to ignore when calculating the initials.
 */
/**
 * Get (up to 2 characters) initials based on display name of the persona.
 *
 * @param displayName - The full name of the person or entity
 * @param isRtl - Whether the display is in RTL
 * @param options - Extra options to control the behavior of getInitials
 *
 * @returns The 1 or 2 character initials based on the name. Or an empty string if no initials
 * could be derived from the name.
 *
 * @internal
 */
export declare function getInitials(displayName: string | undefined | null, isRtl: boolean, options?: {
    /** Should initials be generated from phone numbers (default false) */
    allowPhoneInitials?: boolean;
    /** Returns only the first initial */
    firstInitialOnly?: boolean;
}): string;

export declare type PartitionAvatarGroupItems<T> = {
    inlineItems: readonly T[];
    overflowItems?: readonly T[];
};

/**
 * Get the inline items and overflowing items based on the array of AvatarGroupItems needed for AvatarGroup.
 *
 * @param options - Configure the partition options
 *
 * @returns Two arrays split into inline items and overflow items based on maxInlineItems.
 */
export declare const partitionAvatarGroupItems: <T>(options: PartitionAvatarGroupItemsOptions<T>) => PartitionAvatarGroupItems<T>;

export declare type PartitionAvatarGroupItemsOptions<T> = {
    items: readonly T[];
    layout?: 'spread' | 'stack' | 'pie';
    maxInlineItems?: number;
};

export declare const renderAvatar_unstable: (state: AvatarState) => JSX.Element;

/**
 * Render the final JSX of AvatarGroup
 */
export declare const renderAvatarGroup_unstable: (state: AvatarGroupState, contextValues: AvatarGroupContextValues) => JSX.Element;

/**
 * Render the final JSX of AvatarGroupItem
 */
export declare const renderAvatarGroupItem_unstable: (state: AvatarGroupItemState) => JSX.Element;

/**
 * Render the final JSX of AvatarGroupPopover
 */
export declare const renderAvatarGroupPopover_unstable: (state: AvatarGroupPopoverState, contextValues: AvatarGroupContextValues) => JSX.Element;

export declare const useAvatar_unstable: (props: AvatarProps, ref: React_2.Ref<HTMLElement>) => AvatarState;

/**
 * @internal
 */
export declare const useAvatarContext: () => AvatarContextValue;

/**
 * Create the state required to render AvatarGroup.
 *
 * The returned state can be modified with hooks such as useAvatarGroupStyles_unstable,
 * before being passed to renderAvatarGroup_unstable.
 *
 * @param props - props from this instance of AvatarGroup
 * @param ref - reference to root HTMLElement of AvatarGroup
 */
export declare const useAvatarGroup_unstable: (props: AvatarGroupProps, ref: React_2.Ref<HTMLElement>) => AvatarGroupState;

export declare const useAvatarGroupContext_unstable: <T>(selector: ContextSelector<AvatarGroupContextValue, T>) => T;

export declare const useAvatarGroupContextValues: (state: AvatarGroupState) => AvatarGroupContextValues;

/**
 * Create the state required to render AvatarGroupItem.
 *
 * The returned state can be modified with hooks such as useAvatarGroupItemStyles_unstable,
 * before being passed to renderAvatarGroupItem_unstable.
 *
 * @param props - props from this instance of AvatarGroupItem
 * @param ref - reference to root HTMLElement of AvatarGroupItem
 */
export declare const useAvatarGroupItem_unstable: (props: AvatarGroupItemProps, ref: React_2.Ref<HTMLElement>) => AvatarGroupItemState;

/**
 * Apply styling to the AvatarGroupItem slots based on the state
 */
export declare const useAvatarGroupItemStyles_unstable: (state: AvatarGroupItemState) => AvatarGroupItemState;

/**
 * Create the state required to render AvatarGroupPopover.
 *
 * The returned state can be modified with hooks such as useAvatarGroupPopoverStyles_unstable,
 * before being passed to renderAvatarGroupPopover_unstable.
 *
 * @param props - props from this instance of AvatarGroupPopover
 */
export declare const useAvatarGroupPopover_unstable: (props: AvatarGroupPopoverProps) => AvatarGroupPopoverState;

export declare const useAvatarGroupPopoverContextValues_unstable: (state: AvatarGroupPopoverState) => AvatarGroupContextValues;

/**
 * Apply styling to the AvatarGroupPopover slots based on the state
 */
export declare const useAvatarGroupPopoverStyles_unstable: (state: AvatarGroupPopoverState) => AvatarGroupPopoverState;

/**
 * Apply styling to the AvatarGroup slots based on the state
 */
export declare const useAvatarGroupStyles_unstable: (state: AvatarGroupState) => AvatarGroupState;

export declare const useAvatarStyles_unstable: (state: AvatarState) => AvatarState;

export { }
