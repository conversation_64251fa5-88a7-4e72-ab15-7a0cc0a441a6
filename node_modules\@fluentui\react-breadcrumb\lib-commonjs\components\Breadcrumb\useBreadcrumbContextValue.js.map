{"version": 3, "sources": ["../src/components/Breadcrumb/useBreadcrumbContextValue.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { BreadcrumbContextValues, BreadcrumbState } from './Breadcrumb.types';\n\nexport function useBreadcrumbContextValues_unstable(state: BreadcrumbState): BreadcrumbContextValues {\n  const { size } = state;\n  return React.useMemo(() => ({ size }), [size]);\n}\n"], "names": ["useBreadcrumbContextValues_unstable", "state", "size", "React", "useMemo"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAGgBA;;;eAAAA;;;;iEAHO;AAGhB,SAASA,oCAAoCC,KAAsB;IACxE,MAAM,EAAEC,IAAI,EAAE,GAAGD;IACjB,OAAOE,OAAMC,OAAO,CAAC,IAAO,CAAA;YAAEF;QAAK,CAAA,GAAI;QAACA;KAAK;AAC/C"}