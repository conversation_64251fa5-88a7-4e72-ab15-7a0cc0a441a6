{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["export { Badge, badgeClassNames, renderBadge_unstable, useBadgeStyles_unstable, useBadge_unstable } from './Badge';\nexport type { BadgeProps, BadgeSlots, BadgeState } from './Badge';\nexport {\n  PresenceBadge,\n  presenceBadgeClassNames,\n  usePresenceBadgeStyles_unstable,\n  usePresenceBadge_unstable,\n  presenceAwayRegular,\n  presenceAwayFilled,\n  presenceAvailableRegular,\n  presenceAvailableFilled,\n  presenceBlockedRegular,\n  presenceBusyFilled,\n  presenceDndRegular,\n  presenceDndFilled,\n  presenceOofRegular,\n  presenceOfflineRegular,\n  presenceUnknownRegular,\n} from './PresenceBadge';\nexport type { PresenceBadgeProps, PresenceBadgeState, PresenceBadgeStatus } from './PresenceBadge';\nexport {\n  CounterBadge,\n  counterBadgeClassNames,\n  useCounterBadgeStyles_unstable,\n  useCounterBadge_unstable,\n} from './CounterBadge';\nexport type { CounterBadgeProps, CounterBadgeState } from './CounterBadge';\n"], "names": ["Badge", "CounterBadge", "PresenceBadge", "badgeClassNames", "counterBadgeClassNames", "presenceAvailableFilled", "presenceAvailableRegular", "presenceAwayFilled", "presenceAwayRegular", "presenceBadgeClassNames", "presenceBlockedRegular", "presenceBusyFilled", "presenceDndFilled", "presenceDndRegular", "presenceOfflineRegular", "presenceOofRegular", "presenceUnknownRegular", "renderBadge_unstable", "useBadgeStyles_unstable", "useBadge_unstable", "useCounterBadgeStyles_unstable", "useCounterBadge_unstable", "usePresenceBadgeStyles_unstable", "usePresenceBadge_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAASA,KAAK;eAALA,YAAK;;IAqBZC,YAAY;eAAZA,0BAAY;;IAlBZC,aAAa;eAAbA,4BAAa;;IAHCC,eAAe;eAAfA,sBAAe;;IAsB7BC,sBAAsB;eAAtBA,oCAAsB;;IAZtBC,uBAAuB;eAAvBA,sCAAuB;;IADvBC,wBAAwB;eAAxBA,uCAAwB;;IADxBC,kBAAkB;eAAlBA,iCAAkB;;IADlBC,mBAAmB;eAAnBA,kCAAmB;;IAHnBC,uBAAuB;eAAvBA,sCAAuB;;IAOvBC,sBAAsB;eAAtBA,qCAAsB;;IACtBC,kBAAkB;eAAlBA,iCAAkB;;IAElBC,iBAAiB;eAAjBA,gCAAiB;;IADjBC,kBAAkB;eAAlBA,iCAAkB;;IAGlBC,sBAAsB;eAAtBA,qCAAsB;;IADtBC,kBAAkB;eAAlBA,iCAAkB;;IAElBC,sBAAsB;eAAtBA,qCAAsB;;IAjBSC,oBAAoB;eAApBA,2BAAoB;;IAAEC,uBAAuB;eAAvBA,8BAAuB;;IAAEC,iBAAiB;eAAjBA,wBAAiB;;IAuB/FC,8BAA8B;eAA9BA,4CAA8B;;IAC9BC,wBAAwB;eAAxBA,sCAAwB;;IAnBxBC,+BAA+B;eAA/BA,8CAA+B;;IAC/BC,yBAAyB;eAAzBA,wCAAyB;;;uBAN8E;+BAkBlG;8BAOA"}