{"version": 3, "sources": ["../src/components/CarouselCard/CarouselCard.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\nimport { useCarouselCard_unstable } from './useCarouselCard';\nimport { renderCarouselCard_unstable } from './renderCarouselCard';\nimport { useCarouselCardStyles_unstable } from './useCarouselCardStyles.styles';\nimport type { CarouselCardProps } from './CarouselCard.types';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\n\n/**\n * The defining wrapper of a carousel's indexed content, they will take up the full\n * viewport of CarouselSlider or div wrapper,\n * users may place multiple items within this Card if desired, with consideration of viewport width.\n *\n * Clickable actions within the content area are available via mouse and tab as expected,\n * non-active card content will be set to inert until moved to active card.\n */\nexport const CarouselCard: ForwardRefComponent<CarouselCardProps> = React.forwardRef((props, ref) => {\n  const state = useCarouselCard_unstable(props, ref);\n\n  useCarouselCardStyles_unstable(state);\n  useCustomStyleHook_unstable('useCarouselCardStyles_unstable')(state);\n\n  return renderCarouselCard_unstable(state);\n});\n\nCarouselCard.displayName = 'CarouselCard';\n"], "names": ["CarouselCard", "React", "forwardRef", "props", "ref", "state", "useCarouselCard_unstable", "useCarouselCardStyles_unstable", "useCustomStyleHook_unstable", "renderCarouselCard_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAgBaA;;;eAAAA;;;;iEAhBU;iCAEkB;oCACG;6CACG;qCAEH;AAUrC,MAAMA,eAAAA,WAAAA,GAAuDC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IAC3F,MAAMC,QAAQC,IAAAA,yCAAAA,EAAyBH,OAAOC;IAE9CG,IAAAA,2DAAAA,EAA+BF;IAC/BG,IAAAA,gDAAAA,EAA4B,kCAAkCH;IAE9D,OAAOI,IAAAA,+CAAAA,EAA4BJ;AACrC;AAEAL,aAAaU,WAAW,GAAG"}