"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    carouselButtonClassNames: function() {
        return carouselButtonClassNames;
    },
    useCarouselButtonStyles_unstable: function() {
        return useCarouselButtonStyles_unstable;
    }
});
const _react = require("@griffel/react");
const _reactbutton = require("@fluentui/react-button");
const carouselButtonClassNames = {
    root: 'fui-CarouselButton',
    icon: 'fui-CarouselButton__icon'
};
/**
 * Styles for the root slot
 */ const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    root: {
        B6of3ja: "fgr6219",
        jrapky: "f10jk5vf",
        sj55zd: "fkfq4zb",
        De3pzq: "fkfdr9r",
        Bkecrkj: "fc5wo7j",
        eoavqd: "f8491dx"
    }
}, {
    d: [
        ".fgr6219{margin-top:auto;}",
        ".f10jk5vf{margin-bottom:auto;}",
        ".fkfq4zb{color:var(--colorNeutralForeground2);}",
        ".fkfdr9r{background-color:var(--colorNeutralBackgroundAlpha);}",
        ".fc5wo7j{pointer-events:all;}"
    ],
    h: [
        ".f8491dx:hover{cursor:pointer;}"
    ]
});
const useCarouselButtonStyles_unstable = (state)=>{
    'use no memo';
    const styles = useStyles();
    state = {
        ...state,
        ...(0, _reactbutton.useButtonStyles_unstable)(state)
    };
    state.root.className = (0, _react.mergeClasses)(carouselButtonClassNames.root, styles.root, state.root.className);
    if (state.icon) {
        state.icon.className = (0, _react.mergeClasses)(carouselButtonClassNames.icon, state.icon.className);
    }
    return state;
};
