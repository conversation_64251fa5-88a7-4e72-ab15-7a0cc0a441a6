{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["export {\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  useARIAButtonShorthand,\n  useARIAButtonProps,\n} from './button/index';\nexport {\n  useActiveDescendant,\n  ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE,\n  ActiveDescendantContextProvider,\n  useActiveDescendantContext,\n  useHasParentActiveDescendantContext,\n} from './activedescendant';\nexport type {\n  ActiveDescendantImperativeRef,\n  ActiveDescendantOptions,\n  ActiveDescendantContextValue,\n  ActiveDescendantChangeEvent,\n} from './activedescendant';\nexport type {\n  ARIAButtonSlotProps,\n  ARIAButtonProps,\n  ARIAButtonResultProps,\n  ARIAButtonType,\n  ARIAButtonElement,\n  ARIAButtonElementIntersection,\n  ARIAButtonAlteredProps,\n} from './button/index';\n\nexport {\n  AriaLiveAnnouncer,\n  renderAriaLiveAnnouncer_unstable,\n  useAriaLiveAnnouncer_unstable,\n  useAriaLiveAnnouncerContextValues_unstable,\n} from './AriaLiveAnnouncer/index';\nexport type { AriaLiveAnnouncerProps, AriaLiveAnnouncerState } from './AriaLiveAnnouncer/index';\n"], "names": ["useARIAButtonShorthand", "useARIAButtonProps", "useActiveDescendant", "ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE", "ActiveDescendantContextProvider", "useActiveDescendantContext", "useHasParentActiveDescendantContext", "AriaLiveAnnouncer", "renderAriaLiveAnnouncer_unstable", "useAriaLiveAnnouncer_unstable", "useAriaLiveAnnouncerContextValues_unstable"], "rangeMappings": ";;;", "mappings": "AAAA,SACE,4DAA4D;AAC5DA,sBAAsB,EACtBC,kBAAkB,QACb,iBAAiB;AACxB,SACEC,mBAAmB,EACnBC,uCAAuC,EACvCC,+BAA+B,EAC/BC,0BAA0B,EAC1BC,mCAAmC,QAC9B,qBAAqB;AAiB5B,SACEC,iBAAiB,EACjBC,gCAAgC,EAChCC,6BAA6B,EAC7BC,0CAA0C,QACrC,4BAA4B"}