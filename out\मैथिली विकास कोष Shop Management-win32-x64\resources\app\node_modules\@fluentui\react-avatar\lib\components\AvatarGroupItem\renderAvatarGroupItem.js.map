{"version": 3, "sources": ["../src/components/AvatarGroupItem/renderAvatarGroupItem.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { AvatarGroupItemState, AvatarGroupItemSlots } from './AvatarGroupItem.types';\n\n/**\n * Render the final JSX of AvatarGroupItem\n */\nexport const renderAvatarGroupItem_unstable = (state: AvatarGroupItemState) => {\n  assertSlots<AvatarGroupItemSlots>(state);\n\n  return (\n    <state.root>\n      <state.avatar />\n      {state.isOverflowItem && <state.overflowLabel />}\n    </state.root>\n  );\n};\n"], "names": ["assertSlots", "renderAvatarGroupItem_unstable", "state", "root", "avatar", "isOverflowItem", "overflowLabel"], "rangeMappings": ";;;;;;;;;;;;", "mappings": "AAAA,0BAA0B,GAC1B,iDAAiD;AAEjD,SAASA,WAAW,QAAQ,4BAA4B;AAGxD;;CAEC,GACD,OAAO,MAAMC,iCAAiC,CAACC;IAC7CF,YAAkCE;IAElC,qBACE,MAACA,MAAMC,IAAI;;0BACT,KAACD,MAAME,MAAM;YACZF,MAAMG,cAAc,kBAAI,KAACH,MAAMI,aAAa;;;AAGnD,EAAE"}