{"version": 3, "sources": ["../src/keyCodes.ts"], "sourcesContent": ["export const Cancel = 3;\nexport const Help = 6;\nexport const Backspace = 8;\nexport const Tab = 9;\nexport const Clear = 12;\nexport const Enter = 13;\nexport const Shift = 16;\nexport const Control = 17;\nexport const Alt = 18;\nexport const Pause = 19;\nexport const CapsLock = 20;\nexport const Escape = 27;\nexport const Convert = 28;\nexport const NonConvert = 29;\nexport const Accept = 30;\nexport const ModeChange = 31;\nexport const Space = 32;\nexport const PageUp = 33;\nexport const PageDown = 34;\nexport const End = 35;\nexport const Home = 36;\nexport const ArrowLeft = 37;\nexport const ArrowUp = 38;\nexport const ArrowRight = 39;\nexport const ArrowDown = 40;\nexport const Select = 41;\nexport const Print = 42;\nexport const Execute = 43;\nexport const PrintScreen = 44;\nexport const Insert = 45;\nexport const Delete = 46;\nexport const Digit0 = 48;\nexport const RightParenthesis = 48;\nexport const Digit1 = 49;\nexport const ExclamationPoint = 49;\nexport const Digit2 = 50;\nexport const AtSign = 50;\nexport const Digit3 = 51;\nexport const PoundSign = 51;\nexport const Digit4 = 52;\nexport const DollarSign = 52;\nexport const Digit5 = 53;\nexport const PercentSign = 53;\nexport const Digit6 = 54;\nexport const Caret = 54;\nexport const Digit7 = 55;\nexport const Ampersand = 55;\nexport const Digit8 = 56;\nexport const MultiplicationSign = 56;\nexport const Digit9 = 57;\nexport const LeftParenthesis = 57;\nexport const a = 65;\nexport const A = 65;\nexport const b = 66;\nexport const B = 66;\nexport const c = 67;\nexport const C = 67;\nexport const d = 68;\nexport const D = 68;\nexport const e = 69;\nexport const E = 69;\nexport const f = 70;\nexport const F = 70;\nexport const g = 71;\nexport const G = 71;\nexport const h = 72;\nexport const H = 72;\nexport const i = 73;\nexport const I = 73;\nexport const j = 74;\nexport const J = 74;\nexport const k = 75;\nexport const K = 75;\nexport const l = 76;\nexport const L = 76;\nexport const m = 77;\nexport const M = 77;\nexport const n = 78;\nexport const N = 78;\nexport const o = 79;\nexport const O = 79;\nexport const p = 80;\nexport const P = 80;\nexport const q = 81;\nexport const Q = 81;\nexport const r = 82;\nexport const R = 82;\nexport const s = 83;\nexport const S = 83;\nexport const t = 84;\nexport const T = 84;\nexport const u = 85;\nexport const U = 85;\nexport const v = 86;\nexport const V = 86;\nexport const w = 87;\nexport const W = 87;\nexport const x = 88;\nexport const X = 88;\nexport const y = 89;\nexport const Y = 89;\nexport const z = 90;\nexport const Z = 90;\nexport const OS = 91;\nexport const ContextMenu = 93;\nexport const F1 = 112;\nexport const F2 = 113;\nexport const F3 = 114;\nexport const F4 = 115;\nexport const F5 = 116;\nexport const F6 = 117;\nexport const F7 = 118;\nexport const F8 = 119;\nexport const F9 = 120;\nexport const F10 = 121;\nexport const F11 = 122;\nexport const F12 = 123;\nexport const F13 = 124;\nexport const F14 = 125;\nexport const F15 = 126;\nexport const F16 = 127;\nexport const F17 = 128;\nexport const F18 = 129;\nexport const F19 = 130;\nexport const F20 = 131;\nexport const F21 = 132;\nexport const F22 = 133;\nexport const F23 = 134;\nexport const F24 = 135;\nexport const NumLock = 144;\nexport const ScrollLock = 145;\nexport const VolumeMute = 181;\nexport const VolumeDown = 182;\nexport const VolumeUp = 183;\nexport const Semicolon = 186;\nexport const EqualsSign = 187;\nexport const PlusSign = 187;\nexport const Comma = 188;\nexport const LeftAngleBracket = 188;\nexport const MinusSign = 189;\nexport const Underscore = 189;\nexport const Decimal = 190;\nexport const RightAngleBracket = 190;\nexport const ForwardSlash = 191;\nexport const QuestionMark = 191;\nexport const GraveAccent = 192;\nexport const Tilde = 192;\nexport const LeftSquareBracket = 219;\nexport const LeftCurlyBrace = 219;\nexport const BackSlash = 220;\nexport const Pipe = 220;\nexport const RightSquareBracket = 221;\nexport const RightCurlyBrace = 221;\nexport const SingleQuote = 222;\nexport const DoubleQuote = 222;\nexport const Meta = 224;\nexport const AltGraph = 225;\nexport const Attn = 246;\nexport const CrSel = 247;\nexport const ExSel = 248;\nexport const EraseEof = 249;\nexport const Play = 250;\nexport const ZoomOut = 251;\n"], "names": ["A", "Accept", "Alt", "AltGraph", "Ampersand", "ArrowDown", "ArrowLeft", "ArrowRight", "ArrowUp", "AtSign", "Attn", "B", "BackSlash", "Backspace", "C", "Cancel", "CapsLock", "<PERSON><PERSON>", "Clear", "Comma", "ContextMenu", "Control", "Convert", "CrSel", "D", "Decimal", "Delete", "Digit0", "Digit1", "Digit2", "Digit3", "Digit4", "Digit5", "Digit6", "Digit7", "Digit8", "Digit9", "DollarSign", "DoubleQuote", "E", "End", "Enter", "EqualsSign", "EraseEof", "Escape", "ExSel", "ExclamationPoint", "Execute", "F", "F1", "F10", "F11", "F12", "F13", "F14", "F15", "F16", "F17", "F18", "F19", "F2", "F20", "F21", "F22", "F23", "F24", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "ForwardSlash", "G", "GraveAccent", "H", "Help", "Home", "I", "Insert", "J", "K", "L", "LeftAngleBracket", "LeftCurlyBrace", "LeftParenthesis", "LeftSquareBracket", "M", "Meta", "MinusSign", "ModeChange", "MultiplicationSign", "N", "NonConvert", "NumLock", "O", "OS", "P", "PageDown", "PageUp", "Pause", "PercentSign", "<PERSON><PERSON>", "Play", "PlusSign", "PoundSign", "Print", "PrintScreen", "Q", "QuestionMark", "R", "RightAngleBracket", "RightCurlyBrace", "RightParenthesis", "RightSquareBracket", "S", "ScrollLock", "Select", "Semicolon", "Shift", "SingleQuote", "Space", "T", "Tab", "<PERSON><PERSON>", "U", "Underscore", "V", "VolumeDown", "VolumeMute", "VolumeUp", "W", "X", "Y", "Z", "ZoomOut", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAoDaA,CAAC;eAADA;;IAtCAC,MAAM;eAANA;;IANAC,GAAG;eAAHA;;IAoJAC,QAAQ;eAARA;;IA9GAC,SAAS;eAATA;;IAtBAC,SAAS;eAATA;;IAHAC,SAAS;eAATA;;IAEAC,UAAU;eAAVA;;IADAC,OAAO;eAAPA;;IAcAC,MAAM;eAANA;;IAyHAC,IAAI;eAAJA;;IAvGAC,CAAC;eAADA;;IA+FAC,SAAS;eAATA;;IAnJAC,SAAS;eAATA;;IAsDAC,CAAC;eAADA;;IAxDAC,MAAM;eAANA;;IAUAC,QAAQ;eAARA;;IAkCAC,KAAK;eAALA;;IAxCAC,KAAK;eAALA;;IAqIAC,KAAK;eAALA;;IAjCAC,WAAW;eAAXA;;IAjGAC,OAAO;eAAPA;;IAKAC,OAAO;eAAPA;;IAkJAC,KAAK;eAALA;;IApGAC,CAAC;eAADA;;IAmFAC,OAAO;eAAPA;;IA/GAC,MAAM;eAANA;;IACAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IAEAC,MAAM;eAANA;;IATAC,UAAU;eAAVA;;IAkHAC,WAAW;eAAXA;;IA9FAC,CAAC;eAADA;;IAzCAC,GAAG;eAAHA;;IAdAC,KAAK;eAALA;;IAkIAC,UAAU;eAAVA;;IAyBAC,QAAQ;eAARA;;IArJAC,MAAM;eAANA;;IAoJAC,KAAK;eAALA;;IA7HAC,gBAAgB;eAAhBA;;IAPAC,OAAO;eAAPA;;IAmCAC,CAAC;eAADA;;IA2CAC,EAAE;eAAFA;;IASAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IAjBAC,EAAE;eAAFA;;IAkBAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IACAC,GAAG;eAAHA;;IArBAC,EAAE;eAAFA;;IACAC,EAAE;eAAFA;;IACAC,EAAE;eAAFA;;IACAC,EAAE;eAAFA;;IACAC,EAAE;eAAFA;;IACAC,EAAE;eAAFA;;IACAC,EAAE;eAAFA;;IA8BAC,YAAY;eAAZA;;IA/EAC,CAAC;eAADA;;IAiFAC,WAAW;eAAXA;;IA/EAC,CAAC;eAADA;;IAjEAC,IAAI;eAAJA;;IAmBAC,IAAI;eAAJA;;IAgDAC,CAAC;eAADA;;IAvCAC,MAAM;eAANA;;IAyCAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAgEAC,gBAAgB;eAAhBA;;IAUAC,cAAc;eAAdA;;IAlGAC,eAAe;eAAfA;;IAiGAC,iBAAiB;eAAjBA;;IAvEAC,CAAC;eAADA;;IA+EAC,IAAI;eAAJA;;IAhBAC,SAAS;eAATA;;IA5HAC,UAAU;eAAVA;;IAiCAC,kBAAkB;eAAlBA;;IA8BAC,CAAC;eAADA;;IAjEAC,UAAU;eAAVA;;IAoHAC,OAAO;eAAPA;;IAjDAC,CAAC;eAADA;;IAuBAC,EAAE;eAAFA;;IArBAC,CAAC;eAADA;;IAhEAC,QAAQ;eAARA;;IADAC,MAAM;eAANA;;IARAC,KAAK;eAALA;;IAiCAC,WAAW;eAAXA;;IA4GAC,IAAI;eAAJA;;IAWAC,IAAI;eAAJA;;IAzBAC,QAAQ;eAARA;;IAlGAC,SAAS;eAATA;;IAZAC,KAAK;eAALA;;IAEAC,WAAW;eAAXA;;IAwDAC,CAAC;eAADA;;IA4DAC,YAAY;eAAZA;;IA1DAC,CAAC;eAADA;;IAwDAC,iBAAiB;eAAjBA;;IAUAC,eAAe;eAAfA;;IAxHAC,gBAAgB;eAAhBA;;IAuHAC,kBAAkB;eAAlBA;;IA/DAC,CAAC;eAADA;;IA0CAC,UAAU;eAAVA;;IAzGAC,MAAM;eAANA;;IA6GAC,SAAS;eAATA;;IAhIAC,KAAK;eAALA;;IAmJAC,WAAW;eAAXA;;IAzIAC,KAAK;eAALA;;IA0EAC,CAAC;eAADA;;IAvFAC,GAAG;eAAHA;;IA+IAC,KAAK;eAALA;;IAtDAC,CAAC;eAADA;;IAgDAC,UAAU;eAAVA;;IA9CAC,CAAC;eAADA;;IAsCAC,UAAU;eAAVA;;IADAC,UAAU;eAAVA;;IAEAC,QAAQ;eAARA;;IArCAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IA4DAC,OAAO;eAAPA;;IA/GAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;IAEAC,CAAC;eAADA;;;AArGN,MAAMnJ,SAAS;AACf,MAAM8D,OAAO;AACb,MAAMhE,YAAY;AAClB,MAAM+G,MAAM;AACZ,MAAM1G,QAAQ;AACd,MAAMuB,QAAQ;AACd,MAAM+E,QAAQ;AACd,MAAMnG,UAAU;AAChB,MAAMnB,MAAM;AACZ,MAAMmG,QAAQ;AACd,MAAMrF,WAAW;AACjB,MAAM4B,SAAS;AACf,MAAMtB,UAAU;AAChB,MAAMwE,aAAa;AACnB,MAAM7F,SAAS;AACf,MAAM0F,aAAa;AACnB,MAAM+B,QAAQ;AACd,MAAMtB,SAAS;AACf,MAAMD,WAAW;AACjB,MAAM3D,MAAM;AACZ,MAAMsC,OAAO;AACb,MAAMxE,YAAY;AAClB,MAAME,UAAU;AAChB,MAAMD,aAAa;AACnB,MAAMF,YAAY;AAClB,MAAMiH,SAAS;AACf,MAAMX,QAAQ;AACd,MAAM5D,UAAU;AAChB,MAAM6D,cAAc;AACpB,MAAM5B,SAAS;AACf,MAAMtD,SAAS;AACf,MAAMC,SAAS;AACf,MAAMuF,mBAAmB;AACzB,MAAMtF,SAAS;AACf,MAAMkB,mBAAmB;AACzB,MAAMjB,SAAS;AACf,MAAMpB,SAAS;AACf,MAAMqB,SAAS;AACf,MAAM4E,YAAY;AAClB,MAAM3E,SAAS;AACf,MAAMM,aAAa;AACnB,MAAML,SAAS;AACf,MAAMsE,cAAc;AACpB,MAAMrE,SAAS;AACf,MAAMhB,QAAQ;AACd,MAAMiB,SAAS;AACf,MAAM9B,YAAY;AAClB,MAAM+B,SAAS;AACf,MAAMyD,qBAAqB;AAC3B,MAAMxD,SAAS;AACf,MAAMkD,kBAAkB;AACxB,MAAMmD,IAAI;AACV,MAAMzI,IAAI;AACV,MAAM0I,IAAI;AACV,MAAM/H,IAAI;AACV,MAAMgI,IAAI;AACV,MAAM7H,IAAI;AACV,MAAM8H,IAAI;AACV,MAAMpH,IAAI;AACV,MAAMqH,IAAI;AACV,MAAMtG,IAAI;AACV,MAAMuG,IAAI;AACV,MAAM9F,IAAI;AACV,MAAM+F,IAAI;AACV,MAAMrE,IAAI;AACV,MAAMsE,IAAI;AACV,MAAMpE,IAAI;AACV,MAAMqE,IAAI;AACV,MAAMlE,IAAI;AACV,MAAMmE,IAAI;AACV,MAAMjE,IAAI;AACV,MAAMkE,IAAI;AACV,MAAMjE,IAAI;AACV,MAAMkE,IAAI;AACV,MAAMjE,IAAI;AACV,MAAMkE,IAAI;AACV,MAAM7D,IAAI;AACV,MAAM8D,IAAI;AACV,MAAMzD,IAAI;AACV,MAAM0D,IAAI;AACV,MAAMvD,IAAI;AACV,MAAMwD,IAAI;AACV,MAAMtD,IAAI;AACV,MAAMuD,IAAI;AACV,MAAM5C,IAAI;AACV,MAAM6C,IAAI;AACV,MAAM3C,IAAI;AACV,MAAM4C,IAAI;AACV,MAAMvC,IAAI;AACV,MAAMwC,IAAI;AACV,MAAMjC,IAAI;AACV,MAAMkC,IAAI;AACV,MAAM/B,IAAI;AACV,MAAMgC,IAAI;AACV,MAAM9B,IAAI;AACV,MAAM+B,IAAI;AACV,MAAM3B,IAAI;AACV,MAAM4B,IAAI;AACV,MAAM3B,IAAI;AACV,MAAM4B,IAAI;AACV,MAAM3B,IAAI;AACV,MAAM4B,IAAI;AACV,MAAM3B,IAAI;AACV,MAAMtC,KAAK;AACX,MAAM7E,cAAc;AACpB,MAAM6B,KAAK;AACX,MAAMW,KAAK;AACX,MAAMM,KAAK;AACX,MAAMC,KAAK;AACX,MAAMC,KAAK;AACX,MAAMC,KAAK;AACX,MAAMC,KAAK;AACX,MAAMC,KAAK;AACX,MAAMC,KAAK;AACX,MAAMtB,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAME,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAM8B,UAAU;AAChB,MAAMsB,aAAa;AACnB,MAAMa,aAAa;AACnB,MAAMD,aAAa;AACnB,MAAME,WAAW;AACjB,MAAMZ,YAAY;AAClB,MAAM7E,aAAa;AACnB,MAAM+D,WAAW;AACjB,MAAMtF,QAAQ;AACd,MAAMiE,mBAAmB;AACzB,MAAMM,YAAY;AAClB,MAAMqC,aAAa;AACnB,MAAMtG,UAAU;AAChB,MAAMuF,oBAAoB;AAC1B,MAAMvC,eAAe;AACrB,MAAMqC,eAAe;AACrB,MAAMnC,cAAc;AACpB,MAAMkD,QAAQ;AACd,MAAMtC,oBAAoB;AAC1B,MAAMF,iBAAiB;AACvB,MAAMzE,YAAY;AAClB,MAAM2F,OAAO;AACb,MAAMY,qBAAqB;AAC3B,MAAMF,kBAAkB;AACxB,MAAMQ,cAAc;AACpB,MAAMnF,cAAc;AACpB,MAAMmD,OAAO;AACb,MAAMtF,WAAW;AACjB,MAAMO,OAAO;AACb,MAAMa,QAAQ;AACd,MAAMsB,QAAQ;AACd,MAAMF,WAAW;AACjB,MAAM6D,OAAO;AACb,MAAMgC,UAAU"}