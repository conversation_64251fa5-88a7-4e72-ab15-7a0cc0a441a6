{"name": "@fluentui/react-avatar", "version": "9.8.5", "description": "React components for building Microsoft web experiences.", "main": "lib-commonjs/index.js", "module": "lib/index.js", "typings": "./dist/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/microsoft/fluentui"}, "license": "MIT", "devDependencies": {"@fluentui/react-provider": "*", "@fluentui/eslint-plugin": "*", "@fluentui/react-conformance": "*", "@fluentui/react-conformance-griffel": "*", "es6-weak-map": "^2.0.2", "@fluentui/scripts-api-extractor": "*", "@fluentui/scripts-cypress": "*"}, "dependencies": {"@fluentui/react-badge": "^9.3.2", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-popover": "^9.11.5", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.25.3", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-tooltip": "^9.7.5", "@fluentui/react-utilities": "^9.22.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}, "beachball": {"disallowedChangeTypes": ["major", "prerelease"]}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./lib-commonjs/index.js", "import": "./lib/index.js", "require": "./lib-commonjs/index.js"}, "./package.json": "./package.json"}, "files": ["*.md", "dist/*.d.ts", "lib", "lib-commonjs"]}