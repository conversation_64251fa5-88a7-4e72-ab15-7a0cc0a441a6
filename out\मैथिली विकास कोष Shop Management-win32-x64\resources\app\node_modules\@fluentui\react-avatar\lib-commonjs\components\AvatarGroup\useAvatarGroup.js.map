{"version": 3, "sources": ["../src/components/AvatarGroup/useAvatarGroup.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { getIntrinsicElementProps, slot } from '@fluentui/react-utilities';\nimport type { AvatarGroupProps, AvatarGroupState } from './AvatarGroup.types';\n\n/**\n * Create the state required to render AvatarGroup.\n *\n * The returned state can be modified with hooks such as useAvatarGroupStyles_unstable,\n * before being passed to renderAvatarGroup_unstable.\n *\n * @param props - props from this instance of AvatarGroup\n * @param ref - reference to root HTMLElement of AvatarGroup\n */\nexport const useAvatarGroup_unstable = (props: AvatarGroupProps, ref: React.Ref<HTMLElement>): AvatarGroupState => {\n  const { layout = 'spread', size = defaultAvatarGroupSize } = props;\n\n  const root = slot.always(\n    getIntrinsicElementProps(\n      'div',\n      {\n        role: 'group',\n        ...props,\n        // FIXME:\n        // `ref` is wrongly assigned to be `HTMLElement` instead of `HTMLDivElement`\n        // but since it would be a breaking change to fix it, we are casting ref to it's proper type\n        ref: ref as React.Ref<HTMLDivElement>,\n      },\n      ['size'],\n    ),\n    { elementType: 'div' },\n  );\n  return { layout, size, components: { root: 'div' }, root };\n};\n\nexport const defaultAvatarGroupSize = 32;\n"], "names": ["defaultAvatarGroupSize", "useAvatarGroup_unstable", "props", "ref", "layout", "size", "root", "slot", "always", "getIntrinsicElementProps", "role", "elementType", "components"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAkCaA,sBAAAA;eAAAA;;IArBAC,uBAAAA;eAAAA;;;;iEAbU;gCACwB;AAYxC,MAAMA,0BAA0B,CAACC,OAAyBC;IAC/D,MAAM,EAAEC,SAAS,QAAQ,EAAEC,OAAOL,sBAAsB,EAAE,GAAGE;IAE7D,MAAMI,OAAOC,oBAAAA,CAAKC,MAAM,CACtBC,IAAAA,wCAAAA,EACE,OACA;QACEC,MAAM;QACN,GAAGR,KAAK;QACR,SAAS;QACT,4EAA4E;QAC5E,4FAA4F;QAC5FC,KAAKA;IACP,GACA;QAAC;KAAO,GAEV;QAAEQ,aAAa;IAAM;IAEvB,OAAO;QAAEP;QAAQC;QAAMO,YAAY;YAAEN,MAAM;QAAM;QAAGA;IAAK;AAC3D;AAEO,MAAMN,yBAAyB"}