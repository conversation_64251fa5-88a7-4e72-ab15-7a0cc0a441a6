"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE: function() {
        return _activedescendant.ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE;
    },
    ActiveDescendantContextProvider: function() {
        return _activedescendant.ActiveDescendantContextProvider;
    },
    AriaLiveAnnouncer: function() {
        return _index1.AriaLiveAnnouncer;
    },
    renderAriaLiveAnnouncer_unstable: function() {
        return _index1.renderAriaLiveAnnouncer_unstable;
    },
    useARIAButtonProps: function() {
        return _index.useARIAButtonProps;
    },
    // eslint-disable-next-line @typescript-eslint/no-deprecated
    useARIAButtonShorthand: function() {
        return _index.useARIAButtonShorthand;
    },
    useActiveDescendant: function() {
        return _activedescendant.useActiveDescendant;
    },
    useActiveDescendantContext: function() {
        return _activedescendant.useActiveDescendantContext;
    },
    useAriaLiveAnnouncerContextValues_unstable: function() {
        return _index1.useAriaLiveAnnouncerContextValues_unstable;
    },
    useAriaLiveAnnouncer_unstable: function() {
        return _index1.useAriaLiveAnnouncer_unstable;
    },
    useHasParentActiveDescendantContext: function() {
        return _activedescendant.useHasParentActiveDescendantContext;
    }
});
const _index = require("./button/index");
const _activedescendant = require("./activedescendant");
const _index1 = require("./AriaLiveAnnouncer/index");
