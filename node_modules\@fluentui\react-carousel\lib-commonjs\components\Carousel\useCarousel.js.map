{"version": 3, "sources": ["../src/components/Carousel/useCarousel.ts"], "sourcesContent": ["import { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nimport {\n  getIntrinsicElementProps,\n  slot,\n  useEventCallback,\n  useIsomorphicLayoutEffect,\n  useMergedRefs,\n} from '@fluentui/react-utilities';\nimport * as React from 'react';\n\nimport type { CarouselProps, CarouselState } from './Carousel.types';\nimport type { CarouselContextValue } from '../CarouselContext.types';\nimport { useEmblaCarousel } from '../useEmblaCarousel';\nimport { useAnnounce } from '@fluentui/react-shared-contexts';\n\n/**\n * Create the state required to render Carousel.\n *\n * The returned state can be modified with hooks such as useCarouselStyles_unstable,\n * before being passed to renderCarousel_unstable.\n *\n * @param props - props from this instance of Carousel\n * @param ref - reference to root HTMLDivElement of Carousel\n */\nexport function useCarousel_unstable(props: CarouselProps, ref: React.Ref<HTMLDivElement>): CarouselState {\n  'use no memo';\n\n  const {\n    align = 'center',\n    circular = false,\n    onActiveIndexChange,\n    groupSize = 'auto',\n    draggable = false,\n    whitespace = false,\n    announcement,\n    motion = 'slide',\n    autoplayInterval = 4000,\n  } = props;\n\n  const { dir } = useFluent();\n  const { activeIndex, carouselApi, containerRef, viewportRef, subscribeForValues, enableAutoplay, resetAutoplay } =\n    useEmblaCarousel({\n      align,\n      direction: dir,\n      loop: circular,\n      slidesToScroll: groupSize,\n      defaultActiveIndex: props.defaultActiveIndex,\n      activeIndex: props.activeIndex,\n      watchDrag: draggable,\n      containScroll: whitespace ? false : 'keepSnaps',\n      motion,\n      onDragIndexChange: onActiveIndexChange,\n      onAutoplayIndexChange: onActiveIndexChange,\n      autoplayInterval,\n    });\n\n  const selectPageByElement: CarouselContextValue['selectPageByElement'] = useEventCallback((event, element, jump) => {\n    const foundIndex = carouselApi.scrollToElement(element, jump);\n    onActiveIndexChange?.(event, { event, type: 'focus', index: foundIndex });\n\n    return foundIndex;\n  });\n\n  const selectPageByIndex: CarouselContextValue['selectPageByIndex'] = useEventCallback((event, index, jump) => {\n    carouselApi.scrollToIndex(index, jump);\n\n    onActiveIndexChange?.(event, { event, type: 'click', index });\n  });\n\n  const selectPageByDirection: CarouselContextValue['selectPageByDirection'] = useEventCallback((event, direction) => {\n    const nextPageIndex = carouselApi.scrollInDirection(direction);\n    onActiveIndexChange?.(event, { event, type: 'click', index: nextPageIndex });\n\n    return nextPageIndex;\n  });\n\n  const mergedContainerRef = useMergedRefs(ref, containerRef);\n\n  // Announce carousel updates\n  const announcementTextRef = React.useRef<string>('');\n  const totalNavLength = React.useRef<number>(0);\n  const navGroupRef = React.useRef<number[][]>([]);\n\n  const { announce } = useAnnounce();\n\n  const updateAnnouncement = useEventCallback(() => {\n    if (totalNavLength.current <= 0 || !announcement) {\n      // Ignore announcements until slides discovered\n      return;\n    }\n\n    const announcementText = announcement(activeIndex, totalNavLength.current, navGroupRef.current);\n\n    if (announcementText !== announcementTextRef.current) {\n      announcementTextRef.current = announcementText;\n      announce(announcementText, { polite: true });\n    }\n  });\n\n  useIsomorphicLayoutEffect(() => {\n    // Subscribe to any non-index carousel state changes\n    return subscribeForValues(data => {\n      if (totalNavLength.current <= 0 && data.navItemsCount > 0 && announcement) {\n        const announcementText = announcement(data.activeIndex, data.navItemsCount, data.groupIndexList);\n        // Initialize our string to prevent updateAnnouncement from reading an initial load\n        announcementTextRef.current = announcementText;\n      }\n      totalNavLength.current = data.navItemsCount;\n      navGroupRef.current = data.groupIndexList;\n      updateAnnouncement();\n    });\n  }, [subscribeForValues, updateAnnouncement, announcement]);\n\n  useIsomorphicLayoutEffect(() => {\n    updateAnnouncement();\n  }, [activeIndex, updateAnnouncement]);\n\n  return {\n    components: {\n      root: 'div',\n    },\n    root: slot.always(\n      getIntrinsicElementProps('div', {\n        ref: mergedContainerRef,\n        role: 'region',\n        ...props,\n      }),\n      { elementType: 'div' },\n    ),\n\n    activeIndex,\n    circular,\n    containerRef: mergedContainerRef,\n    viewportRef,\n    selectPageByElement,\n    selectPageByDirection,\n    selectPageByIndex,\n    subscribeForValues,\n    enableAutoplay,\n    resetAutoplay,\n  };\n}\n"], "names": ["useCarousel_unstable", "props", "ref", "align", "circular", "onActiveIndexChange", "groupSize", "draggable", "whitespace", "announcement", "motion", "autoplayInterval", "dir", "useFluent", "activeIndex", "carousel<PERSON><PERSON>", "containerRef", "viewportRef", "subscribeForValues", "enableAutoplay", "resetAutoplay", "useEmblaCarousel", "direction", "loop", "slidesToScroll", "defaultActiveIndex", "watchDrag", "containScroll", "onDragIndexChange", "onAutoplayIndexChange", "selectPageByElement", "useEventCallback", "event", "element", "jump", "foundIndex", "scrollToElement", "type", "index", "selectPageByIndex", "scrollToIndex", "selectPageByDirection", "nextPageIndex", "scrollInDirection", "mergedContainerRef", "useMergedRefs", "announcementTextRef", "React", "useRef", "totalNavLength", "navGroupRef", "announce", "useAnnounce", "updateAnnouncement", "current", "announcementText", "polite", "useIsomorphicLayoutEffect", "data", "navItemsCount", "groupIndexList", "components", "root", "slot", "always", "getIntrinsicElementProps", "role", "elementType"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAwBgBA;;;eAAAA;;;;qCAxBgC;gCAOzC;iEACgB;kCAIU;AAY1B,SAASA,qBAAqBC,KAAoB,EAAEC,GAA8B;IACvF;IAEA,MAAM,EACJC,QAAQ,QAAQ,EAChBC,WAAW,KAAK,EAChBC,mBAAmB,EACnBC,YAAY,MAAM,EAClBC,YAAY,KAAK,EACjBC,aAAa,KAAK,EAClBC,YAAY,EACZC,SAAS,OAAO,EAChBC,mBAAmB,IAAI,EACxB,GAAGV;IAEJ,MAAM,EAAEW,GAAG,EAAE,GAAGC,IAAAA,uCAAAA;IAChB,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,aAAa,EAAE,GAC9GC,IAAAA,kCAAAA,EAAiB;QACflB;QACAmB,WAAWV;QACXW,MAAMnB;QACNoB,gBAAgBlB;QAChBmB,oBAAoBxB,MAAMwB,kBAAkB;QAC5CX,aAAab,MAAMa,WAAW;QAC9BY,WAAWnB;QACXoB,eAAenB,aAAa,QAAQ;QACpCE;QACAkB,mBAAmBvB;QACnBwB,uBAAuBxB;QACvBM;IACF;IAEF,MAAMmB,sBAAmEC,IAAAA,gCAAAA,EAAiB,CAACC,OAAOC,SAASC;QACzG,MAAMC,aAAapB,YAAYqB,eAAe,CAACH,SAASC;QACxD7B,wBAAAA,QAAAA,wBAAAA,KAAAA,IAAAA,KAAAA,IAAAA,oBAAsB2B,OAAO;YAAEA;YAAOK,MAAM;YAASC,OAAOH;QAAW;QAEvE,OAAOA;IACT;IAEA,MAAMI,oBAA+DR,IAAAA,gCAAAA,EAAiB,CAACC,OAAOM,OAAOJ;QACnGnB,YAAYyB,aAAa,CAACF,OAAOJ;QAEjC7B,wBAAAA,QAAAA,wBAAAA,KAAAA,IAAAA,KAAAA,IAAAA,oBAAsB2B,OAAO;YAAEA;YAAOK,MAAM;YAASC;QAAM;IAC7D;IAEA,MAAMG,wBAAuEV,IAAAA,gCAAAA,EAAiB,CAACC,OAAOV;QACpG,MAAMoB,gBAAgB3B,YAAY4B,iBAAiB,CAACrB;QACpDjB,wBAAAA,QAAAA,wBAAAA,KAAAA,IAAAA,KAAAA,IAAAA,oBAAsB2B,OAAO;YAAEA;YAAOK,MAAM;YAASC,OAAOI;QAAc;QAE1E,OAAOA;IACT;IAEA,MAAME,qBAAqBC,IAAAA,6BAAAA,EAAc3C,KAAKc;IAE9C,4BAA4B;IAC5B,MAAM8B,sBAAsBC,OAAMC,MAAM,CAAS;IACjD,MAAMC,iBAAiBF,OAAMC,MAAM,CAAS;IAC5C,MAAME,cAAcH,OAAMC,MAAM,CAAa,EAAE;IAE/C,MAAM,EAAEG,QAAQ,EAAE,GAAGC,IAAAA,gCAAAA;IAErB,MAAMC,qBAAqBtB,IAAAA,gCAAAA,EAAiB;QAC1C,IAAIkB,eAAeK,OAAO,IAAI,KAAK,CAAC7C,cAAc;YAChD,+CAA+C;YAC/C;QACF;QAEA,MAAM8C,mBAAmB9C,aAAaK,aAAamC,eAAeK,OAAO,EAAEJ,YAAYI,OAAO;QAE9F,IAAIC,qBAAqBT,oBAAoBQ,OAAO,EAAE;YACpDR,oBAAoBQ,OAAO,GAAGC;YAC9BJ,SAASI,kBAAkB;gBAAEC,QAAQ;YAAK;QAC5C;IACF;IAEAC,IAAAA,yCAAAA,EAA0B;QACxB,oDAAoD;QACpD,OAAOvC,mBAAmBwC,CAAAA;YACxB,IAAIT,eAAeK,OAAO,IAAI,KAAKI,KAAKC,aAAa,GAAG,KAAKlD,cAAc;gBACzE,MAAM8C,mBAAmB9C,aAAaiD,KAAK5C,WAAW,EAAE4C,KAAKC,aAAa,EAAED,KAAKE,cAAc;gBAC/F,mFAAmF;gBACnFd,oBAAoBQ,OAAO,GAAGC;YAChC;YACAN,eAAeK,OAAO,GAAGI,KAAKC,aAAa;YAC3CT,YAAYI,OAAO,GAAGI,KAAKE,cAAc;YACzCP;QACF;IACF,GAAG;QAACnC;QAAoBmC;QAAoB5C;KAAa;IAEzDgD,IAAAA,yCAAAA,EAA0B;QACxBJ;IACF,GAAG;QAACvC;QAAauC;KAAmB;IAEpC,OAAO;QACLQ,YAAY;YACVC,MAAM;QACR;QACAA,MAAMC,oBAAAA,CAAKC,MAAM,CACfC,IAAAA,wCAAAA,EAAyB,OAAO;YAC9B/D,KAAK0C;YACLsB,MAAM;YACN,GAAGjE,KAAK;QACV,IACA;YAAEkE,aAAa;QAAM;QAGvBrD;QACAV;QACAY,cAAc4B;QACd3B;QACAa;QACAW;QACAF;QACArB;QACAC;QACAC;IACF;AACF"}