{"version": 3, "sources": ["../src/utils/getInitials.ts"], "sourcesContent": ["/**\n * Regular expressions matching characters to ignore when calculating the initials.\n */\n\n/**\n * Regular expression matching characters within various types of enclosures, including the enclosures themselves\n *  so for example, (xyz) [xyz] {xyz} all would be ignored\n */\nconst UNWANTED_ENCLOSURES_REGEX: RegExp = /[\\(\\[\\{][^\\)\\]\\}]*[\\)\\]\\}]/g;\n\n/**\n * Regular expression matching special ASCII characters except space, plus some unicode special characters.\n * Applies after unwanted enclosures have been removed\n */\nconst UNWANTED_CHARS_REGEX: RegExp = /[\\0-\\u001F\\!-/:-@\\[-`\\{-\\u00BF\\u0250-\\u036F\\uD800-\\uFFFF]/g;\n\n/**\n * Regular expression matching phone numbers. Applied after chars matching UNWANTED_CHARS_REGEX have been removed\n * and number has been trimmed for whitespaces\n */\nconst PHONENUMBER_REGEX: RegExp = /^\\d+[\\d\\s]*(:?ext|x|)\\s*\\d+$/i;\n\n/** Regular expression matching one or more spaces. */\nconst MULTIPLE_WHITESPACES_REGEX: RegExp = /\\s+/g;\n\n/**\n * Regular expression matching languages for which we currently don't support initials.\n * Arabic:   Arabic, Arabic Supplement, Arabic Extended-A.\n * Korean:   Hangul Jamo, Hangul Compatibility Jamo, Hangul Jamo Extended-A, Hangul Syllables, Hangul Jamo Extended-B.\n * Japanese: Hiragana, Katakana.\n * CJK:      CJK Unified Ideographs Extension A, CJK Unified Ideographs, CJK Compatibility Ideographs,\n *             CJK Unified Ideographs Extension B\n */\nconst UNSUPPORTED_TEXT_REGEX: RegExp =\n  /[\\u0600-\\u06FF\\u0750-\\u077F\\u08A0-\\u08FF\\u1100-\\u11FF\\u3130-\\u318F\\uA960-\\uA97F\\uAC00-\\uD7AF\\uD7B0-\\uD7FF\\u3040-\\u309F\\u30A0-\\u30FF\\u3400-\\u4DBF\\u4E00-\\u9FFF\\uF900-\\uFAFF]|[\\uD840-\\uD869][\\uDC00-\\uDED6]/;\n\nfunction getInitialsLatin(displayName: string, isRtl: boolean, firstInitialOnly?: boolean): string {\n  let initials = '';\n\n  const splits: string[] = displayName.split(' ');\n  if (splits.length !== 0) {\n    initials += splits[0].charAt(0).toUpperCase();\n  }\n\n  if (!firstInitialOnly) {\n    if (splits.length === 2) {\n      initials += splits[1].charAt(0).toUpperCase();\n    } else if (splits.length === 3) {\n      initials += splits[2].charAt(0).toUpperCase();\n    }\n  }\n\n  if (isRtl && initials.length > 1) {\n    return initials.charAt(1) + initials.charAt(0);\n  }\n\n  return initials;\n}\n\nfunction cleanupDisplayName(displayName: string): string {\n  displayName = displayName.replace(UNWANTED_ENCLOSURES_REGEX, '');\n  displayName = displayName.replace(UNWANTED_CHARS_REGEX, '');\n  displayName = displayName.replace(MULTIPLE_WHITESPACES_REGEX, ' ');\n  displayName = displayName.trim();\n\n  return displayName;\n}\n\n/**\n * Get (up to 2 characters) initials based on display name of the persona.\n *\n * @param displayName - The full name of the person or entity\n * @param isRtl - Whether the display is in RTL\n * @param options - Extra options to control the behavior of getInitials\n *\n * @returns The 1 or 2 character initials based on the name. Or an empty string if no initials\n * could be derived from the name.\n *\n * @internal\n */\nexport function getInitials(\n  displayName: string | undefined | null,\n  isRtl: boolean,\n  options?: {\n    /** Should initials be generated from phone numbers (default false) */\n    allowPhoneInitials?: boolean;\n\n    /** Returns only the first initial */\n    firstInitialOnly?: boolean;\n  },\n): string {\n  if (!displayName) {\n    return '';\n  }\n\n  displayName = cleanupDisplayName(displayName);\n\n  // For names containing CJK characters, and phone numbers, we don't display initials\n  if (\n    UNSUPPORTED_TEXT_REGEX.test(displayName) ||\n    (!options?.allowPhoneInitials && PHONENUMBER_REGEX.test(displayName))\n  ) {\n    return '';\n  }\n\n  return getInitialsLatin(displayName, isRtl, options?.firstInitialOnly);\n}\n"], "names": ["getInitials", "UNWANTED_ENCLOSURES_REGEX", "UNWANTED_CHARS_REGEX", "PHONENUMBER_REGEX", "MULTIPLE_WHITESPACES_REGEX", "UNSUPPORTED_TEXT_REGEX", "getInitialsLatin", "displayName", "isRtl", "firstInitialOnly", "initials", "splits", "split", "length", "char<PERSON>t", "toUpperCase", "cleanupDisplayName", "replace", "trim", "options", "test", "allowPhoneInitials"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;;+BAyEeA;;;eAAAA;;;AAxEhB,MAAMC,4BAAoC;AAE1C;;;CAGC,GACD,MAAMC,uBAA+B;AAErC;;;CAGC,GACD,MAAMC,oBAA4B;AAElC,oDAAoD,GACpD,MAAMC,6BAAqC;AAE3C;;;;;;;CAOC,GACD,MAAMC,yBACJ;AAEF,SAASC,iBAAiBC,WAAmB,EAAEC,KAAc,EAAEC,gBAA0B;IACvF,IAAIC,WAAW;IAEf,MAAMC,SAAmBJ,YAAYK,KAAK,CAAC;IAC3C,IAAID,OAAOE,MAAM,KAAK,GAAG;QACvBH,YAAYC,MAAM,CAAC,EAAE,CAACG,MAAM,CAAC,GAAGC,WAAW;IAC7C;IAEA,IAAI,CAACN,kBAAkB;QACrB,IAAIE,OAAOE,MAAM,KAAK,GAAG;YACvBH,YAAYC,MAAM,CAAC,EAAE,CAACG,MAAM,CAAC,GAAGC,WAAW;QAC7C,OAAO,IAAIJ,OAAOE,MAAM,KAAK,GAAG;YAC9BH,YAAYC,MAAM,CAAC,EAAE,CAACG,MAAM,CAAC,GAAGC,WAAW;QAC7C;IACF;IAEA,IAAIP,SAASE,SAASG,MAAM,GAAG,GAAG;QAChC,OAAOH,SAASI,MAAM,CAAC,KAAKJ,SAASI,MAAM,CAAC;IAC9C;IAEA,OAAOJ;AACT;AAEA,SAASM,mBAAmBT,WAAmB;IAC7CA,cAAcA,YAAYU,OAAO,CAAChB,2BAA2B;IAC7DM,cAAcA,YAAYU,OAAO,CAACf,sBAAsB;IACxDK,cAAcA,YAAYU,OAAO,CAACb,4BAA4B;IAC9DG,cAAcA,YAAYW,IAAI;IAE9B,OAAOX;AACT;AAcO,SAASP,YACdO,WAAsC,EACtCC,KAAc,EACdW,OAMC;IAED,IAAI,CAACZ,aAAa;QAChB,OAAO;IACT;IAEAA,cAAcS,mBAAmBT;IAEjC,oFAAoF;IACpF,IACEF,uBAAuBe,IAAI,CAACb,gBAC3B,CAACY,CAAAA,YAAAA,QAAAA,YAAAA,KAAAA,IAAAA,KAAAA,IAAAA,QAASE,kBAAkB,AAAlBA,KAAsBlB,kBAAkBiB,IAAI,CAACb,cACxD;QACA,OAAO;IACT;IAEA,OAAOD,iBAAiBC,aAAaC,OAAOW,YAAAA,QAAAA,YAAAA,KAAAA,IAAAA,KAAAA,IAAAA,QAASV,gBAAgB;AACvE"}