"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AvatarGroupItem: function() {
        return _index.AvatarGroupItem;
    },
    avatarGroupItemClassNames: function() {
        return _index.avatarGroupItemClassNames;
    },
    renderAvatarGroupItem_unstable: function() {
        return _index.renderAvatarGroupItem_unstable;
    },
    useAvatarGroupItemStyles_unstable: function() {
        return _index.useAvatarGroupItemStyles_unstable;
    },
    useAvatarGroupItem_unstable: function() {
        return _index.useAvatarGroupItem_unstable;
    },
    useGroupChildClassName: function() {
        return _index.useGroupChildClassName;
    }
});
const _index = require("./components/AvatarGroupItem/index");
