"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "renderAlert_unstable", {
    enumerable: true,
    get: function() {
        return renderAlert_unstable;
    }
});
const _jsxruntime = require("@fluentui/react-jsx-runtime/jsx-runtime");
const _reactutilities = require("@fluentui/react-utilities");
const renderAlert_unstable = (state)=>{
    // eslint-disable-next-line deprecation/deprecation
    (0, _reactutilities.assertSlots)(state);
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(state.root, {
        children: [
            state.icon && /*#__PURE__*/ (0, _jsxruntime.jsx)(state.icon, {}),
            state.avatar && /*#__PURE__*/ (0, _jsxruntime.jsx)(state.avatar, {}),
            state.root.children,
            state.action && /*#__PURE__*/ (0, _jsxruntime.jsx)(state.action, {})
        ]
    });
};
