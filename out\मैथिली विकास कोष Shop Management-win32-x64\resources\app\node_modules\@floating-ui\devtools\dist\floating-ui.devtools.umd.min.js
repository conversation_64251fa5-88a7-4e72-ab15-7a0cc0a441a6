!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIDevtools={})}(this,(function(e){"use strict";const t="__FUIDT_CONTROLLER__",n="__FUIDT_ELEMENT_METADATA__",o="__FUIDT_SERIALIZED_DATA_CHANGE__";function l(e,t){var n,o;const l=e;return Boolean((null==l||null==(n=l.ownerDocument)?void 0:n.defaultView)&&l instanceof l.ownerDocument.defaultView[null!=(o=null==t?void 0:t.constructorName)?o:"HTMLElement"])}const r=e=>Boolean(l(e)&&n in e&&null!==e.parentElement),i=e=>{let{defaultView:l}=e;l&&(l[t]||(l[t]=(e=>{let t=null;const l=new MutationObserver((e=>{if(t)for(const n of e)"childList"===n.type&&Array.from(n.removedNodes).includes(t)&&i.withdraw()})),i={get selectedElement(){return t},select:e=>(r(e)&&(t=e,l.observe(e.parentElement,{childList:!0,subtree:!1})),t&&e&&t[n].references.has(e)||i.withdraw(),t),withdraw:()=>{t=null,l.disconnect(),e.postMessage(o)}};return i})(l)))};let s=0;const a=()=>{const e=new Map,t=new WeakMap;return{add:n=>{if(t.has(n))return t.get(n);const o="__FUIDT_HTML_ELEMENT_REFERENCE__:"+s++;return e.set(o,n),t.set(n,o),o},get:n=>{const o=e.get(n);if(o&&t.has(o))return o},has:e=>t.has(e)}},d=function(e,s){return void 0===e&&(e=document),void 0===s&&(s=u),{name:"@floating-ui/devtools",fn:d=>{const{[n]:u}=r(d.elements.floating)?d.elements.floating:Object.assign(d.elements.floating,{[n]:{references:a(),serializedData:[]}}),f=(c=s(d),_=u.references,JSON.parse(JSON.stringify(c,((e,t)=>l(t)?_.add(t):"object"==typeof t&&t&&Object.getPrototypeOf(t)!==Object.prototype&&Object.getPrototypeOf(t)!==Array.prototype?"toString"in t?t.toString():void 0:t))));var c,_;u.serializedData.unshift(f);const p=(e=>{var n,o;return i(e),null!=(n=null==(o=e.defaultView)?void 0:o[t])?n:null})(e);var g;u.serializedData.length>1&&d.elements.floating===(null==p?void 0:p.selectedElement)&&(null==(g=e.defaultView)||g.postMessage(o));return{}}}},u=e=>({...e,type:"FloatingUIMiddleware"});e.devtools=d,e.middleware=d}));
