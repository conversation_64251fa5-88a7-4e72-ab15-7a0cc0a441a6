{"version": 3, "sources": ["../src/components/CarouselNavImageButton/CarouselNavImageButton.types.ts"], "sourcesContent": ["import { ARIAButtonSlotProps } from '@fluentui/react-aria';\nimport type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\n\nexport type CarouselNavImageButtonSlots = {\n  /**\n   * ARIA compliant nav buttons used to jump to pages\n   */\n  root: NonNullable<Slot<ARIAButtonSlotProps>>;\n  /**\n   * Required: The image within the button\n   */\n  image: NonNullable<Slot<'img'>>;\n};\n\n/**\n * CarouselNavImageButton Props\n */\nexport type CarouselNavImageButtonProps = ComponentProps<CarouselNavImageButtonSlots> & {};\n\n/**\n * State used in rendering CarouselNavImageButton\n */\nexport type CarouselNavImageButtonState = ComponentState<CarouselNavImageButtonSlots> & {\n  /**\n   * Enables selection state control\n   */\n  selected?: boolean;\n};\n"], "names": [], "rangeMappings": ";;", "mappings": "AAmBA;;CAEC"}