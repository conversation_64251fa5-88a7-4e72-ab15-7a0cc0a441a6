{"version": 3, "sources": ["useCarouselNavStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses, shorthands } from '@griffel/react';\nimport { createCustomFocusIndicatorStyle } from '@fluentui/react-tabster';\nimport { tokens } from '@fluentui/react-theme';\nexport const carouselNavClassNames = {\n    root: 'fui-CarouselNav'\n};\n/**\n * Styles for the root slot\n */ const useStyles = makeStyles({\n    root: {\n        display: 'flex',\n        flexDirection: 'row',\n        alignItems: 'center',\n        justifyContent: 'center',\n        pointerEvents: 'all',\n        ...createCustomFocusIndicatorStyle({\n            outline: `${tokens.strokeWidthThick} solid ${tokens.colorStrokeFocus2}`,\n            borderRadius: tokens.borderRadiusMedium,\n            ...shorthands.borderColor('transparent')\n        }),\n        borderRadius: tokens.borderRadiusXLarge,\n        margin: `auto ${tokens.spacingHorizontalS}`,\n        backgroundColor: tokens.colorNeutralBackgroundAlpha\n    }\n});\n/**\n * Apply styling to the CarouselNav slots based on the state\n */ export const useCarouselNavStyles_unstable = (state)=>{\n    'use no memo';\n    const styles = useStyles();\n    state.root.className = mergeClasses(carouselNavClassNames.root, styles.root, state.root.className);\n    return state;\n};\n"], "names": ["carouselNavClassNames", "useCarouselNavStyles_unstable", "root", "useStyles", "__styles", "mc9l5x", "Beiy3e4", "Bt984gj", "Brf1p80", "Bkecrkj", "Bfpq7zp", "g9k6zt", "Bn4voq9", "giviqs", "Bw81rd7", "kdpuga", "dm238s", "B6xbmo0", "B3whbx2", "B8q5s1w", "Bci5o5g", "n8qw10", "Bdrgwmp", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "j<PERSON><PERSON>", "Frg6f3", "t21cq0", "B6of3ja", "B74szlk", "De3pzq", "d", "p", "state", "styles", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAGaA,qBAAqB;eAArBA;;IAwBIC,6BAA6B;eAA7BA;;;uBA3BoC;AAG9C,MAAMD,wBAAwB;IACjCE,MAAM;AACV;AACA;;CAEA,GAAI,MAAMC,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAF,MAAA;QAAAG,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;KAAA;AAAA;AAmBX,MAAMlC,gCAAiCmC,CAAAA;IAC9C;IACA,MAAMC,SAASlC;IACfiC,MAAMlC,IAAI,CAACoC,SAAS,GAAGC,IAAAA,mBAAY,EAACvC,sBAAsBE,IAAI,EAAEmC,OAAOnC,IAAI,EAAEkC,MAAMlC,IAAI,CAACoC,SAAS;IACjG,OAAOF;AACX"}