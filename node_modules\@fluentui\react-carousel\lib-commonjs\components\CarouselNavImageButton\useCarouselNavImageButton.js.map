{"version": 3, "sources": ["../src/components/CarouselNavImageButton/useCarouselNavImageButton.ts"], "sourcesContent": ["import { type ARIAButtonElement, type ARIAButtonSlotProps, useARIAButtonProps } from '@fluentui/react-aria';\nimport {\n  getIntrinsicElementProps,\n  isHTMLElement,\n  slot,\n  useEventCallback,\n  useIsomorphicLayoutEffect,\n  useMergedRefs,\n} from '@fluentui/react-utilities';\nimport { useTabsterAttributes } from '@fluentui/react-tabster';\nimport * as React from 'react';\n\nimport { useCarouselNavIndexContext } from '../CarouselNav/CarouselNavIndexContext';\nimport { useCarouselContext_unstable as useCarouselContext } from '../CarouselContext';\nimport type { CarouselNavImageButtonProps, CarouselNavImageButtonState } from './CarouselNavImageButton.types';\n\n/**\n * Create the state required to render CarouselNavImageButton.\n *\n * The returned state can be modified with hooks such as useCarouselNavImageButtonStyles_unstable,\n * before being passed to renderCarouselNavImageButton_unstable.\n *\n * @param props - props from this instance of CarouselNavImageButton\n * @param ref - reference to root HTMLButtonElement | HTMLAnchorElement of CarouselNavImageButton\n */\nexport const useCarouselNavImageButton_unstable = (\n  props: CarouselNavImageButtonProps,\n  ref: React.Ref<ARIAButtonElement>,\n): CarouselNavImageButtonState => {\n  const { onClick, as = 'button' } = props;\n\n  const index = useCarouselNavIndexContext();\n  const selectPageByIndex = useCarouselContext(ctx => ctx.selectPageByIndex);\n  const selected = useCarouselContext(ctx => ctx.activeIndex === index);\n  const subscribeForValues = useCarouselContext(ctx => ctx.subscribeForValues);\n\n  const handleClick: ARIAButtonSlotProps['onClick'] = useEventCallback(event => {\n    onClick?.(event);\n\n    if (!event.defaultPrevented && isHTMLElement(event.target)) {\n      selectPageByIndex(event, index);\n    }\n  });\n\n  const defaultTabProps = useTabsterAttributes({\n    focusable: { isDefault: selected },\n  });\n\n  const buttonRef = React.useRef<HTMLElement>();\n  const _carouselButton = slot.always<ARIAButtonSlotProps>(\n    getIntrinsicElementProps(as, useARIAButtonProps(props.as, props)),\n    {\n      elementType: 'button',\n      defaultProps: {\n        ref: useMergedRefs(ref, buttonRef),\n        role: 'tab',\n        type: 'button',\n        'aria-selected': selected,\n        ...defaultTabProps,\n      },\n    },\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    return subscribeForValues(data => {\n      const controlList = data.groupIndexList?.[index] ?? [];\n      const _controlledSlideIds = controlList\n        .map((slideIndex: number) => {\n          return data.slideNodes[slideIndex].id;\n        })\n        .join(' ');\n      if (buttonRef.current) {\n        buttonRef.current.setAttribute('aria-controls', _controlledSlideIds);\n      }\n    });\n  }, [subscribeForValues, index]);\n\n  // Override onClick\n  _carouselButton.onClick = handleClick;\n\n  const image = slot.always(\n    getIntrinsicElementProps('img', {\n      'aria-hidden': true, // Hidden as button is responsible for navigation description\n      alt: '',\n      role: 'presentation',\n      ...props.image,\n    }),\n    { elementType: 'img' },\n  );\n\n  return {\n    components: {\n      root: 'button',\n      image: 'img',\n    },\n    root: _carouselButton,\n    image,\n    selected,\n  };\n};\n"], "names": ["useCarouselNavImageButton_unstable", "props", "ref", "onClick", "as", "index", "useCarouselNavIndexContext", "selectPageByIndex", "useCarouselContext", "ctx", "selected", "activeIndex", "subscribeForValues", "handleClick", "useEventCallback", "event", "defaultPrevented", "isHTMLElement", "target", "defaultTabProps", "useTabsterAttributes", "focusable", "isDefault", "buttonRef", "React", "useRef", "_carousel<PERSON><PERSON>on", "slot", "always", "getIntrinsicElementProps", "useARIAButtonProps", "elementType", "defaultProps", "useMergedRefs", "role", "type", "useIsomorphicLayoutEffect", "data", "controlList", "groupIndexList", "_controlledSlideIds", "map", "slideIndex", "slideNodes", "id", "join", "current", "setAttribute", "image", "alt", "components", "root"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAyBaA;;;eAAAA;;;;2BAzBwE;gCAQ9E;8BAC8B;iEACd;yCAEoB;iCACuB;AAY3D,MAAMA,qCAAqC,CAChDC,OACAC;IAEA,MAAM,EAAEC,OAAO,EAAEC,KAAK,QAAQ,EAAE,GAAGH;IAEnC,MAAMI,QAAQC,IAAAA,mDAAAA;IACd,MAAMC,oBAAoBC,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIF,iBAAiB;IACzE,MAAMG,WAAWF,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIE,WAAW,KAAKN;IAC/D,MAAMO,qBAAqBJ,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIG,kBAAkB;IAE3E,MAAMC,cAA8CC,IAAAA,gCAAAA,EAAiBC,CAAAA;QACnEZ,YAAAA,QAAAA,YAAAA,KAAAA,IAAAA,KAAAA,IAAAA,QAAUY;QAEV,IAAI,CAACA,MAAMC,gBAAgB,IAAIC,IAAAA,6BAAAA,EAAcF,MAAMG,MAAM,GAAG;YAC1DX,kBAAkBQ,OAAOV;QAC3B;IACF;IAEA,MAAMc,kBAAkBC,IAAAA,kCAAAA,EAAqB;QAC3CC,WAAW;YAAEC,WAAWZ;QAAS;IACnC;IAEA,MAAMa,YAAYC,OAAMC,MAAM;IAC9B,MAAMC,kBAAkBC,oBAAAA,CAAKC,MAAM,CACjCC,IAAAA,wCAAAA,EAAyBzB,IAAI0B,IAAAA,6BAAAA,EAAmB7B,MAAMG,EAAE,EAAEH,SAC1D;QACE8B,aAAa;QACbC,cAAc;YACZ9B,KAAK+B,IAAAA,6BAAAA,EAAc/B,KAAKqB;YACxBW,MAAM;YACNC,MAAM;YACN,iBAAiBzB;YACjB,GAAGS,eAAe;QACpB;IACF;IAGFiB,IAAAA,yCAAAA,EAA0B;QACxB,OAAOxB,mBAAmByB,CAAAA;gBACJA;gBAAAA;YAApB,MAAMC,cAAcD,CAAAA,6BAAAA,CAAAA,uBAAAA,KAAKE,cAAc,AAAdA,MAAc,QAAnBF,yBAAAA,KAAAA,IAAAA,KAAAA,IAAAA,oBAAqB,CAAChC,MAAM,AAANA,MAAM,QAA5BgC,+BAAAA,KAAAA,IAAAA,6BAAgC,EAAE;YACtD,MAAMG,sBAAsBF,YACzBG,GAAG,CAAC,CAACC;gBACJ,OAAOL,KAAKM,UAAU,CAACD,WAAW,CAACE,EAAE;YACvC,GACCC,IAAI,CAAC;YACR,IAAItB,UAAUuB,OAAO,EAAE;gBACrBvB,UAAUuB,OAAO,CAACC,YAAY,CAAC,iBAAiBP;YAClD;QACF;IACF,GAAG;QAAC5B;QAAoBP;KAAM;IAE9B,mBAAmB;IACnBqB,gBAAgBvB,OAAO,GAAGU;IAE1B,MAAMmC,QAAQrB,oBAAAA,CAAKC,MAAM,CACvBC,IAAAA,wCAAAA,EAAyB,OAAO;QAC9B,eAAe;QACfoB,KAAK;QACLf,MAAM;QACN,GAAGjC,MAAM+C,KAAK;IAChB,IACA;QAAEjB,aAAa;IAAM;IAGvB,OAAO;QACLmB,YAAY;YACVC,MAAM;YACNH,OAAO;QACT;QACAG,MAAMzB;QACNsB;QACAtC;IACF;AACF"}