{"version": 3, "sources": ["useBadgeStyles.styles.js"], "sourcesContent": ["import { shorthands, makeResetStyles, makeStyles, mergeClasses } from '@griffel/react';\nimport { tokens, typographyStyles } from '@fluentui/react-theme';\nexport const badgeClassNames = {\n    root: 'fui-Badge',\n    icon: 'fui-Badge__icon'\n};\n// The text content of the badge has additional horizontal padding, but there is no `text` slot to add that padding to.\n// Instead, add extra padding to the root, and a negative margin on the icon to \"remove\" the extra padding on the icon.\nconst textPadding = tokens.spacingHorizontalXXS;\nconst useRootClassName = makeResetStyles({\n    display: 'inline-flex',\n    boxSizing: 'border-box',\n    alignItems: 'center',\n    justifyContent: 'center',\n    position: 'relative',\n    ...typographyStyles.caption1Strong,\n    height: '20px',\n    minWidth: '20px',\n    padding: `0 calc(${tokens.spacingHorizontalXS} + ${textPadding})`,\n    borderRadius: tokens.borderRadiusCircular,\n    // Use a transparent stroke (rather than no border) so the border is visible in high contrast\n    borderColor: tokens.colorTransparentStroke,\n    '::after': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        bottom: 0,\n        right: 0,\n        borderStyle: 'solid',\n        borderColor: 'inherit',\n        borderWidth: tokens.strokeWidthThin,\n        borderRadius: 'inherit'\n    }\n});\nconst useRootStyles = makeStyles({\n    fontSmallToTiny: {\n        ...typographyStyles.caption2Strong\n    },\n    // size\n    tiny: {\n        width: '6px',\n        height: '6px',\n        fontSize: '4px',\n        lineHeight: '4px',\n        minWidth: 'unset',\n        padding: 'unset'\n    },\n    'extra-small': {\n        width: '10px',\n        height: '10px',\n        fontSize: '6px',\n        lineHeight: '6px',\n        minWidth: 'unset',\n        padding: 'unset'\n    },\n    small: {\n        minWidth: '16px',\n        height: '16px',\n        padding: `0 calc(${tokens.spacingHorizontalXXS} + ${textPadding})`\n    },\n    medium: {\n    },\n    large: {\n        minWidth: '24px',\n        height: '24px',\n        padding: `0 calc(${tokens.spacingHorizontalXS} + ${textPadding})`\n    },\n    'extra-large': {\n        minWidth: '32px',\n        height: '32px',\n        padding: `0 calc(${tokens.spacingHorizontalSNudge} + ${textPadding})`\n    },\n    // shape\n    square: {\n        borderRadius: tokens.borderRadiusNone\n    },\n    rounded: {\n        borderRadius: tokens.borderRadiusMedium\n    },\n    roundedSmallToTiny: {\n        borderRadius: tokens.borderRadiusSmall\n    },\n    circular: {\n    },\n    // hide the boder when appearance is \"ghost\"\n    borderGhost: {\n        // The border is applied in an ::after pseudo-element because it should not affect layout.\n        // The padding and size of the badge should be the same regardless of whether or not it has a border.\n        '::after': {\n            display: 'none'\n        }\n    },\n    // appearance: filled\n    filled: {\n    },\n    'filled-brand': {\n        backgroundColor: tokens.colorBrandBackground,\n        color: tokens.colorNeutralForegroundOnBrand\n    },\n    'filled-danger': {\n        backgroundColor: tokens.colorPaletteRedBackground3,\n        color: tokens.colorNeutralForegroundOnBrand\n    },\n    'filled-important': {\n        backgroundColor: tokens.colorNeutralForeground1,\n        color: tokens.colorNeutralBackground1\n    },\n    'filled-informative': {\n        backgroundColor: tokens.colorNeutralBackground5,\n        color: tokens.colorNeutralForeground3\n    },\n    'filled-severe': {\n        backgroundColor: tokens.colorPaletteDarkOrangeBackground3,\n        color: tokens.colorNeutralForegroundOnBrand\n    },\n    'filled-subtle': {\n        backgroundColor: tokens.colorNeutralBackground1,\n        color: tokens.colorNeutralForeground1\n    },\n    'filled-success': {\n        backgroundColor: tokens.colorPaletteGreenBackground3,\n        color: tokens.colorNeutralForegroundOnBrand\n    },\n    'filled-warning': {\n        backgroundColor: tokens.colorPaletteYellowBackground3,\n        color: tokens.colorNeutralForeground1Static\n    },\n    // appearance: ghost\n    ghost: {\n    },\n    'ghost-brand': {\n        color: tokens.colorBrandForeground1\n    },\n    'ghost-danger': {\n        color: tokens.colorPaletteRedForeground3\n    },\n    'ghost-important': {\n        color: tokens.colorNeutralForeground1\n    },\n    'ghost-informative': {\n        color: tokens.colorNeutralForeground3\n    },\n    'ghost-severe': {\n        color: tokens.colorPaletteDarkOrangeForeground3\n    },\n    'ghost-subtle': {\n        color: tokens.colorNeutralForegroundStaticInverted\n    },\n    'ghost-success': {\n        color: tokens.colorPaletteGreenForeground3\n    },\n    'ghost-warning': {\n        color: tokens.colorPaletteYellowForeground2\n    },\n    // appearance: outline\n    outline: {\n        ...shorthands.borderColor('currentColor')\n    },\n    'outline-brand': {\n        color: tokens.colorBrandForeground1\n    },\n    'outline-danger': {\n        color: tokens.colorPaletteRedForeground3,\n        ...shorthands.borderColor(tokens.colorPaletteRedBorder2)\n    },\n    'outline-important': {\n        color: tokens.colorNeutralForeground3,\n        ...shorthands.borderColor(tokens.colorNeutralStrokeAccessible)\n    },\n    'outline-informative': {\n        color: tokens.colorNeutralForeground3,\n        ...shorthands.borderColor(tokens.colorNeutralStroke2)\n    },\n    'outline-severe': {\n        color: tokens.colorPaletteDarkOrangeForeground3\n    },\n    'outline-subtle': {\n        color: tokens.colorNeutralForegroundStaticInverted\n    },\n    'outline-success': {\n        color: tokens.colorPaletteGreenForeground3,\n        ...shorthands.borderColor(tokens.colorPaletteGreenBorder2)\n    },\n    'outline-warning': {\n        color: tokens.colorPaletteYellowForeground2\n    },\n    // appearance: tint\n    tint: {\n    },\n    'tint-brand': {\n        backgroundColor: tokens.colorBrandBackground2,\n        color: tokens.colorBrandForeground2,\n        ...shorthands.borderColor(tokens.colorBrandStroke2)\n    },\n    'tint-danger': {\n        backgroundColor: tokens.colorPaletteRedBackground1,\n        color: tokens.colorPaletteRedForeground1,\n        ...shorthands.borderColor(tokens.colorPaletteRedBorder1)\n    },\n    'tint-important': {\n        backgroundColor: tokens.colorNeutralForeground3,\n        color: tokens.colorNeutralBackground1,\n        ...shorthands.borderColor(tokens.colorTransparentStroke)\n    },\n    'tint-informative': {\n        backgroundColor: tokens.colorNeutralBackground4,\n        color: tokens.colorNeutralForeground3,\n        ...shorthands.borderColor(tokens.colorNeutralStroke2)\n    },\n    'tint-severe': {\n        backgroundColor: tokens.colorPaletteDarkOrangeBackground1,\n        color: tokens.colorPaletteDarkOrangeForeground1,\n        ...shorthands.borderColor(tokens.colorPaletteDarkOrangeBorder1)\n    },\n    'tint-subtle': {\n        backgroundColor: tokens.colorNeutralBackground1,\n        color: tokens.colorNeutralForeground3,\n        ...shorthands.borderColor(tokens.colorNeutralStroke2)\n    },\n    'tint-success': {\n        backgroundColor: tokens.colorPaletteGreenBackground1,\n        color: tokens.colorPaletteGreenForeground1,\n        ...shorthands.borderColor(tokens.colorPaletteGreenBorder1)\n    },\n    'tint-warning': {\n        backgroundColor: tokens.colorPaletteYellowBackground1,\n        color: tokens.colorPaletteYellowForeground1,\n        ...shorthands.borderColor(tokens.colorPaletteYellowBorder1)\n    }\n});\nconst useIconRootClassName = makeResetStyles({\n    display: 'flex',\n    lineHeight: '1',\n    margin: `0 calc(-1 * ${textPadding})`,\n    fontSize: '12px'\n});\nconst useIconStyles = makeStyles({\n    beforeText: {\n        marginRight: `calc(${tokens.spacingHorizontalXXS} + ${textPadding})`\n    },\n    afterText: {\n        marginLeft: `calc(${tokens.spacingHorizontalXXS} + ${textPadding})`\n    },\n    beforeTextXL: {\n        marginRight: `calc(${tokens.spacingHorizontalXS} + ${textPadding})`\n    },\n    afterTextXL: {\n        marginLeft: `calc(${tokens.spacingHorizontalXS} + ${textPadding})`\n    },\n    // size\n    tiny: {\n        fontSize: '6px'\n    },\n    'extra-small': {\n        fontSize: '10px'\n    },\n    small: {\n        fontSize: '12px'\n    },\n    medium: {\n    },\n    large: {\n        fontSize: '16px'\n    },\n    'extra-large': {\n        fontSize: '20px'\n    }\n});\n/**\n * Applies style classnames to slots\n */ export const useBadgeStyles_unstable = (state)=>{\n    'use no memo';\n    const rootClassName = useRootClassName();\n    const rootStyles = useRootStyles();\n    const smallToTiny = state.size === 'small' || state.size === 'extra-small' || state.size === 'tiny';\n    state.root.className = mergeClasses(badgeClassNames.root, rootClassName, smallToTiny && rootStyles.fontSmallToTiny, rootStyles[state.size], rootStyles[state.shape], state.shape === 'rounded' && smallToTiny && rootStyles.roundedSmallToTiny, state.appearance === 'ghost' && rootStyles.borderGhost, rootStyles[state.appearance], rootStyles[`${state.appearance}-${state.color}`], state.root.className);\n    const iconRootClassName = useIconRootClassName();\n    const iconStyles = useIconStyles();\n    if (state.icon) {\n        let iconPositionClass;\n        if (state.root.children) {\n            if (state.size === 'extra-large') {\n                iconPositionClass = state.iconPosition === 'after' ? iconStyles.afterTextXL : iconStyles.beforeTextXL;\n            } else {\n                iconPositionClass = state.iconPosition === 'after' ? iconStyles.afterText : iconStyles.beforeText;\n            }\n        }\n        state.icon.className = mergeClasses(badgeClassNames.icon, iconRootClassName, iconPositionClass, iconStyles[state.size], state.icon.className);\n    }\n    return state;\n};\n"], "names": ["badgeClassNames", "useBadgeStyles_unstable", "root", "icon", "textPadding", "tokens", "spacingHorizontalXXS", "useRootClassName", "__resetStyles", "useRootStyles", "__styles", "fontSmallToTiny", "Bahqtrf", "Be2twd7", "Bhrd7zp", "Bg96gwp", "tiny", "a9b677", "Bqenvij", "Bf4jedk", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "small", "medium", "large", "square", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "rounded", "roundedSmallToTiny", "circular", "borderGhost", "ap17g6", "filled", "De3pzq", "sj55zd", "ghost", "outline", "g2u3we", "h3c5rm", "B9xav0g", "zhjwy3", "tint", "d", "p", "useIconRootClassName", "useIconStyles", "beforeText", "t21cq0", "afterText", "Frg6f3", "beforeTextXL", "afterTextXL", "state", "rootClassName", "rootStyles", "smallToTiny", "size", "className", "mergeClasses", "shape", "appearance", "color", "iconRootClassName", "iconStyles", "iconPositionClass", "children", "iconPosition"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAEaA,eAAe;eAAfA;;IA6QIC,uBAAuB;eAAvBA;;;uBA/QqD;4BAC7B;AAClC,MAAMD,kBAAkB;IAC3BE,MAAM;IACNC,MAAM;AACV;AACA,uHAAA;AACA,uHAAA;AACA,MAAMC,cAAcC,kBAAM,CAACC,oBAAoB;AAC/C,MAAMC,mBAAgB,WAAA,GAAGC,IAAAA,oBAAA,EAAA,WAAA,YAAA;IAAA;IAAA;IAAA;IAAA;CAyBxB;AACD,MAAMC,gBAAa,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAC,iBAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAAC,MAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAL,SAAA;QAAAE,SAAA;QAAAI,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAA,eAAA;QAAAP,QAAA;QAAAC,SAAA;QAAAL,SAAA;QAAAE,SAAA;QAAAI,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAAC,OAAA;QAAAN,SAAA;QAAAD,SAAA;QAAAE,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAAE,QAAA,CAAA;IAAAC,OAAA;QAAAR,SAAA;QAAAD,SAAA;QAAAE,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAA,eAAA;QAAAL,SAAA;QAAAD,SAAA;QAAAE,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAAI,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAAC,SAAA;QAAAL,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAAE,oBAAA;QAAAN,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAAG,UAAA,CAAA;IAAAC,aAAA;QAAAC,QAAA;IAAA;IAAAC,QAAA,CAAA;IAAA,gBAAA;QAAAC,QAAA;QAAAC,QAAA;IAAA;IAAA,iBAAA;QAAAD,QAAA;QAAAC,QAAA;IAAA;IAAA,oBAAA;QAAAD,QAAA;QAAAC,QAAA;IAAA;IAAA,sBAAA;QAAAD,QAAA;QAAAC,QAAA;IAAA;IAAA,iBAAA;QAAAD,QAAA;QAAAC,QAAA;IAAA;IAAA,iBAAA;QAAAD,QAAA;QAAAC,QAAA;IAAA;IAAA,kBAAA;QAAAD,QAAA;QAAAC,QAAA;IAAA;IAAA,kBAAA;QAAAD,QAAA;QAAAC,QAAA;IAAA;IAAAC,OAAA,CAAA;IAAA,eAAA;QAAAD,QAAA;IAAA;IAAA,gBAAA;QAAAA,QAAA;IAAA;IAAA,mBAAA;QAAAA,QAAA;IAAA;IAAA,qBAAA;QAAAA,QAAA;IAAA;IAAA,gBAAA;QAAAA,QAAA;IAAA;IAAA,gBAAA;QAAAA,QAAA;IAAA;IAAA,iBAAA;QAAAA,QAAA;IAAA;IAAA,iBAAA;QAAAA,QAAA;IAAA;IAAAE,SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,iBAAA;QAAAN,QAAA;IAAA;IAAA,kBAAA;QAAAA,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,qBAAA;QAAAN,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,uBAAA;QAAAN,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,kBAAA;QAAAN,QAAA;IAAA;IAAA,kBAAA;QAAAA,QAAA;IAAA;IAAA,mBAAA;QAAAA,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,mBAAA;QAAAN,QAAA;IAAA;IAAAO,MAAA,CAAA;IAAA,cAAA;QAAAR,QAAA;QAAAC,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,eAAA;QAAAP,QAAA;QAAAC,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,kBAAA;QAAAP,QAAA;QAAAC,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,oBAAA;QAAAP,QAAA;QAAAC,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,eAAA;QAAAP,QAAA;QAAAC,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,eAAA;QAAAP,QAAA;QAAAC,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,gBAAA;QAAAP,QAAA;QAAAC,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAA,gBAAA;QAAAP,QAAA;QAAAC,QAAA;QAAAG,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;AAAA,GAAA;IAAAE,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AAoMtB,MAAMC,uBAAoB,WAAA,GAAG3C,IAAAA,oBAAA,EAAA,WAAA,MAAA;IAAA;CAK5B;AACD,MAAM4C,gBAAa,WAAA,GAAG1C,IAAAA,eAAA,EAAA;IAAA2C,YAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAC,WAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAC,cAAA;QAAAH,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAI,aAAA;QAAAF,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAxC,MAAA;QAAAH,SAAA;IAAA;IAAA,eAAA;QAAAA,SAAA;IAAA;IAAAY,OAAA;QAAAZ,SAAA;IAAA;IAAAa,QAAA,CAAA;IAAAC,OAAA;QAAAd,SAAA;IAAA;IAAA,eAAA;QAAAA,SAAA;IAAA;AAAA,GAAA;IAAAoC,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AAkCX,MAAMhD,0BAA2B0D,CAAAA;IACxC;IACA,MAAMC,gBAAgBrD;IACtB,MAAMsD,aAAapD;IACnB,MAAMqD,cAAcH,MAAMI,IAAI,KAAK,WAAWJ,MAAMI,IAAI,KAAK,iBAAiBJ,MAAMI,IAAI,KAAK;IAC7FJ,MAAMzD,IAAI,CAAC8D,SAAS,GAAGC,IAAAA,mBAAY,EAACjE,gBAAgBE,IAAI,EAAE0D,eAAeE,eAAeD,WAAWlD,eAAe,EAAEkD,UAAU,CAACF,MAAMI,IAAI,CAAC,EAAEF,UAAU,CAACF,MAAMO,KAAK,CAAC,EAAEP,MAAMO,KAAK,KAAK,aAAaJ,eAAeD,WAAW1B,kBAAkB,EAAEwB,MAAMQ,UAAU,KAAK,WAAWN,WAAWxB,WAAW,EAAEwB,UAAU,CAACF,MAAMQ,UAAU,CAAC,EAAEN,UAAU,CAAC,CAAA,EAAGF,MAAMQ,UAAU,CAAA,CAAA,EAAIR,MAAMS,KAAK,CAAA,CAAE,CAAC,EAAET,MAAMzD,IAAI,CAAC8D,SAAS;IAC5Y,MAAMK,oBAAoBlB;IAC1B,MAAMmB,aAAalB;IACnB,IAAIO,MAAMxD,IAAI,EAAE;QACZ,IAAIoE;QACJ,IAAIZ,MAAMzD,IAAI,CAACsE,QAAQ,EAAE;YACrB,IAAIb,MAAMI,IAAI,KAAK,eAAe;gBAC9BQ,oBAAoBZ,MAAMc,YAAY,KAAK,UAAUH,WAAWZ,WAAW,GAAGY,WAAWb,YAAY;YACzG,OAAO;gBACHc,oBAAoBZ,MAAMc,YAAY,KAAK,UAAUH,WAAWf,SAAS,GAAGe,WAAWjB,UAAU;YACrG;QACJ;QACAM,MAAMxD,IAAI,CAAC6D,SAAS,GAAGC,IAAAA,mBAAY,EAACjE,gBAAgBG,IAAI,EAAEkE,mBAAmBE,mBAAmBD,UAAU,CAACX,MAAMI,IAAI,CAAC,EAAEJ,MAAMxD,IAAI,CAAC6D,SAAS;IAChJ;IACA,OAAOL;AACX"}