"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "Avatar", {
    enumerable: true,
    get: function() {
        return Avatar;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _renderAvatar = require("./renderAvatar");
const _useAvatar = require("./useAvatar");
const _reactsharedcontexts = require("@fluentui/react-shared-contexts");
const _useAvatarStylesstyles = require("./useAvatarStyles.styles");
const Avatar = /*#__PURE__*/ _react.forwardRef((props, ref)=>{
    const state = (0, _useAvatar.useAvatar_unstable)(props, ref);
    (0, _useAvatarStylesstyles.useAvatarStyles_unstable)(state);
    (0, _reactsharedcontexts.useCustomStyleHook_unstable)('useAvatarStyles_unstable')(state);
    return (0, _renderAvatar.renderAvatar_unstable)(state);
});
Avatar.displayName = 'Avatar';
