{"version": 3, "sources": ["../src/AvatarGroupItem.ts"], "sourcesContent": ["export type {\n  AvatarGroupItemProps,\n  AvatarGroupItemSlots,\n  AvatarGroupItemState,\n} from './components/AvatarGroupItem/index';\nexport {\n  AvatarGroupItem,\n  avatarGroupItemClassNames,\n  renderAvatarGroupItem_unstable,\n  useAvatarGroupItemStyles_unstable,\n  useAvatarGroupItem_unstable,\n  useGroupChildClassName,\n} from './components/AvatarGroupItem/index';\n"], "names": ["AvatarGroupItem", "avatarGroupItemClassNames", "renderAvatarGroupItem_unstable", "useAvatarGroupItemStyles_unstable", "useAvatarGroupItem_unstable", "useGroupChildClassName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAMEA,eAAe;eAAfA,sBAAe;;IACfC,yBAAyB;eAAzBA,gCAAyB;;IACzBC,8BAA8B;eAA9BA,qCAA8B;;IAC9BC,iCAAiC;eAAjCA,wCAAiC;;IACjCC,2BAA2B;eAA3BA,kCAA2B;;IAC3BC,sBAAsB;eAAtBA,6BAAsB;;;uBACjB"}