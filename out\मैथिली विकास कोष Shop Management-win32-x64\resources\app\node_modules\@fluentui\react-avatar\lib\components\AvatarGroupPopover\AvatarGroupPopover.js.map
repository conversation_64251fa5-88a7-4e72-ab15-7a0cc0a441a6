{"version": 3, "sources": ["../src/components/AvatarGroupPopover/AvatarGroupPopover.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAvatarGroupPopover_unstable } from './renderAvatarGroupPopover';\nimport { useAvatarGroupPopoverContextValues_unstable } from './useAvatarGroupPopoverContextValues';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAvatarGroupPopover_unstable } from './useAvatarGroupPopover';\nimport { useAvatarGroupPopoverStyles_unstable } from './useAvatarGroupPopoverStyles.styles';\nimport type { AvatarGroupPopoverProps } from './AvatarGroupPopover.types';\n\n/**\n * The AvatarGroupPopover component provides a button with a Popover containing the children provided.\n */\nexport const AvatarGroupPopover: React.FC<AvatarGroupPopoverProps> = props => {\n  const state = useAvatarGroupPopover_unstable(props);\n  const contextValues = useAvatarGroupPopoverContextValues_unstable(state);\n\n  useAvatarGroupPopoverStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAvatarGroupPopoverStyles_unstable')(state);\n\n  return renderAvatarGroupPopover_unstable(state, contextValues);\n};\n\nAvatarGroupPopover.displayName = 'AvatarGroupPopover';\n"], "names": ["React", "renderAvatarGroupPopover_unstable", "useAvatarGroupPopoverContextValues_unstable", "useCustomStyleHook_unstable", "useAvatarGroupPopover_unstable", "useAvatarGroupPopoverStyles_unstable", "AvatarGroupPopover", "props", "state", "contextValues", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,iCAAiC,QAAQ,6BAA6B;AAC/E,SAASC,2CAA2C,QAAQ,uCAAuC;AACnG,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,8BAA8B,QAAQ,0BAA0B;AACzE,SAASC,oCAAoC,QAAQ,uCAAuC;AAG5F;;CAEC,GACD,OAAO,MAAMC,qBAAwDC,CAAAA;IACnE,MAAMC,QAAQJ,+BAA+BG;IAC7C,MAAME,gBAAgBP,4CAA4CM;IAElEH,qCAAqCG;IAErCL,4BAA4B,wCAAwCK;IAEpE,OAAOP,kCAAkCO,OAAOC;AAClD,EAAE;AAEFH,mBAAmBI,WAAW,GAAG"}