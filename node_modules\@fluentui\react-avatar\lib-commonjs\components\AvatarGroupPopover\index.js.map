{"version": 3, "sources": ["../src/components/AvatarGroupPopover/index.ts"], "sourcesContent": ["export { AvatarGroupPopover } from './AvatarGroupPopover';\nexport type {\n  AvatarGroupPopoverProps,\n  AvatarGroupPopoverSlots,\n  AvatarGroupPopoverState,\n} from './AvatarGroupPopover.types';\nexport { renderAvatarGroupPopover_unstable } from './renderAvatarGroupPopover';\nexport { useAvatarGroupPopover_unstable } from './useAvatarGroupPopover';\nexport {\n  avatarGroupPopoverClassNames,\n  useAvatarGroupPopoverStyles_unstable,\n} from './useAvatarGroupPopoverStyles.styles';\nexport { useAvatarGroupPopoverContextValues_unstable } from './useAvatarGroupPopoverContextValues';\n"], "names": ["AvatarGroupPopover", "avatarGroupPopoverClassNames", "renderAvatarGroupPopover_unstable", "useAvatarGroupPopoverContextValues_unstable", "useAvatarGroupPopoverStyles_unstable", "useAvatarGroupPopover_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAASA,kBAAkB;eAAlBA,sCAAkB;;IASzBC,4BAA4B;eAA5BA,+DAA4B;;IAHrBC,iCAAiC;eAAjCA,2DAAiC;;IAMjCC,2CAA2C;eAA3CA,+EAA2C;;IAFlDC,oCAAoC;eAApCA,uEAAoC;;IAH7BC,8BAA8B;eAA9BA,qDAA8B;;;oCAPJ;0CAMe;uCACH;mDAIxC;oDACqD"}