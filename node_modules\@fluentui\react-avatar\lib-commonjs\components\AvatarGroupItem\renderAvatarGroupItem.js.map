{"version": 3, "sources": ["../src/components/AvatarGroupItem/renderAvatarGroupItem.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { AvatarGroupItemState, AvatarGroupItemSlots } from './AvatarGroupItem.types';\n\n/**\n * Render the final JSX of AvatarGroupItem\n */\nexport const renderAvatarGroupItem_unstable = (state: AvatarGroupItemState) => {\n  assertSlots<AvatarGroupItemSlots>(state);\n\n  return (\n    <state.root>\n      <state.avatar />\n      {state.isOverflowItem && <state.overflowLabel />}\n    </state.root>\n  );\n};\n"], "names": ["renderAvatarGroupItem_unstable", "state", "assertSlots", "_jsxs", "root", "_jsx", "avatar", "isOverflowItem", "overflowLabel"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BASaA;;;eAAAA;;;4BARb;gCAE4B;AAMrB,MAAMA,iCAAiC,CAACC;IAC7CC,IAAAA,2BAAAA,EAAkCD;IAElC,OAAA,WAAA,GACEE,IAAAA,gBAAA,EAACF,MAAMG,IAAI,EAAA;;0BACTC,IAAAA,eAAA,EAACJ,MAAMK,MAAM,EAAA,CAAA;YACZL,MAAMM,cAAc,IAAA,WAAA,GAAIF,IAAAA,eAAA,EAACJ,MAAMO,aAAa,EAAA,CAAA;;;AAGnD"}