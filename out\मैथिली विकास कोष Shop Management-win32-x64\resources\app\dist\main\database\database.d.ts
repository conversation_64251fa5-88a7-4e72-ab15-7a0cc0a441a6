import Database from 'better-sqlite3';
import { QueryResult, PaginatedResult, SearchFilters, SortOptions } from '../../shared/types/database';
/**
 * Database service for Maithili Vikas Kosh Shop Management System
 * Uses better-sqlite3 for high performance SQLite operations
 */
export declare class DatabaseService {
    private db;
    private dbPath;
    private schemaPath;
    private isInitialized;
    constructor();
    /**
     * Initialize the database connection and run migrations
     */
    initialize(): Promise<void>;
    /**
     * Run database migrations
     */
    private runMigrations;
    /**
     * Get database connection (ensure initialized)
     */
    private getDb;
    /**
     * Execute a query and return results
     */
    query<T = any>(sql: string, params?: any[]): QueryResult<T[]>;
    /**
     * Execute a query and return first result
     */
    queryOne<T = any>(sql: string, params?: any[]): QueryResult<T>;
    /**
     * Execute an insert/update/delete query
     */
    execute(sql: string, params?: any[]): QueryResult<Database.RunResult>;
    /**
     * Execute multiple queries in a transaction
     */
    transaction(queries: Array<{
        sql: string;
        params?: any[];
    }>): QueryResult<any>;
    /**
     * Get paginated results
     */
    paginate<T = any>(sql: string, params?: any[], page?: number, limit?: number): QueryResult<PaginatedResult<T>>;
    /**
     * Build WHERE clause from search filters
     */
    buildWhereClause(filters: SearchFilters): {
        where: string;
        params: any[];
    };
    /**
     * Build ORDER BY clause from sort options
     */
    buildOrderClause(sort?: SortOptions): string;
    /**
     * Close database connection
     */
    close(): void;
    /**
     * Get database statistics
     */
    getStats(): QueryResult<any>;
    /**
     * Get database file size
     */
    private getDbSize;
}
export declare const databaseService: DatabaseService;
//# sourceMappingURL=database.d.ts.map