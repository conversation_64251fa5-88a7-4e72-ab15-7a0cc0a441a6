/// <reference types="react" />

import type { ARIAButtonSlotProps } from '@fluentui/react-aria';
import type { ComponentProps } from '@fluentui/react-utilities';
import type { ComponentState } from '@fluentui/react-utilities';
import { ContextSelector } from '@fluentui/react-context-selector';
import { FC } from 'react';
import type { ForwardRefComponent } from '@fluentui/react-utilities';
import type { PresenceMotionSlotProps } from '@fluentui/react-motion';
import { Provider } from 'react';
import { ProviderProps } from 'react';
import * as React_2 from 'react';
import type { Slot } from '@fluentui/react-utilities';
import type { SlotClassNames } from '@fluentui/react-utilities';

/**
 * Define a styled Accordion, using the `useAccordion_unstable` and `useAccordionStyles_unstable` hooks.
 */
export declare const Accordion: ForwardRefComponent<AccordionProps> & (<TItem>(props: AccordionProps<TItem>) => JSX.Element);

export declare const accordionClassNames: SlotClassNames<AccordionSlots>;

export declare type AccordionContextValue<Value = AccordionItemValue> = {
    /**
     * The list of opened panels by index
     */
    openItems: AccordionItemValue[];
    /**
     * Callback used by AccordionItem to request a change on it's own opened state
     * Should be used to toggle AccordionItem
     */
    requestToggle: (data: AccordionRequestToggleData<Value>) => void;
    collapsible: boolean;
    multiple: boolean;
    navigation: 'linear' | 'circular' | undefined;
};

export declare type AccordionContextValues = {
    accordion: AccordionContextValue;
};

/**
 * Define a styled AccordionHeader, using the `useAccordionHeader_unstable` and `useAccordionHeaderStyles_unstable`
 * hooks.
 */
export declare const AccordionHeader: ForwardRefComponent<AccordionHeaderProps>;

export declare const accordionHeaderClassNames: SlotClassNames<AccordionHeaderSlots>;

export declare type AccordionHeaderContextValue = {
    disabled: boolean;
    open: boolean;
    expandIconPosition: AccordionHeaderExpandIconPosition;
    size: AccordionHeaderSize;
};

export declare type AccordionHeaderContextValues = {
    accordionHeader: AccordionHeaderContextValue;
};

export declare type AccordionHeaderExpandIconPosition = 'start' | 'end';

export declare type AccordionHeaderProps = ComponentProps<Partial<AccordionHeaderSlots>> & {
    /**
     * The position of the expand  icon slot in heading.
     */
    expandIconPosition?: AccordionHeaderExpandIconPosition;
    /**
     * Indicates if the AccordionHeader should be rendered inline.
     */
    inline?: boolean;
    /**
     * Size of spacing in the heading.
     */
    size?: AccordionHeaderSize;
};

export declare const AccordionHeaderProvider: React_2.Provider<AccordionHeaderContextValue>;

export declare type AccordionHeaderSize = 'small' | 'medium' | 'large' | 'extra-large';

export declare type AccordionHeaderSlots = {
    /**
     * The element wrapping the button. By default this is a div, but can be a heading.
     */
    root: NonNullable<Slot<'div', 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'>>;
    /**
     * The component to be used as button in heading
     */
    button: NonNullable<Slot<ARIAButtonSlotProps<'a'>>>;
    /**
     * Expand icon slot rendered before (or after) children content in heading.
     */
    expandIcon?: Slot<'span'>;
    /**
     * Expand icon slot rendered before (or after) children content in heading.
     */
    icon?: Slot<'div'>;
};

export declare type AccordionHeaderState = ComponentState<AccordionHeaderSlots> & Required<Pick<AccordionHeaderProps, 'inline'>> & AccordionHeaderContextValue;

export declare type AccordionIndex = number | number[];

/**
 * Define a styled AccordionItem, using the `useAccordionItem_unstable` and `useAccordionItemStyles_unstable` hooks.
 */
export declare const AccordionItem: ForwardRefComponent<AccordionItemProps>;

export declare const accordionItemClassNames: SlotClassNames<AccordionItemSlots>;

export declare type AccordionItemContextValue<Value = AccordionItemValue> = {
    open: boolean;
    disabled: boolean;
    value: Value;
    /**
     * @deprecated - use `requestToggle` from AccordionContent instead
     */
    onHeaderClick(event: AccordionToggleEvent): void;
};

export declare type AccordionItemContextValues<Value = AccordionItemValue> = {
    accordionItem: AccordionItemContextValue<Value>;
};

export declare type AccordionItemProps<Value = AccordionItemValue> = ComponentProps<AccordionItemSlots> & {
    /**
     * Disables opening/closing of panel.
     */
    disabled?: boolean;
    /**
     * Required value that identifies this item inside an Accordion component.
     */
    value: Value;
};

export declare const AccordionItemProvider: React_2.Provider<AccordionItemContextValue<unknown>>;

export declare type AccordionItemSlots = {
    root: NonNullable<Slot<'div'>>;
};

export declare type AccordionItemState<Value = AccordionItemValue> = ComponentState<AccordionItemSlots> & AccordionItemContextValue<Value>;

export declare type AccordionItemValue = unknown;

/**
 * Define a styled AccordionPanel, using the `useAccordionPanel_unstable` and `useAccordionPanelStyles_unstable` hooks.
 */
export declare const AccordionPanel: ForwardRefComponent<AccordionPanelProps>;

export declare const accordionPanelClassNames: SlotClassNames<Omit<AccordionPanelSlots, 'collapseMotion'>>;

export declare type AccordionPanelProps = ComponentProps<AccordionPanelSlots>;

export declare type AccordionPanelSlots = {
    root: NonNullable<Slot<'div'>>;
    collapseMotion?: Slot<PresenceMotionSlotProps>;
};

export declare type AccordionPanelState = ComponentState<AccordionPanelSlots> & {
    /**
     * Internal open state, provided by context.
     */
    open: boolean;
};

export declare type AccordionProps<Value = AccordionItemValue> = ComponentProps<AccordionSlots> & {
    /**
     * Default value for the uncontrolled state of the panel.
     */
    defaultOpenItems?: Value | Value[];
    /**
     * Indicates if Accordion support multiple Panels closed at the same time.
     */
    collapsible?: boolean;
    /**
     * Indicates if Accordion support multiple Panels opened at the same time.
     */
    multiple?: boolean;
    /**
     * @deprecated Arrow keyboard navigation is not recommended for accordions. Consider using Tree if arrow navigation is a hard requirement.
     * Indicates if keyboard navigation is available and gives two options, linear or circular navigation.
     */
    navigation?: 'linear' | 'circular';
    /**
     * Callback to be called when the opened items change.
     */
    onToggle?: AccordionToggleEventHandler<Value>;
    /**
     * Controls the state of the panel.
     */
    openItems?: Value | Value[];
};

export declare const AccordionProvider: Provider<AccordionContextValue<unknown>> & FC<ProviderProps<AccordionContextValue<unknown>>>;

declare type AccordionRequestToggleData<Value = AccordionItemValue> = {
    event: AccordionToggleEvent;
} & Pick<AccordionToggleData<Value>, 'value'>;

export declare type AccordionSlots = {
    root: NonNullable<Slot<'div'>>;
};

export declare type AccordionState<Value = AccordionItemValue> = ComponentState<AccordionSlots> & AccordionContextValue<Value>;

export declare type AccordionToggleData<Value = AccordionItemValue> = {
    value: Value;
    openItems: Value[];
};

export declare type AccordionToggleEvent<E = HTMLElement> = React_2.MouseEvent<E> | React_2.KeyboardEvent<E>;

export declare type AccordionToggleEventHandler<Value = AccordionItemValue> = (event: AccordionToggleEvent, data: AccordionToggleData<Value>) => void;

/**
 * Function that renders the final JSX of the component
 */
export declare const renderAccordion_unstable: (state: AccordionState, contextValues: AccordionContextValues) => JSX.Element;

/**
 * Function that renders the final JSX of the component
 */
export declare const renderAccordionHeader_unstable: (state: AccordionHeaderState, contextValues: AccordionHeaderContextValues) => JSX.Element;

/**
 * Function that renders the final JSX of the component
 */
export declare const renderAccordionItem_unstable: (state: AccordionItemState, contextValues: AccordionItemContextValues) => JSX.Element;

/**
 * Function that renders the final JSX of the component
 */
export declare const renderAccordionPanel_unstable: (state: AccordionPanelState) => JSX.Element;

/**
 * Returns the props and state required to render the component
 * @param props - Accordion properties
 * @param ref - reference to root HTMLElement of Accordion
 */
export declare const useAccordion_unstable: <Value = unknown>(props: AccordionProps<Value>, ref: React_2.Ref<HTMLElement>) => AccordionState<Value>;

export declare const useAccordionContext_unstable: <T>(selector: ContextSelector<AccordionContextValue<unknown>, T>) => T;

export declare function useAccordionContextValues_unstable(state: AccordionState): AccordionContextValues;

/**
 * Returns the props and state required to render the component
 * @param props - AccordionHeader properties
 * @param ref - reference to root HTMLElement of AccordionHeader
 */
export declare const useAccordionHeader_unstable: (props: AccordionHeaderProps, ref: React_2.Ref<HTMLElement>) => AccordionHeaderState;

export declare const useAccordionHeaderContext_unstable: () => AccordionHeaderContextValue;

export declare function useAccordionHeaderContextValues_unstable(state: AccordionHeaderState): AccordionHeaderContextValues;

/** Applies style classnames to slots */
export declare const useAccordionHeaderStyles_unstable: (state: AccordionHeaderState) => AccordionHeaderState;

/**
 * Returns the props and state required to render the component
 * @param props - AccordionItem properties
 * @param ref - reference to root HTMLElement of AccordionItem
 */
export declare const useAccordionItem_unstable: (props: AccordionItemProps, ref: React_2.Ref<HTMLElement>) => AccordionItemState;

export declare const useAccordionItemContext_unstable: () => AccordionItemContextValue<unknown>;

export declare function useAccordionItemContextValues_unstable(state: AccordionItemState): AccordionItemContextValues;

export declare const useAccordionItemStyles_unstable: (state: AccordionItemState) => AccordionItemState;

/**
 * Returns the props and state required to render the component
 * @param props - AccordionPanel properties
 * @param ref - reference to root HTMLElement of AccordionPanel
 */
export declare const useAccordionPanel_unstable: (props: AccordionPanelProps, ref: React_2.Ref<HTMLElement>) => AccordionPanelState;

/** Applies style classnames to slots */
export declare const useAccordionPanelStyles_unstable: (state: AccordionPanelState) => AccordionPanelState;

export declare const useAccordionStyles_unstable: (state: AccordionState) => AccordionState;

export { }
