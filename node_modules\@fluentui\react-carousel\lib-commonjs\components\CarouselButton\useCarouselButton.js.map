{"version": 3, "sources": ["../src/components/CarouselButton/useCarouselButton.tsx"], "sourcesContent": ["import { type ARIAButtonElement } from '@fluentui/react-aria';\nimport { useButton_unstable } from '@fluentui/react-button';\nimport { ChevronLeftRegular, ChevronRightRegular } from '@fluentui/react-icons';\nimport {\n  mergeCallbacks,\n  useEventCallback,\n  slot,\n  useIsomorphicLayoutEffect,\n  useMergedRefs,\n} from '@fluentui/react-utilities';\nimport * as React from 'react';\n\nimport { useCarouselContext_unstable as useCarouselContext } from '../CarouselContext';\nimport type { CarouselButtonProps, CarouselButtonState } from './CarouselButton.types';\nimport type { CarouselUpdateData } from '../Carousel/Carousel.types';\nimport { carouselButtonClassNames } from './useCarouselButtonStyles.styles';\nimport { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\n\n/**\n * Create the state required to render CarouselButton.\n *\n * The returned state can be modified with hooks such as useCarouselButtonStyles_unstable,\n * before being passed to renderCarouselButton_unstable.\n *\n * @param props - props from this instance of CarouselButton\n * @param ref - reference to root HTMLDivElement of CarouselButton\n */\nexport const useCarouselButton_unstable = (\n  props: CarouselButtonProps,\n  ref: React.Ref<ARIAButtonElement>,\n): CarouselButtonState => {\n  const { navType = 'next' } = props;\n\n  // Locally tracks the total number of slides, will only update if this changes.\n  const [totalSlides, setTotalSlides] = React.useState(0);\n\n  const { dir } = useFluent();\n  const buttonRef = React.useRef<HTMLButtonElement>();\n  const circular = useCarouselContext(ctx => ctx.circular);\n  const [canLoop, setCanLoop] = React.useState(circular);\n  const containerRef = useCarouselContext(ctx => ctx.containerRef);\n  const selectPageByDirection = useCarouselContext(ctx => ctx.selectPageByDirection);\n  const subscribeForValues = useCarouselContext(ctx => ctx.subscribeForValues);\n  const resetAutoplay = useCarouselContext(ctx => ctx.resetAutoplay);\n\n  const isTrailing = useCarouselContext(ctx => {\n    if (circular && canLoop) {\n      return false;\n    }\n\n    if (navType === 'prev') {\n      return ctx.activeIndex === 0;\n    }\n\n    return ctx.activeIndex === totalSlides - 1;\n  });\n\n  const handleClick = (event: React.MouseEvent<HTMLButtonElement & HTMLAnchorElement>) => {\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n\n    const nextIndex = selectPageByDirection(event, navType);\n\n    let _trailing = false;\n    if (navType === 'prev') {\n      _trailing = nextIndex === 0;\n    } else {\n      _trailing = nextIndex === totalSlides - 1;\n    }\n\n    if (!circular && _trailing && containerRef?.current) {\n      // Focus non-disabled element\n      const buttonRefs: NodeListOf<HTMLButtonElement> = containerRef.current.querySelectorAll(\n        `.${carouselButtonClassNames.root}`,\n      );\n      buttonRefs.forEach(_buttonRef => {\n        if (_buttonRef !== buttonRef.current) {\n          _buttonRef.focus();\n        }\n      });\n    }\n\n    resetAutoplay();\n  };\n\n  useIsomorphicLayoutEffect(() => {\n    return subscribeForValues((data: CarouselUpdateData) => {\n      if (data.canLoop !== undefined) {\n        // Only update canLoop if it has been defined by the carousel engine\n        setCanLoop(data.canLoop);\n      }\n      setTotalSlides(data.navItemsCount);\n    });\n  }, [subscribeForValues]);\n\n  const nextArrowIcon = dir === 'ltr' ? <ChevronRightRegular /> : <ChevronLeftRegular />;\n  const prevArrowIcon = dir === 'ltr' ? <ChevronLeftRegular /> : <ChevronRightRegular />;\n\n  return {\n    navType,\n    // We lean on react-button class to handle styling and icon enhancements\n    ...useButton_unstable(\n      {\n        icon: slot.optional(props.icon, {\n          defaultProps: {\n            children: navType === 'next' ? nextArrowIcon : prevArrowIcon,\n          },\n          renderByDefault: true,\n          elementType: 'span',\n        }),\n        disabled: isTrailing,\n        tabIndex: isTrailing ? -1 : 0,\n        'aria-disabled': isTrailing,\n        appearance: 'subtle',\n        ...props,\n        onClick: useEventCallback(mergeCallbacks(handleClick, props.onClick)),\n      },\n      useMergedRefs(ref, buttonRef) as React.Ref<HTMLButtonElement>,\n    ),\n  };\n};\n"], "names": ["useCarouselButton_unstable", "props", "ref", "navType", "totalSlides", "setTotalSlides", "React", "useState", "dir", "useFluent", "buttonRef", "useRef", "circular", "useCarouselContext", "ctx", "canLoop", "setCanLoop", "containerRef", "selectPageByDirection", "subscribeForValues", "resetAutoplay", "isTrailing", "activeIndex", "handleClick", "event", "isDefaultPrevented", "nextIndex", "_trailing", "current", "buttonRefs", "querySelectorAll", "carouselButtonClassNames", "root", "for<PERSON>ach", "_buttonRef", "focus", "useIsomorphicLayoutEffect", "data", "undefined", "navItemsCount", "nextArrowIcon", "createElement", "ChevronRightRegular", "ChevronLeftRegular", "prevArrowIcon", "useButton_unstable", "icon", "slot", "optional", "defaultProps", "children", "renderByDefault", "elementType", "disabled", "tabIndex", "appearance", "onClick", "useEventCallback", "mergeCallbacks", "useMergedRefs"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BA2BaA;;;eAAAA;;;;6BA1BsB;4BACqB;gCAOjD;iEACgB;iCAE2C;+CAGzB;qCACO;AAWzC,MAAMA,6BAA6B,CACxCC,OACAC;IAEA,MAAM,EAAEC,UAAU,MAAM,EAAE,GAAGF;IAE7B,+EAA+E;IAC/E,MAAM,CAACG,aAAaC,eAAe,GAAGC,OAAMC,QAAQ,CAAC;IAErD,MAAM,EAAEC,GAAG,EAAE,GAAGC,IAAAA,uCAAAA;IAChB,MAAMC,YAAYJ,OAAMK,MAAM;IAC9B,MAAMC,WAAWC,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIF,QAAQ;IACvD,MAAM,CAACG,SAASC,WAAW,GAAGV,OAAMC,QAAQ,CAACK;IAC7C,MAAMK,eAAeJ,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIG,YAAY;IAC/D,MAAMC,wBAAwBL,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAII,qBAAqB;IACjF,MAAMC,qBAAqBN,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIK,kBAAkB;IAC3E,MAAMC,gBAAgBP,IAAAA,4CAAAA,EAAmBC,CAAAA,MAAOA,IAAIM,aAAa;IAEjE,MAAMC,aAAaR,IAAAA,4CAAAA,EAAmBC,CAAAA;QACpC,IAAIF,YAAYG,SAAS;YACvB,OAAO;QACT;QAEA,IAAIZ,YAAY,QAAQ;YACtB,OAAOW,IAAIQ,WAAW,KAAK;QAC7B;QAEA,OAAOR,IAAIQ,WAAW,KAAKlB,cAAc;IAC3C;IAEA,MAAMmB,cAAc,CAACC;QACnB,IAAIA,MAAMC,kBAAkB,IAAI;YAC9B;QACF;QAEA,MAAMC,YAAYR,sBAAsBM,OAAOrB;QAE/C,IAAIwB,YAAY;QAChB,IAAIxB,YAAY,QAAQ;YACtBwB,YAAYD,cAAc;QAC5B,OAAO;YACLC,YAAYD,cAActB,cAAc;QAC1C;QAEA,IAAI,CAACQ,YAAYe,aAAaV,CAAAA,iBAAAA,QAAAA,iBAAAA,KAAAA,IAAAA,KAAAA,IAAAA,aAAcW,OAAO,AAAPA,GAAS;YACnD,6BAA6B;YAC7B,MAAMC,aAA4CZ,aAAaW,OAAO,CAACE,gBAAgB,CACrF,CAAC,CAAC,EAAEC,uDAAAA,CAAyBC,IAAI,CAAC,CAAC;YAErCH,WAAWI,OAAO,CAACC,CAAAA;gBACjB,IAAIA,eAAexB,UAAUkB,OAAO,EAAE;oBACpCM,WAAWC,KAAK;gBAClB;YACF;QACF;QAEAf;IACF;IAEAgB,IAAAA,yCAAAA,EAA0B;QACxB,OAAOjB,mBAAmB,CAACkB;YACzB,IAAIA,KAAKtB,OAAO,KAAKuB,WAAW;gBAC9B,oEAAoE;gBACpEtB,WAAWqB,KAAKtB,OAAO;YACzB;YACAV,eAAegC,KAAKE,aAAa;QACnC;IACF,GAAG;QAACpB;KAAmB;IAEvB,MAAMqB,gBAAgBhC,QAAQ,QAAA,WAAA,GAAQF,OAAAmC,aAAA,CAACC,+BAAAA,EAAAA,QAAAA,WAAAA,GAAyBpC,OAAAmC,aAAA,CAACE,8BAAAA,EAAAA;IACjE,MAAMC,gBAAgBpC,QAAQ,QAAA,WAAA,GAAQF,OAAAmC,aAAA,CAACE,8BAAAA,EAAAA,QAAAA,WAAAA,GAAwBrC,OAAAmC,aAAA,CAACC,+BAAAA,EAAAA;IAEhE,OAAO;QACLvC;QACA,wEAAwE;QACxE,GAAG0C,IAAAA,+BAAAA,EACD;YACEC,MAAMC,oBAAAA,CAAKC,QAAQ,CAAC/C,MAAM6C,IAAI,EAAE;gBAC9BG,cAAc;oBACZC,UAAU/C,YAAY,SAASqC,gBAAgBI;gBACjD;gBACAO,iBAAiB;gBACjBC,aAAa;YACf;YACAC,UAAUhC;YACViC,UAAUjC,aAAa,CAAC,IAAI;YAC5B,iBAAiBA;YACjBkC,YAAY;YACZ,GAAGtD,KAAK;YACRuD,SAASC,IAAAA,gCAAAA,EAAiBC,IAAAA,8BAAAA,EAAenC,aAAatB,MAAMuD,OAAO;QACrE,GACAG,IAAAA,6BAAAA,EAAczD,KAAKQ,WACpB;IACH;AACF"}