{"version": 3, "sources": ["../src/AvatarGroup.ts"], "sourcesContent": ["export type {\n  AvatarGroupContextValue,\n  AvatarGroupContextValues,\n  AvatarGroupProps,\n  AvatarGroupSlots,\n  AvatarGroupState,\n} from './components/AvatarGroup/index';\nexport {\n  AvatarGroup,\n  avatarGroupClassNames,\n  defaultAvatarGroupSize,\n  renderAvatarGroup_unstable,\n  useAvatarGroupContextValues,\n  useAvatarGroupStyles_unstable,\n  useAvatarGroup_unstable,\n} from './components/AvatarGroup/index';\n"], "names": ["AvatarGroup", "avatarGroupClassNames", "defaultAvatarGroupSize", "renderAvatarGroup_unstable", "useAvatarGroupContextValues", "useAvatarGroupStyles_unstable", "useAvatarGroup_unstable"], "rangeMappings": "", "mappings": "AAOA,SACEA,WAAW,EACXC,qBAAqB,EACrBC,sBAAsB,EACtBC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,uBAAuB,QAClB,iCAAiC"}