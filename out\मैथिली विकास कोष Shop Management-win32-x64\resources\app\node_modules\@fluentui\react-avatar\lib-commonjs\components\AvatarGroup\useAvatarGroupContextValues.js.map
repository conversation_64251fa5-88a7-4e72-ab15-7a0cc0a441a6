{"version": 3, "sources": ["../src/components/AvatarGroup/useAvatarGroupContextValues.ts"], "sourcesContent": ["import { AvatarGroupContextValue, AvatarGroupContextValues, AvatarGroupState } from '../AvatarGroup';\n\nexport const useAvatarGroupContextValues = (state: AvatarGroupState): AvatarGroupContextValues => {\n  const { layout, size } = state;\n\n  const avatarGroup: AvatarGroupContextValue = {\n    layout,\n    size,\n  };\n\n  return { avatarGroup };\n};\n"], "names": ["useAvatarGroupContextValues", "state", "layout", "size", "avatarGroup"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAEaA;;;eAAAA;;;AAAN,MAAMA,8BAA8B,CAACC;IAC1C,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE,GAAGF;IAEzB,MAAMG,cAAuC;QAC3CF;QACAC;IACF;IAEA,OAAO;QAAEC;IAAY;AACvB"}