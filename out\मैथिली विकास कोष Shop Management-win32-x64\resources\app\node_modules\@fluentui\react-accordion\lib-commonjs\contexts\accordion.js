"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AccordionProvider: function() {
        return AccordionProvider;
    },
    useAccordionContext_unstable: function() {
        return useAccordionContext_unstable;
    }
});
const _reactcontextselector = require("@fluentui/react-context-selector");
const AccordionContext = (0, _reactcontextselector.createContext)(undefined);
const accordionContextDefaultValue = {
    openItems: [],
    collapsible: false,
    multiple: false,
    navigation: undefined,
    requestToggle () {
    /* noop */ }
};
const { Provider: AccordionProvider } = AccordionContext;
const useAccordionContext_unstable = (selector)=>(0, _reactcontextselector.useContextSelector)(AccordionContext, (ctx = accordionContextDefaultValue)=>selector(ctx));
