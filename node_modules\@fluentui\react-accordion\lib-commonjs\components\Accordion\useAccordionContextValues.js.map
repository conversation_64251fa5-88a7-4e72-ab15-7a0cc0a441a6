{"version": 3, "sources": ["../src/components/Accordion/useAccordionContextValues.ts"], "sourcesContent": ["import type { AccordionContextValue } from '../../contexts/accordion';\nimport type { AccordionContextValues, AccordionState } from './Accordion.types';\n\nexport function useAccordionContextValues_unstable(state: AccordionState): AccordionContextValues {\n  const { navigation, openItems, requestToggle, multiple, collapsible } = state;\n\n  // This context is created with \"@fluentui/react-context-selector\", these is no sense to memoize it\n  const accordion: AccordionContextValue = {\n    navigation,\n    openItems,\n    requestToggle,\n    collapsible,\n    multiple,\n  };\n\n  return { accordion };\n}\n"], "names": ["useAccordionContextValues_unstable", "state", "navigation", "openItems", "requestToggle", "multiple", "collapsible", "accordion"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAGgBA;;;eAAAA;;;AAAT,SAASA,mCAAmCC,KAAqB;IACtE,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,WAAW,EAAE,GAAGL;IAExE,mGAAmG;IACnG,MAAMM,YAAmC;QACvCL;QACAC;QACAC;QACAE;QACAD;IACF;IAEA,OAAO;QAAEE;IAAU;AACrB"}