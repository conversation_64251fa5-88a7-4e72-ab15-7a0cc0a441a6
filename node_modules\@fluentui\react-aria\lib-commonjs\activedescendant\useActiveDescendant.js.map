{"version": 3, "sources": ["../src/activedescendant/useActiveDescendant.ts"], "sourcesContent": ["import * as React from 'react';\nimport { useEventCallback, useMergedRefs } from '@fluentui/react-utilities';\nimport { useOnKeyboardNavigationChange } from '@fluentui/react-tabster';\nimport { useOptionWalker } from './useOptionWalker';\nimport type { ActiveDescendantImperativeRef, ActiveDescendantOptions, UseActiveDescendantReturn } from './types';\nimport { ACTIVEDESCENDANT_ATTRIBUTE, ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE } from './constants';\nimport { scrollIntoView } from './scrollIntoView';\n\ninterface ActiveDescendantChangeEventDetail {\n  id: string;\n  previousId: string | null;\n}\n\nexport type ActiveDescendantChangeEvent = CustomEvent<ActiveDescendantChangeEventDetail>;\n\nexport const createActiveDescendantChangeEvent = (\n  detail: ActiveDescendantChangeEventDetail,\n): ActiveDescendantChangeEvent =>\n  new CustomEvent<ActiveDescendantChangeEventDetail>('activedescendantchange', {\n    bubbles: true,\n    cancelable: false,\n    composed: true,\n    detail,\n  });\n\nexport function useActiveDescendant<TActiveParentElement extends HTMLElement, TListboxElement extends HTMLElement>(\n  options: ActiveDescendantOptions,\n): UseActiveDescendantReturn<TActiveParentElement, TListboxElement> {\n  const { imperativeRef, matchOption: matchOptionUnstable } = options;\n  const focusVisibleRef = React.useRef(false);\n  const shouldShowFocusVisibleAttrRef = React.useRef(true);\n  const activeIdRef = React.useRef<string | null>(null);\n  const lastActiveIdRef = React.useRef<string | null>(null);\n  const activeParentRef = React.useRef<TActiveParentElement>(null);\n  const attributeVisibilityRef = React.useRef(true);\n\n  const removeAttribute = React.useCallback(() => {\n    activeParentRef.current?.removeAttribute('aria-activedescendant');\n  }, []);\n\n  const setAttribute = React.useCallback((id?: string) => {\n    if (id) {\n      activeIdRef.current = id;\n    }\n    if (attributeVisibilityRef.current && activeIdRef.current) {\n      activeParentRef.current?.setAttribute('aria-activedescendant', activeIdRef.current);\n    }\n  }, []);\n\n  useOnKeyboardNavigationChange(isNavigatingWithKeyboard => {\n    focusVisibleRef.current = isNavigatingWithKeyboard;\n\n    const active = getActiveDescendant();\n    if (!active) {\n      return;\n    }\n\n    if (isNavigatingWithKeyboard && shouldShowFocusVisibleAttrRef.current) {\n      active.setAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE, '');\n    } else {\n      active.removeAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE);\n    }\n  });\n\n  const matchOption = useEventCallback(matchOptionUnstable);\n  const listboxRef = React.useRef<TListboxElement>(null);\n  const { optionWalker, listboxCallbackRef } = useOptionWalker<TListboxElement>({ matchOption });\n\n  const getActiveDescendant = React.useCallback(() => {\n    return listboxRef.current?.querySelector<HTMLElement>(`#${activeIdRef.current}`);\n  }, [listboxRef]);\n\n  const setShouldShowFocusVisibleAttribute = React.useCallback(\n    (shouldShow: boolean) => {\n      shouldShowFocusVisibleAttrRef.current = shouldShow;\n\n      const active = getActiveDescendant();\n      if (!active) {\n        return;\n      }\n\n      if (shouldShow && focusVisibleRef.current) {\n        active.setAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE, '');\n      } else {\n        active.removeAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE);\n      }\n    },\n    [getActiveDescendant],\n  );\n\n  const blurActiveDescendant = React.useCallback(() => {\n    const active = getActiveDescendant();\n    if (active) {\n      active.removeAttribute(ACTIVEDESCENDANT_ATTRIBUTE);\n      active.removeAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE);\n    }\n\n    removeAttribute();\n    lastActiveIdRef.current = activeIdRef.current;\n    activeIdRef.current = null;\n    return active?.id ?? null;\n  }, [getActiveDescendant, removeAttribute]);\n\n  const focusActiveDescendant = React.useCallback(\n    (nextActive: HTMLElement | null) => {\n      if (!nextActive) {\n        return;\n      }\n\n      const previousActiveId = blurActiveDescendant();\n\n      scrollIntoView(nextActive);\n      setAttribute(nextActive.id);\n      nextActive.setAttribute(ACTIVEDESCENDANT_ATTRIBUTE, '');\n\n      if (focusVisibleRef.current && shouldShowFocusVisibleAttrRef.current) {\n        nextActive.setAttribute(ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE, '');\n      }\n\n      const event = createActiveDescendantChangeEvent({ id: nextActive.id, previousId: previousActiveId });\n      nextActive.dispatchEvent(event);\n    },\n    [blurActiveDescendant, setAttribute],\n  );\n\n  const controller: ActiveDescendantImperativeRef = React.useMemo(\n    () => ({\n      first: ({ passive } = {}) => {\n        const first = optionWalker.first();\n        if (!passive) {\n          focusActiveDescendant(first);\n        }\n\n        return first?.id;\n      },\n      last: ({ passive } = {}) => {\n        const last = optionWalker.last();\n        if (!passive) {\n          focusActiveDescendant(last);\n        }\n\n        return last?.id;\n      },\n      next: ({ passive } = {}) => {\n        const active = getActiveDescendant();\n        if (!active) {\n          return;\n        }\n\n        optionWalker.setCurrent(active);\n        const next = optionWalker.next();\n        if (!passive) {\n          focusActiveDescendant(next);\n        }\n\n        return next?.id;\n      },\n      prev: ({ passive } = {}) => {\n        const active = getActiveDescendant();\n        if (!active) {\n          return;\n        }\n\n        optionWalker.setCurrent(active);\n        const next = optionWalker.prev();\n\n        if (!passive) {\n          focusActiveDescendant(next);\n        }\n\n        return next?.id;\n      },\n      blur: () => {\n        blurActiveDescendant();\n      },\n      active: () => {\n        return getActiveDescendant()?.id;\n      },\n      focus: (id: string) => {\n        if (!listboxRef.current) {\n          return;\n        }\n\n        const target = listboxRef.current.querySelector<HTMLElement>(`#${id}`);\n        if (target) {\n          focusActiveDescendant(target);\n        }\n      },\n      focusLastActive: () => {\n        if (!listboxRef.current || !lastActiveIdRef.current) {\n          return;\n        }\n\n        const target = listboxRef.current.querySelector<HTMLElement>(`#${lastActiveIdRef.current}`);\n        if (target) {\n          focusActiveDescendant(target);\n          return true;\n        }\n      },\n      find(predicate, { passive, startFrom } = {}) {\n        const target = optionWalker.find(predicate, startFrom);\n        if (!passive) {\n          focusActiveDescendant(target);\n        }\n\n        return target?.id;\n      },\n      scrollActiveIntoView: () => {\n        if (!listboxRef.current) {\n          return;\n        }\n\n        const active = getActiveDescendant();\n        if (!active) {\n          return;\n        }\n\n        scrollIntoView(active);\n      },\n      showAttributes() {\n        attributeVisibilityRef.current = true;\n        setAttribute();\n      },\n      hideAttributes() {\n        attributeVisibilityRef.current = false;\n        removeAttribute();\n      },\n      showFocusVisibleAttributes() {\n        setShouldShowFocusVisibleAttribute(true);\n      },\n      hideFocusVisibleAttributes() {\n        setShouldShowFocusVisibleAttribute(false);\n      },\n    }),\n    [\n      optionWalker,\n      listboxRef,\n      setAttribute,\n      removeAttribute,\n      focusActiveDescendant,\n      blurActiveDescendant,\n      getActiveDescendant,\n      setShouldShowFocusVisibleAttribute,\n    ],\n  );\n\n  React.useImperativeHandle(imperativeRef, () => controller);\n\n  return { listboxRef: useMergedRefs(listboxRef, listboxCallbackRef), activeParentRef, controller };\n}\n"], "names": ["createActiveDescendantChangeEvent", "useActiveDescendant", "detail", "CustomEvent", "bubbles", "cancelable", "composed", "options", "imperativeRef", "matchOption", "matchOptionUnstable", "focusVisibleRef", "React", "useRef", "shouldShowFocusVisibleAttrRef", "activeIdRef", "lastActiveIdRef", "activeParentRef", "attributeVisibilityRef", "removeAttribute", "useCallback", "current", "setAttribute", "id", "useOnKeyboardNavigationChange", "isNavigatingWithKeyboard", "active", "getActiveDescendant", "ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE", "useEventCallback", "listboxRef", "<PERSON><PERSON><PERSON><PERSON>", "listboxCallbackRef", "useOptionWalker", "querySelector", "setShouldShowFocusVisibleAttribute", "shouldShow", "blurActiveDescendant", "ACTIVEDESCENDANT_ATTRIBUTE", "focusActiveDescendant", "nextActive", "previousActiveId", "scrollIntoView", "event", "previousId", "dispatchEvent", "controller", "useMemo", "first", "passive", "last", "next", "setCurrent", "prev", "blur", "focus", "target", "focusLastActive", "find", "predicate", "startFrom", "scrollActiveIntoView", "showAttributes", "hideAttributes", "showFocusVisibleAttributes", "hideFocusVisibleAttributes", "useImperativeHandle", "useMergedRefs"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAeaA,iCAAiC;eAAjCA;;IAUGC,mBAAmB;eAAnBA;;;;iEAzBO;gCACyB;8BACF;iCACd;2BAEoD;gCACrD;AASxB,MAAMD,oCAAoC,CAC/CE,SAEA,IAAIC,YAA+C,0BAA0B;QAC3EC,SAAS;QACTC,YAAY;QACZC,UAAU;QACVJ;IACF;AAEK,SAASD,oBACdM,OAAgC;IAEhC,MAAM,EAAEC,aAAa,EAAEC,aAAaC,mBAAmB,EAAE,GAAGH;IAC5D,MAAMI,kBAAkBC,OAAMC,MAAM,CAAC;IACrC,MAAMC,gCAAgCF,OAAMC,MAAM,CAAC;IACnD,MAAME,cAAcH,OAAMC,MAAM,CAAgB;IAChD,MAAMG,kBAAkBJ,OAAMC,MAAM,CAAgB;IACpD,MAAMI,kBAAkBL,OAAMC,MAAM,CAAuB;IAC3D,MAAMK,yBAAyBN,OAAMC,MAAM,CAAC;IAE5C,MAAMM,kBAAkBP,OAAMQ,WAAW,CAAC;YACxCH;SAAAA,2BAAAA,gBAAgBI,OAAO,cAAvBJ,+CAAAA,yBAAyBE,eAAe,CAAC;IAC3C,GAAG,EAAE;IAEL,MAAMG,eAAeV,OAAMQ,WAAW,CAAC,CAACG;QACtC,IAAIA,IAAI;YACNR,YAAYM,OAAO,GAAGE;QACxB;QACA,IAAIL,uBAAuBG,OAAO,IAAIN,YAAYM,OAAO,EAAE;gBACzDJ;aAAAA,2BAAAA,gBAAgBI,OAAO,cAAvBJ,+CAAAA,yBAAyBK,YAAY,CAAC,yBAAyBP,YAAYM,OAAO;QACpF;IACF,GAAG,EAAE;IAELG,IAAAA,2CAA6B,EAACC,CAAAA;QAC5Bd,gBAAgBU,OAAO,GAAGI;QAE1B,MAAMC,SAASC;QACf,IAAI,CAACD,QAAQ;YACX;QACF;QAEA,IAAID,4BAA4BX,8BAA8BO,OAAO,EAAE;YACrEK,OAAOJ,YAAY,CAACM,kDAAuC,EAAE;QAC/D,OAAO;YACLF,OAAOP,eAAe,CAACS,kDAAuC;QAChE;IACF;IAEA,MAAMnB,cAAcoB,IAAAA,gCAAgB,EAACnB;IACrC,MAAMoB,aAAalB,OAAMC,MAAM,CAAkB;IACjD,MAAM,EAAEkB,YAAY,EAAEC,kBAAkB,EAAE,GAAGC,IAAAA,gCAAe,EAAkB;QAAExB;IAAY;IAE5F,MAAMkB,sBAAsBf,OAAMQ,WAAW,CAAC;YACrCU;QAAP,QAAOA,sBAAAA,WAAWT,OAAO,cAAlBS,0CAAAA,oBAAoBI,aAAa,CAAc,CAAC,CAAC,EAAEnB,YAAYM,OAAO,CAAC,CAAC;IACjF,GAAG;QAACS;KAAW;IAEf,MAAMK,qCAAqCvB,OAAMQ,WAAW,CAC1D,CAACgB;QACCtB,8BAA8BO,OAAO,GAAGe;QAExC,MAAMV,SAASC;QACf,IAAI,CAACD,QAAQ;YACX;QACF;QAEA,IAAIU,cAAczB,gBAAgBU,OAAO,EAAE;YACzCK,OAAOJ,YAAY,CAACM,kDAAuC,EAAE;QAC/D,OAAO;YACLF,OAAOP,eAAe,CAACS,kDAAuC;QAChE;IACF,GACA;QAACD;KAAoB;IAGvB,MAAMU,uBAAuBzB,OAAMQ,WAAW,CAAC;QAC7C,MAAMM,SAASC;QACf,IAAID,QAAQ;YACVA,OAAOP,eAAe,CAACmB,qCAA0B;YACjDZ,OAAOP,eAAe,CAACS,kDAAuC;QAChE;QAEAT;QACAH,gBAAgBK,OAAO,GAAGN,YAAYM,OAAO;QAC7CN,YAAYM,OAAO,GAAG;YACfK;QAAP,OAAOA,CAAAA,aAAAA,mBAAAA,6BAAAA,OAAQH,EAAE,cAAVG,wBAAAA,aAAc;IACvB,GAAG;QAACC;QAAqBR;KAAgB;IAEzC,MAAMoB,wBAAwB3B,OAAMQ,WAAW,CAC7C,CAACoB;QACC,IAAI,CAACA,YAAY;YACf;QACF;QAEA,MAAMC,mBAAmBJ;QAEzBK,IAAAA,8BAAc,EAACF;QACflB,aAAakB,WAAWjB,EAAE;QAC1BiB,WAAWlB,YAAY,CAACgB,qCAA0B,EAAE;QAEpD,IAAI3B,gBAAgBU,OAAO,IAAIP,8BAA8BO,OAAO,EAAE;YACpEmB,WAAWlB,YAAY,CAACM,kDAAuC,EAAE;QACnE;QAEA,MAAMe,QAAQ3C,kCAAkC;YAAEuB,IAAIiB,WAAWjB,EAAE;YAAEqB,YAAYH;QAAiB;QAClGD,WAAWK,aAAa,CAACF;IAC3B,GACA;QAACN;QAAsBf;KAAa;IAGtC,MAAMwB,aAA4ClC,OAAMmC,OAAO,CAC7D,IAAO,CAAA;YACLC,OAAO,CAAC,EAAEC,OAAO,EAAE,GAAG,CAAC,CAAC;gBACtB,MAAMD,QAAQjB,aAAaiB,KAAK;gBAChC,IAAI,CAACC,SAAS;oBACZV,sBAAsBS;gBACxB;gBAEA,OAAOA,kBAAAA,4BAAAA,MAAOzB,EAAE;YAClB;YACA2B,MAAM,CAAC,EAAED,OAAO,EAAE,GAAG,CAAC,CAAC;gBACrB,MAAMC,OAAOnB,aAAamB,IAAI;gBAC9B,IAAI,CAACD,SAAS;oBACZV,sBAAsBW;gBACxB;gBAEA,OAAOA,iBAAAA,2BAAAA,KAAM3B,EAAE;YACjB;YACA4B,MAAM,CAAC,EAAEF,OAAO,EAAE,GAAG,CAAC,CAAC;gBACrB,MAAMvB,SAASC;gBACf,IAAI,CAACD,QAAQ;oBACX;gBACF;gBAEAK,aAAaqB,UAAU,CAAC1B;gBACxB,MAAMyB,OAAOpB,aAAaoB,IAAI;gBAC9B,IAAI,CAACF,SAAS;oBACZV,sBAAsBY;gBACxB;gBAEA,OAAOA,iBAAAA,2BAAAA,KAAM5B,EAAE;YACjB;YACA8B,MAAM,CAAC,EAAEJ,OAAO,EAAE,GAAG,CAAC,CAAC;gBACrB,MAAMvB,SAASC;gBACf,IAAI,CAACD,QAAQ;oBACX;gBACF;gBAEAK,aAAaqB,UAAU,CAAC1B;gBACxB,MAAMyB,OAAOpB,aAAasB,IAAI;gBAE9B,IAAI,CAACJ,SAAS;oBACZV,sBAAsBY;gBACxB;gBAEA,OAAOA,iBAAAA,2BAAAA,KAAM5B,EAAE;YACjB;YACA+B,MAAM;gBACJjB;YACF;YACAX,QAAQ;oBACCC;gBAAP,QAAOA,uBAAAA,mCAAAA,2CAAAA,qBAAuBJ,EAAE;YAClC;YACAgC,OAAO,CAAChC;gBACN,IAAI,CAACO,WAAWT,OAAO,EAAE;oBACvB;gBACF;gBAEA,MAAMmC,SAAS1B,WAAWT,OAAO,CAACa,aAAa,CAAc,CAAC,CAAC,EAAEX,GAAG,CAAC;gBACrE,IAAIiC,QAAQ;oBACVjB,sBAAsBiB;gBACxB;YACF;YACAC,iBAAiB;gBACf,IAAI,CAAC3B,WAAWT,OAAO,IAAI,CAACL,gBAAgBK,OAAO,EAAE;oBACnD;gBACF;gBAEA,MAAMmC,SAAS1B,WAAWT,OAAO,CAACa,aAAa,CAAc,CAAC,CAAC,EAAElB,gBAAgBK,OAAO,CAAC,CAAC;gBAC1F,IAAImC,QAAQ;oBACVjB,sBAAsBiB;oBACtB,OAAO;gBACT;YACF;YACAE,MAAKC,SAAS,EAAE,EAAEV,OAAO,EAAEW,SAAS,EAAE,GAAG,CAAC,CAAC;gBACzC,MAAMJ,SAASzB,aAAa2B,IAAI,CAACC,WAAWC;gBAC5C,IAAI,CAACX,SAAS;oBACZV,sBAAsBiB;gBACxB;gBAEA,OAAOA,mBAAAA,6BAAAA,OAAQjC,EAAE;YACnB;YACAsC,sBAAsB;gBACpB,IAAI,CAAC/B,WAAWT,OAAO,EAAE;oBACvB;gBACF;gBAEA,MAAMK,SAASC;gBACf,IAAI,CAACD,QAAQ;oBACX;gBACF;gBAEAgB,IAAAA,8BAAc,EAAChB;YACjB;YACAoC;gBACE5C,uBAAuBG,OAAO,GAAG;gBACjCC;YACF;YACAyC;gBACE7C,uBAAuBG,OAAO,GAAG;gBACjCF;YACF;YACA6C;gBACE7B,mCAAmC;YACrC;YACA8B;gBACE9B,mCAAmC;YACrC;QACF,CAAA,GACA;QACEJ;QACAD;QACAR;QACAH;QACAoB;QACAF;QACAV;QACAQ;KACD;IAGHvB,OAAMsD,mBAAmB,CAAC1D,eAAe,IAAMsC;IAE/C,OAAO;QAAEhB,YAAYqC,IAAAA,6BAAa,EAACrC,YAAYE;QAAqBf;QAAiB6B;IAAW;AAClG"}