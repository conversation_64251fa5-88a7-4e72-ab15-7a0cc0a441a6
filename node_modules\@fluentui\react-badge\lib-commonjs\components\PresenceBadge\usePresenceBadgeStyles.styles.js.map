{"version": 3, "sources": ["usePresenceBadgeStyles.styles.js"], "sourcesContent": ["import { makeResetStyles, makeStyles, mergeClasses } from '@griffel/react';\nimport { tokens } from '@fluentui/react-theme';\nexport const presenceBadgeClassNames = {\n    root: 'fui-PresenceBadge',\n    icon: 'fui-PresenceBadge__icon'\n};\nconst getIsBusy = (status)=>{\n    if (status === 'busy' || status === 'do-not-disturb' || status === 'blocked') {\n        return true;\n    }\n    return false;\n};\nconst useRootClassName = makeResetStyles({\n    display: 'inline-flex',\n    boxSizing: 'border-box',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: tokens.borderRadiusCircular,\n    backgroundColor: tokens.colorNeutralBackground1,\n    // The background color bleeds around the edge of the icon due to antialiasing on the svg and element background.\n    // Since all presence icons have a border around the edge that is at least 1px wide*, we can inset the background\n    // using padding and backgroundClip. The icon has margin: -1px to account for the padding.\n    // (* except size=\"tiny\", where backgroundClip is unset)\n    padding: '1px',\n    backgroundClip: 'content-box'\n});\nconst useIconClassName = makeResetStyles({\n    display: 'flex',\n    margin: '-1px'\n});\nconst useStyles = makeStyles({\n    statusBusy: {\n        color: tokens.colorPaletteRedBackground3\n    },\n    statusAway: {\n        color: tokens.colorPaletteMarigoldBackground3\n    },\n    statusAvailable: {\n        color: tokens.colorPaletteLightGreenForeground3\n    },\n    statusOffline: {\n        color: tokens.colorNeutralForeground3\n    },\n    statusOutOfOffice: {\n        color: tokens.colorPaletteBerryForeground3\n    },\n    statusUnknown: {\n        color: tokens.colorNeutralForeground3\n    },\n    outOfOffice: {\n        color: tokens.colorNeutralBackground1\n    },\n    outOfOfficeAvailable: {\n        color: tokens.colorPaletteLightGreenForeground3\n    },\n    outOfOfficeBusy: {\n        color: tokens.colorPaletteRedBackground3\n    },\n    outOfOfficeUnknown: {\n        color: tokens.colorNeutralForeground3\n    },\n    // Icons are not resizeable, and these sizes are currently missing\n    // use `!important` to size the currently available icons to the missing ones\n    //\n    tiny: {\n        aspectRatio: '1',\n        width: '6px',\n        backgroundClip: 'unset',\n        '& svg': {\n            width: '6px !important',\n            height: '6px !important'\n        }\n    },\n    large: {\n        aspectRatio: '1',\n        width: '20px',\n        '& svg': {\n            width: '20px !important',\n            height: '20px !important'\n        }\n    },\n    extraLarge: {\n        aspectRatio: '1',\n        width: '28px',\n        '& svg': {\n            width: '28px !important',\n            height: '28px !important'\n        }\n    }\n});\n/**\n * Applies style classnames to slots\n */ export const usePresenceBadgeStyles_unstable = (state)=>{\n    'use no memo';\n    const rootClassName = useRootClassName();\n    const iconClassName = useIconClassName();\n    const styles = useStyles();\n    const isBusy = getIsBusy(state.status);\n    state.root.className = mergeClasses(presenceBadgeClassNames.root, rootClassName, isBusy && styles.statusBusy, state.status === 'away' && styles.statusAway, state.status === 'available' && styles.statusAvailable, state.status === 'offline' && styles.statusOffline, state.status === 'out-of-office' && styles.statusOutOfOffice, state.status === 'unknown' && styles.statusUnknown, state.outOfOffice && styles.outOfOffice, state.outOfOffice && state.status === 'available' && styles.outOfOfficeAvailable, state.outOfOffice && isBusy && styles.outOfOfficeBusy, state.outOfOffice && (state.status === 'out-of-office' || state.status === 'away' || state.status === 'offline') && styles.statusOutOfOffice, state.outOfOffice && state.status === 'unknown' && styles.outOfOfficeUnknown, state.size === 'tiny' && styles.tiny, state.size === 'large' && styles.large, state.size === 'extra-large' && styles.extraLarge, state.root.className);\n    if (state.icon) {\n        state.icon.className = mergeClasses(presenceBadgeClassNames.icon, iconClassName, state.icon.className);\n    }\n    return state;\n};\n"], "names": ["presenceBadgeClassNames", "usePresenceBadgeStyles_unstable", "root", "icon", "getIsBusy", "status", "useRootClassName", "__resetStyles", "useIconClassName", "useStyles", "__styles", "statusBusy", "sj55zd", "statusAway", "statusAvailable", "statusOffline", "statusOutOfOffice", "statusUnknown", "outOfOffice", "outOfOfficeAvailable", "outOfOfficeBusy", "outOfOfficeUnknown", "tiny", "Bubjx69", "a9b677", "B2eet1l", "B5pe6w7", "p4uzdd", "large", "extraLarge", "d", "state", "rootClassName", "iconClassName", "styles", "isBusy", "className", "mergeClasses", "size"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAEaA,uBAAuB;eAAvBA;;IA0FIC,+BAA+B;eAA/BA;;;uBA5FyC;AAEnD,MAAMD,0BAA0B;IACnCE,MAAM;IACNC,MAAM;AACV;AACA,MAAMC,YAAaC,CAAAA;IACf,IAAIA,WAAW,UAAUA,WAAW,oBAAoBA,WAAW,WAAW;QAC1E,OAAO;IACX;IACA,OAAO;AACX;AACA,MAAMC,mBAAgB,WAAA,GAAGC,IAAAA,oBAAA,EAAA,WAAA,MAAA;IAAA;CAaxB;AACD,MAAMC,mBAAgB,WAAA,GAAGD,IAAAA,oBAAA,EAAA,YAAA,MAAA;IAAA;CAGxB;AACD,MAAME,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAC,YAAA;QAAAC,QAAA;IAAA;IAAAC,YAAA;QAAAD,QAAA;IAAA;IAAAE,iBAAA;QAAAF,QAAA;IAAA;IAAAG,eAAA;QAAAH,QAAA;IAAA;IAAAI,mBAAA;QAAAJ,QAAA;IAAA;IAAAK,eAAA;QAAAL,QAAA;IAAA;IAAAM,aAAA;QAAAN,QAAA;IAAA;IAAAO,sBAAA;QAAAP,QAAA;IAAA;IAAAQ,iBAAA;QAAAR,QAAA;IAAA;IAAAS,oBAAA;QAAAT,QAAA;IAAA;IAAAU,MAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAAC,OAAA;QAAAL,SAAA;QAAAC,QAAA;QAAAE,SAAA;QAAAC,QAAA;IAAA;IAAAE,YAAA;QAAAN,SAAA;QAAAC,QAAA;QAAAE,SAAA;QAAAC,QAAA;IAAA;AAAA,GAAA;IAAAG,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AA8DP,MAAM7B,kCAAmC8B,CAAAA;IAChD;IACA,MAAMC,gBAAgB1B;IACtB,MAAM2B,gBAAgBzB;IACtB,MAAM0B,SAASzB;IACf,MAAM0B,SAAS/B,UAAU2B,MAAM1B,MAAM;IACrC0B,MAAM7B,IAAI,CAACkC,SAAS,GAAGC,IAAAA,mBAAY,EAACrC,wBAAwBE,IAAI,EAAE8B,eAAeG,UAAUD,OAAOvB,UAAU,EAAEoB,MAAM1B,MAAM,KAAK,UAAU6B,OAAOrB,UAAU,EAAEkB,MAAM1B,MAAM,KAAK,eAAe6B,OAAOpB,eAAe,EAAEiB,MAAM1B,MAAM,KAAK,aAAa6B,OAAOnB,aAAa,EAAEgB,MAAM1B,MAAM,KAAK,mBAAmB6B,OAAOlB,iBAAiB,EAAEe,MAAM1B,MAAM,KAAK,aAAa6B,OAAOjB,aAAa,EAAEc,MAAMb,WAAW,IAAIgB,OAAOhB,WAAW,EAAEa,MAAMb,WAAW,IAAIa,MAAM1B,MAAM,KAAK,eAAe6B,OAAOf,oBAAoB,EAAEY,MAAMb,WAAW,IAAIiB,UAAUD,OAAOd,eAAe,EAAEW,MAAMb,WAAW,IAAKa,CAAAA,MAAM1B,MAAM,KAAK,mBAAmB0B,MAAM1B,MAAM,KAAK,UAAU0B,MAAM1B,MAAM,KAAK,SAAA,KAAc6B,OAAOlB,iBAAiB,EAAEe,MAAMb,WAAW,IAAIa,MAAM1B,MAAM,KAAK,aAAa6B,OAAOb,kBAAkB,EAAEU,MAAMO,IAAI,KAAK,UAAUJ,OAAOZ,IAAI,EAAES,MAAMO,IAAI,KAAK,WAAWJ,OAAON,KAAK,EAAEG,MAAMO,IAAI,KAAK,iBAAiBJ,OAAOL,UAAU,EAAEE,MAAM7B,IAAI,CAACkC,SAAS;IAC75B,IAAIL,MAAM5B,IAAI,EAAE;QACZ4B,MAAM5B,IAAI,CAACiC,SAAS,GAAGC,IAAAA,mBAAY,EAACrC,wBAAwBG,IAAI,EAAE8B,eAAeF,MAAM5B,IAAI,CAACiC,SAAS;IACzG;IACA,OAAOL;AACX"}