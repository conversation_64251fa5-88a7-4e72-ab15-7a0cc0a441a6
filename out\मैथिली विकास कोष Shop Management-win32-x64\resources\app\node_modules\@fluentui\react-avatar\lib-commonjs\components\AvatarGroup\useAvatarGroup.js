"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    defaultAvatarGroupSize: function() {
        return defaultAvatarGroupSize;
    },
    useAvatarGroup_unstable: function() {
        return useAvatarGroup_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _reactutilities = require("@fluentui/react-utilities");
const useAvatarGroup_unstable = (props, ref)=>{
    const { layout = 'spread', size = defaultAvatarGroupSize } = props;
    const root = _reactutilities.slot.always((0, _reactutilities.getIntrinsicElementProps)('div', {
        role: 'group',
        ...props,
        // FIXME:
        // `ref` is wrongly assigned to be `HTMLElement` instead of `HTMLDivElement`
        // but since it would be a breaking change to fix it, we are casting ref to it's proper type
        ref: ref
    }, [
        'size'
    ]), {
        elementType: 'div'
    });
    return {
        layout,
        size,
        components: {
            root: 'div'
        },
        root
    };
};
const defaultAvatarGroupSize = 32;
