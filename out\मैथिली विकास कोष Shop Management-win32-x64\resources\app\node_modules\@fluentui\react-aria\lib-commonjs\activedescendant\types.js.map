{"version": 3, "sources": ["../src/activedescendant/types.ts"], "sourcesContent": ["import * as React from 'react';\n\nexport interface ActiveDescendantImperativeRef {\n  first: (options?: IteratorOptions) => string | undefined;\n  last: (options?: IteratorOptions) => string | undefined;\n  next: (options?: IteratorOptions) => string | undefined;\n  prev: (options?: IteratorOptions) => string | undefined;\n  find: (predicate: (id: string) => boolean, options?: IteratorOptions & FindOptions) => string | undefined;\n  blur: () => void;\n  active: () => string | undefined;\n  focus: (id: string) => void;\n  /**\n   * @deprecated This function is not used internally anymore and will be removed in the future\n   */\n  focusLastActive: () => void;\n  /**\n   * Scrolls the active option into view, if it still exists\n   */\n  scrollActiveIntoView: () => void;\n  hideAttributes: () => void;\n  showAttributes: () => void;\n  hideFocusVisibleAttributes: () => void;\n  showFocusVisibleAttributes: () => void;\n}\n\nexport interface ActiveDescendantOptions {\n  /**\n   * @param el - HTML element to test\n   * @returns whether the element can be an active descendant\n   */\n  matchOption: (el: HTMLElement) => boolean;\n  /**\n   * Forward imperative refs when exposing functionality from a React component\n   */\n  imperativeRef?: React.RefObject<ActiveDescendantImperativeRef>;\n}\n\nexport interface FindOptions {\n  /**\n   * Starts the search from a specific id\n   */\n  startFrom?: string;\n}\n\nexport interface UseActiveDescendantReturn<\n  TActiveParentElement extends HTMLElement = HTMLElement,\n  TListboxElement extends HTMLElement = HTMLElement,\n> {\n  /**\n   * Attach this to the element that contains all active descendants\n   */\n  listboxRef: React.Ref<TListboxElement>;\n  /**\n   * Attach this to the element that has an active descendant\n   */\n  activeParentRef: React.Ref<TActiveParentElement>;\n  /**\n   * Imperative functions to manage active descendants within the listboxRef\n   */\n  controller: ActiveDescendantImperativeRef;\n}\n\nexport interface IteratorOptions {\n  /**\n   * When passive, the active descendant is changed\n   * @default false\n   */\n  passive?: boolean;\n}\n"], "names": [], "rangeMappings": ";;;;;", "mappings": ";;;;;iEAAuB"}