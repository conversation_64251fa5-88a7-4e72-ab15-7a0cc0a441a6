{"version": 3, "sources": ["useAccordionItemStyles.styles.js"], "sourcesContent": ["import { mergeClasses } from '@griffel/react';\nexport const accordionItemClassNames = {\n    root: 'fui-AccordionItem'\n};\nexport const useAccordionItemStyles_unstable = (state)=>{\n    'use no memo';\n    state.root.className = mergeClasses(accordionItemClassNames.root, state.root.className);\n    return state;\n};\n"], "names": ["accordionItemClassNames", "useAccordionItemStyles_unstable", "root", "state", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IACaA,uBAAuB;eAAvBA;;IAGAC,+BAA+B;eAA/BA;;;uBAJgB;AACtB,MAAMD,0BAA0B;IACnCE,MAAM;AACV;AACO,MAAMD,kCAAmCE,CAAAA;IAC5C;IACAA,MAAMD,IAAI,CAACE,SAAS,GAAGC,IAAAA,mBAAY,EAACL,wBAAwBE,IAAI,EAAEC,MAAMD,IAAI,CAACE,SAAS;IACtF,OAAOD;AACX"}