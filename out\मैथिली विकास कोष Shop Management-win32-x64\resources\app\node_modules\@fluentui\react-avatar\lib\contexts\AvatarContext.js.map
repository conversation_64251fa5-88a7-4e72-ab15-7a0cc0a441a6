{"version": 3, "sources": ["../src/contexts/AvatarContext.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { AvatarShape, AvatarSize } from '../components/Avatar/Avatar.types';\n\nconst avatarContext = React.createContext<AvatarContextValue | undefined>(undefined);\n\n/**\n * @internal\n */\nexport interface AvatarContextValue {\n  shape?: AvatarShape;\n  size?: AvatarSize;\n}\n\nconst avatarContextDefaultValue: AvatarContextValue = {};\n\n/**\n * @internal\n */\nexport const AvatarContextProvider = avatarContext.Provider;\n\n/**\n * @internal\n */\nexport const useAvatarContext = () => React.useContext(avatarContext) ?? avatarContextDefaultValue;\n"], "names": ["React", "avatarContext", "createContext", "undefined", "avatarContextDefaultValue", "AvatarContextProvider", "Provider", "useAvatarContext", "useContext"], "rangeMappings": ";;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAG/B,MAAMC,gBAAgBD,MAAME,aAAa,CAAiCC;AAU1E,MAAMC,4BAAgD,CAAC;AAEvD;;CAEC,GACD,OAAO,MAAMC,wBAAwBJ,cAAcK,QAAQ,CAAC;AAE5D;;CAEC,GACD,OAAO,MAAMC,mBAAmB;QAAMP;WAAAA,CAAAA,oBAAAA,MAAMQ,UAAU,CAACP,4BAAjBD,+BAAAA,oBAAmCI;AAAwB,EAAE"}