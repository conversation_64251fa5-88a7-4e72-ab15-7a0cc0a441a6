import type { FluentIcon } from "../utils/createFluentIcon";
export declare const SaveSearch20Regular: FluentIcon;
export declare const SaveSync20Filled: FluentIcon;
export declare const SaveSync20Regular: FluentIcon;
export declare const Savings16Color: FluentIcon;
export declare const Savings16Filled: FluentIcon;
export declare const Savings16Regular: FluentIcon;
export declare const Savings20Color: FluentIcon;
export declare const Savings20Filled: FluentIcon;
export declare const Savings20Regular: FluentIcon;
export declare const Savings24Color: FluentIcon;
export declare const Savings24Filled: FluentIcon;
export declare const Savings24Regular: FluentIcon;
export declare const Savings32Color: FluentIcon;
export declare const Savings32Filled: FluentIcon;
export declare const Savings32Regular: FluentIcon;
export declare const ScaleFill20Filled: FluentIcon;
export declare const ScaleFill20Regular: FluentIcon;
export declare const ScaleFill24Filled: FluentIcon;
export declare const ScaleFill24Regular: FluentIcon;
export declare const ScaleFit16Filled: FluentIcon;
export declare const ScaleFit16Regular: FluentIcon;
export declare const ScaleFit20Filled: FluentIcon;
export declare const ScaleFit20Regular: FluentIcon;
export declare const ScaleFit24Filled: FluentIcon;
export declare const ScaleFit24Regular: FluentIcon;
export declare const Scales20Filled: FluentIcon;
export declare const Scales20Regular: FluentIcon;
export declare const Scales24Filled: FluentIcon;
export declare const Scales24Regular: FluentIcon;
export declare const Scales32Filled: FluentIcon;
export declare const Scales32Regular: FluentIcon;
export declare const Scan16Filled: FluentIcon;
export declare const Scan16Regular: FluentIcon;
export declare const Scan20Filled: FluentIcon;
export declare const Scan20Regular: FluentIcon;
export declare const Scan24Filled: FluentIcon;
export declare const Scan24Regular: FluentIcon;
export declare const ScanCamera16Filled: FluentIcon;
export declare const ScanCamera16Regular: FluentIcon;
export declare const ScanCamera20Filled: FluentIcon;
export declare const ScanCamera20Regular: FluentIcon;
export declare const ScanCamera24Filled: FluentIcon;
export declare const ScanCamera24Regular: FluentIcon;
export declare const ScanCamera28Filled: FluentIcon;
export declare const ScanCamera28Regular: FluentIcon;
export declare const ScanCamera48Filled: FluentIcon;
export declare const ScanCamera48Regular: FluentIcon;
export declare const ScanDash12Filled: FluentIcon;
export declare const ScanDash12Regular: FluentIcon;
export declare const ScanDash16Filled: FluentIcon;
export declare const ScanDash16Regular: FluentIcon;
export declare const ScanDash20Filled: FluentIcon;
export declare const ScanDash20Regular: FluentIcon;
export declare const ScanDash24Filled: FluentIcon;
export declare const ScanDash24Regular: FluentIcon;
export declare const ScanDash28Filled: FluentIcon;
export declare const ScanDash28Regular: FluentIcon;
export declare const ScanDash32Filled: FluentIcon;
export declare const ScanDash32Regular: FluentIcon;
export declare const ScanDash48Filled: FluentIcon;
export declare const ScanDash48Regular: FluentIcon;
export declare const ScanObject20Filled: FluentIcon;
export declare const ScanObject20Regular: FluentIcon;
export declare const ScanObject24Filled: FluentIcon;
export declare const ScanObject24Regular: FluentIcon;
export declare const ScanPerson16Color: FluentIcon;
export declare const ScanPerson16Filled: FluentIcon;
export declare const ScanPerson16Regular: FluentIcon;
export declare const ScanPerson20Color: FluentIcon;
export declare const ScanPerson20Filled: FluentIcon;
export declare const ScanPerson20Regular: FluentIcon;
export declare const ScanPerson24Color: FluentIcon;
export declare const ScanPerson24Filled: FluentIcon;
export declare const ScanPerson24Regular: FluentIcon;
export declare const ScanPerson28Color: FluentIcon;
export declare const ScanPerson28Filled: FluentIcon;
export declare const ScanPerson28Regular: FluentIcon;
export declare const ScanPerson48Color: FluentIcon;
export declare const ScanPerson48Filled: FluentIcon;
export declare const ScanPerson48Regular: FluentIcon;
export declare const ScanQrCode24Filled: FluentIcon;
export declare const ScanQrCode24Regular: FluentIcon;
export declare const ScanTable20Filled: FluentIcon;
export declare const ScanTable20Regular: FluentIcon;
export declare const ScanTable24Filled: FluentIcon;
export declare const ScanTable24Regular: FluentIcon;
export declare const ScanText16Filled: FluentIcon;
export declare const ScanText16Regular: FluentIcon;
export declare const ScanText20Filled: FluentIcon;
export declare const ScanText20Regular: FluentIcon;
export declare const ScanText24Filled: FluentIcon;
export declare const ScanText24Regular: FluentIcon;
export declare const ScanText28Filled: FluentIcon;
export declare const ScanText28Regular: FluentIcon;
export declare const ScanThumbUp16Filled: FluentIcon;
export declare const ScanThumbUp16Regular: FluentIcon;
export declare const ScanThumbUp20Filled: FluentIcon;
export declare const ScanThumbUp20Regular: FluentIcon;
export declare const ScanThumbUp24Filled: FluentIcon;
export declare const ScanThumbUp24Regular: FluentIcon;
export declare const ScanThumbUp28Filled: FluentIcon;
export declare const ScanThumbUp28Regular: FluentIcon;
export declare const ScanThumbUp48Filled: FluentIcon;
export declare const ScanThumbUp48Regular: FluentIcon;
export declare const ScanThumbUpOff16Filled: FluentIcon;
export declare const ScanThumbUpOff16Regular: FluentIcon;
export declare const ScanThumbUpOff20Filled: FluentIcon;
export declare const ScanThumbUpOff20Regular: FluentIcon;
export declare const ScanThumbUpOff24Filled: FluentIcon;
export declare const ScanThumbUpOff24Regular: FluentIcon;
export declare const ScanThumbUpOff28Filled: FluentIcon;
export declare const ScanThumbUpOff28Regular: FluentIcon;
export declare const ScanThumbUpOff48Filled: FluentIcon;
export declare const ScanThumbUpOff48Regular: FluentIcon;
export declare const ScanType20Color: FluentIcon;
export declare const ScanType20Filled: FluentIcon;
export declare const ScanType20Regular: FluentIcon;
export declare const ScanType24Color: FluentIcon;
export declare const ScanType24Filled: FluentIcon;
export declare const ScanType24Regular: FluentIcon;
export declare const ScanTypeCheckmark20Filled: FluentIcon;
export declare const ScanTypeCheckmark20Regular: FluentIcon;
export declare const ScanTypeCheckmark24Filled: FluentIcon;
export declare const ScanTypeCheckmark24Regular: FluentIcon;
export declare const ScanTypeOff20Filled: FluentIcon;
export declare const ScanTypeOff20Regular: FluentIcon;
export declare const ScanTypeOff24Filled: FluentIcon;
export declare const ScanTypeOff24Regular: FluentIcon;
export declare const Scratchpad20Filled: FluentIcon;
export declare const Scratchpad20Regular: FluentIcon;
export declare const Scratchpad24Filled: FluentIcon;
export declare const Scratchpad24Regular: FluentIcon;
export declare const ScreenCut20Filled: FluentIcon;
export declare const ScreenCut20Regular: FluentIcon;
export declare const ScreenPerson20Filled: FluentIcon;
export declare const ScreenPerson20Regular: FluentIcon;
export declare const ScreenSearch20Filled: FluentIcon;
export declare const ScreenSearch20Regular: FluentIcon;
export declare const ScreenSearch24Filled: FluentIcon;
export declare const ScreenSearch24Regular: FluentIcon;
export declare const Screenshot16Filled: FluentIcon;
export declare const Screenshot16Regular: FluentIcon;
export declare const Screenshot20Filled: FluentIcon;
export declare const Screenshot20Regular: FluentIcon;
export declare const Screenshot24Filled: FluentIcon;
export declare const Screenshot24Regular: FluentIcon;
export declare const Screenshot28Filled: FluentIcon;
export declare const Screenshot28Regular: FluentIcon;
export declare const ScreenshotRecord16Filled: FluentIcon;
export declare const ScreenshotRecord16Regular: FluentIcon;
export declare const ScreenshotRecord20Filled: FluentIcon;
export declare const ScreenshotRecord20Regular: FluentIcon;
export declare const ScreenshotRecord24Filled: FluentIcon;
export declare const ScreenshotRecord24Regular: FluentIcon;
export declare const ScreenshotRecord28Filled: FluentIcon;
export declare const ScreenshotRecord28Regular: FluentIcon;
export declare const Script16Filled: FluentIcon;
export declare const Script16Regular: FluentIcon;
export declare const Script20Filled: FluentIcon;
export declare const Script20Regular: FluentIcon;
export declare const Script24Filled: FluentIcon;
export declare const Script24Regular: FluentIcon;
export declare const Script32Filled: FluentIcon;
export declare const Script32Regular: FluentIcon;
export declare const Search12Filled: FluentIcon;
export declare const Search12Regular: FluentIcon;
export declare const Search16Filled: FluentIcon;
export declare const Search16Regular: FluentIcon;
export declare const Search20Filled: FluentIcon;
export declare const Search20Regular: FluentIcon;
export declare const Search24Filled: FluentIcon;
export declare const Search24Regular: FluentIcon;
export declare const Search28Filled: FluentIcon;
export declare const Search28Regular: FluentIcon;
export declare const Search32Filled: FluentIcon;
export declare const Search32Regular: FluentIcon;
export declare const Search48Filled: FluentIcon;
export declare const Search48Regular: FluentIcon;
export declare const SearchInfo20Filled: FluentIcon;
export declare const SearchInfo20Regular: FluentIcon;
export declare const SearchInfo24Filled: FluentIcon;
export declare const SearchInfo24Regular: FluentIcon;
export declare const SearchSettings20Filled: FluentIcon;
export declare const SearchSettings20Regular: FluentIcon;
export declare const SearchShield20Filled: FluentIcon;
export declare const SearchShield20Regular: FluentIcon;
export declare const SearchSparkle16Color: FluentIcon;
export declare const SearchSparkle16Filled: FluentIcon;
export declare const SearchSparkle16Regular: FluentIcon;
export declare const SearchSparkle20Color: FluentIcon;
export declare const SearchSparkle20Filled: FluentIcon;
export declare const SearchSparkle20Regular: FluentIcon;
export declare const SearchSparkle24Color: FluentIcon;
export declare const SearchSparkle24Filled: FluentIcon;
export declare const SearchSparkle24Regular: FluentIcon;
export declare const SearchSparkle28Color: FluentIcon;
export declare const SearchSparkle28Filled: FluentIcon;
export declare const SearchSparkle28Regular: FluentIcon;
export declare const SearchSparkle32Color: FluentIcon;
export declare const SearchSparkle32Filled: FluentIcon;
export declare const SearchSparkle32Regular: FluentIcon;
export declare const SearchSparkle48Color: FluentIcon;
export declare const SearchSparkle48Filled: FluentIcon;
export declare const SearchSparkle48Regular: FluentIcon;
export declare const SearchSquare16Filled: FluentIcon;
export declare const SearchSquare16Regular: FluentIcon;
export declare const SearchSquare20Filled: FluentIcon;
export declare const SearchSquare20Regular: FluentIcon;
export declare const SearchSquare24Filled: FluentIcon;
export declare const SearchSquare24Regular: FluentIcon;
export declare const SearchVisual16Color: FluentIcon;
export declare const SearchVisual16Filled: FluentIcon;
export declare const SearchVisual16Regular: FluentIcon;
export declare const SearchVisual20Color: FluentIcon;
export declare const SearchVisual20Filled: FluentIcon;
export declare const SearchVisual20Regular: FluentIcon;
export declare const SearchVisual24Color: FluentIcon;
export declare const SearchVisual24Filled: FluentIcon;
export declare const SearchVisual24Regular: FluentIcon;
export declare const Seat16Filled: FluentIcon;
export declare const Seat16Regular: FluentIcon;
export declare const Seat20Filled: FluentIcon;
export declare const Seat20Regular: FluentIcon;
export declare const Seat24Filled: FluentIcon;
export declare const Seat24Regular: FluentIcon;
export declare const SeatAdd16Filled: FluentIcon;
export declare const SeatAdd16Regular: FluentIcon;
export declare const SeatAdd20Filled: FluentIcon;
export declare const SeatAdd20Regular: FluentIcon;
export declare const SeatAdd24Filled: FluentIcon;
export declare const SeatAdd24Regular: FluentIcon;
export declare const SelectAllOff16Filled: FluentIcon;
export declare const SelectAllOff16Regular: FluentIcon;
export declare const SelectAllOff20Filled: FluentIcon;
export declare const SelectAllOff20Regular: FluentIcon;
export declare const SelectAllOff24Filled: FluentIcon;
export declare const SelectAllOff24Regular: FluentIcon;
export declare const SelectAllOn16Filled: FluentIcon;
export declare const SelectAllOn16Regular: FluentIcon;
export declare const SelectAllOn20Filled: FluentIcon;
export declare const SelectAllOn20Regular: FluentIcon;
export declare const SelectAllOn24Filled: FluentIcon;
export declare const SelectAllOn24Regular: FluentIcon;
export declare const SelectObject20Filled: FluentIcon;
export declare const SelectObject20Regular: FluentIcon;
export declare const SelectObject24Filled: FluentIcon;
export declare const SelectObject24Regular: FluentIcon;
export declare const SelectObjectSkew20Filled: FluentIcon;
export declare const SelectObjectSkew20Regular: FluentIcon;
export declare const SelectObjectSkew24Filled: FluentIcon;
export declare const SelectObjectSkew24Regular: FluentIcon;
export declare const SelectObjectSkewDismiss20Filled: FluentIcon;
export declare const SelectObjectSkewDismiss20Regular: FluentIcon;
export declare const SelectObjectSkewDismiss24Filled: FluentIcon;
export declare const SelectObjectSkewDismiss24Regular: FluentIcon;
export declare const SelectObjectSkewEdit20Filled: FluentIcon;
export declare const SelectObjectSkewEdit20Regular: FluentIcon;
export declare const SelectObjectSkewEdit24Filled: FluentIcon;
export declare const SelectObjectSkewEdit24Regular: FluentIcon;
export declare const Send16Color: FluentIcon;
export declare const Send16Filled: FluentIcon;
export declare const Send16Regular: FluentIcon;
export declare const Send20Color: FluentIcon;
export declare const Send20Filled: FluentIcon;
export declare const Send20Regular: FluentIcon;
export declare const Send24Color: FluentIcon;
export declare const Send24Filled: FluentIcon;
export declare const Send24Regular: FluentIcon;
export declare const Send28Color: FluentIcon;
export declare const Send28Filled: FluentIcon;
export declare const Send28Regular: FluentIcon;
export declare const Send32Color: FluentIcon;
export declare const Send32Filled: FluentIcon;
export declare const Send32Regular: FluentIcon;
export declare const Send48Color: FluentIcon;
export declare const Send48Filled: FluentIcon;
export declare const Send48Regular: FluentIcon;
export declare const SendBeaker16Filled: FluentIcon;
export declare const SendBeaker16Regular: FluentIcon;
export declare const SendBeaker20Filled: FluentIcon;
export declare const SendBeaker20Regular: FluentIcon;
export declare const SendBeaker24Filled: FluentIcon;
export declare const SendBeaker24Regular: FluentIcon;
export declare const SendBeaker28Filled: FluentIcon;
export declare const SendBeaker28Regular: FluentIcon;
export declare const SendBeaker32Filled: FluentIcon;
export declare const SendBeaker32Regular: FluentIcon;
export declare const SendBeaker48Filled: FluentIcon;
export declare const SendBeaker48Regular: FluentIcon;
export declare const SendClock20Color: FluentIcon;
export declare const SendClock20Filled: FluentIcon;
export declare const SendClock20Regular: FluentIcon;
export declare const SendClock24Color: FluentIcon;
export declare const SendClock24Filled: FluentIcon;
export declare const SendClock24Regular: FluentIcon;
export declare const SendClock32Color: FluentIcon;
export declare const SendClock32Filled: FluentIcon;
export declare const SendClock32Light: FluentIcon;
export declare const SendClock32Regular: FluentIcon;
export declare const SendCopy20Filled: FluentIcon;
export declare const SendCopy20Regular: FluentIcon;
export declare const SendCopy24Filled: FluentIcon;
export declare const SendCopy24Regular: FluentIcon;
export declare const SendPerson16Filled: FluentIcon;
export declare const SendPerson16Regular: FluentIcon;
export declare const SendPerson20Filled: FluentIcon;
export declare const SendPerson20Regular: FluentIcon;
export declare const SendPerson24Filled: FluentIcon;
export declare const SendPerson24Regular: FluentIcon;
export declare const SerialPort16Filled: FluentIcon;
export declare const SerialPort16Regular: FluentIcon;
export declare const SerialPort20Filled: FluentIcon;
export declare const SerialPort20Regular: FluentIcon;
export declare const SerialPort24Filled: FluentIcon;
export declare const SerialPort24Regular: FluentIcon;
export declare const Server12Filled: FluentIcon;
export declare const Server12Regular: FluentIcon;
export declare const Server16Filled: FluentIcon;
export declare const Server16Regular: FluentIcon;
export declare const Server20Filled: FluentIcon;
export declare const Server20Regular: FluentIcon;
export declare const Server24Filled: FluentIcon;
export declare const Server24Regular: FluentIcon;
export declare const ServerLink16Filled: FluentIcon;
export declare const ServerLink16Regular: FluentIcon;
export declare const ServerLink20Filled: FluentIcon;
export declare const ServerLink20Regular: FluentIcon;
export declare const ServerLink24Filled: FluentIcon;
export declare const ServerLink24Regular: FluentIcon;
export declare const ServerMultiple20Filled: FluentIcon;
export declare const ServerMultiple20Regular: FluentIcon;
export declare const ServerPlay20Filled: FluentIcon;
export declare const ServerPlay20Regular: FluentIcon;
export declare const ServerSurface16Filled: FluentIcon;
export declare const ServerSurface16Regular: FluentIcon;
export declare const ServerSurfaceMultiple16Filled: FluentIcon;
export declare const ServerSurfaceMultiple16Regular: FluentIcon;
export declare const ServiceBell16Filled: FluentIcon;
export declare const ServiceBell16Regular: FluentIcon;
export declare const ServiceBell20Filled: FluentIcon;
export declare const ServiceBell20Regular: FluentIcon;
export declare const ServiceBell24Filled: FluentIcon;
export declare const ServiceBell24Regular: FluentIcon;
export declare const Settings16Color: FluentIcon;
export declare const Settings16Filled: FluentIcon;
export declare const Settings16Regular: FluentIcon;
export declare const Settings20Color: FluentIcon;
export declare const Settings20Filled: FluentIcon;
export declare const Settings20Regular: FluentIcon;
export declare const Settings24Color: FluentIcon;
export declare const Settings24Filled: FluentIcon;
export declare const Settings24Regular: FluentIcon;
export declare const Settings28Color: FluentIcon;
export declare const Settings28Filled: FluentIcon;
export declare const Settings28Regular: FluentIcon;
export declare const Settings32Color: FluentIcon;
export declare const Settings32Filled: FluentIcon;
export declare const Settings32Light: FluentIcon;
export declare const Settings32Regular: FluentIcon;
export declare const Settings48Color: FluentIcon;
export declare const Settings48Filled: FluentIcon;
export declare const Settings48Regular: FluentIcon;
export declare const SettingsChat16Filled: FluentIcon;
export declare const SettingsChat16Regular: FluentIcon;
export declare const SettingsChat20Filled: FluentIcon;
export declare const SettingsChat20Regular: FluentIcon;
export declare const SettingsChat24Filled: FluentIcon;
export declare const SettingsChat24Regular: FluentIcon;
export declare const SettingsCogMultiple20Filled: FluentIcon;
export declare const SettingsCogMultiple20Regular: FluentIcon;
export declare const SettingsCogMultiple24Filled: FluentIcon;
export declare const SettingsCogMultiple24Regular: FluentIcon;
export declare const ShapeExclude16Filled: FluentIcon;
export declare const ShapeExclude16Regular: FluentIcon;
export declare const ShapeExclude20Filled: FluentIcon;
export declare const ShapeExclude20Regular: FluentIcon;
export declare const ShapeExclude24Filled: FluentIcon;
export declare const ShapeExclude24Regular: FluentIcon;
export declare const ShapeIntersect16Filled: FluentIcon;
export declare const ShapeIntersect16Regular: FluentIcon;
export declare const ShapeIntersect20Filled: FluentIcon;
export declare const ShapeIntersect20Regular: FluentIcon;
export declare const ShapeIntersect24Filled: FluentIcon;
export declare const ShapeIntersect24Regular: FluentIcon;
export declare const ShapeOrganic16Filled: FluentIcon;
export declare const ShapeOrganic16Regular: FluentIcon;
export declare const ShapeOrganic20Filled: FluentIcon;
export declare const ShapeOrganic20Regular: FluentIcon;
export declare const ShapeOrganic24Filled: FluentIcon;
export declare const ShapeOrganic24Regular: FluentIcon;
export declare const ShapeOrganic28Filled: FluentIcon;
export declare const ShapeOrganic28Regular: FluentIcon;
export declare const ShapeOrganic32Filled: FluentIcon;
export declare const ShapeOrganic32Regular: FluentIcon;
export declare const ShapeOrganic48Filled: FluentIcon;
export declare const ShapeOrganic48Regular: FluentIcon;
export declare const ShapeSubtract16Filled: FluentIcon;
export declare const ShapeSubtract16Regular: FluentIcon;
export declare const ShapeSubtract20Filled: FluentIcon;
export declare const ShapeSubtract20Regular: FluentIcon;
export declare const ShapeSubtract24Filled: FluentIcon;
export declare const ShapeSubtract24Regular: FluentIcon;
export declare const ShapeUnion16Filled: FluentIcon;
export declare const ShapeUnion16Regular: FluentIcon;
export declare const ShapeUnion20Filled: FluentIcon;
export declare const ShapeUnion20Regular: FluentIcon;
export declare const ShapeUnion24Filled: FluentIcon;
export declare const ShapeUnion24Regular: FluentIcon;
export declare const Shapes16Filled: FluentIcon;
export declare const Shapes16Regular: FluentIcon;
export declare const Shapes20Filled: FluentIcon;
export declare const Shapes20Regular: FluentIcon;
export declare const Shapes24Filled: FluentIcon;
export declare const Shapes24Regular: FluentIcon;
export declare const Shapes28Filled: FluentIcon;
export declare const Shapes28Regular: FluentIcon;
export declare const Shapes32Filled: FluentIcon;
export declare const Shapes32Regular: FluentIcon;
export declare const Shapes48Filled: FluentIcon;
export declare const Shapes48Regular: FluentIcon;
export declare const Share16Filled: FluentIcon;
export declare const Share16Regular: FluentIcon;
export declare const Share20Filled: FluentIcon;
export declare const Share20Regular: FluentIcon;
export declare const Share24Filled: FluentIcon;
export declare const Share24Regular: FluentIcon;
export declare const Share28Filled: FluentIcon;
export declare const Share28Regular: FluentIcon;
export declare const Share32Filled: FluentIcon;
export declare const Share32Light: FluentIcon;
export declare const Share32Regular: FluentIcon;
export declare const Share48Filled: FluentIcon;
export declare const Share48Regular: FluentIcon;
export declare const ShareAndroid16Color: FluentIcon;
export declare const ShareAndroid16Filled: FluentIcon;
export declare const ShareAndroid16Regular: FluentIcon;
export declare const ShareAndroid20Color: FluentIcon;
export declare const ShareAndroid20Filled: FluentIcon;
export declare const ShareAndroid20Regular: FluentIcon;
export declare const ShareAndroid24Color: FluentIcon;
export declare const ShareAndroid24Filled: FluentIcon;
export declare const ShareAndroid24Regular: FluentIcon;
export declare const ShareAndroid32Color: FluentIcon;
export declare const ShareAndroid32Filled: FluentIcon;
export declare const ShareAndroid32Regular: FluentIcon;
export declare const ShareCloseTray20Filled: FluentIcon;
export declare const ShareCloseTray20Regular: FluentIcon;
export declare const ShareCloseTray24Filled: FluentIcon;
export declare const ShareCloseTray24Regular: FluentIcon;
export declare const ShareIos20Color: FluentIcon;
export declare const ShareIos20Filled: FluentIcon;
export declare const ShareIos20Regular: FluentIcon;
export declare const ShareIos24Color: FluentIcon;
export declare const ShareIos24Filled: FluentIcon;
export declare const ShareIos24Regular: FluentIcon;
export declare const ShareIos28Color: FluentIcon;
export declare const ShareIos28Filled: FluentIcon;
export declare const ShareIos28Regular: FluentIcon;
export declare const ShareIos32Color: FluentIcon;
export declare const ShareIos32Filled: FluentIcon;
export declare const ShareIos32Regular: FluentIcon;
export declare const ShareIos48Color: FluentIcon;
export declare const ShareIos48Filled: FluentIcon;
export declare const ShareIos48Regular: FluentIcon;
export declare const ShareMultiple16Filled: FluentIcon;
export declare const ShareMultiple16Regular: FluentIcon;
export declare const ShareMultiple20Filled: FluentIcon;
export declare const ShareMultiple20Regular: FluentIcon;
export declare const ShareMultiple24Filled: FluentIcon;
export declare const ShareMultiple24Regular: FluentIcon;
export declare const ShareScreenPerson16Filled: FluentIcon;
export declare const ShareScreenPerson16Regular: FluentIcon;
export declare const ShareScreenPerson20Filled: FluentIcon;
export declare const ShareScreenPerson20Regular: FluentIcon;
export declare const ShareScreenPerson24Filled: FluentIcon;
export declare const ShareScreenPerson24Regular: FluentIcon;
export declare const ShareScreenPerson28Filled: FluentIcon;
export declare const ShareScreenPerson28Regular: FluentIcon;
export declare const ShareScreenPersonOverlay16Filled: FluentIcon;
export declare const ShareScreenPersonOverlay16Regular: FluentIcon;
export declare const ShareScreenPersonOverlay20Filled: FluentIcon;
export declare const ShareScreenPersonOverlay20Regular: FluentIcon;
export declare const ShareScreenPersonOverlay24Filled: FluentIcon;
export declare const ShareScreenPersonOverlay24Regular: FluentIcon;
export declare const ShareScreenPersonOverlay28Filled: FluentIcon;
export declare const ShareScreenPersonOverlay28Regular: FluentIcon;
export declare const ShareScreenPersonOverlayInside16Filled: FluentIcon;
export declare const ShareScreenPersonOverlayInside16Regular: FluentIcon;
export declare const ShareScreenPersonOverlayInside20Filled: FluentIcon;
export declare const ShareScreenPersonOverlayInside20Regular: FluentIcon;
export declare const ShareScreenPersonOverlayInside24Filled: FluentIcon;
export declare const ShareScreenPersonOverlayInside24Regular: FluentIcon;
export declare const ShareScreenPersonOverlayInside28Filled: FluentIcon;
export declare const ShareScreenPersonOverlayInside28Regular: FluentIcon;
export declare const ShareScreenPersonP16Filled: FluentIcon;
export declare const ShareScreenPersonP16Regular: FluentIcon;
export declare const ShareScreenPersonP20Filled: FluentIcon;
export declare const ShareScreenPersonP20Regular: FluentIcon;
export declare const ShareScreenPersonP24Filled: FluentIcon;
export declare const ShareScreenPersonP24Regular: FluentIcon;
export declare const ShareScreenPersonP28Filled: FluentIcon;
export declare const ShareScreenPersonP28Regular: FluentIcon;
export declare const ShareScreenStart16Filled: FluentIcon;
export declare const ShareScreenStart16Regular: FluentIcon;
export declare const ShareScreenStart20Filled: FluentIcon;
export declare const ShareScreenStart20Regular: FluentIcon;
export declare const ShareScreenStart24Filled: FluentIcon;
export declare const ShareScreenStart24Regular: FluentIcon;
export declare const ShareScreenStart28Filled: FluentIcon;
export declare const ShareScreenStart28Regular: FluentIcon;
export declare const ShareScreenStart48Filled: FluentIcon;
export declare const ShareScreenStart48Regular: FluentIcon;
export declare const ShareScreenStop16Filled: FluentIcon;
export declare const ShareScreenStop16Regular: FluentIcon;
export declare const ShareScreenStop20Filled: FluentIcon;
export declare const ShareScreenStop20Regular: FluentIcon;
export declare const ShareScreenStop24Filled: FluentIcon;
export declare const ShareScreenStop24Regular: FluentIcon;
export declare const ShareScreenStop28Filled: FluentIcon;
export declare const ShareScreenStop28Regular: FluentIcon;
export declare const ShareScreenStop48Filled: FluentIcon;
export declare const ShareScreenStop48Regular: FluentIcon;
export declare const Shield12Filled: FluentIcon;
export declare const Shield12Regular: FluentIcon;
export declare const Shield16Color: FluentIcon;
export declare const Shield16Filled: FluentIcon;
export declare const Shield16Regular: FluentIcon;
export declare const Shield20Color: FluentIcon;
export declare const Shield20Filled: FluentIcon;
export declare const Shield20Regular: FluentIcon;
export declare const Shield24Color: FluentIcon;
export declare const Shield24Filled: FluentIcon;
export declare const Shield24Regular: FluentIcon;
export declare const Shield28Color: FluentIcon;
export declare const Shield28Filled: FluentIcon;
export declare const Shield28Regular: FluentIcon;
export declare const Shield32Color: FluentIcon;
export declare const Shield32Filled: FluentIcon;
export declare const Shield32Regular: FluentIcon;
export declare const Shield48Color: FluentIcon;
export declare const Shield48Filled: FluentIcon;
export declare const Shield48Regular: FluentIcon;
export declare const ShieldAdd16Filled: FluentIcon;
export declare const ShieldAdd16Regular: FluentIcon;
export declare const ShieldAdd20Filled: FluentIcon;
export declare const ShieldAdd20Regular: FluentIcon;
export declare const ShieldAdd24Filled: FluentIcon;
export declare const ShieldAdd24Regular: FluentIcon;
export declare const ShieldAdd28Filled: FluentIcon;
export declare const ShieldAdd28Regular: FluentIcon;
export declare const ShieldAdd32Filled: FluentIcon;
export declare const ShieldAdd32Regular: FluentIcon;
export declare const ShieldAdd48Filled: FluentIcon;
export declare const ShieldAdd48Regular: FluentIcon;
export declare const ShieldArrowRight16Filled: FluentIcon;
export declare const ShieldArrowRight16Regular: FluentIcon;
export declare const ShieldArrowRight20Filled: FluentIcon;
export declare const ShieldArrowRight20Regular: FluentIcon;
export declare const ShieldArrowRight24Filled: FluentIcon;
export declare const ShieldArrowRight24Regular: FluentIcon;
export declare const ShieldArrowRight28Filled: FluentIcon;
export declare const ShieldArrowRight28Regular: FluentIcon;
export declare const ShieldArrowRight32Filled: FluentIcon;
export declare const ShieldArrowRight32Regular: FluentIcon;
export declare const ShieldArrowRight48Filled: FluentIcon;
export declare const ShieldArrowRight48Regular: FluentIcon;
export declare const ShieldBadge20Filled: FluentIcon;
export declare const ShieldBadge20Regular: FluentIcon;
export declare const ShieldBadge24Filled: FluentIcon;
export declare const ShieldBadge24Regular: FluentIcon;
export declare const ShieldCheckmark16Color: FluentIcon;
export declare const ShieldCheckmark16Filled: FluentIcon;
export declare const ShieldCheckmark16Regular: FluentIcon;
export declare const ShieldCheckmark20Color: FluentIcon;
export declare const ShieldCheckmark20Filled: FluentIcon;
export declare const ShieldCheckmark20Regular: FluentIcon;
export declare const ShieldCheckmark24Color: FluentIcon;
export declare const ShieldCheckmark24Filled: FluentIcon;
export declare const ShieldCheckmark24Regular: FluentIcon;
export declare const ShieldCheckmark28Color: FluentIcon;
export declare const ShieldCheckmark28Filled: FluentIcon;
export declare const ShieldCheckmark28Regular: FluentIcon;
export declare const ShieldCheckmark32Filled: FluentIcon;
export declare const ShieldCheckmark32Regular: FluentIcon;
export declare const ShieldCheckmark48Color: FluentIcon;
export declare const ShieldCheckmark48Filled: FluentIcon;
export declare const ShieldCheckmark48Regular: FluentIcon;
export declare const ShieldDismiss16Filled: FluentIcon;
export declare const ShieldDismiss16Regular: FluentIcon;
export declare const ShieldDismiss20Filled: FluentIcon;
export declare const ShieldDismiss20Regular: FluentIcon;
export declare const ShieldDismiss24Filled: FluentIcon;
export declare const ShieldDismiss24Regular: FluentIcon;
export declare const ShieldDismissShield20Filled: FluentIcon;
export declare const ShieldDismissShield20Regular: FluentIcon;
export declare const ShieldError16Filled: FluentIcon;
export declare const ShieldError16Regular: FluentIcon;
export declare const ShieldError20Filled: FluentIcon;
export declare const ShieldError20Regular: FluentIcon;
export declare const ShieldError24Filled: FluentIcon;
export declare const ShieldError24Regular: FluentIcon;
export declare const ShieldError28Filled: FluentIcon;
export declare const ShieldError28Regular: FluentIcon;
export declare const ShieldError32Filled: FluentIcon;
export declare const ShieldError32Light: FluentIcon;
export declare const ShieldError32Regular: FluentIcon;
export declare const ShieldError48Filled: FluentIcon;
export declare const ShieldError48Regular: FluentIcon;
export declare const ShieldGlobe16Filled: FluentIcon;
export declare const ShieldGlobe16Regular: FluentIcon;
export declare const ShieldGlobe20Filled: FluentIcon;
export declare const ShieldGlobe20Regular: FluentIcon;
export declare const ShieldGlobe24Filled: FluentIcon;
export declare const ShieldGlobe24Regular: FluentIcon;
export declare const ShieldKeyhole16Filled: FluentIcon;
export declare const ShieldKeyhole16Regular: FluentIcon;
export declare const ShieldKeyhole20Filled: FluentIcon;
export declare const ShieldKeyhole20Regular: FluentIcon;
export declare const ShieldKeyhole24Filled: FluentIcon;
export declare const ShieldKeyhole24Regular: FluentIcon;
export declare const ShieldLock16Filled: FluentIcon;
export declare const ShieldLock16Regular: FluentIcon;
export declare const ShieldLock20Filled: FluentIcon;
export declare const ShieldLock20Regular: FluentIcon;
export declare const ShieldLock24Filled: FluentIcon;
export declare const ShieldLock24Regular: FluentIcon;
export declare const ShieldLock28Filled: FluentIcon;
export declare const ShieldLock28Regular: FluentIcon;
export declare const ShieldLock48Filled: FluentIcon;
export declare const ShieldLock48Regular: FluentIcon;
export declare const ShieldPerson20Filled: FluentIcon;
export declare const ShieldPerson20Regular: FluentIcon;
export declare const ShieldPersonAdd20Filled: FluentIcon;
export declare const ShieldPersonAdd20Regular: FluentIcon;
export declare const ShieldProhibited20Filled: FluentIcon;
export declare const ShieldProhibited20Regular: FluentIcon;
export declare const ShieldProhibited24Filled: FluentIcon;
export declare const ShieldProhibited24Regular: FluentIcon;
export declare const ShieldQuestion16Filled: FluentIcon;
export declare const ShieldQuestion16Regular: FluentIcon;
export declare const ShieldQuestion20Filled: FluentIcon;
export declare const ShieldQuestion20Regular: FluentIcon;
export declare const ShieldQuestion24Filled: FluentIcon;
export declare const ShieldQuestion24Regular: FluentIcon;
export declare const ShieldQuestion32Filled: FluentIcon;
export declare const ShieldQuestion32Regular: FluentIcon;
export declare const ShieldSettings16Filled: FluentIcon;
export declare const ShieldSettings16Regular: FluentIcon;
export declare const ShieldSettings20Filled: FluentIcon;
export declare const ShieldSettings20Regular: FluentIcon;
export declare const ShieldSettings24Filled: FluentIcon;
export declare const ShieldSettings24Regular: FluentIcon;
export declare const ShieldSettings28Filled: FluentIcon;
export declare const ShieldSettings28Regular: FluentIcon;
export declare const ShieldTask16Filled: FluentIcon;
export declare const ShieldTask16Regular: FluentIcon;
export declare const ShieldTask20Filled: FluentIcon;
export declare const ShieldTask20Regular: FluentIcon;
export declare const ShieldTask24Filled: FluentIcon;
export declare const ShieldTask24Regular: FluentIcon;
export declare const ShieldTask28Filled: FluentIcon;
export declare const ShieldTask28Regular: FluentIcon;
export declare const ShieldTask32Filled: FluentIcon;
export declare const ShieldTask32Regular: FluentIcon;
export declare const ShieldTask48Filled: FluentIcon;
export declare const ShieldTask48Regular: FluentIcon;
export declare const Shifts16Color: FluentIcon;
export declare const Shifts16Filled: FluentIcon;
export declare const Shifts16Regular: FluentIcon;
export declare const Shifts20Color: FluentIcon;
export declare const Shifts20Filled: FluentIcon;
export declare const Shifts20Regular: FluentIcon;
export declare const Shifts24Color: FluentIcon;
export declare const Shifts24Filled: FluentIcon;
export declare const Shifts24Regular: FluentIcon;
export declare const Shifts28Color: FluentIcon;
export declare const Shifts28Filled: FluentIcon;
export declare const Shifts28Regular: FluentIcon;
export declare const Shifts30Minutes20Filled: FluentIcon;
export declare const Shifts30Minutes20Regular: FluentIcon;
export declare const Shifts30Minutes24Filled: FluentIcon;
export declare const Shifts30Minutes24Regular: FluentIcon;
export declare const Shifts32Color: FluentIcon;
export declare const Shifts32Filled: FluentIcon;
export declare const Shifts32Regular: FluentIcon;
export declare const ShiftsActivity16Filled: FluentIcon;
export declare const ShiftsActivity16Regular: FluentIcon;
export declare const ShiftsActivity20Filled: FluentIcon;
export declare const ShiftsActivity20Regular: FluentIcon;
export declare const ShiftsActivity24Filled: FluentIcon;
export declare const ShiftsActivity24Regular: FluentIcon;
export declare const ShiftsAdd20Filled: FluentIcon;
export declare const ShiftsAdd20Regular: FluentIcon;
export declare const ShiftsAdd24Filled: FluentIcon;
export declare const ShiftsAdd24Regular: FluentIcon;
export declare const ShiftsAvailability20Filled: FluentIcon;
export declare const ShiftsAvailability20Regular: FluentIcon;
export declare const ShiftsAvailability24Filled: FluentIcon;
export declare const ShiftsAvailability24Regular: FluentIcon;
export declare const ShiftsCheckmark20Filled: FluentIcon;
export declare const ShiftsCheckmark20Regular: FluentIcon;
export declare const ShiftsCheckmark24Filled: FluentIcon;
export declare const ShiftsCheckmark24Regular: FluentIcon;
export declare const ShiftsDay20Filled: FluentIcon;
export declare const ShiftsDay20Regular: FluentIcon;
export declare const ShiftsDay24Filled: FluentIcon;
export declare const ShiftsDay24Regular: FluentIcon;
export declare const ShiftsOpen20Filled: FluentIcon;
export declare const ShiftsOpen20Regular: FluentIcon;
export declare const ShiftsOpen24Filled: FluentIcon;
export declare const ShiftsOpen24Regular: FluentIcon;
export declare const ShiftsProhibited20Filled: FluentIcon;
export declare const ShiftsProhibited20Regular: FluentIcon;
export declare const ShiftsProhibited24Filled: FluentIcon;
export declare const ShiftsProhibited24Regular: FluentIcon;
export declare const ShiftsQuestionMark20Filled: FluentIcon;
export declare const ShiftsQuestionMark20Regular: FluentIcon;
export declare const ShiftsQuestionMark24Filled: FluentIcon;
export declare const ShiftsQuestionMark24Regular: FluentIcon;
export declare const ShiftsTeam20Filled: FluentIcon;
export declare const ShiftsTeam20Regular: FluentIcon;
export declare const ShiftsTeam24Filled: FluentIcon;
export declare const ShiftsTeam24Regular: FluentIcon;
export declare const ShoppingBag16Filled: FluentIcon;
export declare const ShoppingBag16Regular: FluentIcon;
export declare const ShoppingBag20Filled: FluentIcon;
export declare const ShoppingBag20Regular: FluentIcon;
export declare const ShoppingBag24Filled: FluentIcon;
export declare const ShoppingBag24Regular: FluentIcon;
export declare const ShoppingBag28Filled: FluentIcon;
export declare const ShoppingBag28Regular: FluentIcon;
export declare const ShoppingBag32Filled: FluentIcon;
export declare const ShoppingBag32Regular: FluentIcon;
export declare const ShoppingBag48Filled: FluentIcon;
export declare const ShoppingBag48Regular: FluentIcon;
export declare const ShoppingBagAdd16Filled: FluentIcon;
export declare const ShoppingBagAdd16Regular: FluentIcon;
export declare const ShoppingBagAdd20Filled: FluentIcon;
export declare const ShoppingBagAdd20Regular: FluentIcon;
export declare const ShoppingBagAdd24Filled: FluentIcon;
export declare const ShoppingBagAdd24Regular: FluentIcon;
export declare const ShoppingBagArrowLeft20Filled: FluentIcon;
export declare const ShoppingBagArrowLeft20Regular: FluentIcon;
export declare const ShoppingBagArrowLeft24Filled: FluentIcon;
export declare const ShoppingBagArrowLeft24Regular: FluentIcon;
export declare const ShoppingBagCheckmark16Filled: FluentIcon;
export declare const ShoppingBagCheckmark16Regular: FluentIcon;
export declare const ShoppingBagCheckmark20Filled: FluentIcon;
export declare const ShoppingBagCheckmark20Regular: FluentIcon;
export declare const ShoppingBagCheckmark24Filled: FluentIcon;
export declare const ShoppingBagCheckmark24Regular: FluentIcon;
export declare const ShoppingBagCheckmark28Filled: FluentIcon;
export declare const ShoppingBagCheckmark28Regular: FluentIcon;
export declare const ShoppingBagCheckmark32Filled: FluentIcon;
export declare const ShoppingBagCheckmark32Regular: FluentIcon;
export declare const ShoppingBagCheckmark48Filled: FluentIcon;
export declare const ShoppingBagCheckmark48Regular: FluentIcon;
export declare const ShoppingBagDismiss20Filled: FluentIcon;
export declare const ShoppingBagDismiss20Regular: FluentIcon;
export declare const ShoppingBagDismiss24Filled: FluentIcon;
export declare const ShoppingBagDismiss24Regular: FluentIcon;
export declare const ShoppingBagPause20Filled: FluentIcon;
export declare const ShoppingBagPause20Regular: FluentIcon;
export declare const ShoppingBagPause24Filled: FluentIcon;
export declare const ShoppingBagPause24Regular: FluentIcon;
export declare const ShoppingBagPercent20Filled: FluentIcon;
export declare const ShoppingBagPercent20Regular: FluentIcon;
export declare const ShoppingBagPercent24Filled: FluentIcon;
export declare const ShoppingBagPercent24Regular: FluentIcon;
export declare const ShoppingBagPlay20Filled: FluentIcon;
export declare const ShoppingBagPlay20Regular: FluentIcon;
export declare const ShoppingBagPlay24Filled: FluentIcon;
export declare const ShoppingBagPlay24Regular: FluentIcon;
export declare const ShoppingBagTag16Filled: FluentIcon;
export declare const ShoppingBagTag16Regular: FluentIcon;
export declare const ShoppingBagTag20Filled: FluentIcon;
export declare const ShoppingBagTag20Regular: FluentIcon;
export declare const ShoppingBagTag24Filled: FluentIcon;
export declare const ShoppingBagTag24Regular: FluentIcon;
export declare const ShoppingBagTag28Filled: FluentIcon;
export declare const ShoppingBagTag28Regular: FluentIcon;
export declare const ShoppingBagTag32Filled: FluentIcon;
export declare const ShoppingBagTag32Regular: FluentIcon;
export declare const ShoppingBagTag48Filled: FluentIcon;
export declare const ShoppingBagTag48Regular: FluentIcon;
export declare const Shortpick20Filled: FluentIcon;
export declare const Shortpick20Regular: FluentIcon;
export declare const Shortpick24Filled: FluentIcon;
export declare const Shortpick24Regular: FluentIcon;
export declare const Showerhead20Filled: FluentIcon;
export declare const Showerhead20Regular: FluentIcon;
export declare const Showerhead24Filled: FluentIcon;
export declare const Showerhead24Regular: FluentIcon;
export declare const Showerhead32Filled: FluentIcon;
export declare const Showerhead32Regular: FluentIcon;
export declare const SidebarSearchLtr20Filled: FluentIcon;
export declare const SidebarSearchLtr20Regular: FluentIcon;
export declare const SidebarSearchRtl20Filled: FluentIcon;
export declare const SidebarSearchRtl20Regular: FluentIcon;
export declare const SignOut20Filled: FluentIcon;
export declare const SignOut20Regular: FluentIcon;
export declare const SignOut24Filled: FluentIcon;
export declare const SignOut24Regular: FluentIcon;
export declare const Signature16Filled: FluentIcon;
export declare const Signature16Regular: FluentIcon;
export declare const Signature20Filled: FluentIcon;
export declare const Signature20Regular: FluentIcon;
export declare const Signature24Filled: FluentIcon;
export declare const Signature24Regular: FluentIcon;
export declare const Signature28Filled: FluentIcon;
export declare const Signature28Regular: FluentIcon;
export declare const Signature32Filled: FluentIcon;
export declare const Signature32Light: FluentIcon;
export declare const Signature32Regular: FluentIcon;
export declare const Sim16Filled: FluentIcon;
export declare const Sim16Regular: FluentIcon;
export declare const Sim20Filled: FluentIcon;
export declare const Sim20Regular: FluentIcon;
export declare const Sim24Filled: FluentIcon;
export declare const Sim24Regular: FluentIcon;
export declare const SkipBack1020Filled: FluentIcon;
export declare const SkipBack1020Regular: FluentIcon;
export declare const SkipBack1024Filled: FluentIcon;
export declare const SkipBack1024Regular: FluentIcon;
export declare const SkipBack1028Filled: FluentIcon;
export declare const SkipBack1028Regular: FluentIcon;
export declare const SkipBack1032Filled: FluentIcon;
export declare const SkipBack1032Regular: FluentIcon;
export declare const SkipBack1048Filled: FluentIcon;
export declare const SkipBack1048Regular: FluentIcon;
export declare const SkipBack1520Filled: FluentIcon;
export declare const SkipBack1520Regular: FluentIcon;
export declare const SkipBack1524Filled: FluentIcon;
export declare const SkipBack1524Regular: FluentIcon;
export declare const SkipBack1528Filled: FluentIcon;
export declare const SkipBack1528Regular: FluentIcon;
export declare const SkipBack1532Filled: FluentIcon;
export declare const SkipBack1532Regular: FluentIcon;
export declare const SkipBack1548Filled: FluentIcon;
export declare const SkipBack1548Regular: FluentIcon;
export declare const SkipForward1020Filled: FluentIcon;
export declare const SkipForward1020Regular: FluentIcon;
export declare const SkipForward1024Filled: FluentIcon;
export declare const SkipForward1024Regular: FluentIcon;
export declare const SkipForward1028Filled: FluentIcon;
export declare const SkipForward1028Regular: FluentIcon;
export declare const SkipForward1032Filled: FluentIcon;
export declare const SkipForward1032Regular: FluentIcon;
export declare const SkipForward1048Filled: FluentIcon;
export declare const SkipForward1048Regular: FluentIcon;
export declare const SkipForward1520Filled: FluentIcon;
export declare const SkipForward1520Regular: FluentIcon;
export declare const SkipForward1524Filled: FluentIcon;
export declare const SkipForward1524Regular: FluentIcon;
export declare const SkipForward1528Filled: FluentIcon;
export declare const SkipForward1528Regular: FluentIcon;
export declare const SkipForward1532Filled: FluentIcon;
export declare const SkipForward1532Regular: FluentIcon;
export declare const SkipForward1548Filled: FluentIcon;
export declare const SkipForward1548Regular: FluentIcon;
export declare const SkipForward3020Filled: FluentIcon;
export declare const SkipForward3020Regular: FluentIcon;
export declare const SkipForward3024Filled: FluentIcon;
export declare const SkipForward3024Regular: FluentIcon;
export declare const SkipForward3028Filled: FluentIcon;
export declare const SkipForward3028Regular: FluentIcon;
export declare const SkipForward3032Filled: FluentIcon;
export declare const SkipForward3032Regular: FluentIcon;
export declare const SkipForward3048Filled: FluentIcon;
export declare const SkipForward3048Regular: FluentIcon;
export declare const SkipForwardTab20Filled: FluentIcon;
export declare const SkipForwardTab20Regular: FluentIcon;
export declare const SkipForwardTab24Filled: FluentIcon;
export declare const SkipForwardTab24Regular: FluentIcon;
export declare const SlashForward12Filled: FluentIcon;
export declare const SlashForward12Regular: FluentIcon;
export declare const SlashForward16Filled: FluentIcon;
export declare const SlashForward16Regular: FluentIcon;
export declare const SlashForward20Filled: FluentIcon;
export declare const SlashForward20Regular: FluentIcon;
export declare const SlashForward24Filled: FluentIcon;
export declare const SlashForward24Regular: FluentIcon;
export declare const Sleep20Filled: FluentIcon;
export declare const Sleep20Regular: FluentIcon;
export declare const Sleep24Filled: FluentIcon;
export declare const Sleep24Regular: FluentIcon;
export declare const SlideAdd16Filled: FluentIcon;
export declare const SlideAdd16Regular: FluentIcon;
export declare const SlideAdd20Filled: FluentIcon;
export declare const SlideAdd20Regular: FluentIcon;
export declare const SlideAdd24Filled: FluentIcon;
export declare const SlideAdd24Regular: FluentIcon;
export declare const SlideAdd28Filled: FluentIcon;
export declare const SlideAdd28Regular: FluentIcon;
export declare const SlideAdd32Filled: FluentIcon;
export declare const SlideAdd32Regular: FluentIcon;
export declare const SlideAdd48Filled: FluentIcon;
export declare const SlideAdd48Regular: FluentIcon;
export declare const SlideArrowRight20Filled: FluentIcon;
export declare const SlideArrowRight20Regular: FluentIcon;
export declare const SlideArrowRight24Filled: FluentIcon;
export declare const SlideArrowRight24Regular: FluentIcon;
export declare const SlideContent24Filled: FluentIcon;
export declare const SlideContent24Regular: FluentIcon;
export declare const SlideEraser16Filled: FluentIcon;
export declare const SlideEraser16Regular: FluentIcon;
export declare const SlideEraser20Filled: FluentIcon;
export declare const SlideEraser20Regular: FluentIcon;
export declare const SlideEraser24Filled: FluentIcon;
export declare const SlideEraser24Regular: FluentIcon;
export declare const SlideGrid20Filled: FluentIcon;
export declare const SlideGrid20Regular: FluentIcon;
export declare const SlideGrid24Filled: FluentIcon;
export declare const SlideGrid24Regular: FluentIcon;
export declare const SlideHide20Filled: FluentIcon;
export declare const SlideHide20Regular: FluentIcon;
export declare const SlideHide24Filled: FluentIcon;
export declare const SlideHide24Regular: FluentIcon;
export declare const SlideLayout20Filled: FluentIcon;
export declare const SlideLayout20Regular: FluentIcon;
export declare const SlideLayout24Filled: FluentIcon;
export declare const SlideLayout24Regular: FluentIcon;
export declare const SlideLink20Filled: FluentIcon;
export declare const SlideLink20Regular: FluentIcon;
export declare const SlideLink24Filled: FluentIcon;
export declare const SlideLink24Regular: FluentIcon;
export declare const SlideMicrophone20Filled: FluentIcon;
export declare const SlideMicrophone20Regular: FluentIcon;
export declare const SlideMicrophone24Filled: FluentIcon;
export declare const SlideMicrophone24Regular: FluentIcon;
export declare const SlideMicrophone32Filled: FluentIcon;
export declare const SlideMicrophone32Regular: FluentIcon;
export declare const SlideMultiple20Filled: FluentIcon;
export declare const SlideMultiple20Regular: FluentIcon;
export declare const SlideMultiple24Filled: FluentIcon;
export declare const SlideMultiple24Regular: FluentIcon;
export declare const SlideMultipleArrowRight20Filled: FluentIcon;
export declare const SlideMultipleArrowRight20Regular: FluentIcon;
export declare const SlideMultipleArrowRight24Filled: FluentIcon;
export declare const SlideMultipleArrowRight24Regular: FluentIcon;
export declare const SlideMultipleSearch20Filled: FluentIcon;
export declare const SlideMultipleSearch20Regular: FluentIcon;
export declare const SlideMultipleSearch24Filled: FluentIcon;
export declare const SlideMultipleSearch24Regular: FluentIcon;
export declare const SlidePlay20Filled: FluentIcon;
export declare const SlidePlay20Regular: FluentIcon;
export declare const SlidePlay24Filled: FluentIcon;
export declare const SlidePlay24Regular: FluentIcon;
export declare const SlideRecord16Filled: FluentIcon;
export declare const SlideRecord16Regular: FluentIcon;
export declare const SlideRecord20Filled: FluentIcon;
export declare const SlideRecord20Regular: FluentIcon;
export declare const SlideRecord24Filled: FluentIcon;
export declare const SlideRecord24Regular: FluentIcon;
export declare const SlideRecord28Filled: FluentIcon;
export declare const SlideRecord28Regular: FluentIcon;
export declare const SlideRecord48Filled: FluentIcon;
export declare const SlideRecord48Regular: FluentIcon;
export declare const SlideSearch16Filled: FluentIcon;
export declare const SlideSearch16Regular: FluentIcon;
export declare const SlideSearch20Filled: FluentIcon;
export declare const SlideSearch20Regular: FluentIcon;
export declare const SlideSearch24Filled: FluentIcon;
export declare const SlideSearch24Regular: FluentIcon;
export declare const SlideSearch28Filled: FluentIcon;
export declare const SlideSearch28Regular: FluentIcon;
export declare const SlideSearch32Filled: FluentIcon;
export declare const SlideSearch32Regular: FluentIcon;
export declare const SlideSettings20Filled: FluentIcon;
export declare const SlideSettings20Regular: FluentIcon;
export declare const SlideSettings24Filled: FluentIcon;
export declare const SlideSettings24Regular: FluentIcon;
export declare const SlideSize20Filled: FluentIcon;
export declare const SlideSize20Regular: FluentIcon;
export declare const SlideSize24Filled: FluentIcon;
export declare const SlideSize24Regular: FluentIcon;
export declare const SlideText16Filled: FluentIcon;
export declare const SlideText16Regular: FluentIcon;
export declare const SlideText20Filled: FluentIcon;
export declare const SlideText20Regular: FluentIcon;
export declare const SlideText24Filled: FluentIcon;
export declare const SlideText24Regular: FluentIcon;
export declare const SlideText28Filled: FluentIcon;
export declare const SlideText28Regular: FluentIcon;
export declare const SlideText48Filled: FluentIcon;
export declare const SlideText48Regular: FluentIcon;
export declare const SlideTextCall16Filled: FluentIcon;
export declare const SlideTextCall16Regular: FluentIcon;
export declare const SlideTextCall20Filled: FluentIcon;
export declare const SlideTextCall20Regular: FluentIcon;
export declare const SlideTextCall24Filled: FluentIcon;
export declare const SlideTextCall24Regular: FluentIcon;
export declare const SlideTextCall28Filled: FluentIcon;
export declare const SlideTextCall28Regular: FluentIcon;
export declare const SlideTextCall48Filled: FluentIcon;
export declare const SlideTextCall48Regular: FluentIcon;
export declare const SlideTextCursor20Filled: FluentIcon;
export declare const SlideTextCursor20Regular: FluentIcon;
export declare const SlideTextCursor24Filled: FluentIcon;
export declare const SlideTextCursor24Regular: FluentIcon;
