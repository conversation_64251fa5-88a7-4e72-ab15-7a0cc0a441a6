{"version": 3, "sources": ["useCarouselViewportStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses } from '@griffel/react';\nexport const carouselViewportClassNames = {\n    root: 'fui-CarouselViewport'\n};\n/**\n * Styles for the root slot\n */ const useStyles = makeStyles({\n    root: {\n        maxWidth: '100%',\n        width: 'auto'\n    }\n});\n/**\n * Apply styling to the CarouselViewport slots based on the state\n */ export const useCarouselViewportStyles_unstable = (state)=>{\n    'use no memo';\n    const styles = useStyles();\n    state.root.className = mergeClasses(carouselViewportClassNames.root, styles.root, state.root.className);\n    return state;\n};\n"], "names": ["carouselViewportClassNames", "useCarouselViewportStyles_unstable", "root", "useStyles", "__styles", "B2u0y6b", "a9b677", "d", "state", "styles", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IACaA,0BAA0B;eAA1BA;;IAaIC,kCAAkC;eAAlCA;;;uBAdwB;AAClC,MAAMD,6BAA6B;IACtCE,MAAM;AACV;AACA;;CAEA,GAAI,MAAMC,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAF,MAAA;QAAAG,SAAA;QAAAC,QAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;QAAA;KAAA;AAAA;AAQX,MAAMN,qCAAsCO,CAAAA;IACnD;IACA,MAAMC,SAASN;IACfK,MAAMN,IAAI,CAACQ,SAAS,GAAGC,IAAAA,mBAAY,EAACX,2BAA2BE,IAAI,EAAEO,OAAOP,IAAI,EAAEM,MAAMN,IAAI,CAACQ,SAAS;IACtG,OAAOF;AACX"}