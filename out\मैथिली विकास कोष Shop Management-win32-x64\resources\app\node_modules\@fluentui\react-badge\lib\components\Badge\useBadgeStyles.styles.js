import { shorthands, __resetStyles, __styles, mergeClasses } from '@griffel/react';
import { tokens, typographyStyles } from '@fluentui/react-theme';
export const badgeClassNames = {
  root: 'fui-Badge',
  icon: 'fui-Badge__icon'
};
// The text content of the badge has additional horizontal padding, but there is no `text` slot to add that padding to.
// Instead, add extra padding to the root, and a negative margin on the icon to "remove" the extra padding on the icon.
const textPadding = tokens.spacingHorizontalXXS;
const useRootClassName = /*#__PURE__*/__resetStyles("r1iycov", "r115jdol", [".r1iycov{display:inline-flex;box-sizing:border-box;align-items:center;justify-content:center;position:relative;font-family:var(--fontFamilyBase);font-size:var(--fontSizeBase200);font-weight:var(--fontWeightSemibold);line-height:var(--lineHeightBase200);height:20px;min-width:20px;padding:0 calc(var(--spacingHorizontalXS) + var(--spacingHorizontalXXS));border-radius:var(--borderRadiusCircular);border-color:var(--colorTransparentStroke);}", ".r1iycov::after{content:\"\";position:absolute;top:0;left:0;bottom:0;right:0;border-style:solid;border-color:inherit;border-width:var(--strokeWidthThin);border-radius:inherit;}", ".r115jdol{display:inline-flex;box-sizing:border-box;align-items:center;justify-content:center;position:relative;font-family:var(--fontFamilyBase);font-size:var(--fontSizeBase200);font-weight:var(--fontWeightSemibold);line-height:var(--lineHeightBase200);height:20px;min-width:20px;padding:0 calc(var(--spacingHorizontalXS) + var(--spacingHorizontalXXS));border-radius:var(--borderRadiusCircular);border-color:var(--colorTransparentStroke);}", ".r115jdol::after{content:\"\";position:absolute;top:0;right:0;bottom:0;left:0;border-style:solid;border-color:inherit;border-width:var(--strokeWidthThin);border-radius:inherit;}"]);
const useRootStyles = /*#__PURE__*/__styles({
  fontSmallToTiny: {
    Bahqtrf: "fk6fouc",
    Be2twd7: "f13mqy1h",
    Bhrd7zp: "fl43uef",
    Bg96gwp: "fcpl73t"
  },
  tiny: {
    a9b677: "f16dn6v3",
    Bqenvij: "f3mu39s",
    Be2twd7: "f130uwy9",
    Bg96gwp: "fod1mrr",
    Bf4jedk: "f18p0k4z",
    Byoj8tv: 0,
    uwmqm3: 0,
    z189sj: 0,
    z8tnut: 0,
    B0ocmuz: "f19jm9xf"
  },
  "extra-small": {
    a9b677: "fpd43o0",
    Bqenvij: "f30q22z",
    Be2twd7: "f1tccstq",
    Bg96gwp: "f1y3arg5",
    Bf4jedk: "f18p0k4z",
    Byoj8tv: 0,
    uwmqm3: 0,
    z189sj: 0,
    z8tnut: 0,
    B0ocmuz: "f19jm9xf"
  },
  small: {
    Bf4jedk: "fq2vo04",
    Bqenvij: "fd461yt",
    Byoj8tv: 0,
    uwmqm3: 0,
    z189sj: 0,
    z8tnut: 0,
    B0ocmuz: "fupdldz"
  },
  medium: {},
  large: {
    Bf4jedk: "f17fgpbq",
    Bqenvij: "frvgh55",
    Byoj8tv: 0,
    uwmqm3: 0,
    z189sj: 0,
    z8tnut: 0,
    B0ocmuz: "f1996nqw"
  },
  "extra-large": {
    Bf4jedk: "fwbmr0d",
    Bqenvij: "f1d2rq10",
    Byoj8tv: 0,
    uwmqm3: 0,
    z189sj: 0,
    z8tnut: 0,
    B0ocmuz: "fty64o7"
  },
  square: {
    Beyfa6y: 0,
    Bbmb7ep: 0,
    Btl43ni: 0,
    B7oj6ja: 0,
    Dimara: "f1fabniw"
  },
  rounded: {
    Beyfa6y: 0,
    Bbmb7ep: 0,
    Btl43ni: 0,
    B7oj6ja: 0,
    Dimara: "ft85np5"
  },
  roundedSmallToTiny: {
    Beyfa6y: 0,
    Bbmb7ep: 0,
    Btl43ni: 0,
    B7oj6ja: 0,
    Dimara: "fq9zq91"
  },
  circular: {},
  borderGhost: {
    ap17g6: "f10ludwy"
  },
  filled: {},
  "filled-brand": {
    De3pzq: "ffp7eso",
    sj55zd: "f1phragk"
  },
  "filled-danger": {
    De3pzq: "fdl5y0r",
    sj55zd: "f1phragk"
  },
  "filled-important": {
    De3pzq: "f1c73kur",
    sj55zd: "fr0bkrk"
  },
  "filled-informative": {
    De3pzq: "f3vzo32",
    sj55zd: "f11d4kpn"
  },
  "filled-severe": {
    De3pzq: "f1s438gw",
    sj55zd: "f1phragk"
  },
  "filled-subtle": {
    De3pzq: "fxugw4r",
    sj55zd: "f19n0e5"
  },
  "filled-success": {
    De3pzq: "flxk52p",
    sj55zd: "f1phragk"
  },
  "filled-warning": {
    De3pzq: "ffq97bm",
    sj55zd: "ff5vbop"
  },
  ghost: {},
  "ghost-brand": {
    sj55zd: "f16muhyy"
  },
  "ghost-danger": {
    sj55zd: "f1whyuy6"
  },
  "ghost-important": {
    sj55zd: "f19n0e5"
  },
  "ghost-informative": {
    sj55zd: "f11d4kpn"
  },
  "ghost-severe": {
    sj55zd: "f1l8vj45"
  },
  "ghost-subtle": {
    sj55zd: "fonrgv7"
  },
  "ghost-success": {
    sj55zd: "f1m7fhi8"
  },
  "ghost-warning": {
    sj55zd: "fpti2h4"
  },
  outline: {
    g2u3we: "f23ftbb",
    h3c5rm: ["f1gkuv52", "f1p1bl80"],
    B9xav0g: "fioka3i",
    zhjwy3: ["f1p1bl80", "f1gkuv52"]
  },
  "outline-brand": {
    sj55zd: "f16muhyy"
  },
  "outline-danger": {
    sj55zd: "f1whyuy6",
    g2u3we: "fyqpifd",
    h3c5rm: ["f3ukxca", "f1k7dugc"],
    B9xav0g: "f1njxb2b",
    zhjwy3: ["f1k7dugc", "f3ukxca"]
  },
  "outline-important": {
    sj55zd: "f11d4kpn",
    g2u3we: "fq0vr37",
    h3c5rm: ["f1byw159", "f11cr0be"],
    B9xav0g: "f1c1zstj",
    zhjwy3: ["f11cr0be", "f1byw159"]
  },
  "outline-informative": {
    sj55zd: "f11d4kpn",
    g2u3we: "f68mrw8",
    h3c5rm: ["f7pw515", "fw35ms5"],
    B9xav0g: "frpde29",
    zhjwy3: ["fw35ms5", "f7pw515"]
  },
  "outline-severe": {
    sj55zd: "f1l8vj45"
  },
  "outline-subtle": {
    sj55zd: "fonrgv7"
  },
  "outline-success": {
    sj55zd: "f1m7fhi8",
    g2u3we: "f1mmhl11",
    h3c5rm: ["f1tjpp2f", "f1ocn5n7"],
    B9xav0g: "f1gjv25d",
    zhjwy3: ["f1ocn5n7", "f1tjpp2f"]
  },
  "outline-warning": {
    sj55zd: "fpti2h4"
  },
  tint: {},
  "tint-brand": {
    De3pzq: "f16xkysk",
    sj55zd: "faj9fo0",
    g2u3we: "f161y7kd",
    h3c5rm: ["f1c8dzaj", "f1sl6hi9"],
    B9xav0g: "f1619yhw",
    zhjwy3: ["f1sl6hi9", "f1c8dzaj"]
  },
  "tint-danger": {
    De3pzq: "ff0poqj",
    sj55zd: "f1hcrxcs",
    g2u3we: "f1oqjm8o",
    h3c5rm: ["fkgrb8g", "frb5wm0"],
    B9xav0g: "f1iai1ph",
    zhjwy3: ["frb5wm0", "fkgrb8g"]
  },
  "tint-important": {
    De3pzq: "f945g0u",
    sj55zd: "fr0bkrk",
    g2u3we: "fghlq4f",
    h3c5rm: ["f1gn591s", "fjscplz"],
    B9xav0g: "fb073pr",
    zhjwy3: ["fjscplz", "f1gn591s"]
  },
  "tint-informative": {
    De3pzq: "f1ctqxl6",
    sj55zd: "f11d4kpn",
    g2u3we: "f68mrw8",
    h3c5rm: ["f7pw515", "fw35ms5"],
    B9xav0g: "frpde29",
    zhjwy3: ["fw35ms5", "f7pw515"]
  },
  "tint-severe": {
    De3pzq: "f1xzsg4",
    sj55zd: "f1k5f75o",
    g2u3we: "fxy9dsj",
    h3c5rm: ["f54u6j2", "fcm23ze"],
    B9xav0g: "f4vf0uq",
    zhjwy3: ["fcm23ze", "f54u6j2"]
  },
  "tint-subtle": {
    De3pzq: "fxugw4r",
    sj55zd: "f11d4kpn",
    g2u3we: "f68mrw8",
    h3c5rm: ["f7pw515", "fw35ms5"],
    B9xav0g: "frpde29",
    zhjwy3: ["fw35ms5", "f7pw515"]
  },
  "tint-success": {
    De3pzq: "f2vsrz6",
    sj55zd: "ffmvakt",
    g2u3we: "fdmic9h",
    h3c5rm: ["f196y6m", "fetptd8"],
    B9xav0g: "f1pev5xq",
    zhjwy3: ["fetptd8", "f196y6m"]
  },
  "tint-warning": {
    De3pzq: "f10s6hli",
    sj55zd: "f42v8de",
    g2u3we: "fn9i3n",
    h3c5rm: ["f1aw8cx4", "f51if14"],
    B9xav0g: "fvq8iai",
    zhjwy3: ["f51if14", "f1aw8cx4"]
  }
}, {
  d: [".fk6fouc{font-family:var(--fontFamilyBase);}", ".f13mqy1h{font-size:var(--fontSizeBase100);}", ".fl43uef{font-weight:var(--fontWeightSemibold);}", ".fcpl73t{line-height:var(--lineHeightBase100);}", ".f16dn6v3{width:6px;}", ".f3mu39s{height:6px;}", ".f130uwy9{font-size:4px;}", ".fod1mrr{line-height:4px;}", ".f18p0k4z{min-width:unset;}", [".f19jm9xf{padding:unset;}", {
    p: -1
  }], ".fpd43o0{width:10px;}", ".f30q22z{height:10px;}", ".f1tccstq{font-size:6px;}", ".f1y3arg5{line-height:6px;}", [".f19jm9xf{padding:unset;}", {
    p: -1
  }], ".fq2vo04{min-width:16px;}", ".fd461yt{height:16px;}", [".fupdldz{padding:0 calc(var(--spacingHorizontalXXS) + var(--spacingHorizontalXXS));}", {
    p: -1
  }], ".f17fgpbq{min-width:24px;}", ".frvgh55{height:24px;}", [".f1996nqw{padding:0 calc(var(--spacingHorizontalXS) + var(--spacingHorizontalXXS));}", {
    p: -1
  }], ".fwbmr0d{min-width:32px;}", ".f1d2rq10{height:32px;}", [".fty64o7{padding:0 calc(var(--spacingHorizontalSNudge) + var(--spacingHorizontalXXS));}", {
    p: -1
  }], [".f1fabniw{border-radius:var(--borderRadiusNone);}", {
    p: -1
  }], [".ft85np5{border-radius:var(--borderRadiusMedium);}", {
    p: -1
  }], [".fq9zq91{border-radius:var(--borderRadiusSmall);}", {
    p: -1
  }], ".f10ludwy::after{display:none;}", ".ffp7eso{background-color:var(--colorBrandBackground);}", ".f1phragk{color:var(--colorNeutralForegroundOnBrand);}", ".fdl5y0r{background-color:var(--colorPaletteRedBackground3);}", ".f1c73kur{background-color:var(--colorNeutralForeground1);}", ".fr0bkrk{color:var(--colorNeutralBackground1);}", ".f3vzo32{background-color:var(--colorNeutralBackground5);}", ".f11d4kpn{color:var(--colorNeutralForeground3);}", ".f1s438gw{background-color:var(--colorPaletteDarkOrangeBackground3);}", ".fxugw4r{background-color:var(--colorNeutralBackground1);}", ".f19n0e5{color:var(--colorNeutralForeground1);}", ".flxk52p{background-color:var(--colorPaletteGreenBackground3);}", ".ffq97bm{background-color:var(--colorPaletteYellowBackground3);}", ".ff5vbop{color:var(--colorNeutralForeground1Static);}", ".f16muhyy{color:var(--colorBrandForeground1);}", ".f1whyuy6{color:var(--colorPaletteRedForeground3);}", ".f1l8vj45{color:var(--colorPaletteDarkOrangeForeground3);}", ".fonrgv7{color:var(--colorNeutralForegroundStaticInverted);}", ".f1m7fhi8{color:var(--colorPaletteGreenForeground3);}", ".fpti2h4{color:var(--colorPaletteYellowForeground2);}", ".f23ftbb{border-top-color:currentColor;}", ".f1gkuv52{border-right-color:currentColor;}", ".f1p1bl80{border-left-color:currentColor;}", ".fioka3i{border-bottom-color:currentColor;}", ".fyqpifd{border-top-color:var(--colorPaletteRedBorder2);}", ".f3ukxca{border-right-color:var(--colorPaletteRedBorder2);}", ".f1k7dugc{border-left-color:var(--colorPaletteRedBorder2);}", ".f1njxb2b{border-bottom-color:var(--colorPaletteRedBorder2);}", ".fq0vr37{border-top-color:var(--colorNeutralStrokeAccessible);}", ".f1byw159{border-right-color:var(--colorNeutralStrokeAccessible);}", ".f11cr0be{border-left-color:var(--colorNeutralStrokeAccessible);}", ".f1c1zstj{border-bottom-color:var(--colorNeutralStrokeAccessible);}", ".f68mrw8{border-top-color:var(--colorNeutralStroke2);}", ".f7pw515{border-right-color:var(--colorNeutralStroke2);}", ".fw35ms5{border-left-color:var(--colorNeutralStroke2);}", ".frpde29{border-bottom-color:var(--colorNeutralStroke2);}", ".f1mmhl11{border-top-color:var(--colorPaletteGreenBorder2);}", ".f1tjpp2f{border-right-color:var(--colorPaletteGreenBorder2);}", ".f1ocn5n7{border-left-color:var(--colorPaletteGreenBorder2);}", ".f1gjv25d{border-bottom-color:var(--colorPaletteGreenBorder2);}", ".f16xkysk{background-color:var(--colorBrandBackground2);}", ".faj9fo0{color:var(--colorBrandForeground2);}", ".f161y7kd{border-top-color:var(--colorBrandStroke2);}", ".f1c8dzaj{border-right-color:var(--colorBrandStroke2);}", ".f1sl6hi9{border-left-color:var(--colorBrandStroke2);}", ".f1619yhw{border-bottom-color:var(--colorBrandStroke2);}", ".ff0poqj{background-color:var(--colorPaletteRedBackground1);}", ".f1hcrxcs{color:var(--colorPaletteRedForeground1);}", ".f1oqjm8o{border-top-color:var(--colorPaletteRedBorder1);}", ".fkgrb8g{border-right-color:var(--colorPaletteRedBorder1);}", ".frb5wm0{border-left-color:var(--colorPaletteRedBorder1);}", ".f1iai1ph{border-bottom-color:var(--colorPaletteRedBorder1);}", ".f945g0u{background-color:var(--colorNeutralForeground3);}", ".fghlq4f{border-top-color:var(--colorTransparentStroke);}", ".f1gn591s{border-right-color:var(--colorTransparentStroke);}", ".fjscplz{border-left-color:var(--colorTransparentStroke);}", ".fb073pr{border-bottom-color:var(--colorTransparentStroke);}", ".f1ctqxl6{background-color:var(--colorNeutralBackground4);}", ".f1xzsg4{background-color:var(--colorPaletteDarkOrangeBackground1);}", ".f1k5f75o{color:var(--colorPaletteDarkOrangeForeground1);}", ".fxy9dsj{border-top-color:var(--colorPaletteDarkOrangeBorder1);}", ".f54u6j2{border-right-color:var(--colorPaletteDarkOrangeBorder1);}", ".fcm23ze{border-left-color:var(--colorPaletteDarkOrangeBorder1);}", ".f4vf0uq{border-bottom-color:var(--colorPaletteDarkOrangeBorder1);}", ".f2vsrz6{background-color:var(--colorPaletteGreenBackground1);}", ".ffmvakt{color:var(--colorPaletteGreenForeground1);}", ".fdmic9h{border-top-color:var(--colorPaletteGreenBorder1);}", ".f196y6m{border-right-color:var(--colorPaletteGreenBorder1);}", ".fetptd8{border-left-color:var(--colorPaletteGreenBorder1);}", ".f1pev5xq{border-bottom-color:var(--colorPaletteGreenBorder1);}", ".f10s6hli{background-color:var(--colorPaletteYellowBackground1);}", ".f42v8de{color:var(--colorPaletteYellowForeground1);}", ".fn9i3n{border-top-color:var(--colorPaletteYellowBorder1);}", ".f1aw8cx4{border-right-color:var(--colorPaletteYellowBorder1);}", ".f51if14{border-left-color:var(--colorPaletteYellowBorder1);}", ".fvq8iai{border-bottom-color:var(--colorPaletteYellowBorder1);}"]
});
const useIconRootClassName = /*#__PURE__*/__resetStyles("rttl5z0", null, [".rttl5z0{display:flex;line-height:1;margin:0 calc(-1 * var(--spacingHorizontalXXS));font-size:12px;}"]);
const useIconStyles = /*#__PURE__*/__styles({
  beforeText: {
    t21cq0: ["f1t8l4o1", "f11juvx6"]
  },
  afterText: {
    Frg6f3: ["f11juvx6", "f1t8l4o1"]
  },
  beforeTextXL: {
    t21cq0: ["f1rs9grm", "f1kwmkpi"]
  },
  afterTextXL: {
    Frg6f3: ["f1kwmkpi", "f1rs9grm"]
  },
  tiny: {
    Be2twd7: "f1tccstq"
  },
  "extra-small": {
    Be2twd7: "fnmn6fi"
  },
  small: {
    Be2twd7: "f1ugzwwg"
  },
  medium: {},
  large: {
    Be2twd7: "f4ybsrx"
  },
  "extra-large": {
    Be2twd7: "fe5j1ua"
  }
}, {
  d: [".f1t8l4o1{margin-right:calc(var(--spacingHorizontalXXS) + var(--spacingHorizontalXXS));}", ".f11juvx6{margin-left:calc(var(--spacingHorizontalXXS) + var(--spacingHorizontalXXS));}", ".f1rs9grm{margin-right:calc(var(--spacingHorizontalXS) + var(--spacingHorizontalXXS));}", ".f1kwmkpi{margin-left:calc(var(--spacingHorizontalXS) + var(--spacingHorizontalXXS));}", ".f1tccstq{font-size:6px;}", ".fnmn6fi{font-size:10px;}", ".f1ugzwwg{font-size:12px;}", ".f4ybsrx{font-size:16px;}", ".fe5j1ua{font-size:20px;}"]
});
/**
 * Applies style classnames to slots
 */
export const useBadgeStyles_unstable = state => {
  'use no memo';

  const rootClassName = useRootClassName();
  const rootStyles = useRootStyles();
  const smallToTiny = state.size === 'small' || state.size === 'extra-small' || state.size === 'tiny';
  state.root.className = mergeClasses(badgeClassNames.root, rootClassName, smallToTiny && rootStyles.fontSmallToTiny, rootStyles[state.size], rootStyles[state.shape], state.shape === 'rounded' && smallToTiny && rootStyles.roundedSmallToTiny, state.appearance === 'ghost' && rootStyles.borderGhost, rootStyles[state.appearance], rootStyles[`${state.appearance}-${state.color}`], state.root.className);
  const iconRootClassName = useIconRootClassName();
  const iconStyles = useIconStyles();
  if (state.icon) {
    let iconPositionClass;
    if (state.root.children) {
      if (state.size === 'extra-large') {
        iconPositionClass = state.iconPosition === 'after' ? iconStyles.afterTextXL : iconStyles.beforeTextXL;
      } else {
        iconPositionClass = state.iconPosition === 'after' ? iconStyles.afterText : iconStyles.beforeText;
      }
    }
    state.icon.className = mergeClasses(badgeClassNames.icon, iconRootClassName, iconPositionClass, iconStyles[state.size], state.icon.className);
  }
  return state;
};