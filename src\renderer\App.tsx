import React from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Title1,
  Body1,
  <PERSON><PERSON>,
  Card,
  CardPreview,
  Text,
} from '@fluentui/react-components';
import {
  ShoppingBag24Regular,
  PaintBrush24Regular,
  Shifts24Regular,
  Person24Regular,
} from '@fluentui/react-icons';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    backgroundColor: tokens.colorNeutralBackground1,
  },
  header: {
    ...shorthands.padding('20px', '24px'),
    backgroundColor: tokens.colorBrandBackground,
    color: tokens.colorNeutralForegroundOnBrand,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    boxShadow: tokens.shadow4,
  },
  headerTitle: {
    color: tokens.colorNeutralForegroundOnBrand,
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
  main: {
    flex: 1,
    ...shorthands.padding('24px'),
    display: 'flex',
    flexDirection: 'column',
    gap: '24px',
  },
  welcomeSection: {
    textAlign: 'center',
    ...shorthands.padding('40px', '20px'),
  },
  cardsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    gap: '20px',
    marginTop: '32px',
  },
  card: {
    cursor: 'pointer',
    transition: 'transform 0.2s ease-in-out',
    ':hover': {
      transform: 'translateY(-2px)',
      boxShadow: tokens.shadow8,
    },
  },
  cardContent: {
    ...shorthands.padding('16px'),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '12px',
    textAlign: 'center',
  },
  icon: {
    fontSize: '48px',
    color: tokens.colorBrandForeground1,
  },
});

const App: React.FC = () => {
  const styles = useStyles();

  const handleCardClick = (section: string) => {
    console.log(`Navigating to ${section}`);
    // TODO: Implement navigation
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerTitle}>
          <PaintBrush24Regular />
          <Title1>मैथिली विकास कोष Shop Management</Title1>
        </div>
        <Button appearance="subtle" style={{ color: tokens.colorNeutralForegroundOnBrand }}>
          Settings
        </Button>
      </header>

      {/* Main Content */}
      <main className={styles.main}>
        <div className={styles.welcomeSection}>
          <Title1>Welcome to Mithila Handcraft Management</Title1>
          <Body1 style={{ marginTop: '12px', color: tokens.colorNeutralForeground2 }}>
            Manage your beautiful Mithila handcrafts, track inventory, and grow your business
          </Body1>
        </div>

        {/* Feature Cards */}
        <div className={styles.cardsGrid}>
          <Card className={styles.card} onClick={() => handleCardClick('products')}>
            <CardPreview>
              <div className={styles.cardContent}>
                <ShoppingBag24Regular className={styles.icon} />
                <Text weight="semibold" size={500}>Products</Text>
                <Body1>Manage bags, sarees, paintings and other handcrafts</Body1>
              </div>
            </CardPreview>
          </Card>

          <Card className={styles.card} onClick={() => handleCardClick('inventory')}>
            <CardPreview>
              <div className={styles.cardContent}>
                <Shifts24Regular className={styles.icon} />
                <Text weight="semibold" size={500}>Inventory</Text>
                <Body1>Track stock levels and manage your inventory</Body1>
              </div>
            </CardPreview>
          </Card>

          <Card className={styles.card} onClick={() => handleCardClick('customers')}>
            <CardPreview>
              <div className={styles.cardContent}>
                <Person24Regular className={styles.icon} />
                <Text weight="semibold" size={500}>Customers</Text>
                <Body1>Manage customer information and purchase history</Body1>
              </div>
            </CardPreview>
          </Card>

          <Card className={styles.card} onClick={() => handleCardClick('artists')}>
            <CardPreview>
              <div className={styles.cardContent}>
                <PaintBrush24Regular className={styles.icon} />
                <Text weight="semibold" size={500}>Artists</Text>
                <Body1>Manage artisan profiles and track commissions</Body1>
              </div>
            </CardPreview>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default App;
