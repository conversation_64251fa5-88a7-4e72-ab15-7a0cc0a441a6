"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    Avatar: function() {
        return _Avatar.Avatar;
    },
    AvatarContextProvider: function() {
        return _index1.AvatarContextProvider;
    },
    AvatarGroup: function() {
        return _AvatarGroup.AvatarGroup;
    },
    AvatarGroupItem: function() {
        return _AvatarGroupItem.AvatarGroupItem;
    },
    AvatarGroupPopover: function() {
        return _AvatarGroupPopover.AvatarGroupPopover;
    },
    AvatarGroupProvider: function() {
        return _index1.AvatarGroupProvider;
    },
    avatarClassNames: function() {
        return _Avatar.avatarClassNames;
    },
    avatarGroupClassNames: function() {
        return _AvatarGroup.avatarGroupClassNames;
    },
    avatarGroupItemClassNames: function() {
        return _AvatarGroupItem.avatarGroupItemClassNames;
    },
    avatarGroupPopoverClassNames: function() {
        return _AvatarGroupPopover.avatarGroupPopoverClassNames;
    },
    getInitials: function() {
        return _index.getInitials;
    },
    partitionAvatarGroupItems: function() {
        return _index.partitionAvatarGroupItems;
    },
    renderAvatarGroupItem_unstable: function() {
        return _AvatarGroupItem.renderAvatarGroupItem_unstable;
    },
    renderAvatarGroupPopover_unstable: function() {
        return _AvatarGroupPopover.renderAvatarGroupPopover_unstable;
    },
    renderAvatarGroup_unstable: function() {
        return _AvatarGroup.renderAvatarGroup_unstable;
    },
    renderAvatar_unstable: function() {
        return _Avatar.renderAvatar_unstable;
    },
    useAvatarContext: function() {
        return _index1.useAvatarContext;
    },
    useAvatarGroupContextValues: function() {
        return _AvatarGroup.useAvatarGroupContextValues;
    },
    useAvatarGroupContext_unstable: function() {
        return _index1.useAvatarGroupContext_unstable;
    },
    useAvatarGroupItemStyles_unstable: function() {
        return _AvatarGroupItem.useAvatarGroupItemStyles_unstable;
    },
    useAvatarGroupItem_unstable: function() {
        return _AvatarGroupItem.useAvatarGroupItem_unstable;
    },
    useAvatarGroupPopoverContextValues_unstable: function() {
        return _AvatarGroupPopover.useAvatarGroupPopoverContextValues_unstable;
    },
    useAvatarGroupPopoverStyles_unstable: function() {
        return _AvatarGroupPopover.useAvatarGroupPopoverStyles_unstable;
    },
    useAvatarGroupPopover_unstable: function() {
        return _AvatarGroupPopover.useAvatarGroupPopover_unstable;
    },
    useAvatarGroupStyles_unstable: function() {
        return _AvatarGroup.useAvatarGroupStyles_unstable;
    },
    useAvatarGroup_unstable: function() {
        return _AvatarGroup.useAvatarGroup_unstable;
    },
    useAvatarStyles_unstable: function() {
        return _Avatar.useAvatarStyles_unstable;
    },
    useAvatar_unstable: function() {
        return _Avatar.useAvatar_unstable;
    }
});
const _Avatar = require("./Avatar");
const _index = require("./utils/index");
const _AvatarGroup = require("./AvatarGroup");
const _AvatarGroupItem = require("./AvatarGroupItem");
const _AvatarGroupPopover = require("./AvatarGroupPopover");
const _index1 = require("./contexts/index");
