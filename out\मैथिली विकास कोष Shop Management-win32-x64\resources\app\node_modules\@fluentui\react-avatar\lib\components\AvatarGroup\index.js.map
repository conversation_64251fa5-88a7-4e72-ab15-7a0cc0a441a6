{"version": 3, "sources": ["../src/components/AvatarGroup/index.ts"], "sourcesContent": ["export { AvatarGroup } from './AvatarGroup';\nexport type {\n  AvatarGroupContextValue,\n  AvatarGroupContextValues,\n  AvatarGroupProps,\n  AvatarGroupSlots,\n  AvatarGroupState,\n} from './AvatarGroup.types';\nexport { renderAvatarGroup_unstable } from './renderAvatarGroup';\nexport { defaultAvatarGroupSize, useAvatarGroup_unstable } from './useAvatarGroup';\nexport { avatarGroupClassNames, useAvatarGroupStyles_unstable } from './useAvatarGroupStyles.styles';\nexport { useAvatarGroupContextValues } from './useAvatarGroupContextValues';\n"], "names": ["AvatarGroup", "renderAvatarGroup_unstable", "defaultAvatarGroupSize", "useAvatarGroup_unstable", "avatarGroupClassNames", "useAvatarGroupStyles_unstable", "useAvatarGroupContextValues"], "rangeMappings": ";;;;", "mappings": "AAAA,SAASA,WAAW,QAAQ,gBAAgB;AAQ5C,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,mBAAmB;AACnF,SAASC,qBAAqB,EAAEC,6BAA6B,QAAQ,gCAAgC;AACrG,SAASC,2BAA2B,QAAQ,gCAAgC"}