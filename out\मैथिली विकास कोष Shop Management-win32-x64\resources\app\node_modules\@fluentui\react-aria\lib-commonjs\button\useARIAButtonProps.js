"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useARIAButtonProps", {
    enumerable: true,
    get: function() {
        return useARIAButtonProps;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _keyboardkeys = require("@fluentui/keyboard-keys");
const _reactutilities = require("@fluentui/react-utilities");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
function useARIAButtonProps(type, props) {
    const { disabled, disabledFocusable = false, ['aria-disabled']: ariaDisabled, onClick, onKeyDown, onKeyUp, ...rest } = props !== null && props !== void 0 ? props : {};
    const normalizedARIADisabled = typeof ariaDisabled === 'string' ? ariaDisabled === 'true' : ariaDisabled;
    const isDisabled = disabled || disabledFocusable || normalizedARIADisabled;
    const handleClick = (0, _reactutilities.useEventCallback)((ev)=>{
        if (isDisabled) {
            ev.preventDefault();
            ev.stopPropagation();
        } else {
            onClick === null || onClick === void 0 ? void 0 : onClick(ev);
        }
    });
    const handleKeyDown = (0, _reactutilities.useEventCallback)((ev)=>{
        onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(ev);
        if (ev.isDefaultPrevented()) {
            return;
        }
        const key = ev.key;
        if (isDisabled && (key === _keyboardkeys.Enter || key === _keyboardkeys.Space)) {
            ev.preventDefault();
            ev.stopPropagation();
            return;
        }
        if (key === _keyboardkeys.Space) {
            ev.preventDefault();
            return;
        } else if (key === _keyboardkeys.Enter) {
            ev.preventDefault();
            ev.currentTarget.click();
        }
    });
    const handleKeyUp = (0, _reactutilities.useEventCallback)((ev)=>{
        onKeyUp === null || onKeyUp === void 0 ? void 0 : onKeyUp(ev);
        if (ev.isDefaultPrevented()) {
            return;
        }
        const key = ev.key;
        if (isDisabled && (key === _keyboardkeys.Enter || key === _keyboardkeys.Space)) {
            ev.preventDefault();
            ev.stopPropagation();
            return;
        }
        if (key === _keyboardkeys.Space) {
            ev.preventDefault();
            ev.currentTarget.click();
        }
    });
    // If a <button> tag is to be rendered we just need to set disabled and aria-disabled correctly
    if (type === 'button' || type === undefined) {
        return {
            ...rest,
            disabled: disabled && !disabledFocusable,
            'aria-disabled': disabledFocusable ? true : normalizedARIADisabled,
            // onclick should still use internal handler to ensure prevention if disabled
            // if disabledFocusable then there's no requirement for handlers as those events should not be propagated
            onClick: disabledFocusable ? undefined : handleClick,
            onKeyUp: disabledFocusable ? undefined : onKeyUp,
            onKeyDown: disabledFocusable ? undefined : onKeyDown
        };
    } else {
        // the role needs to be explicitly set if the href is undefined
        const isLink = !!rest.href;
        let roleOverride = isLink ? undefined : 'button';
        if (!roleOverride && isDisabled) {
            // need to set role=link explicitly for disabled links
            roleOverride = 'link';
        }
        const resultProps = {
            role: roleOverride,
            tabIndex: disabledFocusable || !isLink && !disabled ? 0 : undefined,
            ...rest,
            // If it's not a <button> than listeners are required even with disabledFocusable
            // Since you cannot assure the default behavior of the element
            // E.g: <a> will redirect on click
            onClick: handleClick,
            onKeyUp: handleKeyUp,
            onKeyDown: handleKeyDown,
            'aria-disabled': isDisabled
        };
        if (type === 'a' && isDisabled) {
            resultProps.href = undefined;
        }
        return resultProps;
    }
}
