# Change Log - @fluentui/priority-overflow

This log was last generated on Fri, 21 Feb 2025 14:30:45 GMT and should not be manually modified.

<!-- Start content -->

## [9.1.15](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.15)

Fri, 21 Feb 2025 14:30:45 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.14..@fluentui/priority-overflow_v9.1.15)

### Patches

- fix: Add a defensive check when getting overflow item records ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by <EMAIL>)

## [9.1.14](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.14)

Mon, 11 Nov 2024 10:01:03 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.13..@fluentui/priority-overflow_v9.1.14)

### Patches

- chore: replace npm-scripts and just-scrtips with nx inferred tasks ([PR #33074](https://github.com/microsoft/fluentui/pull/33074) by <EMAIL>)

## [9.1.13](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.13)

Thu, 06 Jun 2024 15:26:28 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.12..@fluentui/priority-overflow_v9.1.13)

### Patches

- chore: disable eslint rule ([PR #30967](https://github.com/microsoft/fluentui/pull/30967) by <EMAIL>)

## [9.1.12](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.12)

Thu, 16 May 2024 09:25:11 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.11..@fluentui/priority-overflow_v9.1.12)

### Patches

- force update the overflow when item is removed ([PR #31340](https://github.com/microsoft/fluentui/pull/31340) by <EMAIL>)

## [9.1.11](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.11)

Thu, 14 Dec 2023 09:58:42 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.10..@fluentui/priority-overflow_v9.1.11)

### Patches

- fix: updates should be synchronous in unit tests ([PR #30014](https://github.com/microsoft/fluentui/pull/30014) by <EMAIL>)

## [9.1.10](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.10)

Mon, 20 Nov 2023 09:55:08 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.9..@fluentui/priority-overflow_v9.1.10)

### Patches

- fix: disconnect should dispose all state correctly ([PR #29375](https://github.com/microsoft/fluentui/pull/29375) by <EMAIL>)

## [9.1.9](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.9)

Thu, 09 Nov 2023 17:29:49 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.8..@fluentui/priority-overflow_v9.1.9)

### Patches

- chore: use package.json#files setup instead of npmignore for all v9 libraries ([PR #29734](https://github.com/microsoft/fluentui/pull/29734) by <EMAIL>)

## [9.1.8](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.8)

Mon, 23 Oct 2023 09:51:55 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.7..@fluentui/priority-overflow_v9.1.8)

### Patches

- fix: Use container window's resize observer ([PR #29551](https://github.com/microsoft/fluentui/pull/29551) by <EMAIL>)

## [9.1.7](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.7)

Tue, 26 Sep 2023 17:49:01 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.6..@fluentui/priority-overflow_v9.1.7)

### Patches

- chore: trigger manual version bump after broken release ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by <EMAIL>)

## [9.1.6](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.6)

Tue, 26 Sep 2023 15:32:06 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.5..@fluentui/priority-overflow_v9.1.6)

### Patches

- fix: bump swc core to mitigate transpilation memory leaks ([PR #29253](https://github.com/microsoft/fluentui/pull/29253) by <EMAIL>)

## [9.1.5](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.5)

Tue, 05 Sep 2023 13:29:14 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.4..@fluentui/priority-overflow_v9.1.5)

### Patches

- bumps @swc/helpers version to 0.5.1 ([PR #28989](https://github.com/microsoft/fluentui/pull/28989) by <EMAIL>)

## [9.1.4](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.4)

Fri, 11 Aug 2023 12:14:24 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.3..@fluentui/priority-overflow_v9.1.4)

### Patches

- fix: flickering in Overflow ([PR #28767](https://github.com/microsoft/fluentui/pull/28767) by <EMAIL>)

## [9.1.3](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.3)

Wed, 09 Aug 2023 13:16:48 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.2..@fluentui/priority-overflow_v9.1.3)

### Patches

- fix: inaccurate calculation of size ([PR #28728](https://github.com/microsoft/fluentui/pull/28728) by <EMAIL>)

## [9.1.2](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.2)

Tue, 25 Jul 2023 13:29:15 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.1..@fluentui/priority-overflow_v9.1.2)

### Patches

- fix: Overflow update should run show/hide steps twice ([PR #28628](https://github.com/microsoft/fluentui/pull/28628) by <EMAIL>)

## [9.1.1](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.1)

Mon, 26 Jun 2023 09:53:53 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.1.0..@fluentui/priority-overflow_v9.1.1)

### Patches

- fix: remove overflow menu if the last overflowed item can take its place ([PR #28285](https://github.com/microsoft/fluentui/pull/28285) by <EMAIL>)

## [9.1.0](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.1.0)

Tue, 20 Jun 2023 12:38:54 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.3..@fluentui/priority-overflow_v9.1.0)

### Minor changes

- feat: Added support for custom divider ([PR #28011](https://github.com/microsoft/fluentui/pull/28011) by <EMAIL>)

## [9.0.3](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.3)

Fri, 12 May 2023 20:28:09 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.2..@fluentui/priority-overflow_v9.0.3)

### Patches

- fix: overflowManager should always dispatch initial state ([PR #27756](https://github.com/microsoft/fluentui/pull/27756) by <EMAIL>)
- chore: exclude .swcrc from being published ([PR #27740](https://github.com/microsoft/fluentui/pull/27740) by <EMAIL>)

## [9.0.2](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.2)

Tue, 21 Mar 2023 21:23:19 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.1..@fluentui/priority-overflow_v9.0.2)

### Patches

- fix: add node field to package.json exports map. ([PR #27154](https://github.com/microsoft/fluentui/pull/27154) by <EMAIL>)
- chore: migrate to swc transpilation approach. ([PR #27250](https://github.com/microsoft/fluentui/pull/27250) by <EMAIL>)

## [9.0.1](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.1)

Tue, 07 Feb 2023 14:13:08 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.0..@fluentui/priority-overflow_v9.0.1)

### Patches

- fix: New overflow items all always dispatch updates to subscriber ([PR #26565](https://github.com/microsoft/fluentui/pull/26565) by <EMAIL>)

## [9.0.0](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.0)

Wed, 18 Jan 2023 16:32:53 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.0-rc.2..@fluentui/priority-overflow_v9.0.0)

### Patches

- Release as stable ([PR #26380](https://github.com/microsoft/fluentui/pull/26380) by <EMAIL>)

## [9.0.0-rc.2](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.0-rc.2)

Mon, 09 Jan 2023 14:34:54 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.0-rc.1..@fluentui/priority-overflow_v9.0.0-rc.2)

### Changes

- fix: Minimum visible overflow items should be respected ([PR #26194](https://github.com/microsoft/fluentui/pull/26194) by <EMAIL>)

## [9.0.0-rc.1](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.0-rc.1)

Thu, 17 Nov 2022 23:05:32 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.0-beta.4..@fluentui/priority-overflow_v9.0.0-rc.1)

### Changes

- feat: Bump to RC ([PR #25659](https://github.com/microsoft/fluentui/pull/25659) by <EMAIL>)

## [9.0.0-beta.4](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.0-beta.4)

Fri, 11 Nov 2022 14:57:50 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.0-beta.3..@fluentui/priority-overflow_v9.0.0-beta.4)

### Changes

- fix: create valid export maps ([PR #25558](https://github.com/microsoft/fluentui/pull/25558) by <EMAIL>)

## [9.0.0-beta.3](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.0-beta.3)

Thu, 13 Oct 2022 11:02:41 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.0-beta.2..@fluentui/priority-overflow_v9.0.0-beta.3)

### Changes

- feat: Adds API to register overflow menus for better available space calculation ([PR #25091](https://github.com/microsoft/fluentui/pull/25091) by <EMAIL>)
- new overflow items should only be enqueued while observing ([PR #25122](https://github.com/microsoft/fluentui/pull/25122) by <EMAIL>)

## [9.0.0-beta.2](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.0-beta.2)

Tue, 28 Jun 2022 15:14:10 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/priority-overflow_v9.0.0-beta.1..@fluentui/priority-overflow_v9.0.0-beta.2)

### Changes

- chore: Mark internal APIs with @internal ([PR #23689](https://github.com/microsoft/fluentui/pull/23689) by <EMAIL>)

## [9.0.0-beta.1](https://github.com/microsoft/fluentui/tree/@fluentui/priority-overflow_v9.0.0-beta.1)

Mon, 23 May 2022 12:13:58 GMT

### Changes

- feat: Initial beta release ([PR #22913](https://github.com/microsoft/fluentui/pull/22913) by <EMAIL>)
