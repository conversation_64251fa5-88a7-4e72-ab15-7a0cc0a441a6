{"version": 3, "sources": ["../src/AriaLiveAnnouncer/useDomAnnounce.ts"], "sourcesContent": ["import { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nimport type { AnnounceOptions } from '@fluentui/react-shared-contexts';\nimport { createPriorityQueue, useTimeout } from '@fluentui/react-utilities';\nimport { useDangerousNeverHidden_unstable as useDangerousNeverHidden } from '@fluentui/react-tabster';\nimport * as React from 'react';\n\nimport type { AriaLiveAnnounceFn, AriaLiveMessage } from './AriaLiveAnnouncer.types';\n\n/** The duration the message needs to be in present in DOM for screen readers to register a change and announce */\nconst MESSAGE_DURATION = 500;\n\nconst VISUALLY_HIDDEN_STYLES = {\n  clip: 'rect(0px, 0px, 0px, 0px)',\n  height: '1px',\n  margin: '-1px',\n  width: '1px',\n  position: 'absolute',\n  overflow: 'hidden',\n  textWrap: 'nowrap',\n};\n\n/* INTERNAL: implementation of the announcer using a live region element */\nexport const useDomAnnounce_unstable = (): AriaLiveAnnounceFn => {\n  const { targetDocument } = useFluent();\n\n  const timeoutRef = React.useRef<number | undefined>(undefined);\n  const [setAnnounceTimeout, clearAnnounceTimeout] = useTimeout();\n  const tabsterNeverHiddenAttributes = useDangerousNeverHidden();\n\n  const elementRef = React.useRef<HTMLDivElement | null>(null);\n\n  const order = React.useRef(0);\n\n  // investigate alert implementation later\n  // const [alertList, setAlertList] = React.useState<string[]>([]);\n\n  const batchMessages = React.useRef<{ batchId: string; message: AriaLiveMessage }[]>([]);\n\n  const [messageQueue] = React.useState(() =>\n    createPriorityQueue<AriaLiveMessage>((a, b) => {\n      if (a.priority !== b.priority) {\n        return b.priority - a.priority;\n      }\n\n      return a.createdAt - b.createdAt;\n    }),\n  );\n\n  const queueMessage = React.useCallback(() => {\n    if (timeoutRef.current || !elementRef.current) {\n      return;\n    }\n\n    const runCycle = () => {\n      if (!elementRef.current) {\n        return;\n      }\n\n      if (targetDocument && messageQueue.peek()) {\n        // need a wrapping element for Narrator/Edge, which currently does not pick up text-only live region changes\n        // consistently\n        // if this is fixed, we can set textContent to the string directly\n\n        const wrappingEl = targetDocument.createElement('span');\n\n        wrappingEl.innerText = messageQueue\n          .all()\n          .filter(msg => msg.message.trim().length > 0)\n          .reduce((prevText, currMsg) => prevText + currMsg.message + '. ', '');\n\n        elementRef.current.innerText = '';\n        elementRef.current.appendChild(wrappingEl);\n\n        messageQueue.clear();\n        batchMessages.current = [];\n\n        // begin new cycle to clear (or update) messages\n        timeoutRef.current = setAnnounceTimeout(() => {\n          runCycle();\n        }, MESSAGE_DURATION);\n      } else {\n        elementRef.current.textContent = '';\n        clearAnnounceTimeout();\n\n        timeoutRef.current = undefined;\n      }\n    };\n\n    runCycle();\n  }, [clearAnnounceTimeout, messageQueue, setAnnounceTimeout, targetDocument]);\n\n  const announce: AriaLiveAnnounceFn = React.useCallback(\n    (message: string, options: AnnounceOptions = {}) => {\n      const { alert = false, priority = 0, batchId } = options;\n\n      // check if message is an alert\n      if (alert) {\n        // TODO: alert implementation\n        // setAlertList([...alertList, message]);\n      }\n\n      const liveMessage: AriaLiveMessage = {\n        message,\n        createdAt: order.current++,\n        priority,\n        batchId,\n      };\n\n      // check if batchId exists\n      if (batchId) {\n        // update associated msg if it does\n        const batchMessage = batchMessages.current.find(msg => msg.batchId === batchId);\n\n        if (batchMessage) {\n          // replace existing message in queue\n          messageQueue.remove(batchMessage.message);\n\n          // update list of existing batchIds w/ most recent message\n          batchMessage.message = liveMessage;\n        } else {\n          // update list of existing batchIds, add new if doesn't already exist\n          batchMessages.current = [...batchMessages.current, { batchId, message: liveMessage }];\n        }\n      }\n\n      // add new message\n      messageQueue.enqueue(liveMessage);\n      queueMessage();\n    },\n    [messageQueue, queueMessage],\n  );\n\n  React.useEffect(() => {\n    if (!targetDocument) {\n      return;\n    }\n\n    const element = targetDocument.createElement('div');\n    element.setAttribute('aria-live', 'assertive');\n\n    Object.entries(tabsterNeverHiddenAttributes).forEach(([key, value]) => {\n      element.setAttribute(key, value);\n    });\n\n    Object.assign(element.style, VISUALLY_HIDDEN_STYLES);\n    targetDocument.body.append(element);\n\n    elementRef.current = element;\n\n    return () => {\n      element.remove();\n      elementRef.current = null;\n      clearAnnounceTimeout();\n      timeoutRef.current = undefined;\n    };\n  }, [clearAnnounceTimeout, tabsterNeverHiddenAttributes, targetDocument]);\n\n  return announce;\n};\n"], "names": ["useDomAnnounce_unstable", "MESSAGE_DURATION", "VISUALLY_HIDDEN_STYLES", "clip", "height", "margin", "width", "position", "overflow", "textWrap", "targetDocument", "useFluent", "timeoutRef", "React", "useRef", "undefined", "setAnnounceTimeout", "clearAnnounceTimeout", "useTimeout", "tabsterNeverHiddenAttributes", "useDangerousNeverHidden", "elementRef", "order", "batchMessages", "messageQueue", "useState", "createPriorityQueue", "a", "b", "priority", "createdAt", "queueMessage", "useCallback", "current", "runCycle", "peek", "wrappingEl", "createElement", "innerText", "all", "filter", "msg", "message", "trim", "length", "reduce", "prevText", "currMsg", "append<PERSON><PERSON><PERSON>", "clear", "textContent", "announce", "options", "alert", "batchId", "liveMessage", "batchMessage", "find", "remove", "enqueue", "useEffect", "element", "setAttribute", "Object", "entries", "for<PERSON>ach", "key", "value", "assign", "style", "body", "append"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAsBaA;;;eAAAA;;;;qCAtBmC;gCAEA;8BAC4B;iEACrD;AAIvB,gHAAgH,GAChH,MAAMC,mBAAmB;AAEzB,MAAMC,yBAAyB;IAC7BC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC,UAAU;IACVC,UAAU;IACVC,UAAU;AACZ;AAGO,MAAMT,0BAA0B;IACrC,MAAM,EAAEU,cAAc,EAAE,GAAGC,IAAAA,uCAAS;IAEpC,MAAMC,aAAaC,OAAMC,MAAM,CAAqBC;IACpD,MAAM,CAACC,oBAAoBC,qBAAqB,GAAGC,IAAAA,0BAAU;IAC7D,MAAMC,+BAA+BC,IAAAA,8CAAuB;IAE5D,MAAMC,aAAaR,OAAMC,MAAM,CAAwB;IAEvD,MAAMQ,QAAQT,OAAMC,MAAM,CAAC;IAE3B,yCAAyC;IACzC,kEAAkE;IAElE,MAAMS,gBAAgBV,OAAMC,MAAM,CAAkD,EAAE;IAEtF,MAAM,CAACU,aAAa,GAAGX,OAAMY,QAAQ,CAAC,IACpCC,IAAAA,mCAAmB,EAAkB,CAACC,GAAGC;YACvC,IAAID,EAAEE,QAAQ,KAAKD,EAAEC,QAAQ,EAAE;gBAC7B,OAAOD,EAAEC,QAAQ,GAAGF,EAAEE,QAAQ;YAChC;YAEA,OAAOF,EAAEG,SAAS,GAAGF,EAAEE,SAAS;QAClC;IAGF,MAAMC,eAAelB,OAAMmB,WAAW,CAAC;QACrC,IAAIpB,WAAWqB,OAAO,IAAI,CAACZ,WAAWY,OAAO,EAAE;YAC7C;QACF;QAEA,MAAMC,WAAW;YACf,IAAI,CAACb,WAAWY,OAAO,EAAE;gBACvB;YACF;YAEA,IAAIvB,kBAAkBc,aAAaW,IAAI,IAAI;gBACzC,4GAA4G;gBAC5G,eAAe;gBACf,kEAAkE;gBAElE,MAAMC,aAAa1B,eAAe2B,aAAa,CAAC;gBAEhDD,WAAWE,SAAS,GAAGd,aACpBe,GAAG,GACHC,MAAM,CAACC,CAAAA,MAAOA,IAAIC,OAAO,CAACC,IAAI,GAAGC,MAAM,GAAG,GAC1CC,MAAM,CAAC,CAACC,UAAUC,UAAYD,WAAWC,QAAQL,OAAO,GAAG,MAAM;gBAEpErB,WAAWY,OAAO,CAACK,SAAS,GAAG;gBAC/BjB,WAAWY,OAAO,CAACe,WAAW,CAACZ;gBAE/BZ,aAAayB,KAAK;gBAClB1B,cAAcU,OAAO,GAAG,EAAE;gBAE1B,gDAAgD;gBAChDrB,WAAWqB,OAAO,GAAGjB,mBAAmB;oBACtCkB;gBACF,GAAGjC;YACL,OAAO;gBACLoB,WAAWY,OAAO,CAACiB,WAAW,GAAG;gBACjCjC;gBAEAL,WAAWqB,OAAO,GAAGlB;YACvB;QACF;QAEAmB;IACF,GAAG;QAACjB;QAAsBO;QAAcR;QAAoBN;KAAe;IAE3E,MAAMyC,WAA+BtC,OAAMmB,WAAW,CACpD,CAACU,SAAiBU,UAA2B,CAAC,CAAC;QAC7C,MAAM,EAAEC,QAAQ,KAAK,EAAExB,WAAW,CAAC,EAAEyB,OAAO,EAAE,GAAGF;QAEjD,+BAA+B;QAC/B,IAAIC,OAAO;QACT,6BAA6B;QAC7B,yCAAyC;QAC3C;QAEA,MAAME,cAA+B;YACnCb;YACAZ,WAAWR,MAAMW,OAAO;YACxBJ;YACAyB;QACF;QAEA,0BAA0B;QAC1B,IAAIA,SAAS;YACX,mCAAmC;YACnC,MAAME,eAAejC,cAAcU,OAAO,CAACwB,IAAI,CAAChB,CAAAA,MAAOA,IAAIa,OAAO,KAAKA;YAEvE,IAAIE,cAAc;gBAChB,oCAAoC;gBACpChC,aAAakC,MAAM,CAACF,aAAad,OAAO;gBAExC,0DAA0D;gBAC1Dc,aAAad,OAAO,GAAGa;YACzB,OAAO;gBACL,qEAAqE;gBACrEhC,cAAcU,OAAO,GAAG;uBAAIV,cAAcU,OAAO;oBAAE;wBAAEqB;wBAASZ,SAASa;oBAAY;iBAAE;YACvF;QACF;QAEA,kBAAkB;QAClB/B,aAAamC,OAAO,CAACJ;QACrBxB;IACF,GACA;QAACP;QAAcO;KAAa;IAG9BlB,OAAM+C,SAAS,CAAC;QACd,IAAI,CAAClD,gBAAgB;YACnB;QACF;QAEA,MAAMmD,UAAUnD,eAAe2B,aAAa,CAAC;QAC7CwB,QAAQC,YAAY,CAAC,aAAa;QAElCC,OAAOC,OAAO,CAAC7C,8BAA8B8C,OAAO,CAAC,CAAC,CAACC,KAAKC,MAAM;YAChEN,QAAQC,YAAY,CAACI,KAAKC;QAC5B;QAEAJ,OAAOK,MAAM,CAACP,QAAQQ,KAAK,EAAEnE;QAC7BQ,eAAe4D,IAAI,CAACC,MAAM,CAACV;QAE3BxC,WAAWY,OAAO,GAAG4B;QAErB,OAAO;YACLA,QAAQH,MAAM;YACdrC,WAAWY,OAAO,GAAG;YACrBhB;YACAL,WAAWqB,OAAO,GAAGlB;QACvB;IACF,GAAG;QAACE;QAAsBE;QAA8BT;KAAe;IAEvE,OAAOyC;AACT"}