{"name": "@fluentui/react-accordion", "version": "9.7.3", "description": "Fluent UI accordion component", "main": "lib-commonjs/index.js", "module": "lib/index.js", "typings": "./dist/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/microsoft/fluentui"}, "license": "MIT", "devDependencies": {"@fluentui/eslint-plugin": "*", "@fluentui/react-conformance": "*", "@fluentui/react-conformance-griffel": "*", "@fluentui/scripts-api-extractor": "*"}, "dependencies": {"@fluentui/react-aria": "^9.15.3", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-motion-components-preview": "^0.6.2", "@fluentui/react-tabster": "^9.25.3", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}, "beachball": {"disallowedChangeTypes": ["major", "prerelease"]}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./lib-commonjs/index.js", "import": "./lib/index.js", "require": "./lib-commonjs/index.js"}, "./package.json": "./package.json"}, "files": ["*.md", "dist/*.d.ts", "lib", "lib-commonjs"]}