{"version": 3, "sources": ["../src/AvatarGroup.ts"], "sourcesContent": ["export type {\n  AvatarGroupContextValue,\n  AvatarGroupContextValues,\n  AvatarGroupProps,\n  AvatarGroupSlots,\n  AvatarGroupState,\n} from './components/AvatarGroup/index';\nexport {\n  AvatarGroup,\n  avatarGroupClassNames,\n  defaultAvatarGroupSize,\n  renderAvatarGroup_unstable,\n  useAvatarGroupContextValues,\n  useAvatarGroupStyles_unstable,\n  useAvatarGroup_unstable,\n} from './components/AvatarGroup/index';\n"], "names": ["AvatarGroup", "avatarGroupClassNames", "defaultAvatarGroupSize", "renderAvatarGroup_unstable", "useAvatarGroupContextValues", "useAvatarGroupStyles_unstable", "useAvatarGroup_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAQEA,WAAW;eAAXA,kBAAW;;IACXC,qBAAqB;eAArBA,4BAAqB;;IACrBC,sBAAsB;eAAtBA,6BAAsB;;IACtBC,0BAA0B;eAA1BA,iCAA0B;;IAC1BC,2BAA2B;eAA3BA,kCAA2B;;IAC3BC,6BAA6B;eAA7BA,oCAA6B;;IAC7BC,uBAAuB;eAAvBA,8BAAuB;;;uBAClB"}