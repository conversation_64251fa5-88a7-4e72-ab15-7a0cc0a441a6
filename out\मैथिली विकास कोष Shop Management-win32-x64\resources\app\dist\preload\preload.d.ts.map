{"version": 3, "file": "preload.d.ts", "sourceRoot": "", "sources": ["../../src/preload/preload.ts"], "names": [], "mappings": "AAyEA,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,MAAM;QACd,WAAW,EAAE;YACX,aAAa,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC;YACrC,cAAc,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;YACpC,cAAc,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;YACpC,WAAW,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;YACjC,QAAQ,EAAE;gBACR,QAAQ,EAAE,MAAM,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC/B,UAAU,EAAE,CAAC,QAAQ,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC5C,WAAW,EAAE,MAAM,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBAClC,aAAa,EAAE,CAAC,WAAW,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;gBAClD,YAAY,EAAE,MAAM,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBACnC,cAAc,EAAE,CAAC,YAAY,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;gBACpD,SAAS,EAAE,MAAM,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBAChC,WAAW,EAAE,CAAC,SAAS,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;aAC/C,CAAC;YACF,KAAK,EAAE;gBACL,WAAW,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC;gBACnC,SAAS,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC;aAClE,CAAC;YACF,aAAa,EAAE;gBACb,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;aACtD,CAAC;YACF,EAAE,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;YAClD,kBAAkB,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;SAC/C,CAAC;KACH;CACF"}