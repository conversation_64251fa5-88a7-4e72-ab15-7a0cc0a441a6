{"version": 3, "sources": ["../src/contexts/accordionItem.ts"], "sourcesContent": ["import * as React from 'react';\nimport { AccordionItemValue } from '../AccordionItem';\nimport { AccordionToggleEvent } from '../Accordion';\n\nexport type AccordionItemContextValue<Value = AccordionItemValue> = {\n  open: boolean;\n  disabled: boolean;\n  value: Value;\n  /**\n   * @deprecated - use `requestToggle` from AccordionContent instead\n   */\n  onHeaderClick(event: AccordionToggleEvent): void;\n};\n\nconst AccordionItemContext = React.createContext<AccordionItemContextValue<unknown> | undefined>(\n  undefined,\n) as React.Context<AccordionItemContextValue<unknown>>;\n\nconst accordionItemContextDefaultValue: AccordionItemContextValue<unknown> = {\n  open: false,\n  disabled: false,\n  value: undefined,\n  onHeaderClick() {\n    /* noop */\n  },\n};\n\nexport const { Provider: AccordionItemProvider } = AccordionItemContext;\n\nexport const useAccordionItemContext_unstable = () => {\n  return React.useContext(AccordionItemContext) ?? accordionItemContextDefaultValue;\n};\n"], "names": ["AccordionItemProvider", "useAccordionItemContext_unstable", "AccordionItemContext", "React", "createContext", "undefined", "accordionItemContextDefaultValue", "open", "disabled", "value", "onHeaderClick", "Provider", "useContext"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IA2ByBA,qBAAqB;eAArBA;;IAEZC,gCAAAA;eAAAA;;;;iEA7BU;AAcvB,MAAMC,qCAAuBC,OAAMC,aAAa,CAC9CC;AAGF,MAAMC,mCAAuE;IAC3EC,MAAM;IACNC,UAAU;IACVC,OAAOJ;IACPK;IACE,QAAQ,GACV;AACF;AAEO,MAAM,EAAEC,UAAUX,qBAAqB,EAAE,GAAGE;AAE5C,MAAMD,mCAAmC;QACvCE;IAAP,OAAOA,CAAAA,oBAAAA,OAAMS,UAAU,CAACV,qBAAAA,MAAAA,QAAjBC,sBAAAA,KAAAA,IAAAA,oBAA0CG;AACnD"}