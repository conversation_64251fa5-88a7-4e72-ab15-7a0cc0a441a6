"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    carouselNavButtonClassNames: function() {
        return carouselNavButtonClassNames;
    },
    useCarouselNavButtonStyles_unstable: function() {
        return useCarouselNavButtonStyles_unstable;
    }
});
const _react = require("@griffel/react");
const carouselNavButtonClassNames = {
    root: 'fui-CarouselNavButton'
};
/**
 * Styles for the root slot
 */ const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    root: {
        Bceei9c: "f1k6fduh",
        Bkecrkj: "fc5wo7j",
        a9b677: "f1van5z7",
        Bqenvij: "f1fkmctz",
        Byoj8tv: 0,
        uwmqm3: 0,
        z189sj: 0,
        z8tnut: 0,
        B0ocmuz: "f1f5q0n8",
        B7ck84d: "f1e4lqlz",
        De3pzq: "f1c21dwh",
        B4j52fo: "fre7gi1",
        Bekrc4i: [
            "f1358rze",
            "f1rvrf73"
        ],
        Bn0qgzm: "fqdk4by",
        ibv6hh: [
            "f1rvrf73",
            "f1358rze"
        ],
        Bsft5z2: "f13zj6fq",
        ap17g6: "f2gz7yw",
        li1rpt: "f1gw3sf2",
        d9w3h3: 0,
        B3778ie: 0,
        B4j8arr: 0,
        Bl18szs: 0,
        Blrzh8d: "f1x820d0",
        Bjuhk93: 0,
        B1q35kw: 0,
        Bw17bha: 0,
        Bcgy8vk: 0,
        Du69r6: 0,
        Gp14am: 0,
        vfts7: 0,
        Bhxzhr1: 0,
        G63luc: 0,
        s924m2: 0,
        Barhvk9: 0,
        Ihftqj: 0,
        wywymt: 0,
        B0n5ga8: 0,
        Bm2nyyq: 0,
        xrcqlc: 0,
        e1d83w: "fnwf5yv",
        Dlnsje: "foue38v",
        a2br6o: "fi4ui2s",
        Bjyk6c5: "f1w4p7kh",
        go7t6h: "fo5b2b9",
        qx5q1e: "f1dpauah",
        f7digc: "f1agqfpv",
        Bfz3el7: "f6pnf7h"
    },
    rootUnselected: {
        Bw0xxkn: 0,
        oeaueh: 0,
        Bpd4iqm: 0,
        Befb4lg: "f71xx7",
        Byu6kyc: 0,
        n8qw10: 0,
        Bbjhlyh: 0,
        i2cumq: 0,
        Bunx835: 0,
        Bdrgwmp: 0,
        mqozju: 0,
        lbo84a: 0,
        Bksnhdo: 0,
        Bci5o5g: 0,
        u5e7qz: 0,
        Bn40d3w: 0,
        B7b6zxw: 0,
        B8q5s1w: 0,
        B5gfjzb: 0,
        Bbcte9g: 0,
        Bqz3imu: "f1j9b7x8",
        Bj9ihqo: 0,
        Bl51kww: 0,
        B3bvztg: 0,
        Btyt4dx: 0,
        Brhw1f9: "f1tdm9ui",
        Bw81rd7: 0,
        kdpuga: 0,
        dm238s: 0,
        B6xbmo0: 0,
        B3whbx2: "f2krc9w",
        Bp15pi3: "f7x02et",
        Bay5ve9: "f1ry2q4s",
        Bni0232: "f1e9f9ku"
    },
    rootSelected: {
        a9b677: "f1eh74fx",
        Byoj8tv: 0,
        uwmqm3: 0,
        z189sj: 0,
        z8tnut: 0,
        B0ocmuz: "fwku66v",
        Bw0xxkn: 0,
        oeaueh: 0,
        Bpd4iqm: 0,
        Befb4lg: "f71xx7",
        Byu6kyc: 0,
        n8qw10: 0,
        Bbjhlyh: 0,
        i2cumq: 0,
        Bunx835: 0,
        Bdrgwmp: 0,
        mqozju: 0,
        lbo84a: 0,
        Bksnhdo: 0,
        Bci5o5g: 0,
        u5e7qz: 0,
        Bn40d3w: 0,
        B7b6zxw: 0,
        B8q5s1w: 0,
        B5gfjzb: 0,
        Bbcte9g: 0,
        Bqz3imu: "f1j9b7x8",
        Bj9ihqo: 0,
        Bl51kww: 0,
        B3bvztg: 0,
        Btyt4dx: 0,
        Brhw1f9: "f1tdm9ui",
        Bw81rd7: 0,
        kdpuga: 0,
        dm238s: 0,
        B6xbmo0: 0,
        B3whbx2: "f2krc9w",
        a2br6o: "f1v6lwa2",
        d9w3h3: 0,
        B3778ie: 0,
        B4j8arr: 0,
        Bl18szs: 0,
        Blrzh8d: "fgm6wgx",
        Bay5ve9: "f1ry2q4s",
        Bni0232: "f1gxfet"
    },
    brand: {
        Bjyk6c5: "fnrv5e1",
        Bp15pi3: "fjsqi2x",
        Glksuk: "frrwqtn",
        Bay5ve9: "f9atwx8",
        Blzl0y7: "fmmpig5",
        Bni0232: "f1e9f9ku"
    },
    unselectedBrand: {
        Bp15pi3: "f7x02et",
        Bjyk6c5: "f1w4p7kh",
        Bay5ve9: "f1ry2q4s",
        Bni0232: "f1e9f9ku"
    }
}, {
    d: [
        ".f1k6fduh{cursor:pointer;}",
        ".fc5wo7j{pointer-events:all;}",
        ".f1van5z7{width:var(--spacingHorizontalS);}",
        ".f1fkmctz{height:var(--spacingVerticalS);}",
        [
            ".f1f5q0n8{padding:var(--spacingVerticalS) var(--spacingHorizontalS);}",
            {
                p: -1
            }
        ],
        ".f1e4lqlz{box-sizing:content-box;}",
        ".f1c21dwh{background-color:var(--colorTransparentBackground);}",
        ".fre7gi1{border-top-width:0;}",
        ".f1358rze{border-right-width:0;}",
        ".f1rvrf73{border-left-width:0;}",
        ".fqdk4by{border-bottom-width:0;}",
        ".f13zj6fq::after{content:\"\";}",
        ".f2gz7yw::after{display:block;}",
        ".f1gw3sf2::after{box-sizing:border-box;}",
        [
            ".f1x820d0::after{border-radius:50%;}",
            {
                p: -1
            }
        ],
        [
            ".fnwf5yv::after{border:none;}",
            {
                p: -2
            }
        ],
        ".foue38v::after{height:var(--spacingVerticalS);}",
        ".fi4ui2s::after{width:var(--spacingHorizontalS);}",
        ".f1w4p7kh::after{background-color:var(--colorNeutralForeground1);}",
        ".fo5b2b9::after{color:var(--colorNeutralForeground1);}",
        [
            ".f71xx7{outline:var(--strokeWidthThin) solid transparent;}",
            {
                p: -1
            }
        ],
        [
            ".f1j9b7x8[data-fui-focus-visible]{border:var(--strokeWidthThick) solid var(--colorStrokeFocus2);}",
            {
                p: -2
            }
        ],
        [
            ".f1tdm9ui[data-fui-focus-visible]{margin:calc(-1 * var(--strokeWidthThick));}",
            {
                p: -1
            }
        ],
        [
            ".f2krc9w[data-fui-focus-visible]{border-radius:var(--borderRadiusMedium);}",
            {
                p: -1
            }
        ],
        ".f7x02et::after{opacity:0.6;}",
        ".f1eh74fx{width:var(--spacingHorizontalL);}",
        [
            ".fwku66v{padding:var(--spacingVerticalS) var(--spacingHorizontalXS);}",
            {
                p: -1
            }
        ],
        [
            ".f71xx7{outline:var(--strokeWidthThin) solid transparent;}",
            {
                p: -1
            }
        ],
        [
            ".f1j9b7x8[data-fui-focus-visible]{border:var(--strokeWidthThick) solid var(--colorStrokeFocus2);}",
            {
                p: -2
            }
        ],
        [
            ".f1tdm9ui[data-fui-focus-visible]{margin:calc(-1 * var(--strokeWidthThick));}",
            {
                p: -1
            }
        ],
        [
            ".f2krc9w[data-fui-focus-visible]{border-radius:var(--borderRadiusMedium);}",
            {
                p: -1
            }
        ],
        ".f1v6lwa2::after{width:var(--spacingHorizontalL);}",
        [
            ".fgm6wgx::after{border-radius:4px;}",
            {
                p: -1
            }
        ],
        ".fnrv5e1::after{background-color:var(--colorCompoundBrandBackground);}",
        ".fjsqi2x::after{opacity:1;}"
    ],
    m: [
        [
            "@media (forced-colors: active){.f1dpauah::after{forced-color-adjust:none;}}",
            {
                m: "(forced-colors: active)"
            }
        ],
        [
            "@media (forced-colors: active){.f1agqfpv::after{background-color:white;}}",
            {
                m: "(forced-colors: active)"
            }
        ],
        [
            "@media (forced-colors: active){.f6pnf7h::after{mix-blend-mode:difference;}}",
            {
                m: "(forced-colors: active)"
            }
        ]
    ],
    h: [
        ".f1ry2q4s:hover::after{opacity:0.75;}",
        ".frrwqtn:hover::after{background-color:var(--colorCompoundBrandBackgroundHover);}",
        ".f9atwx8:hover::after{opacity:1;}"
    ],
    a: [
        ".f1e9f9ku:active::after{opacity:1;}",
        ".f1gxfet:active::after{opacity:0.65;}",
        ".fmmpig5:active::after{background-color:var(--colorCompoundBrandBackgroundPressed);}"
    ]
});
const useCarouselNavButtonStyles_unstable = (state)=>{
    'use no memo';
    const styles = useStyles();
    const { selected, appearance } = state;
    state.root.className = (0, _react.mergeClasses)(carouselNavButtonClassNames.root, styles.root, selected ? styles.rootSelected : styles.rootUnselected, appearance === 'brand' && styles.brand, !selected && appearance === 'brand' && styles.unselectedBrand, state.root.className);
    return state;
};
