{"name": "resedit", "version": "2.0.3", "engines": {"node": ">=14", "npm": ">=7"}, "engineStrict": true, "description": "Node.js library editing Windows Resource data", "type": "module", "module": "./dist/index.js", "exports": {".": "./dist/index.js", "./cjs": {"require": "./cjs.cjs"}}, "types": "./dist/index.d.ts", "author": "jet", "license": "MIT", "homepage": "https://github.com/jet2jet/resedit-js", "keywords": ["windows", "resource", "javascript", "library", "version", "icon", "edit", "resedit", "pe-executable", "authenticode"], "repository": {"type": "git", "url": "https://github.com/jet2jet/resedit-js.git"}, "funding": {"type": "github", "url": "https://github.com/sponsors/jet2jet"}, "scripts": {"build": "npm run build:esm", "build:esm": "tsc -p ./tsconfig.app.json", "lint": "npm run lint:prettier && npm run lint:eslint", "lint:eslint": "eslint -c .eslintrc.yml --ext .js,.jsx,.ts,.tsx .", "lint:eslint:fix": "eslint -c .eslintrc.yml --fix --ext .js,.jsx,.ts,.tsx .", "lint:fix": "npm run lint:prettier:fix && npm run lint:eslint:fix", "lint:prettier": "prettier --config ./.prettierrc.yml --check \"**/*.{js,jsx,ts,tsx,yml,json,md}\"", "lint:prettier:fix": "prettier --config ./.prettierrc.yml --write \"**/*.{js,jsx,ts,tsx,yml,json,md}\"", "test-base": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test": "npm run test-base -- --config ./jest.config.basic.js", "test:basic": "npm run test-base -- --config ./jest.config.basic.js", "test:lint": "npm run lint", "test:win-x86": "npm run test-base -- --config ./jest.config.win-x86.js", "test:win-x64": "npm run test-base -- --config ./jest.config.win-x64.js", "version": "node ./tools/updateVersion.js ./src/main/version.ts && git add -A ./src/main/version.ts", "typecheck": "tsc --project tsconfig.json --noEmit && echo \"Done\"."}, "dependencies": {"pe-library": "^1.0.1"}, "devDependencies": {"@types/jest": "^29.4.0", "@types/node": "^14.18.36", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-config-standard": "^17.0.0", "eslint-config-standard-with-typescript": "^34.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-promise": "^6.1.1", "jest": "^29.4.3", "prettier": "^2.8.4", "ts-jest": "^29.0.5", "typescript": "~4.9.5"}}