{"version": 3, "sources": ["useAvatarGroupStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses } from '@griffel/react';\nimport { tokens } from '@fluentui/react-theme';\nimport { useSizeStyles } from '../Avatar/useAvatarStyles.styles';\nexport const avatarGroupClassNames = {\n    root: 'fui-AvatarGroup'\n};\n/**\n * Styles for the root slot.\n */ const useStyles = makeStyles({\n    base: {\n        display: 'inline-flex',\n        position: 'relative'\n    },\n    pie: {\n        clipPath: 'circle(50%)',\n        backgroundColor: tokens.colorTransparentStroke,\n        '@media (forced-colors: active)': {\n            backgroundColor: 'CanvasText'\n        }\n    }\n});\n/**\n * Apply styling to the AvatarGroup slots based on the state\n */ export const useAvatarGroupStyles_unstable = (state)=>{\n    'use no memo';\n    const { layout, size } = state;\n    const styles = useStyles();\n    const sizeStyles = useSizeStyles();\n    state.root.className = mergeClasses(avatarGroupClassNames.root, styles.base, layout === 'pie' && sizeStyles[size], layout === 'pie' && styles.pie, state.root.className);\n    return state;\n};\n"], "names": ["avatarGroupClassNames", "useAvatarGroupStyles_unstable", "root", "useStyles", "__styles", "base", "mc9l5x", "qhf8xq", "pie", "Bgl5zvf", "De3pzq", "Bsw6fvg", "d", "m", "state", "layout", "size", "styles", "sizeStyles", "useSizeStyles", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAGaA,qBAAqB;eAArBA;;IAoBIC,6BAA6B;eAA7BA;;;uBAvBwB;uCAEX;AACvB,MAAMD,wBAAwB;IACjCE,MAAM;AACV;AACA;;CAEA,GAAI,MAAMC,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAC,MAAA;QAAAC,QAAA;QAAAC,QAAA;IAAA;IAAAC,KAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;KAAA;AAAA;AAeX,MAAMZ,gCAAiCa,CAAAA;IAC9C;IACA,MAAM,EAAEC,MAAM,EAAEC,IAAAA,EAAM,GAAGF;IACzB,MAAMG,SAASd;IACf,MAAMe,aAAaC,IAAAA,oCAAa;IAChCL,MAAMZ,IAAI,CAACkB,SAAS,GAAGC,IAAAA,mBAAY,EAACrB,sBAAsBE,IAAI,EAAEe,OAAOZ,IAAI,EAAEU,WAAW,SAASG,UAAU,CAACF,KAAK,EAAED,WAAW,SAASE,OAAOT,GAAG,EAAEM,MAAMZ,IAAI,CAACkB,SAAS;IACvK,OAAON;AACX"}