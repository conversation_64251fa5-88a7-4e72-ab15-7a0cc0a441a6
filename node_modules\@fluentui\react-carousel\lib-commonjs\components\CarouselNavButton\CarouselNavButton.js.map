{"version": 3, "sources": ["../src/components/CarouselNavButton/CarouselNavButton.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\nimport { useCarouselNavButton_unstable } from './useCarouselNavButton';\nimport { renderCarouselNavButton_unstable } from './renderCarouselNavButton';\nimport { useCarouselNavButtonStyles_unstable } from './useCarouselNavButtonStyles.styles';\nimport type { CarouselNavButtonProps } from './CarouselNavButton.types';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\n\n/**\n * The child element of CarouselNav, a singular button that will set the carousels active value on click.\n */\nexport const CarouselNavButton: ForwardRefComponent<CarouselNavButtonProps> = React.forwardRef((props, ref) => {\n  const state = useCarouselNavButton_unstable(props, ref);\n\n  useCarouselNavButtonStyles_unstable(state);\n  useCustomStyleHook_unstable('useCarouselNavButtonStyles_unstable')(state);\n\n  return renderCarouselNavButton_unstable(state);\n});\n\nCarouselNavButton.displayName = 'CarouselNavButton';\n"], "names": ["CarouselNavButton", "React", "forwardRef", "props", "ref", "state", "useCarouselNavButton_unstable", "useCarouselNavButtonStyles_unstable", "useCustomStyleHook_unstable", "renderCarouselNavButton_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAWaA;;;eAAAA;;;;iEAXU;sCAEuB;yCACG;kDACG;qCAER;AAKrC,MAAMA,oBAAAA,WAAAA,GAAiEC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IACrG,MAAMC,QAAQC,IAAAA,mDAAAA,EAA8BH,OAAOC;IAEnDG,IAAAA,qEAAAA,EAAoCF;IACpCG,IAAAA,gDAAAA,EAA4B,uCAAuCH;IAEnE,OAAOI,IAAAA,yDAAAA,EAAiCJ;AAC1C;AAEAL,kBAAkBU,WAAW,GAAG"}