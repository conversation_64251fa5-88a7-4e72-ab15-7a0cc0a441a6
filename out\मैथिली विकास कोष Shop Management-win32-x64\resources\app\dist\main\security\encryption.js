"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.encryptionService = exports.EncryptionService = void 0;
const crypto = __importStar(require("crypto"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const electron_1 = require("electron");
/**
 * Encryption service for Maithili Vikas Kosh Shop Management System
 * Provides AES-256 encryption for sensitive data and database files
 */
class EncryptionService {
    constructor() {
        this.encryptionKey = null;
        this.algorithm = 'aes-256-gcm';
        // Store encryption key in user data directory
        const userDataPath = electron_1.app.getPath('userData');
        this.keyFile = path.join(userDataPath, '.encryption-key');
    }
    static getInstance() {
        if (!EncryptionService.instance) {
            EncryptionService.instance = new EncryptionService();
        }
        return EncryptionService.instance;
    }
    /**
     * Initialize encryption service
     */
    async initialize() {
        try {
            await this.loadOrCreateEncryptionKey();
            console.log('🔐 Encryption service initialized');
        }
        catch (error) {
            console.error('❌ Failed to initialize encryption service:', error);
            throw error;
        }
    }
    /**
     * Load existing encryption key or create a new one
     */
    async loadOrCreateEncryptionKey() {
        try {
            if (fs.existsSync(this.keyFile)) {
                // Load existing key
                const keyData = fs.readFileSync(this.keyFile);
                this.encryptionKey = keyData;
                console.log('🔑 Loaded existing encryption key');
            }
            else {
                // Create new key
                this.encryptionKey = crypto.randomBytes(32); // 256-bit key
                // Ensure directory exists
                const keyDir = path.dirname(this.keyFile);
                if (!fs.existsSync(keyDir)) {
                    fs.mkdirSync(keyDir, { recursive: true });
                }
                // Save key to file with restricted permissions
                fs.writeFileSync(this.keyFile, this.encryptionKey, { mode: 0o600 });
                console.log('🔑 Created new encryption key');
            }
        }
        catch (error) {
            console.error('❌ Failed to load/create encryption key:', error);
            throw error;
        }
    }
    /**
     * Encrypt data using AES-256-GCM
     */
    encrypt(data) {
        if (!this.encryptionKey) {
            throw new Error('Encryption service not initialized');
        }
        const iv = crypto.randomBytes(16); // 128-bit IV
        const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);
        cipher.setAAD(Buffer.from('maithili-shop', 'utf8'));
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        const tag = cipher.getAuthTag();
        return {
            encrypted,
            iv: iv.toString('hex'),
            tag: tag.toString('hex')
        };
    }
    /**
     * Decrypt data using AES-256-GCM
     */
    decrypt(encryptedData) {
        if (!this.encryptionKey) {
            throw new Error('Encryption service not initialized');
        }
        const iv = Buffer.from(encryptedData.iv, 'hex');
        const decipher = crypto.createDecipheriv(this.algorithm, this.encryptionKey, iv);
        decipher.setAAD(Buffer.from('maithili-shop', 'utf8'));
        decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
    /**
     * Hash password with salt using PBKDF2
     */
    hashPassword(password, salt) {
        const passwordSalt = salt || crypto.randomBytes(32).toString('hex');
        const hash = crypto.pbkdf2Sync(password, passwordSalt, 10000, 64, 'sha512').toString('hex');
        return {
            hash,
            salt: passwordSalt
        };
    }
    /**
     * Verify password against hash
     */
    verifyPassword(password, hash, salt) {
        const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
        return hash === verifyHash;
    }
    /**
     * Generate secure random token
     */
    generateToken(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }
    /**
     * Encrypt file
     */
    async encryptFile(inputPath, outputPath) {
        if (!this.encryptionKey) {
            throw new Error('Encryption service not initialized');
        }
        return new Promise((resolve, reject) => {
            const iv = crypto.randomBytes(16);
            const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);
            const input = fs.createReadStream(inputPath);
            const output = fs.createWriteStream(outputPath);
            // Write IV to the beginning of the file
            output.write(iv);
            input.pipe(cipher).pipe(output);
            output.on('finish', () => resolve());
            output.on('error', reject);
            input.on('error', reject);
        });
    }
    /**
     * Decrypt file
     */
    async decryptFile(inputPath, outputPath) {
        if (!this.encryptionKey) {
            throw new Error('Encryption service not initialized');
        }
        return new Promise((resolve, reject) => {
            const input = fs.createReadStream(inputPath);
            const output = fs.createWriteStream(outputPath);
            // Read IV from the beginning of the file
            const ivBuffer = Buffer.alloc(16);
            let ivRead = false;
            input.on('readable', () => {
                if (!ivRead) {
                    const chunk = input.read(16);
                    if (chunk) {
                        chunk.copy(ivBuffer);
                        ivRead = true;
                        const decipher = crypto.createDecipheriv(this.algorithm, this.encryptionKey, ivBuffer);
                        input.pipe(decipher).pipe(output);
                    }
                }
            });
            output.on('finish', () => resolve());
            output.on('error', reject);
            input.on('error', reject);
        });
    }
    /**
     * Get encryption status
     */
    isInitialized() {
        return this.encryptionKey !== null;
    }
}
exports.EncryptionService = EncryptionService;
// Export singleton instance
exports.encryptionService = EncryptionService.getInstance();
//# sourceMappingURL=encryption.js.map