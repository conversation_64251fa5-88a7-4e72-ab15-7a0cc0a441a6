{"version": 3, "sources": ["../src/button/useARIAButtonShorthand.ts"], "sourcesContent": ["import { resolveShorthand } from '@fluentui/react-utilities';\nimport { useARIAButtonProps } from './useARIAButtonProps';\nimport type { ResolveShorthandFunction } from '@fluentui/react-utilities';\nimport type { ARIAButtonProps, ARIAButtonSlotProps, ARIAButtonType } from './types';\n\n/**\n * @internal\n *\n * @deprecated use useARIAButtonProps instead\n *\n * This function expects to receive a slot, if `as` property is not desired use `useARIAButtonProps` instead\n *\n * Button keyboard handling, role, disabled and tabIndex implementation that ensures ARIA spec\n * for multiple scenarios of shorthand properties. Ensuring 1st rule of ARIA for cases\n * where no attribute addition is required.\n */\nexport const useARIAButtonShorthand = ((value, options) => {\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  const shorthand = resolveShorthand(value, options);\n  const shorthandARIAButton = useARIAButtonProps<ARIAButtonType, ARIAButtonProps>(shorthand?.as ?? 'button', shorthand);\n  return shorthand && shorthandARIAButton;\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n}) as ResolveShorthandFunction<ARIAButtonSlotProps>;\n"], "names": ["resolve<PERSON><PERSON><PERSON>d", "useARIAButtonProps", "useARIAButtonShorthand", "value", "options", "shorthand", "shorthandARIAButton", "as"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,SAASA,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,kBAAkB,QAAQ,uBAAuB;AAI1D;;;;;;;;;;CAUC,GACD,OAAO,MAAMC,yBAA0B,CAACC,OAAOC;IAC7C,4DAA4D;IAC5D,MAAMC,YAAYL,iBAAiBG,OAAOC;QACsCC;IAAhF,MAAMC,sBAAsBL,mBAAoDI,CAAAA,gBAAAA,sBAAAA,gCAAAA,UAAWE,EAAE,cAAbF,2BAAAA,gBAAiB,UAAUA;IAC3G,OAAOA,aAAaC;AACpB,4DAA4D;AAC9D,EAAoD"}