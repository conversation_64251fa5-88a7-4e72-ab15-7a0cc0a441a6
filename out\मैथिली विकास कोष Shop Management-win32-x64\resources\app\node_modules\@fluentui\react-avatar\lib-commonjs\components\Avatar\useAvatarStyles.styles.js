"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    avatarClassNames: function() {
        return avatarClassNames;
    },
    useAvatarStyles_unstable: function() {
        return useAvatarStyles_unstable;
    },
    useSizeStyles: function() {
        return useSizeStyles;
    }
});
const _react = require("@griffel/react");
const avatarClassNames = {
    root: 'fui-Avatar',
    image: 'fui-Avatar__image',
    initials: 'fui-Avatar__initials',
    icon: 'fui-Avatar__icon',
    badge: 'fui-Avatar__badge'
};
// CSS variables used internally in Avatar's styles
const vars = {
    badgeRadius: '--fui-Avatar-badgeRadius',
    badgeGap: '--fui-Avatar-badgeGap',
    badgeAlign: '--fui-Avatar-badgeAlign',
    ringWidth: '--fui-Avatar-ringWidth'
};
const useRootClassName = /*#__PURE__*/ (0, _react.__resetStyles)("r81b29z", "r1aatmv", {
    r: [
        ".r81b29z{display:inline-block;flex-shrink:0;position:relative;vertical-align:middle;border-radius:var(--borderRadiusCircular);font-family:var(--fontFamilyBase);font-weight:var(--fontWeightSemibold);font-size:var(--fontSizeBase300);width:32px;height:32px;}",
        ".r81b29z::before,.r81b29z::after{position:absolute;top:0;left:0;bottom:0;right:0;z-index:-1;margin:calc(-2 * var(--fui-Avatar-ringWidth, 0px));border-radius:inherit;transition-property:margin,opacity;transition-timing-function:var(--curveEasyEaseMax),var(--curveLinear);transition-duration:var(--durationUltraSlow),var(--durationSlower);}",
        ".r81b29z::before{border-style:solid;border-width:var(--fui-Avatar-ringWidth);}",
        ".r1aatmv{display:inline-block;flex-shrink:0;position:relative;vertical-align:middle;border-radius:var(--borderRadiusCircular);font-family:var(--fontFamilyBase);font-weight:var(--fontWeightSemibold);font-size:var(--fontSizeBase300);width:32px;height:32px;}",
        ".r1aatmv::before,.r1aatmv::after{position:absolute;top:0;right:0;bottom:0;left:0;z-index:-1;margin:calc(-2 * var(--fui-Avatar-ringWidth, 0px));border-radius:inherit;transition-property:margin,opacity;transition-timing-function:var(--curveEasyEaseMax),var(--curveLinear);transition-duration:var(--durationUltraSlow),var(--durationSlower);}",
        ".r1aatmv::before{border-style:solid;border-width:var(--fui-Avatar-ringWidth);}"
    ],
    s: [
        "@media screen and (prefers-reduced-motion: reduce){.r81b29z::before,.r81b29z::after{transition-duration:0.01ms;}}",
        "@media screen and (prefers-reduced-motion: reduce){.r1aatmv::before,.r1aatmv::after{transition-duration:0.01ms;}}"
    ]
});
const useImageClassName = /*#__PURE__*/ (0, _react.__resetStyles)("r136dc0n", "rjly0nl", [
    ".r136dc0n{position:absolute;top:0;left:0;width:100%;height:100%;border-radius:inherit;object-fit:cover;vertical-align:top;}",
    ".rjly0nl{position:absolute;top:0;right:0;width:100%;height:100%;border-radius:inherit;object-fit:cover;vertical-align:top;}"
]);
const useIconInitialsClassName = /*#__PURE__*/ (0, _react.__resetStyles)("rip04v", "r31uzil", [
    ".rip04v{position:absolute;box-sizing:border-box;top:0;left:0;width:100%;height:100%;line-height:1;border:var(--strokeWidthThin) solid var(--colorTransparentStroke);display:flex;align-items:center;justify-content:center;vertical-align:center;text-align:center;-webkit-user-select:none;-moz-user-select:none;user-select:none;border-radius:inherit;}",
    ".r31uzil{position:absolute;box-sizing:border-box;top:0;right:0;width:100%;height:100%;line-height:1;border:var(--strokeWidthThin) solid var(--colorTransparentStroke);display:flex;align-items:center;justify-content:center;vertical-align:center;text-align:center;-webkit-user-select:none;-moz-user-select:none;user-select:none;border-radius:inherit;}"
]);
/**
 * Helper to create a maskImage that punches out a circle larger than the badge by `badgeGap`.
 * This creates a transparent gap between the badge and Avatar.
 *
 * Used by the icon, initials, and image slots, as well as the ring ::before pseudo-element.
 */ const badgeMask = (margin)=>{
    // Center the cutout at the badge's radius away from the edge.
    // The ring (::before) also has a 2 * ringWidth margin that also needs to be offset.
    const centerOffset = margin ? `calc(var(${vars.badgeRadius}) + ${margin})` : `var(${vars.badgeRadius})`;
    // radial-gradient does not have anti-aliasing, so the transparent and opaque gradient stops are offset by +/- 0.25px
    // to "fade" from transparent to opaque over a half-pixel and ease the transition.
    const innerRadius = `calc(var(${vars.badgeRadius}) + var(${vars.badgeGap}) - 0.25px)`;
    const outerRadius = `calc(var(${vars.badgeRadius}) + var(${vars.badgeGap}) + 0.25px)`;
    return `radial-gradient(circle at bottom ${centerOffset} var(${vars.badgeAlign}) ${centerOffset}, ` + `transparent ${innerRadius}, white ${outerRadius})`;
};
const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    textCaption2Strong: {
        Be2twd7: "f13mqy1h"
    },
    textCaption1Strong: {
        Be2twd7: "fy9rknc"
    },
    textSubtitle2: {
        Be2twd7: "fod5ikn"
    },
    textSubtitle1: {
        Be2twd7: "f1pp30po"
    },
    textTitle3: {
        Be2twd7: "f1x0m3f5"
    },
    squareSmall: {
        Beyfa6y: 0,
        Bbmb7ep: 0,
        Btl43ni: 0,
        B7oj6ja: 0,
        Dimara: "fq9zq91"
    },
    squareMedium: {
        Beyfa6y: 0,
        Bbmb7ep: 0,
        Btl43ni: 0,
        B7oj6ja: 0,
        Dimara: "ft85np5"
    },
    squareLarge: {
        Beyfa6y: 0,
        Bbmb7ep: 0,
        Btl43ni: 0,
        B7oj6ja: 0,
        Dimara: "f1o0qvyv"
    },
    squareXLarge: {
        Beyfa6y: 0,
        Bbmb7ep: 0,
        Btl43ni: 0,
        B7oj6ja: 0,
        Dimara: "f1kijzfu"
    },
    activeOrInactive: {
        Bz10aip: "ftfx35i",
        Bmy1vo4: "fv0atk9",
        B3o57yi: "f1iry5bo",
        Bkqvd7p: "f15n41j8",
        Hwfdqs: "f1onx1g3"
    },
    ring: {
        Ftih45: "f1wl9k8s"
    },
    ringBadgeCutout: {
        f4a502: "fp2gujx"
    },
    ringThick: {
        of393c: "fq1w1vq"
    },
    ringThicker: {
        of393c: "fzg6ace"
    },
    ringThickest: {
        of393c: "f1nu8p71"
    },
    shadow: {
        Bsft5z2: "f13zj6fq"
    },
    shadow4: {
        Be6vj1x: "fcjn15l"
    },
    shadow8: {
        Be6vj1x: "f1tm8t9f"
    },
    shadow16: {
        Be6vj1x: "f1a1aohj"
    },
    shadow28: {
        Be6vj1x: "fond6v5"
    },
    inactive: {
        abs64n: "fp25eh",
        Bz10aip: "f1clczzi",
        Bkqvd7p: "f1l3s34x",
        Bfgortx: 0,
        Bnvr3x9: 0,
        b2tv09: 0,
        Bucmhp4: 0,
        iayac2: "flkahu5",
        b6ubon: "fw457kn",
        Bqinb2h: "f1wmllxl"
    },
    badge: {
        qhf8xq: "f1euv43f",
        B5kzvoi: "f1yab3r1",
        j35jbq: [
            "f1e31b4d",
            "f1vgc2s3"
        ]
    },
    badgeCutout: {
        btxmck: "f1eugkqs"
    },
    badgeAlign: {
        Dnlfbu: [
            "f1tlnv9o",
            "f1y9kyih"
        ]
    },
    tiny: {
        Bdjeniz: "f1uwoubl",
        niu6jh: "fid048z"
    },
    "extra-small": {
        Bdjeniz: "f13ar0e0",
        niu6jh: "fid048z"
    },
    small: {
        Bdjeniz: "fwwuruf",
        niu6jh: "fid048z"
    },
    medium: {
        Bdjeniz: "f1af27q5",
        niu6jh: "fid048z"
    },
    large: {
        Bdjeniz: "f18yy57a",
        niu6jh: "f924bxt"
    },
    "extra-large": {
        Bdjeniz: "f2jg042",
        niu6jh: "f924bxt"
    },
    icon12: {
        Be2twd7: "f1ugzwwg"
    },
    icon16: {
        Be2twd7: "f4ybsrx"
    },
    icon20: {
        Be2twd7: "fe5j1ua"
    },
    icon24: {
        Be2twd7: "f1rt2boy"
    },
    icon28: {
        Be2twd7: "f24l1pt"
    },
    icon32: {
        Be2twd7: "ffl51b"
    },
    icon48: {
        Be2twd7: "f18m8u13"
    }
}, {
    d: [
        ".f13mqy1h{font-size:var(--fontSizeBase100);}",
        ".fy9rknc{font-size:var(--fontSizeBase200);}",
        ".fod5ikn{font-size:var(--fontSizeBase400);}",
        ".f1pp30po{font-size:var(--fontSizeBase500);}",
        ".f1x0m3f5{font-size:var(--fontSizeBase600);}",
        [
            ".fq9zq91{border-radius:var(--borderRadiusSmall);}",
            {
                p: -1
            }
        ],
        [
            ".ft85np5{border-radius:var(--borderRadiusMedium);}",
            {
                p: -1
            }
        ],
        [
            ".f1o0qvyv{border-radius:var(--borderRadiusLarge);}",
            {
                p: -1
            }
        ],
        [
            ".f1kijzfu{border-radius:var(--borderRadiusXLarge);}",
            {
                p: -1
            }
        ],
        ".ftfx35i{transform:perspective(1px);}",
        ".fv0atk9{transition-property:transform,opacity;}",
        ".f1iry5bo{transition-duration:var(--durationUltraSlow),var(--durationFaster);}",
        ".f15n41j8{transition-timing-function:var(--curveEasyEaseMax),var(--curveLinear);}",
        ".f1wl9k8s::before{content:\"\";}",
        ".fp2gujx::before{-webkit-mask-image:radial-gradient(circle at bottom calc(var(--fui-Avatar-badgeRadius) + 2 * var(--fui-Avatar-ringWidth)) var(--fui-Avatar-badgeAlign) calc(var(--fui-Avatar-badgeRadius) + 2 * var(--fui-Avatar-ringWidth)), transparent calc(var(--fui-Avatar-badgeRadius) + var(--fui-Avatar-badgeGap) - 0.25px), white calc(var(--fui-Avatar-badgeRadius) + var(--fui-Avatar-badgeGap) + 0.25px));mask-image:radial-gradient(circle at bottom calc(var(--fui-Avatar-badgeRadius) + 2 * var(--fui-Avatar-ringWidth)) var(--fui-Avatar-badgeAlign) calc(var(--fui-Avatar-badgeRadius) + 2 * var(--fui-Avatar-ringWidth)), transparent calc(var(--fui-Avatar-badgeRadius) + var(--fui-Avatar-badgeGap) - 0.25px), white calc(var(--fui-Avatar-badgeRadius) + var(--fui-Avatar-badgeGap) + 0.25px));}",
        ".fq1w1vq{--fui-Avatar-ringWidth:var(--strokeWidthThick);}",
        ".fzg6ace{--fui-Avatar-ringWidth:var(--strokeWidthThicker);}",
        ".f1nu8p71{--fui-Avatar-ringWidth:var(--strokeWidthThickest);}",
        ".f13zj6fq::after{content:\"\";}",
        ".fcjn15l::after{box-shadow:var(--shadow4);}",
        ".f1tm8t9f::after{box-shadow:var(--shadow8);}",
        ".f1a1aohj::after{box-shadow:var(--shadow16);}",
        ".fond6v5::after{box-shadow:var(--shadow28);}",
        ".fp25eh{opacity:0.8;}",
        ".f1clczzi{transform:scale(0.875);}",
        ".f1l3s34x{transition-timing-function:var(--curveDecelerateMin),var(--curveLinear);}",
        [
            ".flkahu5::before,.flkahu5::after{margin:0;}",
            {
                p: -1
            }
        ],
        ".fw457kn::before,.fw457kn::after{opacity:0;}",
        ".f1wmllxl::before,.f1wmllxl::after{transition-timing-function:var(--curveDecelerateMin),var(--curveLinear);}",
        ".f1euv43f{position:absolute;}",
        ".f1yab3r1{bottom:0;}",
        ".f1e31b4d{right:0;}",
        ".f1vgc2s3{left:0;}",
        ".f1eugkqs{-webkit-mask-image:radial-gradient(circle at bottom var(--fui-Avatar-badgeRadius) var(--fui-Avatar-badgeAlign) var(--fui-Avatar-badgeRadius), transparent calc(var(--fui-Avatar-badgeRadius) + var(--fui-Avatar-badgeGap) - 0.25px), white calc(var(--fui-Avatar-badgeRadius) + var(--fui-Avatar-badgeGap) + 0.25px));mask-image:radial-gradient(circle at bottom var(--fui-Avatar-badgeRadius) var(--fui-Avatar-badgeAlign) var(--fui-Avatar-badgeRadius), transparent calc(var(--fui-Avatar-badgeRadius) + var(--fui-Avatar-badgeGap) - 0.25px), white calc(var(--fui-Avatar-badgeRadius) + var(--fui-Avatar-badgeGap) + 0.25px));}",
        ".f1tlnv9o{--fui-Avatar-badgeAlign:right;}",
        ".f1y9kyih{--fui-Avatar-badgeAlign:left;}",
        ".f1uwoubl{--fui-Avatar-badgeRadius:3px;}",
        ".fid048z{--fui-Avatar-badgeGap:var(--strokeWidthThin);}",
        ".f13ar0e0{--fui-Avatar-badgeRadius:5px;}",
        ".fwwuruf{--fui-Avatar-badgeRadius:6px;}",
        ".f1af27q5{--fui-Avatar-badgeRadius:8px;}",
        ".f18yy57a{--fui-Avatar-badgeRadius:10px;}",
        ".f924bxt{--fui-Avatar-badgeGap:var(--strokeWidthThick);}",
        ".f2jg042{--fui-Avatar-badgeRadius:14px;}",
        ".f1ugzwwg{font-size:12px;}",
        ".f4ybsrx{font-size:16px;}",
        ".fe5j1ua{font-size:20px;}",
        ".f1rt2boy{font-size:24px;}",
        ".f24l1pt{font-size:28px;}",
        ".ffl51b{font-size:32px;}",
        ".f18m8u13{font-size:48px;}"
    ],
    m: [
        [
            "@media screen and (prefers-reduced-motion: reduce){.f1onx1g3{transition-duration:0.01ms;}}",
            {
                m: "screen and (prefers-reduced-motion: reduce)"
            }
        ]
    ]
});
const useSizeStyles = /*#__PURE__*/ (0, _react.__styles)({
    "16": {
        a9b677: "fjw5fx7",
        Bqenvij: "fd461yt"
    },
    "20": {
        a9b677: "f64fuq3",
        Bqenvij: "fjamq6b"
    },
    "24": {
        a9b677: "fq4mcun",
        Bqenvij: "frvgh55"
    },
    "28": {
        a9b677: "f1w9dchk",
        Bqenvij: "fxldao9"
    },
    "32": {
        a9b677: "f1szoe96",
        Bqenvij: "f1d2rq10"
    },
    "36": {
        a9b677: "fpdz1er",
        Bqenvij: "f8ljn23"
    },
    "40": {
        a9b677: "feqmc2u",
        Bqenvij: "fbhnoac"
    },
    "48": {
        a9b677: "f124akge",
        Bqenvij: "ff2sm71"
    },
    "56": {
        a9b677: "f1u66zr1",
        Bqenvij: "fzki0ko"
    },
    "64": {
        a9b677: "fa9ln6p",
        Bqenvij: "f16k9i2m"
    },
    "72": {
        a9b677: "fhcae8x",
        Bqenvij: "f1shusfg"
    },
    "96": {
        a9b677: "f1kyr2gn",
        Bqenvij: "fypu0ge"
    },
    "120": {
        a9b677: "fwfqyga",
        Bqenvij: "fjr5b71"
    },
    "128": {
        a9b677: "f1iksgmy",
        Bqenvij: "fele2au"
    }
}, {
    d: [
        ".fjw5fx7{width:16px;}",
        ".fd461yt{height:16px;}",
        ".f64fuq3{width:20px;}",
        ".fjamq6b{height:20px;}",
        ".fq4mcun{width:24px;}",
        ".frvgh55{height:24px;}",
        ".f1w9dchk{width:28px;}",
        ".fxldao9{height:28px;}",
        ".f1szoe96{width:32px;}",
        ".f1d2rq10{height:32px;}",
        ".fpdz1er{width:36px;}",
        ".f8ljn23{height:36px;}",
        ".feqmc2u{width:40px;}",
        ".fbhnoac{height:40px;}",
        ".f124akge{width:48px;}",
        ".ff2sm71{height:48px;}",
        ".f1u66zr1{width:56px;}",
        ".fzki0ko{height:56px;}",
        ".fa9ln6p{width:64px;}",
        ".f16k9i2m{height:64px;}",
        ".fhcae8x{width:72px;}",
        ".f1shusfg{height:72px;}",
        ".f1kyr2gn{width:96px;}",
        ".fypu0ge{height:96px;}",
        ".fwfqyga{width:120px;}",
        ".fjr5b71{height:120px;}",
        ".f1iksgmy{width:128px;}",
        ".fele2au{height:128px;}"
    ]
});
const useColorStyles = /*#__PURE__*/ (0, _react.__styles)({
    neutral: {
        sj55zd: "f11d4kpn",
        De3pzq: "f18f03hv"
    },
    brand: {
        sj55zd: "fonrgv7",
        De3pzq: "f1blnnmj"
    },
    "dark-red": {
        sj55zd: "fqjd1y1",
        De3pzq: "f1vq2oo4"
    },
    cranberry: {
        sj55zd: "fg9gses",
        De3pzq: "f1lwxszt"
    },
    red: {
        sj55zd: "f23f7i0",
        De3pzq: "f1q9qhfq"
    },
    pumpkin: {
        sj55zd: "fjnan08",
        De3pzq: "fz91bi3"
    },
    peach: {
        sj55zd: "fknu15p",
        De3pzq: "f1b9nr51"
    },
    marigold: {
        sj55zd: "f9603vw",
        De3pzq: "f3z4w6d"
    },
    gold: {
        sj55zd: "fmq0uwp",
        De3pzq: "fg50kya"
    },
    brass: {
        sj55zd: "f28g5vo",
        De3pzq: "f4w2gd0"
    },
    brown: {
        sj55zd: "ftl572b",
        De3pzq: "f14wu1f4"
    },
    forest: {
        sj55zd: "f1gymlvd",
        De3pzq: "f19ut4y6"
    },
    seafoam: {
        sj55zd: "fnnb6wn",
        De3pzq: "f1n057jc"
    },
    "dark-green": {
        sj55zd: "ff58qw8",
        De3pzq: "f11t05wk"
    },
    "light-teal": {
        sj55zd: "f1up9qbj",
        De3pzq: "f42feg1"
    },
    teal: {
        sj55zd: "f135dsb4",
        De3pzq: "f6hvv1p"
    },
    steel: {
        sj55zd: "f151dlcp",
        De3pzq: "f1lnp8zf"
    },
    blue: {
        sj55zd: "f1rjv50u",
        De3pzq: "f1ggcpy6"
    },
    "royal-blue": {
        sj55zd: "f1emykk5",
        De3pzq: "f12rj61f"
    },
    cornflower: {
        sj55zd: "fqsigj7",
        De3pzq: "f8k7hur"
    },
    navy: {
        sj55zd: "f1nj97xi",
        De3pzq: "f19gw0ux"
    },
    lavender: {
        sj55zd: "fwctg0i",
        De3pzq: "ff379vm"
    },
    purple: {
        sj55zd: "fjrsgpu",
        De3pzq: "f1mzf1e1"
    },
    grape: {
        sj55zd: "f1fiiydq",
        De3pzq: "f1o4k8oy"
    },
    lilac: {
        sj55zd: "f1res9jt",
        De3pzq: "f1x6mz1o"
    },
    pink: {
        sj55zd: "fv3fbbi",
        De3pzq: "fydlv6t"
    },
    magenta: {
        sj55zd: "f1f1fwnz",
        De3pzq: "f4xb6j5"
    },
    plum: {
        sj55zd: "f8ptl6j",
        De3pzq: "fqo8e26"
    },
    beige: {
        sj55zd: "f1ntv3ld",
        De3pzq: "f101elhj"
    },
    mink: {
        sj55zd: "f1fscmp",
        De3pzq: "f13g8o5c"
    },
    platinum: {
        sj55zd: "f1dr00v2",
        De3pzq: "fkh7blw"
    },
    anchor: {
        sj55zd: "f1f3ti53",
        De3pzq: "fu4yj0j"
    }
}, {
    d: [
        ".f11d4kpn{color:var(--colorNeutralForeground3);}",
        ".f18f03hv{background-color:var(--colorNeutralBackground6);}",
        ".fonrgv7{color:var(--colorNeutralForegroundStaticInverted);}",
        ".f1blnnmj{background-color:var(--colorBrandBackgroundStatic);}",
        ".fqjd1y1{color:var(--colorPaletteDarkRedForeground2);}",
        ".f1vq2oo4{background-color:var(--colorPaletteDarkRedBackground2);}",
        ".fg9gses{color:var(--colorPaletteCranberryForeground2);}",
        ".f1lwxszt{background-color:var(--colorPaletteCranberryBackground2);}",
        ".f23f7i0{color:var(--colorPaletteRedForeground2);}",
        ".f1q9qhfq{background-color:var(--colorPaletteRedBackground2);}",
        ".fjnan08{color:var(--colorPalettePumpkinForeground2);}",
        ".fz91bi3{background-color:var(--colorPalettePumpkinBackground2);}",
        ".fknu15p{color:var(--colorPalettePeachForeground2);}",
        ".f1b9nr51{background-color:var(--colorPalettePeachBackground2);}",
        ".f9603vw{color:var(--colorPaletteMarigoldForeground2);}",
        ".f3z4w6d{background-color:var(--colorPaletteMarigoldBackground2);}",
        ".fmq0uwp{color:var(--colorPaletteGoldForeground2);}",
        ".fg50kya{background-color:var(--colorPaletteGoldBackground2);}",
        ".f28g5vo{color:var(--colorPaletteBrassForeground2);}",
        ".f4w2gd0{background-color:var(--colorPaletteBrassBackground2);}",
        ".ftl572b{color:var(--colorPaletteBrownForeground2);}",
        ".f14wu1f4{background-color:var(--colorPaletteBrownBackground2);}",
        ".f1gymlvd{color:var(--colorPaletteForestForeground2);}",
        ".f19ut4y6{background-color:var(--colorPaletteForestBackground2);}",
        ".fnnb6wn{color:var(--colorPaletteSeafoamForeground2);}",
        ".f1n057jc{background-color:var(--colorPaletteSeafoamBackground2);}",
        ".ff58qw8{color:var(--colorPaletteDarkGreenForeground2);}",
        ".f11t05wk{background-color:var(--colorPaletteDarkGreenBackground2);}",
        ".f1up9qbj{color:var(--colorPaletteLightTealForeground2);}",
        ".f42feg1{background-color:var(--colorPaletteLightTealBackground2);}",
        ".f135dsb4{color:var(--colorPaletteTealForeground2);}",
        ".f6hvv1p{background-color:var(--colorPaletteTealBackground2);}",
        ".f151dlcp{color:var(--colorPaletteSteelForeground2);}",
        ".f1lnp8zf{background-color:var(--colorPaletteSteelBackground2);}",
        ".f1rjv50u{color:var(--colorPaletteBlueForeground2);}",
        ".f1ggcpy6{background-color:var(--colorPaletteBlueBackground2);}",
        ".f1emykk5{color:var(--colorPaletteRoyalBlueForeground2);}",
        ".f12rj61f{background-color:var(--colorPaletteRoyalBlueBackground2);}",
        ".fqsigj7{color:var(--colorPaletteCornflowerForeground2);}",
        ".f8k7hur{background-color:var(--colorPaletteCornflowerBackground2);}",
        ".f1nj97xi{color:var(--colorPaletteNavyForeground2);}",
        ".f19gw0ux{background-color:var(--colorPaletteNavyBackground2);}",
        ".fwctg0i{color:var(--colorPaletteLavenderForeground2);}",
        ".ff379vm{background-color:var(--colorPaletteLavenderBackground2);}",
        ".fjrsgpu{color:var(--colorPalettePurpleForeground2);}",
        ".f1mzf1e1{background-color:var(--colorPalettePurpleBackground2);}",
        ".f1fiiydq{color:var(--colorPaletteGrapeForeground2);}",
        ".f1o4k8oy{background-color:var(--colorPaletteGrapeBackground2);}",
        ".f1res9jt{color:var(--colorPaletteLilacForeground2);}",
        ".f1x6mz1o{background-color:var(--colorPaletteLilacBackground2);}",
        ".fv3fbbi{color:var(--colorPalettePinkForeground2);}",
        ".fydlv6t{background-color:var(--colorPalettePinkBackground2);}",
        ".f1f1fwnz{color:var(--colorPaletteMagentaForeground2);}",
        ".f4xb6j5{background-color:var(--colorPaletteMagentaBackground2);}",
        ".f8ptl6j{color:var(--colorPalettePlumForeground2);}",
        ".fqo8e26{background-color:var(--colorPalettePlumBackground2);}",
        ".f1ntv3ld{color:var(--colorPaletteBeigeForeground2);}",
        ".f101elhj{background-color:var(--colorPaletteBeigeBackground2);}",
        ".f1fscmp{color:var(--colorPaletteMinkForeground2);}",
        ".f13g8o5c{background-color:var(--colorPaletteMinkBackground2);}",
        ".f1dr00v2{color:var(--colorPalettePlatinumForeground2);}",
        ".fkh7blw{background-color:var(--colorPalettePlatinumBackground2);}",
        ".f1f3ti53{color:var(--colorPaletteAnchorForeground2);}",
        ".fu4yj0j{background-color:var(--colorPaletteAnchorBackground2);}"
    ]
});
const useRingColorStyles = /*#__PURE__*/ (0, _react.__styles)({
    neutral: {
        Bic5iru: "f1uuiafn"
    },
    brand: {
        Bic5iru: "f1uuiafn"
    },
    "dark-red": {
        Bic5iru: "f1t2x9on"
    },
    cranberry: {
        Bic5iru: "f1pvshc9"
    },
    red: {
        Bic5iru: "f1ectbk9"
    },
    pumpkin: {
        Bic5iru: "fvzpl0b"
    },
    peach: {
        Bic5iru: "fwj2kd7"
    },
    marigold: {
        Bic5iru: "fr120vy"
    },
    gold: {
        Bic5iru: "f8xmmar"
    },
    brass: {
        Bic5iru: "f1hbety2"
    },
    brown: {
        Bic5iru: "f1vg3s4g"
    },
    forest: {
        Bic5iru: "f1m3olm5"
    },
    seafoam: {
        Bic5iru: "f17xiqtr"
    },
    "dark-green": {
        Bic5iru: "fx32vyh"
    },
    "light-teal": {
        Bic5iru: "f1mkihwv"
    },
    teal: {
        Bic5iru: "fecnooh"
    },
    steel: {
        Bic5iru: "f15hfgzm"
    },
    blue: {
        Bic5iru: "fqproka"
    },
    "royal-blue": {
        Bic5iru: "f17v2w59"
    },
    cornflower: {
        Bic5iru: "fp0q1mo"
    },
    navy: {
        Bic5iru: "f1nlym55"
    },
    lavender: {
        Bic5iru: "f62vk8h"
    },
    purple: {
        Bic5iru: "f15zl69q"
    },
    grape: {
        Bic5iru: "f53w4j7"
    },
    lilac: {
        Bic5iru: "fu2771t"
    },
    pink: {
        Bic5iru: "fzflscs"
    },
    magenta: {
        Bic5iru: "fb6rmqc"
    },
    plum: {
        Bic5iru: "f1a4gm5b"
    },
    beige: {
        Bic5iru: "f1qpf9z1"
    },
    mink: {
        Bic5iru: "f1l7or83"
    },
    platinum: {
        Bic5iru: "fzrj0iu"
    },
    anchor: {
        Bic5iru: "f8oz6wf"
    }
}, {
    d: [
        ".f1uuiafn::before{color:var(--colorBrandStroke1);}",
        ".f1t2x9on::before{color:var(--colorPaletteDarkRedBorderActive);}",
        ".f1pvshc9::before{color:var(--colorPaletteCranberryBorderActive);}",
        ".f1ectbk9::before{color:var(--colorPaletteRedBorderActive);}",
        ".fvzpl0b::before{color:var(--colorPalettePumpkinBorderActive);}",
        ".fwj2kd7::before{color:var(--colorPalettePeachBorderActive);}",
        ".fr120vy::before{color:var(--colorPaletteMarigoldBorderActive);}",
        ".f8xmmar::before{color:var(--colorPaletteGoldBorderActive);}",
        ".f1hbety2::before{color:var(--colorPaletteBrassBorderActive);}",
        ".f1vg3s4g::before{color:var(--colorPaletteBrownBorderActive);}",
        ".f1m3olm5::before{color:var(--colorPaletteForestBorderActive);}",
        ".f17xiqtr::before{color:var(--colorPaletteSeafoamBorderActive);}",
        ".fx32vyh::before{color:var(--colorPaletteDarkGreenBorderActive);}",
        ".f1mkihwv::before{color:var(--colorPaletteLightTealBorderActive);}",
        ".fecnooh::before{color:var(--colorPaletteTealBorderActive);}",
        ".f15hfgzm::before{color:var(--colorPaletteSteelBorderActive);}",
        ".fqproka::before{color:var(--colorPaletteBlueBorderActive);}",
        ".f17v2w59::before{color:var(--colorPaletteRoyalBlueBorderActive);}",
        ".fp0q1mo::before{color:var(--colorPaletteCornflowerBorderActive);}",
        ".f1nlym55::before{color:var(--colorPaletteNavyBorderActive);}",
        ".f62vk8h::before{color:var(--colorPaletteLavenderBorderActive);}",
        ".f15zl69q::before{color:var(--colorPalettePurpleBorderActive);}",
        ".f53w4j7::before{color:var(--colorPaletteGrapeBorderActive);}",
        ".fu2771t::before{color:var(--colorPaletteLilacBorderActive);}",
        ".fzflscs::before{color:var(--colorPalettePinkBorderActive);}",
        ".fb6rmqc::before{color:var(--colorPaletteMagentaBorderActive);}",
        ".f1a4gm5b::before{color:var(--colorPalettePlumBorderActive);}",
        ".f1qpf9z1::before{color:var(--colorPaletteBeigeBorderActive);}",
        ".f1l7or83::before{color:var(--colorPaletteMinkBorderActive);}",
        ".fzrj0iu::before{color:var(--colorPalettePlatinumBorderActive);}",
        ".f8oz6wf::before{color:var(--colorPaletteAnchorBorderActive);}"
    ]
});
const useAvatarStyles_unstable = (state)=>{
    'use no memo';
    const { size, shape, active, activeAppearance, color } = state;
    const rootClassName = useRootClassName();
    const imageClassName = useImageClassName();
    const iconInitialsClassName = useIconInitialsClassName();
    const styles = useStyles();
    const sizeStyles = useSizeStyles();
    const colorStyles = useColorStyles();
    const ringColorStyles = useRingColorStyles();
    const rootClasses = [
        rootClassName,
        size !== 32 && sizeStyles[size]
    ];
    if (state.badge) {
        rootClasses.push(styles.badgeAlign, styles[state.badge.size || 'medium']);
    }
    if (size <= 24) {
        rootClasses.push(styles.textCaption2Strong);
    } else if (size <= 28) {
        rootClasses.push(styles.textCaption1Strong);
    } else if (size <= 40) {
    // Default text size included in useRootClassName
    } else if (size <= 56) {
        rootClasses.push(styles.textSubtitle2);
    } else if (size <= 96) {
        rootClasses.push(styles.textSubtitle1);
    } else {
        rootClasses.push(styles.textTitle3);
    }
    if (shape === 'square') {
        if (size <= 24) {
            rootClasses.push(styles.squareSmall);
        } else if (size <= 48) {
            rootClasses.push(styles.squareMedium);
        } else if (size <= 72) {
            rootClasses.push(styles.squareLarge);
        } else {
            rootClasses.push(styles.squareXLarge);
        }
    }
    if (active === 'active' || active === 'inactive') {
        rootClasses.push(styles.activeOrInactive);
        if (activeAppearance === 'ring' || activeAppearance === 'ring-shadow') {
            rootClasses.push(styles.ring, ringColorStyles[color]);
            if (state.badge) {
                rootClasses.push(styles.ringBadgeCutout);
            }
            if (size <= 48) {
                rootClasses.push(styles.ringThick);
            } else if (size <= 64) {
                rootClasses.push(styles.ringThicker);
            } else {
                rootClasses.push(styles.ringThickest);
            }
        }
        if (activeAppearance === 'shadow' || activeAppearance === 'ring-shadow') {
            rootClasses.push(styles.shadow);
            if (size <= 28) {
                rootClasses.push(styles.shadow4);
            } else if (size <= 48) {
                rootClasses.push(styles.shadow8);
            } else if (size <= 64) {
                rootClasses.push(styles.shadow16);
            } else {
                rootClasses.push(styles.shadow28);
            }
        }
        // Note: The inactive style overrides some of the activeAppearance styles and must be applied after them
        if (active === 'inactive') {
            rootClasses.push(styles.inactive);
        }
    }
    state.root.className = (0, _react.mergeClasses)(avatarClassNames.root, ...rootClasses, state.root.className);
    if (state.badge) {
        state.badge.className = (0, _react.mergeClasses)(avatarClassNames.badge, styles.badge, state.badge.className);
    }
    if (state.image) {
        state.image.className = (0, _react.mergeClasses)(avatarClassNames.image, imageClassName, colorStyles[color], state.badge && styles.badgeCutout, state.image.className);
    }
    if (state.initials) {
        state.initials.className = (0, _react.mergeClasses)(avatarClassNames.initials, iconInitialsClassName, colorStyles[color], state.badge && styles.badgeCutout, state.initials.className);
    }
    if (state.icon) {
        let iconSizeClass;
        if (size <= 16) {
            iconSizeClass = styles.icon12;
        } else if (size <= 24) {
            iconSizeClass = styles.icon16;
        } else if (size <= 40) {
            iconSizeClass = styles.icon20;
        } else if (size <= 48) {
            iconSizeClass = styles.icon24;
        } else if (size <= 56) {
            iconSizeClass = styles.icon28;
        } else if (size <= 72) {
            iconSizeClass = styles.icon32;
        } else {
            iconSizeClass = styles.icon48;
        }
        state.icon.className = (0, _react.mergeClasses)(avatarClassNames.icon, iconInitialsClassName, iconSizeClass, colorStyles[color], state.badge && styles.badgeCutout, state.icon.className);
    }
    return state;
};
