"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useAvatarGroupPopover_unstable", {
    enumerable: true,
    get: function() {
        return useAvatarGroupPopover_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _AvatarGroupContext = require("../../contexts/AvatarGroupContext");
const _useAvatarGroup = require("../AvatarGroup/useAvatarGroup");
const _reactutilities = require("@fluentui/react-utilities");
const _reacticons = require("@fluentui/react-icons");
const _reactpopover = require("@fluentui/react-popover");
const _reacttooltip = require("@fluentui/react-tooltip");
const useAvatarGroupPopover_unstable = (props)=>{
    var _useAvatarGroupContext_unstable;
    const size = (_useAvatarGroupContext_unstable = (0, _AvatarGroupContext.useAvatarGroupContext_unstable)((ctx)=>ctx.size)) !== null && _useAvatarGroupContext_unstable !== void 0 ? _useAvatarGroupContext_unstable : _useAvatarGroup.defaultAvatarGroupSize;
    const layout = (0, _AvatarGroupContext.useAvatarGroupContext_unstable)((ctx)=>ctx.layout);
    const { indicator = size < 24 ? 'icon' : 'count', count = _react.Children.count(props.children), children, ...restOfProps } = props;
    const [popoverOpen, setPopoverOpen] = (0, _reactutilities.useControllableState)({
        state: props.open,
        defaultState: props.defaultOpen,
        initialState: false
    });
    const handleOnPopoverChange = (e, data)=>{
        var _restOfProps_onOpenChange;
        (_restOfProps_onOpenChange = restOfProps.onOpenChange) === null || _restOfProps_onOpenChange === void 0 ? void 0 : _restOfProps_onOpenChange.call(restOfProps, e, data);
        setPopoverOpen(data.open);
    };
    let triggerButtonChildren;
    if (layout === 'pie') {
        triggerButtonChildren = null;
    } else if (indicator === 'icon') {
        triggerButtonChildren = /*#__PURE__*/ _react.createElement(_reacticons.MoreHorizontalRegular, null);
    } else {
        triggerButtonChildren = count > 99 ? '99+' : `+${count}`;
    }
    return {
        count,
        indicator,
        layout,
        popoverOpen,
        size,
        components: {
            root: _reactpopover.Popover,
            triggerButton: 'button',
            content: 'ul',
            popoverSurface: _reactpopover.PopoverSurface,
            tooltip: _reacttooltip.Tooltip
        },
        root: _reactutilities.slot.always({
            // Popover expects a child for its children. The children are added in the renderAvatarGroupPopover.
            children: /*#__PURE__*/ _react.createElement(_react.Fragment, null),
            size: 'small',
            trapFocus: true,
            ...restOfProps,
            open: popoverOpen,
            onOpenChange: handleOnPopoverChange
        }, {
            elementType: _reactpopover.Popover
        }),
        triggerButton: _reactutilities.slot.always(props.triggerButton, {
            defaultProps: {
                children: triggerButtonChildren,
                type: 'button'
            },
            elementType: 'button'
        }),
        content: _reactutilities.slot.always(props.content, {
            defaultProps: {
                children,
                role: 'list'
            },
            elementType: 'ul'
        }),
        popoverSurface: _reactutilities.slot.always(props.popoverSurface, {
            defaultProps: {
                'aria-label': 'Overflow',
                tabIndex: 0
            },
            elementType: _reactpopover.PopoverSurface
        }),
        tooltip: _reactutilities.slot.always(props.tooltip, {
            defaultProps: {
                content: 'View more people.',
                relationship: 'label'
            },
            elementType: _reacttooltip.Tooltip
        })
    };
};
