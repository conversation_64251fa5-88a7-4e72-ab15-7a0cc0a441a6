{"version": 3, "sources": ["../src/components/AccordionItem/AccordionItem.types.ts"], "sourcesContent": ["import type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\nimport { AccordionItemContextValue } from '../../contexts/accordionItem';\n\nexport type AccordionItemContextValues<Value = AccordionItemValue> = {\n  accordionItem: AccordionItemContextValue<Value>;\n};\n\nexport type AccordionItemSlots = {\n  root: NonNullable<Slot<'div'>>;\n};\n\nexport type AccordionItemProps<Value = AccordionItemValue> = ComponentProps<AccordionItemSlots> & {\n  /**\n   * Disables opening/closing of panel.\n   */\n  disabled?: boolean;\n  /**\n   * Required value that identifies this item inside an Accordion component.\n   */\n  value: Value;\n};\n\nexport type AccordionItemValue = unknown;\n\nexport type AccordionItemState<Value = AccordionItemValue> = ComponentState<AccordionItemSlots> &\n  AccordionItemContextValue<Value>;\n"], "names": [], "rangeMappings": "", "mappings": ""}