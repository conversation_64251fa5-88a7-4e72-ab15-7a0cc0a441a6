"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.productService = exports.ProductService = void 0;
const database_1 = require("../database/database");
/**
 * Product service for managing handcraft products
 */
class ProductService {
    /**
     * Create a new product
     */
    async createProduct(productData) {
        try {
            const result = database_1.databaseService.execute(`
        INSERT INTO products (
          name, name_hindi, description, description_hindi, category_id, artist_id,
          sku, barcode, cost_price, selling_price, mrp, weight, dimensions,
          materials, materials_hindi, colors, is_active, tags, images
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
                productData.name,
                productData.nameHindi,
                productData.description || null,
                productData.descriptionHindi || null,
                productData.categoryId,
                productData.artistId,
                productData.sku,
                productData.barcode || null,
                productData.costPrice,
                productData.sellingPrice,
                productData.mrp,
                productData.weight || null,
                productData.dimensions || null,
                productData.materials || null,
                productData.materialsHindi || null,
                productData.colors || null,
                productData.isActive ? 1 : 0,
                productData.tags || null,
                productData.images || null
            ]);
            if (!result.success || !result.data) {
                return { success: false, error: result.error || 'Unknown error' };
            }
            // Get the created product
            const createdProduct = await this.getProductById(result.data.lastInsertRowid);
            return createdProduct;
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to create product'
            };
        }
    }
    /**
     * Get product by ID with related data
     */
    async getProductById(id) {
        return database_1.databaseService.queryOne(`
      SELECT 
        p.*,
        c.name as categoryName,
        a.name as artistName
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      WHERE p.id = ?
    `, [id]);
    }
    /**
     * Get product by SKU
     */
    async getProductBySku(sku) {
        return database_1.databaseService.queryOne(`
      SELECT * FROM products WHERE sku = ?
    `, [sku]);
    }
    /**
     * Get all products with pagination and filtering
     */
    async getProducts(filters, sort, page = 1, limit = 50) {
        let sql = `
      SELECT 
        p.*,
        c.name as categoryName,
        a.name as artistName,
        i.current_stock,
        i.available_stock
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      LEFT JOIN inventory i ON p.id = i.product_id
    `;
        let params = [];
        if (filters) {
            const { where, params: whereParams } = database_1.databaseService.buildWhereClause(filters);
            sql += ` ${where.replace('WHERE', 'WHERE p.')}`;
            params = whereParams;
        }
        sql += ` ${database_1.databaseService.buildOrderClause(sort)}`;
        return database_1.databaseService.paginate(sql, params, page, limit);
    }
    /**
     * Update product
     */
    async updateProduct(id, productData) {
        try {
            const updates = [];
            const params = [];
            if (productData.name !== undefined) {
                updates.push('name = ?');
                params.push(productData.name);
            }
            if (productData.nameHindi !== undefined) {
                updates.push('name_hindi = ?');
                params.push(productData.nameHindi);
            }
            if (productData.description !== undefined) {
                updates.push('description = ?');
                params.push(productData.description);
            }
            if (productData.descriptionHindi !== undefined) {
                updates.push('description_hindi = ?');
                params.push(productData.descriptionHindi);
            }
            if (productData.categoryId !== undefined) {
                updates.push('category_id = ?');
                params.push(productData.categoryId);
            }
            if (productData.artistId !== undefined) {
                updates.push('artist_id = ?');
                params.push(productData.artistId);
            }
            if (productData.sku !== undefined) {
                updates.push('sku = ?');
                params.push(productData.sku);
            }
            if (productData.barcode !== undefined) {
                updates.push('barcode = ?');
                params.push(productData.barcode);
            }
            if (productData.costPrice !== undefined) {
                updates.push('cost_price = ?');
                params.push(productData.costPrice);
            }
            if (productData.sellingPrice !== undefined) {
                updates.push('selling_price = ?');
                params.push(productData.sellingPrice);
            }
            if (productData.mrp !== undefined) {
                updates.push('mrp = ?');
                params.push(productData.mrp);
            }
            if (productData.weight !== undefined) {
                updates.push('weight = ?');
                params.push(productData.weight);
            }
            if (productData.dimensions !== undefined) {
                updates.push('dimensions = ?');
                params.push(productData.dimensions);
            }
            if (productData.materials !== undefined) {
                updates.push('materials = ?');
                params.push(productData.materials);
            }
            if (productData.materialsHindi !== undefined) {
                updates.push('materials_hindi = ?');
                params.push(productData.materialsHindi);
            }
            if (productData.colors !== undefined) {
                updates.push('colors = ?');
                params.push(productData.colors);
            }
            if (productData.isActive !== undefined) {
                updates.push('is_active = ?');
                params.push(productData.isActive ? 1 : 0);
            }
            if (productData.tags !== undefined) {
                updates.push('tags = ?');
                params.push(productData.tags);
            }
            if (productData.images !== undefined) {
                updates.push('images = ?');
                params.push(productData.images);
            }
            if (updates.length === 0) {
                return { success: false, error: 'No fields to update' };
            }
            updates.push('updated_at = datetime("now")');
            params.push(id);
            const result = database_1.databaseService.execute(`
        UPDATE products SET ${updates.join(', ')} WHERE id = ?
      `, params);
            if (!result.success) {
                return { success: false, error: result.error || 'Unknown error' };
            }
            // Get the updated product
            return await this.getProductById(id);
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update product'
            };
        }
    }
    /**
     * Delete product (soft delete by setting inactive)
     */
    async deleteProduct(id) {
        const result = database_1.databaseService.execute(`
      UPDATE products SET is_active = 0, updated_at = datetime('now') WHERE id = ?
    `, [id]);
        return {
            success: result.success,
            data: result.success,
            error: result.error || undefined
        };
    }
    /**
     * Search products by name or description
     */
    async searchProducts(query, limit = 20) {
        return database_1.databaseService.query(`
      SELECT 
        p.*,
        c.name as categoryName,
        a.name as artistName
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      WHERE p.is_active = 1 
        AND (p.name LIKE ? OR p.name_hindi LIKE ? OR p.description LIKE ? OR p.sku LIKE ?)
      ORDER BY p.name
      LIMIT ?
    `, [`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`, limit]);
    }
    /**
     * Get products by category
     */
    async getProductsByCategory(categoryId, limit = 50) {
        return database_1.databaseService.query(`
      SELECT 
        p.*,
        c.name as categoryName,
        a.name as artistName
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      WHERE p.category_id = ? AND p.is_active = 1
      ORDER BY p.name
      LIMIT ?
    `, [categoryId, limit]);
    }
    /**
     * Get products by artist
     */
    async getProductsByArtist(artistId, limit = 50) {
        return database_1.databaseService.query(`
      SELECT 
        p.*,
        c.name as categoryName,
        a.name as artistName
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN artists a ON p.artist_id = a.id
      WHERE p.artist_id = ? AND p.is_active = 1
      ORDER BY p.name
      LIMIT ?
    `, [artistId, limit]);
    }
    /**
     * Get low stock products
     */
    async getLowStockProducts() {
        return database_1.databaseService.query(`
      SELECT 
        p.*,
        i.current_stock as currentStock,
        i.min_stock_level as minStockLevel
      FROM products p
      INNER JOIN inventory i ON p.id = i.product_id
      WHERE p.is_active = 1 AND i.current_stock <= i.min_stock_level
      ORDER BY i.current_stock ASC
    `);
    }
    /**
     * Generate unique SKU
     */
    async generateSku(categoryId, artistId) {
        var _a, _b;
        const categoryResult = await database_1.databaseService.queryOne('SELECT name FROM categories WHERE id = ?', [categoryId]);
        const artistResult = await database_1.databaseService.queryOne('SELECT name FROM artists WHERE id = ?', [artistId]);
        const categoryCode = ((_a = categoryResult.data) === null || _a === void 0 ? void 0 : _a.name.substring(0, 3).toUpperCase()) || 'CAT';
        const artistCode = ((_b = artistResult.data) === null || _b === void 0 ? void 0 : _b.name.substring(0, 3).toUpperCase()) || 'ART';
        const timestamp = Date.now().toString().slice(-6);
        return `${categoryCode}-${artistCode}-${timestamp}`;
    }
}
exports.ProductService = ProductService;
exports.productService = new ProductService();
//# sourceMappingURL=productService.js.map