import type { FluentIcon } from "../utils/createFluentIcon";
export declare const AccessTime20Filled: FluentIcon;
export declare const AccessTime20Regular: FluentIcon;
export declare const AccessTime24Filled: FluentIcon;
export declare const AccessTime24Regular: FluentIcon;
export declare const Accessibility16Filled: FluentIcon;
export declare const Accessibility16Regular: FluentIcon;
export declare const Accessibility20Filled: FluentIcon;
export declare const Accessibility20Regular: FluentIcon;
export declare const Accessibility24Filled: FluentIcon;
export declare const Accessibility24Regular: FluentIcon;
export declare const Accessibility28Filled: FluentIcon;
export declare const Accessibility28Regular: FluentIcon;
export declare const Accessibility32Filled: FluentIcon;
export declare const Accessibility32Regular: FluentIcon;
export declare const Accessibility48Filled: FluentIcon;
export declare const Accessibility48Regular: FluentIcon;
export declare const AccessibilityCheckmark20Filled: FluentIcon;
export declare const AccessibilityCheckmark20Regular: FluentIcon;
export declare const AccessibilityCheckmark24Filled: FluentIcon;
export declare const AccessibilityCheckmark24Regular: FluentIcon;
export declare const AccessibilityCheckmark28Filled: FluentIcon;
export declare const AccessibilityCheckmark28Regular: FluentIcon;
export declare const AccessibilityCheckmark32Filled: FluentIcon;
export declare const AccessibilityCheckmark32Light: FluentIcon;
export declare const AccessibilityCheckmark32Regular: FluentIcon;
export declare const AccessibilityCheckmark48Filled: FluentIcon;
export declare const AccessibilityCheckmark48Regular: FluentIcon;
export declare const AccessibilityError20Filled: FluentIcon;
export declare const AccessibilityError20Regular: FluentIcon;
export declare const AccessibilityError24Filled: FluentIcon;
export declare const AccessibilityError24Regular: FluentIcon;
export declare const AccessibilityMore16Filled: FluentIcon;
export declare const AccessibilityMore16Regular: FluentIcon;
export declare const AccessibilityMore20Filled: FluentIcon;
export declare const AccessibilityMore20Regular: FluentIcon;
export declare const AccessibilityMore24Filled: FluentIcon;
export declare const AccessibilityMore24Regular: FluentIcon;
export declare const AccessibilityQuestionMark20Filled: FluentIcon;
export declare const AccessibilityQuestionMark20Regular: FluentIcon;
export declare const AccessibilityQuestionMark24Filled: FluentIcon;
export declare const AccessibilityQuestionMark24Regular: FluentIcon;
export declare const Add12Filled: FluentIcon;
export declare const Add12Regular: FluentIcon;
export declare const Add16Filled: FluentIcon;
export declare const Add16Regular: FluentIcon;
export declare const Add20Filled: FluentIcon;
export declare const Add20Regular: FluentIcon;
export declare const Add24Filled: FluentIcon;
export declare const Add24Regular: FluentIcon;
export declare const Add28Filled: FluentIcon;
export declare const Add28Regular: FluentIcon;
export declare const Add32Filled: FluentIcon;
export declare const Add32Light: FluentIcon;
export declare const Add32Regular: FluentIcon;
export declare const Add48Filled: FluentIcon;
export declare const Add48Regular: FluentIcon;
export declare const AddCircle12Filled: FluentIcon;
export declare const AddCircle12Regular: FluentIcon;
export declare const AddCircle16Color: FluentIcon;
export declare const AddCircle16Filled: FluentIcon;
export declare const AddCircle16Regular: FluentIcon;
export declare const AddCircle20Color: FluentIcon;
export declare const AddCircle20Filled: FluentIcon;
export declare const AddCircle20Regular: FluentIcon;
export declare const AddCircle24Color: FluentIcon;
export declare const AddCircle24Filled: FluentIcon;
export declare const AddCircle24Regular: FluentIcon;
export declare const AddCircle28Color: FluentIcon;
export declare const AddCircle28Filled: FluentIcon;
export declare const AddCircle28Regular: FluentIcon;
export declare const AddCircle32Color: FluentIcon;
export declare const AddCircle32Filled: FluentIcon;
export declare const AddCircle32Regular: FluentIcon;
export declare const AddCircle48Filled: FluentIcon;
export declare const AddCircle48Regular: FluentIcon;
export declare const AddSquare16Filled: FluentIcon;
export declare const AddSquare16Regular: FluentIcon;
export declare const AddSquare20Filled: FluentIcon;
export declare const AddSquare20Regular: FluentIcon;
export declare const AddSquare24Filled: FluentIcon;
export declare const AddSquare24Regular: FluentIcon;
export declare const AddSquare28Filled: FluentIcon;
export declare const AddSquare28Regular: FluentIcon;
export declare const AddSquare32Filled: FluentIcon;
export declare const AddSquare32Regular: FluentIcon;
export declare const AddSquare48Filled: FluentIcon;
export declare const AddSquare48Regular: FluentIcon;
export declare const AddSquareMultiple16Filled: FluentIcon;
export declare const AddSquareMultiple16Regular: FluentIcon;
export declare const AddSquareMultiple20Filled: FluentIcon;
export declare const AddSquareMultiple20Regular: FluentIcon;
export declare const AddSquareMultiple24Filled: FluentIcon;
export declare const AddSquareMultiple24Regular: FluentIcon;
export declare const AddStarburst16Color: FluentIcon;
export declare const AddStarburst16Filled: FluentIcon;
export declare const AddStarburst16Regular: FluentIcon;
export declare const AddStarburst20Color: FluentIcon;
export declare const AddStarburst20Filled: FluentIcon;
export declare const AddStarburst20Regular: FluentIcon;
export declare const AddStarburst24Color: FluentIcon;
export declare const AddStarburst24Filled: FluentIcon;
export declare const AddStarburst24Regular: FluentIcon;
export declare const AddStarburst28Color: FluentIcon;
export declare const AddStarburst28Filled: FluentIcon;
export declare const AddStarburst28Regular: FluentIcon;
export declare const AddStarburst32Color: FluentIcon;
export declare const AddStarburst32Filled: FluentIcon;
export declare const AddStarburst32Regular: FluentIcon;
export declare const AddStarburst48Color: FluentIcon;
export declare const AddStarburst48Filled: FluentIcon;
export declare const AddStarburst48Regular: FluentIcon;
export declare const AddSubtractCircle16Filled: FluentIcon;
export declare const AddSubtractCircle16Regular: FluentIcon;
export declare const AddSubtractCircle20Filled: FluentIcon;
export declare const AddSubtractCircle20Regular: FluentIcon;
export declare const AddSubtractCircle24Filled: FluentIcon;
export declare const AddSubtractCircle24Regular: FluentIcon;
export declare const AddSubtractCircle28Filled: FluentIcon;
export declare const AddSubtractCircle28Regular: FluentIcon;
export declare const AddSubtractCircle48Filled: FluentIcon;
export declare const AddSubtractCircle48Regular: FluentIcon;
export declare const Agents16Color: FluentIcon;
export declare const Agents16Filled: FluentIcon;
export declare const Agents16Regular: FluentIcon;
export declare const Agents20Color: FluentIcon;
export declare const Agents20Filled: FluentIcon;
export declare const Agents20Regular: FluentIcon;
export declare const Agents24Color: FluentIcon;
export declare const Agents24Filled: FluentIcon;
export declare const Agents24Regular: FluentIcon;
export declare const Agents28Color: FluentIcon;
export declare const Agents28Filled: FluentIcon;
export declare const Agents28Regular: FluentIcon;
export declare const Agents32Color: FluentIcon;
export declare const Agents32Filled: FluentIcon;
export declare const Agents32Regular: FluentIcon;
export declare const Agents48Color: FluentIcon;
export declare const Agents48Filled: FluentIcon;
export declare const Agents48Regular: FluentIcon;
export declare const AgentsAdd20Filled: FluentIcon;
export declare const AgentsAdd20Regular: FluentIcon;
export declare const AgentsAdd24Filled: FluentIcon;
export declare const AgentsAdd24Regular: FluentIcon;
export declare const Airplane16Filled: FluentIcon;
export declare const Airplane16Regular: FluentIcon;
export declare const Airplane20Filled: FluentIcon;
export declare const Airplane20Regular: FluentIcon;
export declare const Airplane24Filled: FluentIcon;
export declare const Airplane24Regular: FluentIcon;
export declare const Airplane28Filled: FluentIcon;
export declare const Airplane28Regular: FluentIcon;
export declare const Airplane32Filled: FluentIcon;
export declare const Airplane32Regular: FluentIcon;
export declare const Airplane48Filled: FluentIcon;
export declare const Airplane48Regular: FluentIcon;
export declare const AirplaneLanding16Filled: FluentIcon;
export declare const AirplaneLanding16Regular: FluentIcon;
export declare const AirplaneLanding20Filled: FluentIcon;
export declare const AirplaneLanding20Regular: FluentIcon;
export declare const AirplaneLanding24Filled: FluentIcon;
export declare const AirplaneLanding24Regular: FluentIcon;
export declare const AirplaneTakeOff16Filled: FluentIcon;
export declare const AirplaneTakeOff16Regular: FluentIcon;
export declare const AirplaneTakeOff20Filled: FluentIcon;
export declare const AirplaneTakeOff20Regular: FluentIcon;
export declare const AirplaneTakeOff24Filled: FluentIcon;
export declare const AirplaneTakeOff24Regular: FluentIcon;
export declare const Album20Filled: FluentIcon;
export declare const Album20Regular: FluentIcon;
export declare const Album24Filled: FluentIcon;
export declare const Album24Regular: FluentIcon;
export declare const AlbumAdd20Filled: FluentIcon;
export declare const AlbumAdd20Regular: FluentIcon;
export declare const AlbumAdd24Filled: FluentIcon;
export declare const AlbumAdd24Regular: FluentIcon;
export declare const Alert12Filled: FluentIcon;
export declare const Alert12Regular: FluentIcon;
export declare const Alert16Color: FluentIcon;
export declare const Alert16Filled: FluentIcon;
export declare const Alert16Regular: FluentIcon;
export declare const Alert20Color: FluentIcon;
export declare const Alert20Filled: FluentIcon;
export declare const Alert20Regular: FluentIcon;
export declare const Alert24Color: FluentIcon;
export declare const Alert24Filled: FluentIcon;
export declare const Alert24Regular: FluentIcon;
export declare const Alert28Color: FluentIcon;
export declare const Alert28Filled: FluentIcon;
export declare const Alert28Regular: FluentIcon;
export declare const Alert32Color: FluentIcon;
export declare const Alert32Filled: FluentIcon;
export declare const Alert32Light: FluentIcon;
export declare const Alert32Regular: FluentIcon;
export declare const Alert48Color: FluentIcon;
export declare const Alert48Filled: FluentIcon;
export declare const Alert48Regular: FluentIcon;
export declare const AlertBadge16Color: FluentIcon;
export declare const AlertBadge16Filled: FluentIcon;
export declare const AlertBadge16Regular: FluentIcon;
export declare const AlertBadge20Color: FluentIcon;
export declare const AlertBadge20Filled: FluentIcon;
export declare const AlertBadge20Regular: FluentIcon;
export declare const AlertBadge24Color: FluentIcon;
export declare const AlertBadge24Filled: FluentIcon;
export declare const AlertBadge24Regular: FluentIcon;
export declare const AlertBadge32Color: FluentIcon;
export declare const AlertBadge32Filled: FluentIcon;
export declare const AlertBadge32Regular: FluentIcon;
export declare const AlertOff16Filled: FluentIcon;
export declare const AlertOff16Regular: FluentIcon;
export declare const AlertOff20Filled: FluentIcon;
export declare const AlertOff20Regular: FluentIcon;
export declare const AlertOff24Filled: FluentIcon;
export declare const AlertOff24Regular: FluentIcon;
export declare const AlertOff28Filled: FluentIcon;
export declare const AlertOff28Regular: FluentIcon;
export declare const AlertOn16Filled: FluentIcon;
export declare const AlertOn16Regular: FluentIcon;
export declare const AlertOn20Filled: FluentIcon;
export declare const AlertOn20Regular: FluentIcon;
export declare const AlertOn24Filled: FluentIcon;
export declare const AlertOn24Regular: FluentIcon;
export declare const AlertSnooze12Filled: FluentIcon;
export declare const AlertSnooze12Regular: FluentIcon;
export declare const AlertSnooze16Filled: FluentIcon;
export declare const AlertSnooze16Regular: FluentIcon;
export declare const AlertSnooze20Filled: FluentIcon;
export declare const AlertSnooze20Regular: FluentIcon;
export declare const AlertSnooze24Filled: FluentIcon;
export declare const AlertSnooze24Regular: FluentIcon;
export declare const AlertUrgent16Color: FluentIcon;
export declare const AlertUrgent16Filled: FluentIcon;
export declare const AlertUrgent16Regular: FluentIcon;
export declare const AlertUrgent20Color: FluentIcon;
export declare const AlertUrgent20Filled: FluentIcon;
export declare const AlertUrgent20Regular: FluentIcon;
export declare const AlertUrgent24Color: FluentIcon;
export declare const AlertUrgent24Filled: FluentIcon;
export declare const AlertUrgent24Regular: FluentIcon;
export declare const AlignBottom16Filled: FluentIcon;
export declare const AlignBottom16Regular: FluentIcon;
export declare const AlignBottom20Filled: FluentIcon;
export declare const AlignBottom20Regular: FluentIcon;
export declare const AlignBottom24Filled: FluentIcon;
export declare const AlignBottom24Regular: FluentIcon;
export declare const AlignBottom28Filled: FluentIcon;
export declare const AlignBottom28Regular: FluentIcon;
export declare const AlignBottom32Filled: FluentIcon;
export declare const AlignBottom32Regular: FluentIcon;
export declare const AlignBottom48Filled: FluentIcon;
export declare const AlignBottom48Regular: FluentIcon;
export declare const AlignCenterHorizontal16Filled: FluentIcon;
export declare const AlignCenterHorizontal16Regular: FluentIcon;
export declare const AlignCenterHorizontal20Filled: FluentIcon;
export declare const AlignCenterHorizontal20Regular: FluentIcon;
export declare const AlignCenterHorizontal24Filled: FluentIcon;
export declare const AlignCenterHorizontal24Regular: FluentIcon;
export declare const AlignCenterHorizontal28Filled: FluentIcon;
export declare const AlignCenterHorizontal28Regular: FluentIcon;
export declare const AlignCenterHorizontal32Filled: FluentIcon;
export declare const AlignCenterHorizontal32Regular: FluentIcon;
export declare const AlignCenterHorizontal48Filled: FluentIcon;
export declare const AlignCenterHorizontal48Regular: FluentIcon;
export declare const AlignCenterVertical16Filled: FluentIcon;
export declare const AlignCenterVertical16Regular: FluentIcon;
export declare const AlignCenterVertical20Filled: FluentIcon;
export declare const AlignCenterVertical20Regular: FluentIcon;
export declare const AlignCenterVertical24Filled: FluentIcon;
export declare const AlignCenterVertical24Regular: FluentIcon;
export declare const AlignCenterVertical28Filled: FluentIcon;
export declare const AlignCenterVertical28Regular: FluentIcon;
export declare const AlignCenterVertical32Filled: FluentIcon;
export declare const AlignCenterVertical32Regular: FluentIcon;
export declare const AlignCenterVertical48Filled: FluentIcon;
export declare const AlignCenterVertical48Regular: FluentIcon;
export declare const AlignDistributeBottom16Filled: FluentIcon;
export declare const AlignDistributeBottom16Regular: FluentIcon;
export declare const AlignDistributeLeft16Filled: FluentIcon;
export declare const AlignDistributeLeft16Regular: FluentIcon;
export declare const AlignDistributeRight16Filled: FluentIcon;
export declare const AlignDistributeRight16Regular: FluentIcon;
export declare const AlignDistributeTop16Filled: FluentIcon;
export declare const AlignDistributeTop16Regular: FluentIcon;
export declare const AlignEndHorizontal20Filled: FluentIcon;
export declare const AlignEndHorizontal20Regular: FluentIcon;
export declare const AlignEndVertical20Filled: FluentIcon;
export declare const AlignEndVertical20Regular: FluentIcon;
export declare const AlignLeft16Filled: FluentIcon;
export declare const AlignLeft16Regular: FluentIcon;
export declare const AlignLeft20Filled: FluentIcon;
export declare const AlignLeft20Regular: FluentIcon;
export declare const AlignLeft24Filled: FluentIcon;
export declare const AlignLeft24Regular: FluentIcon;
export declare const AlignLeft28Filled: FluentIcon;
export declare const AlignLeft28Regular: FluentIcon;
export declare const AlignLeft32Filled: FluentIcon;
export declare const AlignLeft32Regular: FluentIcon;
export declare const AlignLeft48Filled: FluentIcon;
export declare const AlignLeft48Regular: FluentIcon;
export declare const AlignRight16Filled: FluentIcon;
export declare const AlignRight16Regular: FluentIcon;
export declare const AlignRight20Filled: FluentIcon;
export declare const AlignRight20Regular: FluentIcon;
export declare const AlignRight24Filled: FluentIcon;
export declare const AlignRight24Regular: FluentIcon;
export declare const AlignRight28Filled: FluentIcon;
export declare const AlignRight28Regular: FluentIcon;
export declare const AlignRight32Filled: FluentIcon;
export declare const AlignRight32Regular: FluentIcon;
export declare const AlignRight48Filled: FluentIcon;
export declare const AlignRight48Regular: FluentIcon;
export declare const AlignSpaceAroundHorizontal20Filled: FluentIcon;
export declare const AlignSpaceAroundHorizontal20Regular: FluentIcon;
export declare const AlignSpaceAroundVertical20Filled: FluentIcon;
export declare const AlignSpaceAroundVertical20Regular: FluentIcon;
export declare const AlignSpaceBetweenHorizontal20Filled: FluentIcon;
export declare const AlignSpaceBetweenHorizontal20Regular: FluentIcon;
export declare const AlignSpaceBetweenVertical20Filled: FluentIcon;
export declare const AlignSpaceBetweenVertical20Regular: FluentIcon;
export declare const AlignSpaceEvenlyHorizontal20Filled: FluentIcon;
export declare const AlignSpaceEvenlyHorizontal20Regular: FluentIcon;
export declare const AlignSpaceEvenlyHorizontal24Filled: FluentIcon;
export declare const AlignSpaceEvenlyHorizontal24Regular: FluentIcon;
export declare const AlignSpaceEvenlyVertical20Filled: FluentIcon;
export declare const AlignSpaceEvenlyVertical20Regular: FluentIcon;
export declare const AlignSpaceEvenlyVertical24Filled: FluentIcon;
export declare const AlignSpaceEvenlyVertical24Regular: FluentIcon;
export declare const AlignSpaceFitVertical20Filled: FluentIcon;
export declare const AlignSpaceFitVertical20Regular: FluentIcon;
export declare const AlignStartHorizontal20Filled: FluentIcon;
export declare const AlignStartHorizontal20Regular: FluentIcon;
export declare const AlignStartVertical20Filled: FluentIcon;
export declare const AlignStartVertical20Regular: FluentIcon;
export declare const AlignStraighten20Filled: FluentIcon;
export declare const AlignStraighten20Regular: FluentIcon;
export declare const AlignStraighten24Filled: FluentIcon;
export declare const AlignStraighten24Regular: FluentIcon;
export declare const AlignStretchHorizontal16Filled: FluentIcon;
export declare const AlignStretchHorizontal16Regular: FluentIcon;
export declare const AlignStretchHorizontal20Filled: FluentIcon;
export declare const AlignStretchHorizontal20Regular: FluentIcon;
export declare const AlignStretchVertical16Filled: FluentIcon;
export declare const AlignStretchVertical16Regular: FluentIcon;
export declare const AlignStretchVertical20Filled: FluentIcon;
export declare const AlignStretchVertical20Regular: FluentIcon;
export declare const AlignTop16Filled: FluentIcon;
export declare const AlignTop16Regular: FluentIcon;
export declare const AlignTop20Filled: FluentIcon;
export declare const AlignTop20Regular: FluentIcon;
export declare const AlignTop24Filled: FluentIcon;
export declare const AlignTop24Regular: FluentIcon;
export declare const AlignTop28Filled: FluentIcon;
export declare const AlignTop28Regular: FluentIcon;
export declare const AlignTop32Filled: FluentIcon;
export declare const AlignTop32Regular: FluentIcon;
export declare const AlignTop48Filled: FluentIcon;
export declare const AlignTop48Regular: FluentIcon;
export declare const AnimalCat16Filled: FluentIcon;
export declare const AnimalCat16Regular: FluentIcon;
export declare const AnimalCat20Filled: FluentIcon;
export declare const AnimalCat20Regular: FluentIcon;
export declare const AnimalCat24Filled: FluentIcon;
export declare const AnimalCat24Regular: FluentIcon;
export declare const AnimalCat28Filled: FluentIcon;
export declare const AnimalCat28Regular: FluentIcon;
export declare const AnimalDog16Filled: FluentIcon;
export declare const AnimalDog16Regular: FluentIcon;
export declare const AnimalDog20Filled: FluentIcon;
export declare const AnimalDog20Regular: FluentIcon;
export declare const AnimalDog24Filled: FluentIcon;
export declare const AnimalDog24Regular: FluentIcon;
export declare const AnimalPawPrint16Color: FluentIcon;
export declare const AnimalPawPrint16Filled: FluentIcon;
export declare const AnimalPawPrint16Regular: FluentIcon;
export declare const AnimalPawPrint20Color: FluentIcon;
export declare const AnimalPawPrint20Filled: FluentIcon;
export declare const AnimalPawPrint20Regular: FluentIcon;
export declare const AnimalPawPrint24Color: FluentIcon;
export declare const AnimalPawPrint24Filled: FluentIcon;
export declare const AnimalPawPrint24Regular: FluentIcon;
export declare const AnimalPawPrint28Color: FluentIcon;
export declare const AnimalPawPrint28Filled: FluentIcon;
export declare const AnimalPawPrint28Regular: FluentIcon;
export declare const AnimalPawPrint32Color: FluentIcon;
export declare const AnimalPawPrint32Filled: FluentIcon;
export declare const AnimalPawPrint32Regular: FluentIcon;
export declare const AnimalPawPrint48Color: FluentIcon;
export declare const AnimalPawPrint48Filled: FluentIcon;
export declare const AnimalPawPrint48Regular: FluentIcon;
export declare const AnimalRabbit16Filled: FluentIcon;
export declare const AnimalRabbit16Regular: FluentIcon;
export declare const AnimalRabbit20Filled: FluentIcon;
export declare const AnimalRabbit20Regular: FluentIcon;
export declare const AnimalRabbit24Filled: FluentIcon;
export declare const AnimalRabbit24Regular: FluentIcon;
export declare const AnimalRabbit28Filled: FluentIcon;
export declare const AnimalRabbit28Regular: FluentIcon;
export declare const AnimalRabbit32Filled: FluentIcon;
export declare const AnimalRabbit32Regular: FluentIcon;
export declare const AnimalRabbitOff20Filled: FluentIcon;
export declare const AnimalRabbitOff20Regular: FluentIcon;
export declare const AnimalRabbitOff32Filled: FluentIcon;
export declare const AnimalRabbitOff32Regular: FluentIcon;
export declare const AnimalTurtle16Filled: FluentIcon;
export declare const AnimalTurtle16Regular: FluentIcon;
export declare const AnimalTurtle20Filled: FluentIcon;
export declare const AnimalTurtle20Regular: FluentIcon;
export declare const AnimalTurtle24Filled: FluentIcon;
export declare const AnimalTurtle24Regular: FluentIcon;
export declare const AnimalTurtle28Filled: FluentIcon;
export declare const AnimalTurtle28Regular: FluentIcon;
export declare const AppFolder16Filled: FluentIcon;
export declare const AppFolder16Regular: FluentIcon;
export declare const AppFolder20Filled: FluentIcon;
export declare const AppFolder20Regular: FluentIcon;
export declare const AppFolder24Filled: FluentIcon;
export declare const AppFolder24Regular: FluentIcon;
export declare const AppFolder28Filled: FluentIcon;
export declare const AppFolder28Regular: FluentIcon;
export declare const AppFolder32Filled: FluentIcon;
export declare const AppFolder32Light: FluentIcon;
export declare const AppFolder32Regular: FluentIcon;
export declare const AppFolder48Filled: FluentIcon;
export declare const AppFolder48Regular: FluentIcon;
export declare const AppGeneric20Filled: FluentIcon;
export declare const AppGeneric20Regular: FluentIcon;
export declare const AppGeneric24Filled: FluentIcon;
export declare const AppGeneric24Regular: FluentIcon;
export declare const AppGeneric32Filled: FluentIcon;
export declare const AppGeneric32Light: FluentIcon;
export declare const AppGeneric32Regular: FluentIcon;
export declare const AppGeneric48Filled: FluentIcon;
export declare const AppGeneric48Regular: FluentIcon;
export declare const AppRecent20Filled: FluentIcon;
export declare const AppRecent20Regular: FluentIcon;
export declare const AppRecent24Filled: FluentIcon;
export declare const AppRecent24Regular: FluentIcon;
export declare const AppStore24Filled: FluentIcon;
export declare const AppStore24Regular: FluentIcon;
export declare const AppTitle20Filled: FluentIcon;
export declare const AppTitle20Regular: FluentIcon;
export declare const AppTitle24Filled: FluentIcon;
export declare const AppTitle24Regular: FluentIcon;
export declare const ApprovalsApp16Color: FluentIcon;
export declare const ApprovalsApp16Filled: FluentIcon;
export declare const ApprovalsApp16Regular: FluentIcon;
export declare const ApprovalsApp20Color: FluentIcon;
export declare const ApprovalsApp20Filled: FluentIcon;
export declare const ApprovalsApp20Regular: FluentIcon;
export declare const ApprovalsApp24Color: FluentIcon;
export declare const ApprovalsApp24Filled: FluentIcon;
export declare const ApprovalsApp24Regular: FluentIcon;
export declare const ApprovalsApp28Color: FluentIcon;
export declare const ApprovalsApp28Filled: FluentIcon;
export declare const ApprovalsApp28Regular: FluentIcon;
export declare const ApprovalsApp32Color: FluentIcon;
export declare const ApprovalsApp32Filled: FluentIcon;
export declare const ApprovalsApp32Regular: FluentIcon;
export declare const ApprovalsApp48Filled: FluentIcon;
export declare const ApprovalsApp48Regular: FluentIcon;
export declare const Apps16Color: FluentIcon;
export declare const Apps16Filled: FluentIcon;
export declare const Apps16Regular: FluentIcon;
export declare const Apps20Color: FluentIcon;
export declare const Apps20Filled: FluentIcon;
export declare const Apps20Regular: FluentIcon;
export declare const Apps24Color: FluentIcon;
export declare const Apps24Filled: FluentIcon;
export declare const Apps24Regular: FluentIcon;
export declare const Apps28Color: FluentIcon;
export declare const Apps28Filled: FluentIcon;
export declare const Apps28Regular: FluentIcon;
export declare const Apps32Color: FluentIcon;
export declare const Apps32Filled: FluentIcon;
export declare const Apps32Regular: FluentIcon;
export declare const Apps48Color: FluentIcon;
export declare const Apps48Filled: FluentIcon;
export declare const Apps48Regular: FluentIcon;
export declare const AppsAddIn16Filled: FluentIcon;
export declare const AppsAddIn16Regular: FluentIcon;
export declare const AppsAddIn20Filled: FluentIcon;
export declare const AppsAddIn20Regular: FluentIcon;
export declare const AppsAddIn24Filled: FluentIcon;
export declare const AppsAddIn24Regular: FluentIcon;
export declare const AppsAddIn28Filled: FluentIcon;
export declare const AppsAddIn28Regular: FluentIcon;
export declare const AppsAddIn32Filled: FluentIcon;
export declare const AppsAddIn32Regular: FluentIcon;
export declare const AppsAddIn48Filled: FluentIcon;
export declare const AppsAddIn48Regular: FluentIcon;
export declare const AppsAddInOff16Filled: FluentIcon;
export declare const AppsAddInOff16Regular: FluentIcon;
export declare const AppsAddInOff20Filled: FluentIcon;
export declare const AppsAddInOff20Regular: FluentIcon;
export declare const AppsAddInOff24Filled: FluentIcon;
export declare const AppsAddInOff24Regular: FluentIcon;
export declare const AppsAddInOff28Filled: FluentIcon;
export declare const AppsAddInOff28Regular: FluentIcon;
export declare const AppsAddInOff32Filled: FluentIcon;
export declare const AppsAddInOff32Regular: FluentIcon;
export declare const AppsAddInOff48Filled: FluentIcon;
export declare const AppsAddInOff48Regular: FluentIcon;
export declare const AppsList20Color: FluentIcon;
export declare const AppsList20Filled: FluentIcon;
export declare const AppsList20Regular: FluentIcon;
export declare const AppsList24Color: FluentIcon;
export declare const AppsList24Filled: FluentIcon;
export declare const AppsList24Regular: FluentIcon;
export declare const AppsList32Color: FluentIcon;
export declare const AppsList32Filled: FluentIcon;
export declare const AppsList32Regular: FluentIcon;
export declare const AppsListDetail20Color: FluentIcon;
export declare const AppsListDetail20Filled: FluentIcon;
export declare const AppsListDetail20Regular: FluentIcon;
export declare const AppsListDetail24Color: FluentIcon;
export declare const AppsListDetail24Filled: FluentIcon;
export declare const AppsListDetail24Regular: FluentIcon;
export declare const AppsListDetail32Color: FluentIcon;
export declare const AppsListDetail32Filled: FluentIcon;
export declare const AppsListDetail32Regular: FluentIcon;
export declare const AppsSettings16Filled: FluentIcon;
export declare const AppsSettings16Regular: FluentIcon;
export declare const AppsSettings20Filled: FluentIcon;
export declare const AppsSettings20Regular: FluentIcon;
export declare const AppsShield16Filled: FluentIcon;
export declare const AppsShield16Regular: FluentIcon;
export declare const AppsShield20Filled: FluentIcon;
export declare const AppsShield20Regular: FluentIcon;
export declare const Archive16Filled: FluentIcon;
export declare const Archive16Regular: FluentIcon;
export declare const Archive20Filled: FluentIcon;
export declare const Archive20Regular: FluentIcon;
export declare const Archive24Filled: FluentIcon;
export declare const Archive24Regular: FluentIcon;
export declare const Archive28Filled: FluentIcon;
export declare const Archive28Regular: FluentIcon;
export declare const Archive32Filled: FluentIcon;
export declare const Archive32Light: FluentIcon;
export declare const Archive32Regular: FluentIcon;
export declare const Archive48Filled: FluentIcon;
export declare const Archive48Regular: FluentIcon;
export declare const ArchiveArrowBack16Filled: FluentIcon;
export declare const ArchiveArrowBack16Regular: FluentIcon;
export declare const ArchiveArrowBack20Filled: FluentIcon;
export declare const ArchiveArrowBack20Regular: FluentIcon;
export declare const ArchiveArrowBack24Filled: FluentIcon;
export declare const ArchiveArrowBack24Regular: FluentIcon;
export declare const ArchiveArrowBack28Filled: FluentIcon;
export declare const ArchiveArrowBack28Regular: FluentIcon;
export declare const ArchiveArrowBack32Filled: FluentIcon;
export declare const ArchiveArrowBack32Regular: FluentIcon;
export declare const ArchiveArrowBack48Filled: FluentIcon;
export declare const ArchiveArrowBack48Regular: FluentIcon;
export declare const ArchiveMultiple16Filled: FluentIcon;
export declare const ArchiveMultiple16Regular: FluentIcon;
export declare const ArchiveMultiple20Filled: FluentIcon;
export declare const ArchiveMultiple20Regular: FluentIcon;
export declare const ArchiveMultiple24Filled: FluentIcon;
export declare const ArchiveMultiple24Regular: FluentIcon;
export declare const ArchiveSettings16Filled: FluentIcon;
export declare const ArchiveSettings16Regular: FluentIcon;
export declare const ArchiveSettings20Filled: FluentIcon;
export declare const ArchiveSettings20Regular: FluentIcon;
export declare const ArchiveSettings24Filled: FluentIcon;
export declare const ArchiveSettings24Regular: FluentIcon;
export declare const ArchiveSettings28Filled: FluentIcon;
export declare const ArchiveSettings28Regular: FluentIcon;
export declare const ArchiveSettings32Filled: FluentIcon;
export declare const ArchiveSettings32Light: FluentIcon;
export declare const ArchiveSettings32Regular: FluentIcon;
export declare const ArrowAutofitContent20Filled: FluentIcon;
export declare const ArrowAutofitContent20Regular: FluentIcon;
export declare const ArrowAutofitContent24Filled: FluentIcon;
export declare const ArrowAutofitContent24Regular: FluentIcon;
export declare const ArrowAutofitDown20Filled: FluentIcon;
export declare const ArrowAutofitDown20Regular: FluentIcon;
export declare const ArrowAutofitDown24Filled: FluentIcon;
export declare const ArrowAutofitDown24Regular: FluentIcon;
export declare const ArrowAutofitHeight20Filled: FluentIcon;
export declare const ArrowAutofitHeight20Regular: FluentIcon;
export declare const ArrowAutofitHeight24Filled: FluentIcon;
export declare const ArrowAutofitHeight24Regular: FluentIcon;
export declare const ArrowAutofitHeightDotted20Filled: FluentIcon;
export declare const ArrowAutofitHeightDotted20Regular: FluentIcon;
export declare const ArrowAutofitHeightDotted24Filled: FluentIcon;
export declare const ArrowAutofitHeightDotted24Regular: FluentIcon;
export declare const ArrowAutofitHeightIn20Filled: FluentIcon;
export declare const ArrowAutofitHeightIn20Regular: FluentIcon;
export declare const ArrowAutofitHeightIn24Filled: FluentIcon;
export declare const ArrowAutofitHeightIn24Regular: FluentIcon;
export declare const ArrowAutofitUp20Filled: FluentIcon;
export declare const ArrowAutofitUp20Regular: FluentIcon;
export declare const ArrowAutofitUp24Filled: FluentIcon;
export declare const ArrowAutofitUp24Regular: FluentIcon;
export declare const ArrowAutofitWidth20Filled: FluentIcon;
export declare const ArrowAutofitWidth20Regular: FluentIcon;
export declare const ArrowAutofitWidth24Filled: FluentIcon;
export declare const ArrowAutofitWidth24Regular: FluentIcon;
export declare const ArrowAutofitWidthDotted20Filled: FluentIcon;
export declare const ArrowAutofitWidthDotted20Regular: FluentIcon;
export declare const ArrowAutofitWidthDotted24Filled: FluentIcon;
export declare const ArrowAutofitWidthDotted24Regular: FluentIcon;
export declare const ArrowBetweenDown20Filled: FluentIcon;
export declare const ArrowBetweenDown20Regular: FluentIcon;
export declare const ArrowBetweenDown24Filled: FluentIcon;
export declare const ArrowBetweenDown24Regular: FluentIcon;
export declare const ArrowBetweenUp20Filled: FluentIcon;
export declare const ArrowBetweenUp20Regular: FluentIcon;
export declare const ArrowBidirectionalLeftRight16Filled: FluentIcon;
export declare const ArrowBidirectionalLeftRight16Regular: FluentIcon;
export declare const ArrowBidirectionalLeftRight20Filled: FluentIcon;
export declare const ArrowBidirectionalLeftRight20Regular: FluentIcon;
export declare const ArrowBidirectionalLeftRight24Filled: FluentIcon;
export declare const ArrowBidirectionalLeftRight24Regular: FluentIcon;
export declare const ArrowBidirectionalLeftRight28Filled: FluentIcon;
export declare const ArrowBidirectionalLeftRight28Regular: FluentIcon;
export declare const ArrowBidirectionalUpDown12Filled: FluentIcon;
export declare const ArrowBidirectionalUpDown12Regular: FluentIcon;
export declare const ArrowBidirectionalUpDown16Filled: FluentIcon;
export declare const ArrowBidirectionalUpDown16Regular: FluentIcon;
export declare const ArrowBidirectionalUpDown20Filled: FluentIcon;
export declare const ArrowBidirectionalUpDown20Regular: FluentIcon;
export declare const ArrowBidirectionalUpDown24Filled: FluentIcon;
export declare const ArrowBidirectionalUpDown24Regular: FluentIcon;
export declare const ArrowBounce12Filled: FluentIcon;
export declare const ArrowBounce12Regular: FluentIcon;
export declare const ArrowBounce16Filled: FluentIcon;
export declare const ArrowBounce16Regular: FluentIcon;
export declare const ArrowBounce20Filled: FluentIcon;
export declare const ArrowBounce20Regular: FluentIcon;
export declare const ArrowBounce24Filled: FluentIcon;
export declare const ArrowBounce24Regular: FluentIcon;
export declare const ArrowBounce28Filled: FluentIcon;
export declare const ArrowBounce28Regular: FluentIcon;
export declare const ArrowBounce48Filled: FluentIcon;
export declare const ArrowBounce48Regular: FluentIcon;
export declare const ArrowCircleDown12Filled: FluentIcon;
export declare const ArrowCircleDown12Regular: FluentIcon;
export declare const ArrowCircleDown16Filled: FluentIcon;
export declare const ArrowCircleDown16Regular: FluentIcon;
export declare const ArrowCircleDown20Filled: FluentIcon;
export declare const ArrowCircleDown20Regular: FluentIcon;
export declare const ArrowCircleDown24Filled: FluentIcon;
export declare const ArrowCircleDown24Regular: FluentIcon;
export declare const ArrowCircleDown28Filled: FluentIcon;
export declare const ArrowCircleDown28Regular: FluentIcon;
export declare const ArrowCircleDown32Filled: FluentIcon;
export declare const ArrowCircleDown32Regular: FluentIcon;
export declare const ArrowCircleDown48Filled: FluentIcon;
export declare const ArrowCircleDown48Regular: FluentIcon;
export declare const ArrowCircleDownDouble20Filled: FluentIcon;
export declare const ArrowCircleDownDouble20Regular: FluentIcon;
export declare const ArrowCircleDownDouble24Filled: FluentIcon;
export declare const ArrowCircleDownDouble24Regular: FluentIcon;
export declare const ArrowCircleDownRight12Filled: FluentIcon;
export declare const ArrowCircleDownRight12Regular: FluentIcon;
export declare const ArrowCircleDownRight16Filled: FluentIcon;
export declare const ArrowCircleDownRight16Regular: FluentIcon;
export declare const ArrowCircleDownRight20Filled: FluentIcon;
export declare const ArrowCircleDownRight20Regular: FluentIcon;
export declare const ArrowCircleDownRight24Filled: FluentIcon;
export declare const ArrowCircleDownRight24Regular: FluentIcon;
export declare const ArrowCircleDownSplit20Filled: FluentIcon;
export declare const ArrowCircleDownSplit20Regular: FluentIcon;
export declare const ArrowCircleDownSplit24Filled: FluentIcon;
export declare const ArrowCircleDownSplit24Regular: FluentIcon;
export declare const ArrowCircleDownUp20Filled: FluentIcon;
export declare const ArrowCircleDownUp20Regular: FluentIcon;
export declare const ArrowCircleLeft12Filled: FluentIcon;
export declare const ArrowCircleLeft12Regular: FluentIcon;
export declare const ArrowCircleLeft16Filled: FluentIcon;
export declare const ArrowCircleLeft16Regular: FluentIcon;
export declare const ArrowCircleLeft20Filled: FluentIcon;
export declare const ArrowCircleLeft20Regular: FluentIcon;
export declare const ArrowCircleLeft24Filled: FluentIcon;
export declare const ArrowCircleLeft24Regular: FluentIcon;
export declare const ArrowCircleLeft28Filled: FluentIcon;
export declare const ArrowCircleLeft28Regular: FluentIcon;
export declare const ArrowCircleLeft32Filled: FluentIcon;
export declare const ArrowCircleLeft32Regular: FluentIcon;
export declare const ArrowCircleLeft48Filled: FluentIcon;
export declare const ArrowCircleLeft48Regular: FluentIcon;
export declare const ArrowCircleRight12Filled: FluentIcon;
export declare const ArrowCircleRight12Regular: FluentIcon;
export declare const ArrowCircleRight16Filled: FluentIcon;
export declare const ArrowCircleRight16Regular: FluentIcon;
export declare const ArrowCircleRight20Filled: FluentIcon;
export declare const ArrowCircleRight20Regular: FluentIcon;
export declare const ArrowCircleRight24Filled: FluentIcon;
export declare const ArrowCircleRight24Regular: FluentIcon;
export declare const ArrowCircleRight28Filled: FluentIcon;
export declare const ArrowCircleRight28Regular: FluentIcon;
export declare const ArrowCircleRight32Filled: FluentIcon;
export declare const ArrowCircleRight32Regular: FluentIcon;
export declare const ArrowCircleRight48Filled: FluentIcon;
export declare const ArrowCircleRight48Regular: FluentIcon;
export declare const ArrowCircleUp12Filled: FluentIcon;
export declare const ArrowCircleUp12Regular: FluentIcon;
export declare const ArrowCircleUp16Filled: FluentIcon;
export declare const ArrowCircleUp16Regular: FluentIcon;
export declare const ArrowCircleUp20Filled: FluentIcon;
export declare const ArrowCircleUp20Regular: FluentIcon;
export declare const ArrowCircleUp24Filled: FluentIcon;
export declare const ArrowCircleUp24Regular: FluentIcon;
export declare const ArrowCircleUp28Filled: FluentIcon;
export declare const ArrowCircleUp28Regular: FluentIcon;
export declare const ArrowCircleUp32Filled: FluentIcon;
export declare const ArrowCircleUp32Regular: FluentIcon;
export declare const ArrowCircleUp48Filled: FluentIcon;
export declare const ArrowCircleUp48Regular: FluentIcon;
export declare const ArrowCircleUpLeft16Filled: FluentIcon;
export declare const ArrowCircleUpLeft16Regular: FluentIcon;
export declare const ArrowCircleUpLeft20Filled: FluentIcon;
export declare const ArrowCircleUpLeft20Regular: FluentIcon;
export declare const ArrowCircleUpLeft24Filled: FluentIcon;
export declare const ArrowCircleUpLeft24Regular: FluentIcon;
export declare const ArrowCircleUpRight16Filled: FluentIcon;
export declare const ArrowCircleUpRight16Regular: FluentIcon;
export declare const ArrowCircleUpRight20Filled: FluentIcon;
export declare const ArrowCircleUpRight20Regular: FluentIcon;
export declare const ArrowCircleUpRight24Filled: FluentIcon;
export declare const ArrowCircleUpRight24Regular: FluentIcon;
export declare const ArrowCircleUpSparkle20Filled: FluentIcon;
export declare const ArrowCircleUpSparkle20Regular: FluentIcon;
export declare const ArrowCircleUpSparkle24Filled: FluentIcon;
export declare const ArrowCircleUpSparkle24Regular: FluentIcon;
export declare const ArrowClockwise12Filled: FluentIcon;
export declare const ArrowClockwise12Regular: FluentIcon;
export declare const ArrowClockwise16Filled: FluentIcon;
export declare const ArrowClockwise16Regular: FluentIcon;
export declare const ArrowClockwise20Filled: FluentIcon;
export declare const ArrowClockwise20Regular: FluentIcon;
export declare const ArrowClockwise24Filled: FluentIcon;
export declare const ArrowClockwise24Regular: FluentIcon;
export declare const ArrowClockwise28Filled: FluentIcon;
export declare const ArrowClockwise28Regular: FluentIcon;
export declare const ArrowClockwise32Filled: FluentIcon;
export declare const ArrowClockwise32Light: FluentIcon;
export declare const ArrowClockwise32Regular: FluentIcon;
export declare const ArrowClockwise48Filled: FluentIcon;
export declare const ArrowClockwise48Regular: FluentIcon;
export declare const ArrowClockwiseDashes12Filled: FluentIcon;
export declare const ArrowClockwiseDashes12Regular: FluentIcon;
export declare const ArrowClockwiseDashes16Color: FluentIcon;
export declare const ArrowClockwiseDashes16Filled: FluentIcon;
export declare const ArrowClockwiseDashes16Regular: FluentIcon;
export declare const ArrowClockwiseDashes20Color: FluentIcon;
export declare const ArrowClockwiseDashes20Filled: FluentIcon;
export declare const ArrowClockwiseDashes20Regular: FluentIcon;
export declare const ArrowClockwiseDashes24Color: FluentIcon;
export declare const ArrowClockwiseDashes24Filled: FluentIcon;
export declare const ArrowClockwiseDashes24Regular: FluentIcon;
export declare const ArrowClockwiseDashes28Filled: FluentIcon;
export declare const ArrowClockwiseDashes28Regular: FluentIcon;
export declare const ArrowClockwiseDashes32Color: FluentIcon;
export declare const ArrowClockwiseDashes32Filled: FluentIcon;
export declare const ArrowClockwiseDashes32Regular: FluentIcon;
export declare const ArrowClockwiseDashes48Filled: FluentIcon;
export declare const ArrowClockwiseDashes48Regular: FluentIcon;
export declare const ArrowClockwiseDashesSettings16Color: FluentIcon;
export declare const ArrowClockwiseDashesSettings16Filled: FluentIcon;
export declare const ArrowClockwiseDashesSettings16Regular: FluentIcon;
export declare const ArrowClockwiseDashesSettings20Color: FluentIcon;
export declare const ArrowClockwiseDashesSettings20Filled: FluentIcon;
export declare const ArrowClockwiseDashesSettings20Regular: FluentIcon;
export declare const ArrowClockwiseDashesSettings24Color: FluentIcon;
export declare const ArrowClockwiseDashesSettings24Filled: FluentIcon;
export declare const ArrowClockwiseDashesSettings24Regular: FluentIcon;
export declare const ArrowClockwiseDashesSettings28Color: FluentIcon;
export declare const ArrowClockwiseDashesSettings28Filled: FluentIcon;
export declare const ArrowClockwiseDashesSettings28Regular: FluentIcon;
export declare const ArrowClockwiseDashesSettings32Color: FluentIcon;
export declare const ArrowClockwiseDashesSettings32Filled: FluentIcon;
export declare const ArrowClockwiseDashesSettings32Regular: FluentIcon;
export declare const ArrowClockwiseDashesSettings48Color: FluentIcon;
export declare const ArrowClockwiseDashesSettings48Filled: FluentIcon;
export declare const ArrowClockwiseDashesSettings48Regular: FluentIcon;
export declare const ArrowCollapseAll16Filled: FluentIcon;
export declare const ArrowCollapseAll16Regular: FluentIcon;
export declare const ArrowCollapseAll20Filled: FluentIcon;
export declare const ArrowCollapseAll20Regular: FluentIcon;
export declare const ArrowCollapseAll24Filled: FluentIcon;
export declare const ArrowCollapseAll24Regular: FluentIcon;
export declare const ArrowCounterclockwise12Filled: FluentIcon;
export declare const ArrowCounterclockwise12Regular: FluentIcon;
export declare const ArrowCounterclockwise16Filled: FluentIcon;
export declare const ArrowCounterclockwise16Regular: FluentIcon;
export declare const ArrowCounterclockwise20Filled: FluentIcon;
export declare const ArrowCounterclockwise20Regular: FluentIcon;
export declare const ArrowCounterclockwise24Filled: FluentIcon;
export declare const ArrowCounterclockwise24Regular: FluentIcon;
export declare const ArrowCounterclockwise28Filled: FluentIcon;
export declare const ArrowCounterclockwise28Regular: FluentIcon;
export declare const ArrowCounterclockwise32Filled: FluentIcon;
export declare const ArrowCounterclockwise32Regular: FluentIcon;
export declare const ArrowCounterclockwise48Filled: FluentIcon;
export declare const ArrowCounterclockwise48Regular: FluentIcon;
export declare const ArrowCounterclockwiseDashes20Filled: FluentIcon;
export declare const ArrowCounterclockwiseDashes20Regular: FluentIcon;
export declare const ArrowCounterclockwiseDashes24Filled: FluentIcon;
export declare const ArrowCounterclockwiseDashes24Regular: FluentIcon;
export declare const ArrowCounterclockwiseInfo20Filled: FluentIcon;
export declare const ArrowCounterclockwiseInfo20Regular: FluentIcon;
export declare const ArrowCounterclockwiseInfo24Filled: FluentIcon;
export declare const ArrowCounterclockwiseInfo24Regular: FluentIcon;
export declare const ArrowCounterclockwiseInfo28Filled: FluentIcon;
export declare const ArrowCounterclockwiseInfo28Regular: FluentIcon;
export declare const ArrowCounterclockwiseInfo32Filled: FluentIcon;
export declare const ArrowCounterclockwiseInfo32Regular: FluentIcon;
export declare const ArrowCounterclockwiseInfo48Filled: FluentIcon;
export declare const ArrowCounterclockwiseInfo48Regular: FluentIcon;
export declare const ArrowCurveDownLeft16Filled: FluentIcon;
export declare const ArrowCurveDownLeft16Regular: FluentIcon;
export declare const ArrowCurveDownLeft20Filled: FluentIcon;
export declare const ArrowCurveDownLeft20Regular: FluentIcon;
export declare const ArrowCurveDownLeft24Filled: FluentIcon;
export declare const ArrowCurveDownLeft24Regular: FluentIcon;
export declare const ArrowCurveDownLeft28Filled: FluentIcon;
export declare const ArrowCurveDownLeft28Regular: FluentIcon;
export declare const ArrowCurveDownRight20Filled: FluentIcon;
export declare const ArrowCurveDownRight20Regular: FluentIcon;
export declare const ArrowCurveUpLeft20Filled: FluentIcon;
export declare const ArrowCurveUpLeft20Regular: FluentIcon;
export declare const ArrowCurveUpRight20Filled: FluentIcon;
export declare const ArrowCurveUpRight20Regular: FluentIcon;
export declare const ArrowDown12Filled: FluentIcon;
export declare const ArrowDown12Regular: FluentIcon;
export declare const ArrowDown16Filled: FluentIcon;
export declare const ArrowDown16Regular: FluentIcon;
export declare const ArrowDown20Filled: FluentIcon;
export declare const ArrowDown20Regular: FluentIcon;
export declare const ArrowDown24Filled: FluentIcon;
export declare const ArrowDown24Regular: FluentIcon;
export declare const ArrowDown28Filled: FluentIcon;
export declare const ArrowDown28Regular: FluentIcon;
export declare const ArrowDown32Filled: FluentIcon;
export declare const ArrowDown32Light: FluentIcon;
export declare const ArrowDown32Regular: FluentIcon;
export declare const ArrowDown48Filled: FluentIcon;
export declare const ArrowDown48Regular: FluentIcon;
export declare const ArrowDownExclamation16Filled: FluentIcon;
export declare const ArrowDownExclamation16Regular: FluentIcon;
export declare const ArrowDownExclamation20Filled: FluentIcon;
export declare const ArrowDownExclamation20Regular: FluentIcon;
export declare const ArrowDownExclamation24Filled: FluentIcon;
export declare const ArrowDownExclamation24Regular: FluentIcon;
export declare const ArrowDownLeft12Filled: FluentIcon;
export declare const ArrowDownLeft12Regular: FluentIcon;
export declare const ArrowDownLeft16Filled: FluentIcon;
export declare const ArrowDownLeft16Regular: FluentIcon;
export declare const ArrowDownLeft20Filled: FluentIcon;
export declare const ArrowDownLeft20Regular: FluentIcon;
export declare const ArrowDownLeft24Filled: FluentIcon;
export declare const ArrowDownLeft24Regular: FluentIcon;
export declare const ArrowDownLeft28Filled: FluentIcon;
export declare const ArrowDownLeft28Regular: FluentIcon;
export declare const ArrowDownLeft32Filled: FluentIcon;
export declare const ArrowDownLeft32Regular: FluentIcon;
export declare const ArrowDownLeft48Filled: FluentIcon;
export declare const ArrowDownLeft48Regular: FluentIcon;
export declare const ArrowDownRight16Filled: FluentIcon;
export declare const ArrowDownRight16Regular: FluentIcon;
export declare const ArrowDownRight20Filled: FluentIcon;
export declare const ArrowDownRight20Regular: FluentIcon;
export declare const ArrowDownRight24Filled: FluentIcon;
export declare const ArrowDownRight24Regular: FluentIcon;
export declare const ArrowDownRight32Filled: FluentIcon;
export declare const ArrowDownRight32Regular: FluentIcon;
export declare const ArrowDownRight48Filled: FluentIcon;
export declare const ArrowDownRight48Regular: FluentIcon;
export declare const ArrowDownload16Filled: FluentIcon;
export declare const ArrowDownload16Regular: FluentIcon;
export declare const ArrowDownload20Filled: FluentIcon;
export declare const ArrowDownload20Regular: FluentIcon;
export declare const ArrowDownload24Filled: FluentIcon;
export declare const ArrowDownload24Regular: FluentIcon;
export declare const ArrowDownload28Filled: FluentIcon;
export declare const ArrowDownload28Regular: FluentIcon;
export declare const ArrowDownload32Filled: FluentIcon;
export declare const ArrowDownload32Light: FluentIcon;
export declare const ArrowDownload32Regular: FluentIcon;
export declare const ArrowDownload48Filled: FluentIcon;
export declare const ArrowDownload48Regular: FluentIcon;
export declare const ArrowDownloadOff16Filled: FluentIcon;
export declare const ArrowDownloadOff16Regular: FluentIcon;
export declare const ArrowDownloadOff20Filled: FluentIcon;
export declare const ArrowDownloadOff20Regular: FluentIcon;
export declare const ArrowDownloadOff24Filled: FluentIcon;
export declare const ArrowDownloadOff24Regular: FluentIcon;
export declare const ArrowDownloadOff28Filled: FluentIcon;
export declare const ArrowDownloadOff28Regular: FluentIcon;
export declare const ArrowDownloadOff32Filled: FluentIcon;
export declare const ArrowDownloadOff32Regular: FluentIcon;
export declare const ArrowDownloadOff48Filled: FluentIcon;
export declare const ArrowDownloadOff48Regular: FluentIcon;
export declare const ArrowEject20Filled: FluentIcon;
export declare const ArrowEject20Regular: FluentIcon;
export declare const ArrowEnter16Filled: FluentIcon;
export declare const ArrowEnter16Regular: FluentIcon;
export declare const ArrowEnter20Filled: FluentIcon;
export declare const ArrowEnter20Regular: FluentIcon;
export declare const ArrowEnterLeft20Filled: FluentIcon;
export declare const ArrowEnterLeft20Regular: FluentIcon;
export declare const ArrowEnterLeft24Filled: FluentIcon;
export declare const ArrowEnterLeft24Regular: FluentIcon;
export declare const ArrowEnterUp20Filled: FluentIcon;
export declare const ArrowEnterUp20Regular: FluentIcon;
export declare const ArrowEnterUp24Filled: FluentIcon;
export declare const ArrowEnterUp24Regular: FluentIcon;
export declare const ArrowExit12Filled: FluentIcon;
export declare const ArrowExit12Regular: FluentIcon;
export declare const ArrowExit16Filled: FluentIcon;
export declare const ArrowExit16Regular: FluentIcon;
export declare const ArrowExit20Filled: FluentIcon;
export declare const ArrowExit20Regular: FluentIcon;
export declare const ArrowExit24Filled: FluentIcon;
export declare const ArrowExit24Regular: FluentIcon;
export declare const ArrowExit28Filled: FluentIcon;
export declare const ArrowExit28Regular: FluentIcon;
export declare const ArrowExit32Filled: FluentIcon;
export declare const ArrowExit32Regular: FluentIcon;
export declare const ArrowExit48Filled: FluentIcon;
export declare const ArrowExit48Regular: FluentIcon;
export declare const ArrowExpand16Filled: FluentIcon;
export declare const ArrowExpand16Regular: FluentIcon;
export declare const ArrowExpand20Filled: FluentIcon;
export declare const ArrowExpand20Regular: FluentIcon;
export declare const ArrowExpand24Filled: FluentIcon;
export declare const ArrowExpand24Regular: FluentIcon;
export declare const ArrowExpand28Filled: FluentIcon;
export declare const ArrowExpand28Regular: FluentIcon;
export declare const ArrowExpandAll16Filled: FluentIcon;
export declare const ArrowExpandAll16Regular: FluentIcon;
export declare const ArrowExpandAll20Filled: FluentIcon;
export declare const ArrowExpandAll20Regular: FluentIcon;
export declare const ArrowExpandAll24Filled: FluentIcon;
export declare const ArrowExpandAll24Regular: FluentIcon;
export declare const ArrowExport16Filled: FluentIcon;
export declare const ArrowExport16Regular: FluentIcon;
export declare const ArrowExport20Filled: FluentIcon;
export declare const ArrowExport20Regular: FluentIcon;
export declare const ArrowExport24Filled: FluentIcon;
export declare const ArrowExport24Regular: FluentIcon;
export declare const ArrowExportLtr16Filled: FluentIcon;
export declare const ArrowExportLtr16Regular: FluentIcon;
export declare const ArrowExportLtr20Filled: FluentIcon;
export declare const ArrowExportLtr20Regular: FluentIcon;
export declare const ArrowExportLtr24Filled: FluentIcon;
export declare const ArrowExportLtr24Regular: FluentIcon;
export declare const ArrowExportRtl16Filled: FluentIcon;
export declare const ArrowExportRtl16Regular: FluentIcon;
export declare const ArrowExportRtl20Filled: FluentIcon;
export declare const ArrowExportRtl20Regular: FluentIcon;
export declare const ArrowExportRtl24Filled: FluentIcon;
export declare const ArrowExportRtl24Regular: FluentIcon;
export declare const ArrowExportUp16Filled: FluentIcon;
export declare const ArrowExportUp16Regular: FluentIcon;
export declare const ArrowExportUp20Filled: FluentIcon;
export declare const ArrowExportUp20Regular: FluentIcon;
export declare const ArrowExportUp24Filled: FluentIcon;
export declare const ArrowExportUp24Regular: FluentIcon;
export declare const ArrowFit16Filled: FluentIcon;
export declare const ArrowFit16Regular: FluentIcon;
export declare const ArrowFit20Filled: FluentIcon;
export declare const ArrowFit20Regular: FluentIcon;
export declare const ArrowFit24Filled: FluentIcon;
export declare const ArrowFit24Regular: FluentIcon;
export declare const ArrowFitIn16Filled: FluentIcon;
export declare const ArrowFitIn16Regular: FluentIcon;
export declare const ArrowFitIn20Filled: FluentIcon;
export declare const ArrowFitIn20Regular: FluentIcon;
export declare const ArrowFitIn24Filled: FluentIcon;
export declare const ArrowFitIn24Regular: FluentIcon;
export declare const ArrowFlowDiagonalUpRight12Filled: FluentIcon;
export declare const ArrowFlowDiagonalUpRight12Regular: FluentIcon;
export declare const ArrowFlowDiagonalUpRight16Filled: FluentIcon;
export declare const ArrowFlowDiagonalUpRight16Regular: FluentIcon;
export declare const ArrowFlowDiagonalUpRight20Filled: FluentIcon;
export declare const ArrowFlowDiagonalUpRight20Regular: FluentIcon;
export declare const ArrowFlowDiagonalUpRight24Filled: FluentIcon;
export declare const ArrowFlowDiagonalUpRight24Regular: FluentIcon;
export declare const ArrowFlowDiagonalUpRight28Filled: FluentIcon;
export declare const ArrowFlowDiagonalUpRight28Regular: FluentIcon;
export declare const ArrowFlowDiagonalUpRight32Filled: FluentIcon;
export declare const ArrowFlowDiagonalUpRight32Regular: FluentIcon;
export declare const ArrowFlowDiagonalUpRight48Filled: FluentIcon;
export declare const ArrowFlowDiagonalUpRight48Regular: FluentIcon;
export declare const ArrowFlowUpRight16Filled: FluentIcon;
export declare const ArrowFlowUpRight16Regular: FluentIcon;
export declare const ArrowFlowUpRight20Filled: FluentIcon;
export declare const ArrowFlowUpRight20Regular: FluentIcon;
export declare const ArrowFlowUpRight24Filled: FluentIcon;
export declare const ArrowFlowUpRight24Regular: FluentIcon;
export declare const ArrowFlowUpRight32Filled: FluentIcon;
export declare const ArrowFlowUpRight32Regular: FluentIcon;
export declare const ArrowFlowUpRightRectangleMultiple20Filled: FluentIcon;
export declare const ArrowFlowUpRightRectangleMultiple20Regular: FluentIcon;
export declare const ArrowFlowUpRightRectangleMultiple24Filled: FluentIcon;
export declare const ArrowFlowUpRightRectangleMultiple24Regular: FluentIcon;
export declare const ArrowForward16Filled: FluentIcon;
