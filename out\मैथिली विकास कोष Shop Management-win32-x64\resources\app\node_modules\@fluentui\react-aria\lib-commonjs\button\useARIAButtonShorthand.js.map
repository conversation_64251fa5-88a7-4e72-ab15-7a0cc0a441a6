{"version": 3, "sources": ["../src/button/useARIAButtonShorthand.ts"], "sourcesContent": ["import { resolveShorthand } from '@fluentui/react-utilities';\nimport { useARIAButtonProps } from './useARIAButtonProps';\nimport type { ResolveShorthandFunction } from '@fluentui/react-utilities';\nimport type { ARIAButtonProps, ARIAButtonSlotProps, ARIAButtonType } from './types';\n\n/**\n * @internal\n *\n * @deprecated use useARIAButtonProps instead\n *\n * This function expects to receive a slot, if `as` property is not desired use `useARIAButtonProps` instead\n *\n * Button keyboard handling, role, disabled and tabIndex implementation that ensures ARIA spec\n * for multiple scenarios of shorthand properties. Ensuring 1st rule of ARIA for cases\n * where no attribute addition is required.\n */\nexport const useARIAButtonShorthand = ((value, options) => {\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n  const shorthand = resolveShorthand(value, options);\n  const shorthandARIAButton = useARIAButtonProps<ARIAButtonType, ARIAButtonProps>(shorthand?.as ?? 'button', shorthand);\n  return shorthand && shorthandARIAButton;\n  // eslint-disable-next-line @typescript-eslint/no-deprecated\n}) as ResolveShorthandFunction<ARIAButtonSlotProps>;\n"], "names": ["useARIAButtonShorthand", "value", "options", "shorthand", "resolve<PERSON><PERSON><PERSON>d", "shorthandARIAButton", "useARIAButtonProps", "as"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAgBaA;;;eAAAA;;;gCAhBoB;oCACE;AAe5B,MAAMA,yBAA0B,CAACC,OAAOC;IAC7C,4DAA4D;IAC5D,MAAMC,YAAYC,IAAAA,gCAAgB,EAACH,OAAOC;QACsCC;IAAhF,MAAME,sBAAsBC,IAAAA,sCAAkB,EAAkCH,CAAAA,gBAAAA,sBAAAA,gCAAAA,UAAWI,EAAE,cAAbJ,2BAAAA,gBAAiB,UAAUA;IAC3G,OAAOA,aAAaE;AACpB,4DAA4D;AAC9D"}