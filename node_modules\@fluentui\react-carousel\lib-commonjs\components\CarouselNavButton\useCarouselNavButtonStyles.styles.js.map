{"version": 3, "sources": ["useCarouselNavButtonStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses, shorthands } from '@griffel/react';\nimport { createCustomFocusIndicatorStyle } from '@fluentui/react-tabster';\nimport { tokens } from '@fluentui/react-theme';\nexport const carouselNavButtonClassNames = {\n    root: 'fui-CarouselNavButton'\n};\n/**\n * Styles for the root slot\n */ const useStyles = makeStyles({\n    root: {\n        cursor: 'pointer',\n        pointerEvents: 'all',\n        width: tokens.spacingHorizontalS,\n        height: tokens.spacingVerticalS,\n        padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalS}`,\n        boxSizing: 'content-box',\n        backgroundColor: tokens.colorTransparentBackground,\n        ...shorthands.borderWidth(0),\n        '::after': {\n            content: '\"\"',\n            display: 'block',\n            boxSizing: 'border-box',\n            borderRadius: '50%',\n            border: 'none',\n            height: tokens.spacingVerticalS,\n            width: tokens.spacingHorizontalS,\n            backgroundColor: tokens.colorNeutralForeground1,\n            color: tokens.colorNeutralForeground1,\n            '@media (forced-colors: active)': {\n                // Bypass OS high contrast with inverted blend mode (otherwise icon is invisible)\n                forcedColorAdjust: 'none',\n                backgroundColor: 'white',\n                mixBlendMode: 'difference'\n            }\n        }\n    },\n    rootUnselected: {\n        outline: `${tokens.strokeWidthThin} solid transparent`,\n        ...createCustomFocusIndicatorStyle({\n            border: `${tokens.strokeWidthThick} solid ${tokens.colorStrokeFocus2}`,\n            margin: `calc(-1 * ${tokens.strokeWidthThick})`,\n            borderRadius: tokens.borderRadiusMedium\n        }),\n        '::after': {\n            opacity: 0.6\n        },\n        ':hover': {\n            '::after': {\n                opacity: 0.75\n            }\n        },\n        ':active': {\n            '::after': {\n                opacity: 1\n            }\n        }\n    },\n    rootSelected: {\n        width: tokens.spacingHorizontalL,\n        padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalXS}`,\n        outline: `${tokens.strokeWidthThin} solid transparent`,\n        ...createCustomFocusIndicatorStyle({\n            border: `${tokens.strokeWidthThick} solid ${tokens.colorStrokeFocus2}`,\n            margin: `calc(-1 * ${tokens.strokeWidthThick})`,\n            borderRadius: tokens.borderRadiusMedium\n        }),\n        '::after': {\n            width: tokens.spacingHorizontalL,\n            borderRadius: '4px'\n        },\n        ':hover': {\n            '::after': {\n                opacity: 0.75\n            }\n        },\n        ':active': {\n            '::after': {\n                opacity: 0.65\n            }\n        }\n    },\n    brand: {\n        '::after': {\n            backgroundColor: tokens.colorCompoundBrandBackground,\n            opacity: 1\n        },\n        ':hover': {\n            '::after': {\n                backgroundColor: tokens.colorCompoundBrandBackgroundHover,\n                opacity: 1\n            }\n        },\n        ':active': {\n            '::after': {\n                backgroundColor: tokens.colorCompoundBrandBackgroundPressed,\n                opacity: 1\n            }\n        }\n    },\n    unselectedBrand: {\n        '::after': {\n            opacity: 0.6,\n            backgroundColor: tokens.colorNeutralForeground1\n        },\n        ':hover': {\n            '::after': {\n                opacity: 0.75\n            }\n        },\n        ':active': {\n            '::after': {\n                opacity: 1\n            }\n        }\n    }\n});\n/**\n * Apply styling to the CarouselNavButton slots based on the state\n */ export const useCarouselNavButtonStyles_unstable = (state)=>{\n    'use no memo';\n    const styles = useStyles();\n    const { selected, appearance } = state;\n    state.root.className = mergeClasses(carouselNavButtonClassNames.root, styles.root, selected ? styles.rootSelected : styles.rootUnselected, appearance === 'brand' && styles.brand, !selected && appearance === 'brand' && styles.unselectedBrand, state.root.className);\n    return state;\n};\n"], "names": ["carouselNavButtonClassNames", "useCarouselNavButtonStyles_unstable", "root", "useStyles", "__styles", "Bceei9c", "Bkecrkj", "a9b677", "Bqenvij", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "B7ck84d", "De3pzq", "B4j52fo", "Bekrc4i", "Bn0qgzm", "ibv6hh", "Bsft5z2", "ap17g6", "li1rpt", "d9w3h3", "B3778ie", "B4j8arr", "Bl18szs", "Blrzh8d", "Bjuhk93", "B1q35kw", "Bw17bha", "Bcgy8vk", "Du69r6", "Gp14am", "vfts7", "Bhxzhr1", "G63luc", "s924m2", "Barhvk9", "Ihftqj", "wywymt", "B0n5ga8", "Bm2nyyq", "xrcqlc", "e1d83w", "Dlnsje", "a2br6o", "Bjyk6c5", "go7t6h", "qx5q1e", "f7digc", "Bfz3el7", "rootUnselected", "Bw0xxkn", "o<PERSON><PERSON>", "Bpd4iqm", "Befb4lg", "Byu6kyc", "n8qw10", "Bbjhlyh", "i2cumq", "Bunx835", "Bdrgwmp", "mqozju", "lbo84a", "Bksnhdo", "Bci5o5g", "u5e7qz", "Bn40d3w", "B7b6zxw", "B8q5s1w", "B5gfjzb", "Bbcte9g", "Bqz3imu", "Bj9ihqo", "Bl51kww", "B3bvztg", "Btyt4dx", "Brhw1f9", "Bw81rd7", "kdpuga", "dm238s", "B6xbmo0", "B3whbx2", "Bp15pi3", "Bay5ve9", "Bni0232", "rootSelected", "brand", "Glksuk", "Blzl0y7", "unselected<PERSON><PERSON>", "d", "p", "m", "h", "a", "state", "styles", "selected", "appearance", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAGaA,2BAA2B;eAA3BA;;IAmHIC,mCAAmC;eAAnCA;;;uBAtHoC;AAG9C,MAAMD,8BAA8B;IACvCE,MAAM;AACV;AACA;;CAEA,GAAI,MAAMC,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAF,MAAA;QAAAG,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,OAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAAC,gBAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAAC,cAAA;QAAAhF,QAAA;QAAAE,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAwC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAArC,QAAA;QAAAvB,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAA0D,SAAA;QAAAC,SAAA;IAAA;IAAAE,OAAA;QAAAzC,SAAA;QAAAqC,SAAA;QAAAK,QAAA;QAAAJ,SAAA;QAAAK,SAAA;QAAAJ,SAAA;IAAA;IAAAK,iBAAA;QAAAP,SAAA;QAAArC,SAAA;QAAAsC,SAAA;QAAAC,SAAA;IAAA;AAAA,GAAA;IAAAM,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;KAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AA8GX,MAAM/F,sCAAuCgG,CAAAA;IACpD;IACA,MAAMC,SAAS/F;IACf,MAAM,EAAEgG,QAAQ,EAAEC,UAAAA,EAAY,GAAGH;IACjCA,MAAM/F,IAAI,CAACmG,SAAS,GAAGC,IAAAA,mBAAY,EAACtG,4BAA4BE,IAAI,EAAEgG,OAAOhG,IAAI,EAAEiG,WAAWD,OAAOX,YAAY,GAAGW,OAAO9C,cAAc,EAAEgD,eAAe,WAAWF,OAAOV,KAAK,EAAE,CAACW,YAAYC,eAAe,WAAWF,OAAOP,eAAe,EAAEM,MAAM/F,IAAI,CAACmG,SAAS;IACtQ,OAAOJ;AACX"}