# Change Log - @fluentui/react-button

This log was last generated on Fri, 27 Jun 2025 13:36:33 GMT and should not be manually modified.

<!-- Start content -->

## [9.5.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.5.3)

Fri, 27 Jun 2025 13:36:33 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.5.2..@fluentui/react-button_v9.5.3)

### Patches

- Bump @fluentui/react-aria to v9.15.3 ([PR #34734](https://github.com/microsoft/fluentui/pull/34734) by beachball)
- Bump @fluentui/react-tabster to v9.25.3 ([PR #34734](https://github.com/microsoft/fluentui/pull/34734) by beachball)

## [9.5.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.5.2)

Thu, 26 Jun 2025 14:11:55 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.5.1..@fluentui/react-button_v9.5.2)

### Patches

- Bump @fluentui/react-aria to v9.15.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-tabster to v9.25.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-utilities to v9.22.0 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)

## [9.5.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.5.1)

Wed, 18 Jun 2025 17:34:00 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.5.0..@fluentui/react-button_v9.5.1)

### Patches

- Bump @fluentui/react-aria to v9.15.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-shared-contexts to v9.24.0 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-tabster to v9.25.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-utilities to v9.21.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)

## [9.5.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.5.0)

Thu, 12 Jun 2025 09:43:33 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.4.8..@fluentui/react-button_v9.5.0)

### Minor changes

- Bump @fluentui/react-aria to v9.15.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-tabster to v9.25.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-utilities to v9.21.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)

## [9.4.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.4.8)

Fri, 06 Jun 2025 13:15:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.4.7..@fluentui/react-button_v9.4.8)

### Patches

- Bump @fluentui/react-aria to v9.14.8 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.55 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-tabster to v9.24.8 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-utilities to v9.20.0 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)

## [9.4.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.4.7)

Wed, 14 May 2025 18:49:20 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.4.6..@fluentui/react-button_v9.4.7)

### Patches

- Bump @fluentui/react-aria to v9.14.7 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)
- Bump @fluentui/react-tabster to v9.24.7 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)

## [9.4.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.4.6)

Thu, 24 Apr 2025 09:59:45 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.4.5..@fluentui/react-button_v9.4.6)

### Patches

- Bump @fluentui/react-aria to v9.14.6 ([PR #34315](https://github.com/microsoft/fluentui/pull/34315) by beachball)
- Bump @fluentui/react-tabster to v9.24.6 ([PR #34315](https://github.com/microsoft/fluentui/pull/34315) by beachball)

## [9.4.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.4.5)

Wed, 16 Apr 2025 19:42:08 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.4.4..@fluentui/react-button_v9.4.5)

### Patches

- Bump @fluentui/react-aria to v9.14.5 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.54 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-tabster to v9.24.5 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-utilities to v9.19.0 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)

## [9.4.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.4.4)

Tue, 01 Apr 2025 15:08:02 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.4.3..@fluentui/react-button_v9.4.4)

### Patches

- Bump @fluentui/react-aria to v9.14.4 ([PR #33909](https://github.com/microsoft/fluentui/pull/33909) by beachball)
- Bump @fluentui/react-tabster to v9.24.4 ([PR #33909](https://github.com/microsoft/fluentui/pull/33909) by beachball)

## [9.4.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.4.3)

Thu, 27 Mar 2025 21:12:51 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.4.2..@fluentui/react-button_v9.4.3)

### Patches

- Bump @fluentui/react-aria to v9.14.3 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.53 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-shared-contexts to v9.23.1 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-tabster to v9.24.3 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-utilities to v9.18.23 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)

## [9.4.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.4.2)

Wed, 19 Mar 2025 15:40:43 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.4.1..@fluentui/react-button_v9.4.2)

### Patches

- Bump @fluentui/react-aria to v9.14.2 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.52 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-shared-contexts to v9.23.0 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-tabster to v9.24.2 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-utilities to v9.18.22 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)

## [9.4.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.4.1)

Tue, 11 Mar 2025 18:58:54 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.4.0..@fluentui/react-button_v9.4.1)

### Patches

- Bump @fluentui/react-aria to v9.14.1 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.51 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-shared-contexts to v9.22.0 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-tabster to v9.24.1 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-utilities to v9.18.21 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)

## [9.4.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.4.0)

Fri, 21 Feb 2025 14:34:05 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.102..@fluentui/react-button_v9.4.0)

### Minor changes

- Bump @fluentui/react-aria to v9.14.0 ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by beachball)
- Bump @fluentui/react-tabster to v9.24.0 ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by beachball)

## [9.3.102](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.102)

Fri, 07 Feb 2025 10:42:11 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.101..@fluentui/react-button_v9.3.102)

### Patches

- fix: Button sets correct disabledFocusable icon color ([PR #33756](https://github.com/microsoft/fluentui/pull/33756) by <EMAIL>)

## [9.3.101](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.101)

Tue, 28 Jan 2025 21:26:33 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.100..@fluentui/react-button_v9.3.101)

### Patches

- fix: make SplitButton divider transparent for subtle and transparent variants ([PR #33726](https://github.com/microsoft/fluentui/pull/33726) by <EMAIL>)

## [9.3.100](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.100)

Wed, 22 Jan 2025 14:00:21 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.99..@fluentui/react-button_v9.3.100)

### Patches

- Bump @fluentui/react-aria to v9.13.14 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.50 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-tabster to v9.23.3 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-utilities to v9.18.20 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)

## [9.3.99](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.99)

Wed, 08 Jan 2025 18:33:36 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.98..@fluentui/react-button_v9.3.99)

### Patches

- Bump @fluentui/react-aria to v9.13.13 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.49 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)

## [9.3.98](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.98)

Mon, 16 Dec 2024 16:26:47 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.97..@fluentui/react-button_v9.3.98)

### Patches

- chore: remove usage of "export *" ([PR #33457](https://github.com/microsoft/fluentui/pull/33457) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.12 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.48 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.2 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-tabster to v9.23.2 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-theme to v9.1.24 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-utilities to v9.18.19 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)

## [9.3.97](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.97)

Mon, 09 Dec 2024 17:38:16 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.96..@fluentui/react-button_v9.3.97)

### Patches

- Bump @fluentui/react-aria to v9.13.11 ([PR #33431](https://github.com/microsoft/fluentui/pull/33431) by beachball)

## [9.3.96](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.96)

Fri, 06 Dec 2024 12:53:46 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.95..@fluentui/react-button_v9.3.96)

### Patches

- Bump @fluentui/react-aria to v9.13.10 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.47 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.1 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-tabster to v9.23.1 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-theme to v9.1.23 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-utilities to v9.18.18 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)

## [9.3.95](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.95)

Mon, 11 Nov 2024 10:01:06 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.94..@fluentui/react-button_v9.3.95)

### Patches

- chore: replace npm-scripts and just-scrtips with nx inferred tasks ([PR #33074](https://github.com/microsoft/fluentui/pull/33074) by <EMAIL>)
- fix: Button with as=a should present as a semantic link ([PR #33050](https://github.com/microsoft/fluentui/pull/33050) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.8 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-aria to v9.13.9 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.46 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.0 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-tabster to v9.23.0 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-theme to v9.1.22 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-utilities to v9.18.17 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)

## [9.3.94](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.94)

Tue, 15 Oct 2024 17:17:53 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.93..@fluentui/react-button_v9.3.94)

### Patches

- Bump @fluentui/react-aria to v9.13.8 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.45 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-shared-contexts to v9.20.2 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-tabster to v9.22.9 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-theme to v9.1.21 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-utilities to v9.18.16 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)

## [9.3.93](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.93)

Tue, 08 Oct 2024 22:05:59 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.92..@fluentui/react-button_v9.3.93)

### Patches

- Bump @fluentui/react-aria to v9.13.7 ([PR #33007](https://github.com/microsoft/fluentui/pull/33007) by beachball)
- Bump @fluentui/react-tabster to v9.22.8 ([PR #33007](https://github.com/microsoft/fluentui/pull/33007) by beachball)

## [9.3.92](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.92)

Thu, 26 Sep 2024 14:15:27 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.91..@fluentui/react-button_v9.3.92)

### Patches

- fix: hovered menubutton icon does not disappear ([PR #32917](https://github.com/microsoft/fluentui/pull/32917) by <EMAIL>)

## [9.3.91](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.91)

Mon, 23 Sep 2024 12:40:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.90..@fluentui/react-button_v9.3.91)

### Patches

- Bump @fluentui/react-aria to v9.13.6 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.44 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-shared-contexts to v9.20.1 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-tabster to v9.22.7 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-theme to v9.1.20 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-utilities to v9.18.15 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)

## [9.3.90](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.90)

Tue, 10 Sep 2024 10:19:04 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.89..@fluentui/react-button_v9.3.90)

### Patches

- fix: medium and large SplitButton menuButton is at least 24px wide ([PR #32309](https://github.com/microsoft/fluentui/pull/32309) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.5 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.43 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-tabster to v9.22.6 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-utilities to v9.18.14 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)

## [9.3.89](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.89)

Thu, 15 Aug 2024 13:49:46 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.88..@fluentui/react-button_v9.3.89)

### Patches

- Bump @fluentui/react-aria to v9.13.4 ([PR #32313](https://github.com/microsoft/fluentui/pull/32313) by beachball)
- Bump @fluentui/react-tabster to v9.22.5 ([PR #32313](https://github.com/microsoft/fluentui/pull/32313) by beachball)

## [9.3.88](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.88)

Thu, 15 Aug 2024 08:22:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.87..@fluentui/react-button_v9.3.88)

### Patches

- Bump @fluentui/react-aria to v9.13.3 ([PR #31885](https://github.com/microsoft/fluentui/pull/31885) by beachball)
- Bump @fluentui/react-tabster to v9.22.4 ([PR #31885](https://github.com/microsoft/fluentui/pull/31885) by beachball)

## [9.3.87](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.87)

Tue, 23 Jul 2024 20:13:14 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.86..@fluentui/react-button_v9.3.87)

### Patches

- Bump @fluentui/react-aria to v9.13.2 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.42 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-shared-contexts to v9.20.0 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-tabster to v9.22.3 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-utilities to v9.18.13 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)

## [9.3.86](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.86)

Mon, 15 Jul 2024 17:25:53 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.85..@fluentui/react-button_v9.3.86)

### Patches

- fix: revert incorectly set npm versions in all packages ([PR #31937](https://github.com/microsoft/fluentui/pull/31937) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.1 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.41 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-shared-contexts to v9.19.1 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-tabster to v9.22.2 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-utilities to v9.18.12 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)

## [9.3.85](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.85)

Mon, 01 Jul 2024 20:30:42 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.84..@fluentui/react-button_v9.3.85)

### Patches

- chore: add eslint react-compiler ([PR #31457](https://github.com/microsoft/fluentui/pull/31457) by <EMAIL>)
- chore: Update react-icons package to ^2.0.245 ([PR #31802](https://github.com/microsoft/fluentui/pull/31802) by <EMAIL>)
- fix: MenuButton is styled correctly when aria-expanded is a string ([PR #31809](https://github.com/microsoft/fluentui/pull/31809) by <EMAIL>)
-  fix: Adding checked condition for icon styles when `ToggleButton` is checked + its appearance is subtle or transparent. ([PR #31789](https://github.com/microsoft/fluentui/pull/31789) by <EMAIL>)
- Bump @fluentui/react-aria to v9.13.0 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.40 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-tabster to v9.22.1 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-utilities to v9.18.11 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)

## [9.3.84](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.84)

Mon, 17 Jun 2024 07:34:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.83..@fluentui/react-button_v9.3.84)

### Patches

- Bump @fluentui/react-aria to v9.12.1 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)
- Bump @fluentui/react-tabster to v9.22.0 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)

## [9.3.83](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.83)

Thu, 06 Jun 2024 15:26:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.82..@fluentui/react-button_v9.3.83)

### Patches

- Bump @fluentui/react-aria to v9.12.0 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.39 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-tabster to v9.21.5 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-utilities to v9.18.10 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)

## [9.3.82](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.82)

Thu, 23 May 2024 08:02:40 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.81..@fluentui/react-button_v9.3.82)

### Patches

- chore: migrate .shorthands() [cxe-red] ([PR #31449](https://github.com/microsoft/fluentui/pull/31449) by <EMAIL>)
- Bump @fluentui/react-aria to v9.11.4 ([commit](https://github.com/microsoft/fluentui/commit/03599d609e8310b08c57d1f871cffbf717d79207) by beachball)
- Bump @fluentui/react-tabster to v9.21.4 ([commit](https://github.com/microsoft/fluentui/commit/03599d609e8310b08c57d1f871cffbf717d79207) by beachball)

## [9.3.81](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.81)

Mon, 20 May 2024 12:44:52 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.80..@fluentui/react-button_v9.3.81)

### Patches

- chore: bump @griffel/react ([PR #31258](https://github.com/microsoft/fluentui/pull/31258) by <EMAIL>)
- Bump @fluentui/react-aria to v9.11.3 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.38 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-shared-contexts to v9.19.0 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-tabster to v9.21.3 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-utilities to v9.18.9 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)

## [9.3.80](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.80)

Thu, 16 May 2024 09:25:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.79..@fluentui/react-button_v9.3.80)

### Patches

- chore: Upgrade react-icons version to 2.0.239 to pick up provider export map fix. ([PR #31287](https://github.com/microsoft/fluentui/pull/31287) by <EMAIL>)

## [9.3.79](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.79)

Thu, 09 May 2024 19:35:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.78..@fluentui/react-button_v9.3.79)

### Patches

- Bump @fluentui/react-aria to v9.11.2 ([PR #31321](https://github.com/microsoft/fluentui/pull/31321) by beachball)
- Bump @fluentui/react-tabster to v9.21.2 ([PR #31321](https://github.com/microsoft/fluentui/pull/31321) by beachball)

## [9.3.78](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.78)

Mon, 06 May 2024 12:55:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.77..@fluentui/react-button_v9.3.78)

### Patches

- Bump @fluentui/react-aria to v9.11.1 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.37 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-shared-contexts to v9.18.0 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-tabster to v9.21.1 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-utilities to v9.18.8 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)

## [9.3.77](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.77)

Thu, 02 May 2024 11:36:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.76..@fluentui/react-button_v9.3.77)

### Patches

- chore: upgrade @fluentui/react-icons to 2.0.237. ([PR #31139](https://github.com/microsoft/fluentui/pull/31139) by <EMAIL>)
- Bump @fluentui/react-aria to v9.11.0 ([PR #31231](https://github.com/microsoft/fluentui/pull/31231) by beachball)
- Bump @fluentui/react-tabster to v9.21.0 ([PR #31231](https://github.com/microsoft/fluentui/pull/31231) by beachball)

## [9.3.76](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.76)

Tue, 23 Apr 2024 08:17:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.75..@fluentui/react-button_v9.3.76)

### Patches

- Bump @fluentui/react-aria to v9.10.5 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.36 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-shared-contexts to v9.17.0 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-tabster to v9.20.1 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-utilities to v9.18.7 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)

## [9.3.75](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.75)

Wed, 17 Apr 2024 21:53:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.74..@fluentui/react-button_v9.3.75)

### Patches

- chore: Update react-icons to 2.0.235 ([PR #31011](https://github.com/microsoft/fluentui/pull/31011) by <EMAIL>)
- Bump @fluentui/react-aria to v9.10.4 ([PR #31100](https://github.com/microsoft/fluentui/pull/31100) by beachball)
- Bump @fluentui/react-tabster to v9.20.0 ([PR #31100](https://github.com/microsoft/fluentui/pull/31100) by beachball)

## [9.3.74](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.74)

Tue, 02 Apr 2024 09:48:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.73..@fluentui/react-button_v9.3.74)

### Patches

- Bump @fluentui/react-aria to v9.10.3 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.35 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-shared-contexts to v9.16.0 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-tabster to v9.19.6 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-utilities to v9.18.6 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)

## [9.3.73](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.73)

Mon, 18 Mar 2024 19:50:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.72..@fluentui/react-button_v9.3.73)

### Patches

- Bump @fluentui/react-aria to v9.10.2 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.34 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-shared-contexts to v9.15.2 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-tabster to v9.19.5 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-theme to v9.1.19 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-utilities to v9.18.5 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)

## [9.3.72](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.72)

Fri, 15 Mar 2024 21:43:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.71..@fluentui/react-button_v9.3.72)

### Patches

- fix: Applying correct styles to icon when ToggleButton is checked and has a subtle or transparent appearance. ([PR #30775](https://github.com/microsoft/fluentui/pull/30775) by <EMAIL>)
- Bump @fluentui/react-aria to v9.10.1 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.33 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-shared-contexts to v9.15.1 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-tabster to v9.19.4 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-theme to v9.1.18 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-utilities to v9.18.4 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)

## [9.3.71](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.71)

Thu, 07 Mar 2024 19:33:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.70..@fluentui/react-button_v9.3.71)

### Patches

- Bump @fluentui/react-aria to v9.10.0 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.32 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-shared-contexts to v9.15.0 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-tabster to v9.19.3 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-theme to v9.1.17 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-utilities to v9.18.3 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)

## [9.3.70](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.70)

Wed, 28 Feb 2024 02:34:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.69..@fluentui/react-button_v9.3.70)

### Patches

- Bump @fluentui/react-aria to v9.9.1 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.31 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-shared-contexts to v9.14.1 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-tabster to v9.19.2 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-utilities to v9.18.2 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)

## [9.3.69](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.69)

Tue, 20 Feb 2024 14:22:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.68..@fluentui/react-button_v9.3.69)

### Patches

- Bump @fluentui/react-aria to v9.9.0 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.30 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-tabster to v9.19.1 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-utilities to v9.18.1 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)

## [9.3.68](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.68)

Tue, 06 Feb 2024 17:55:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.67..@fluentui/react-button_v9.3.68)

### Patches

- Bump @fluentui/react-aria to v9.8.2 ([PR #30392](https://github.com/microsoft/fluentui/pull/30392) by beachball)
- Bump @fluentui/react-tabster to v9.19.0 ([PR #30392](https://github.com/microsoft/fluentui/pull/30392) by beachball)

## [9.3.67](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.67)

Tue, 30 Jan 2024 23:16:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.66..@fluentui/react-button_v9.3.67)

### Patches

- Bump @fluentui/react-aria to v9.8.1 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.29 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-tabster to v9.18.0 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-utilities to v9.18.0 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)

## [9.3.66](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.66)

Tue, 23 Jan 2024 15:11:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.65..@fluentui/react-button_v9.3.66)

### Patches

- Bump @fluentui/react-aria to v9.8.0 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.28 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-tabster to v9.17.4 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-utilities to v9.17.0 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)

## [9.3.65](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.65)

Thu, 18 Jan 2024 14:25:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.64..@fluentui/react-button_v9.3.65)

### Patches

- Bump @fluentui/react-aria to v9.7.3 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.27 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-shared-contexts to v9.14.0 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-tabster to v9.17.3 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-utilities to v9.16.1 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)

## [9.3.64](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.64)

Wed, 17 Jan 2024 16:18:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.63..@fluentui/react-button_v9.3.64)

### Patches

- Bump @fluentui/react-aria to v9.7.2 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.26 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-tabster to v9.17.2 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-utilities to v9.16.0 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)

## [9.3.63](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.63)

Tue, 16 Jan 2024 13:14:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.62..@fluentui/react-button_v9.3.63)

### Patches

- fix: correct version of @types/react-dom peer dep that matches for 16.x ([PR #30259](https://github.com/microsoft/fluentui/pull/30259) by <EMAIL>)
- Bump @fluentui/react-aria to v9.7.1 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.25 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)
- Bump @fluentui/react-tabster to v9.17.1 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)

## [9.3.62](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.62)

Thu, 11 Jan 2024 09:04:29 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.61..@fluentui/react-button_v9.3.62)

### Patches

- Bump @fluentui/react-aria to v9.7.0 ([PR #30259](https://github.com/microsoft/fluentui/pull/30259) by beachball)

## [9.3.61](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.61)

Mon, 08 Jan 2024 16:24:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.60..@fluentui/react-button_v9.3.61)

### Patches

- Update version of @fluentui/react-icons to 2.0.224 ([PR #30078](https://github.com/microsoft/fluentui/pull/30078) by <EMAIL>)
- Bump @fluentui/react-aria to v9.6.2 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.24 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-tabster to v9.17.0 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)
- Bump @fluentui/react-utilities to v9.15.6 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)

## [9.3.60](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.60)

Wed, 03 Jan 2024 09:26:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.59..@fluentui/react-button_v9.3.60)

### Patches

- Bump @fluentui/react-aria to v9.6.1 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.23 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-tabster to v9.16.1 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)
- Bump @fluentui/react-utilities to v9.15.5 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)

## [9.3.59](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.59)

Mon, 18 Dec 2023 14:40:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.58..@fluentui/react-button_v9.3.59)

### Patches

- chore: adopts useARIAButtonProps instead of deprecated method useARIAButtonShorthand ([PR #29735](https://github.com/microsoft/fluentui/pull/29735) by <EMAIL>)
- Bump @fluentui/react-aria to v9.6.0 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.22 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-shared-contexts to v9.13.2 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-tabster to v9.16.0 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-utilities to v9.15.4 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)

## [9.3.58](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.58)

Thu, 14 Dec 2023 09:58:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.57..@fluentui/react-button_v9.3.58)

### Patches

- Bump @fluentui/react-aria to v9.5.0 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.21 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-shared-contexts to v9.13.1 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-tabster to v9.15.1 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-utilities to v9.15.3 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)

## [9.3.57](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.57)

Thu, 30 Nov 2023 13:42:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.56..@fluentui/react-button_v9.3.57)

### Patches

- Bump @fluentui/react-aria to v9.4.0 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.20 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)
- Bump @fluentui/react-tabster to v9.15.0 ([PR #29929](https://github.com/microsoft/fluentui/pull/29929) by beachball)

## [9.3.56](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.56)

Mon, 20 Nov 2023 09:55:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.55..@fluentui/react-button_v9.3.56)

### Patches

- Bump @fluentui/react-shared-contexts to v9.13.0 ([PR #29878](https://github.com/microsoft/fluentui/pull/29878) by beachball)
- Bump @fluentui/react-tabster to v9.14.6 ([PR #29878](https://github.com/microsoft/fluentui/pull/29878) by beachball)

## [9.3.55](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.55)

Tue, 14 Nov 2023 17:51:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.54..@fluentui/react-button_v9.3.55)

### Patches

- Bump @fluentui/react-tabster to v9.14.5 ([PR #29835](https://github.com/microsoft/fluentui/pull/29835) by beachball)

## [9.3.54](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.54)

Thu, 09 Nov 2023 17:29:51 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.53..@fluentui/react-button_v9.3.54)

### Patches

- chore: use package.json#files setup instead of npmignore for all v9 libraries ([PR #29734](https://github.com/microsoft/fluentui/pull/29734) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.7 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-aria to v9.3.44 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.19 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-shared-contexts to v9.12.0 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-tabster to v9.14.4 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-theme to v9.1.16 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-utilities to v9.15.2 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)

## [9.3.53](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.53)

Wed, 01 Nov 2023 12:55:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.52..@fluentui/react-button_v9.3.53)

### Patches

- Bump @fluentui/react-shared-contexts to v9.11.1 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)
- Bump @fluentui/react-tabster to v9.14.3 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)
- Bump @fluentui/react-theme to v9.1.15 ([PR #29663](https://github.com/microsoft/fluentui/pull/29663) by beachball)

## [9.3.52](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.52)

Sat, 28 Oct 2023 23:35:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.51..@fluentui/react-button_v9.3.52)

### Patches

- Bump @fluentui/react-shared-contexts to v9.11.0 ([commit](https://github.com/microsoft/fluentui/commit/555b0fae3ec7f052e765557ae243c58000514f92) by beachball)
- Bump @fluentui/react-tabster to v9.14.2 ([commit](https://github.com/microsoft/fluentui/commit/555b0fae3ec7f052e765557ae243c58000514f92) by beachball)

## [9.3.51](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.51)

Mon, 23 Oct 2023 09:51:57 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.50..@fluentui/react-button_v9.3.51)

### Patches

- Bump @fluentui/react-tabster to v9.14.1 ([commit](https://github.com/microsoft/fluentui/commit/e4ef1febe8a185dddc10f8936944d177d50bc396) by beachball)

## [9.3.50](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.50)

Wed, 18 Oct 2023 17:54:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.49..@fluentui/react-button_v9.3.50)

### Patches

- chore: migrate from getNativeElementProps to getIntrinsicElementProps ([PR #29499](https://github.com/microsoft/fluentui/pull/29499) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.43 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.18 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-tabster to v9.14.0 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)
- Bump @fluentui/react-utilities to v9.15.1 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)

## [9.3.49](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.49)

Thu, 12 Oct 2023 14:55:42 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.48..@fluentui/react-button_v9.3.49)

### Patches

- fix: Transparent buttons visible in teams high contrast mode ([PR #29497](https://github.com/microsoft/fluentui/pull/29497) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.17 ([PR #29513](https://github.com/microsoft/fluentui/pull/29513) by beachball)

## [9.3.48](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.48)

Wed, 11 Oct 2023 13:54:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.47..@fluentui/react-button_v9.3.48)

### Patches

- Fix: Buttons internal focus border was not consistent on firefox ([PR #29441](https://github.com/microsoft/fluentui/pull/29441) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.42 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.16 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-tabster to v9.13.6 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)
- Bump @fluentui/react-utilities to v9.15.0 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)

## [9.3.47](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.47)

Mon, 09 Oct 2023 20:45:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.46..@fluentui/react-button_v9.3.47)

### Patches

- Bump @fluentui/react-aria to v9.3.41 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.15 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-shared-contexts to v9.10.0 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-tabster to v9.13.5 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)
- Bump @fluentui/react-utilities to v9.14.2 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)

## [9.3.46](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.46)

Thu, 05 Oct 2023 15:25:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.45..@fluentui/react-button_v9.3.46)

### Patches

- Bump @fluentui/react-aria to v9.3.40 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.14 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-tabster to v9.13.4 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)
- Bump @fluentui/react-utilities to v9.14.1 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)

## [9.3.45](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.45)

Wed, 04 Oct 2023 08:45:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.44..@fluentui/react-button_v9.3.45)

### Patches

- Fix: Button focus borders were extending past bounding box causing overlap, focus border is now inset ([PR #28705](https://github.com/microsoft/fluentui/pull/28705) by <EMAIL>)
- fix: Icon in disabled Button shouldn't change color on hover or pressed. ([PR #29342](https://github.com/microsoft/fluentui/pull/29342) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.39 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.13 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-tabster to v9.13.3 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)
- Bump @fluentui/react-utilities to v9.14.0 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)

## [9.3.44](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.44)

Mon, 02 Oct 2023 08:56:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.43..@fluentui/react-button_v9.3.44)

### Patches

- Bump @fluentui/react-shared-contexts to v9.9.2 ([PR #29301](https://github.com/microsoft/fluentui/pull/29301) by beachball)
- Bump @fluentui/react-tabster to v9.13.2 ([PR #29301](https://github.com/microsoft/fluentui/pull/29301) by beachball)

## [9.3.43](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.43)

Tue, 26 Sep 2023 17:49:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.42..@fluentui/react-button_v9.3.43)

### Patches

- chore: trigger manual version bump after broken release ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.6 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-aria to v9.3.38 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.12 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-shared-contexts to v9.9.1 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-tabster to v9.13.1 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-theme to v9.1.14 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-utilities to v9.13.5 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)

## [9.3.42](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.42)

Tue, 26 Sep 2023 15:32:04 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.41..@fluentui/react-button_v9.3.42)

### Patches

- fix: bump swc core to mitigate transpilation memory leaks ([PR #29253](https://github.com/microsoft/fluentui/pull/29253) by <EMAIL>)
- chore: Update react-icons version to pick up IconDirectionContextProvider updated export ([PR #29151](https://github.com/microsoft/fluentui/pull/29151) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.5 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-aria to v9.3.37 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.11 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-shared-contexts to v9.9.0 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-tabster to v9.13.0 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-theme to v9.1.13 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-utilities to v9.13.4 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)

## [9.3.41](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.41)

Wed, 20 Sep 2023 17:47:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.40..@fluentui/react-button_v9.3.41)

### Patches

- chore: trigger manual version bump after broken release ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.10 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)
- Bump @fluentui/react-shared-contexts to v9.8.1 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)
- Bump @fluentui/react-tabster to v9.12.11 ([PR #29197](https://github.com/microsoft/fluentui/pull/29197) by beachball)

## [9.3.40](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.40)

Wed, 20 Sep 2023 14:59:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.39..@fluentui/react-button_v9.3.40)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.9 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)
- Bump @fluentui/react-shared-contexts to v9.8.0 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)
- Bump @fluentui/react-tabster to v9.12.10 ([PR #29193](https://github.com/microsoft/fluentui/pull/29193) by beachball)

## [9.3.39](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.39)

Thu, 14 Sep 2023 16:44:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.38..@fluentui/react-button_v9.3.39)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.8 ([PR #29145](https://github.com/microsoft/fluentui/pull/29145) by beachball)

## [9.3.38](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.38)

Tue, 12 Sep 2023 08:51:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.37..@fluentui/react-button_v9.3.38)

### Patches

- fix: subtle mode hover style is now visible in windows HCM. ([PR #29116](https://github.com/microsoft/fluentui/pull/29116) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.7 ([PR #29129](https://github.com/microsoft/fluentui/pull/29129) by beachball)
- Bump @fluentui/react-tabster to v9.12.9 ([PR #29129](https://github.com/microsoft/fluentui/pull/29129) by beachball)

## [9.3.37](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.37)

Wed, 06 Sep 2023 13:31:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.36..@fluentui/react-button_v9.3.37)

### Patches

- Bump @fluentui/react-aria to v9.3.36 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.6 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-tabster to v9.12.8 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)
- Bump @fluentui/react-utilities to v9.13.3 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)

## [9.3.36](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.36)

Tue, 05 Sep 2023 15:39:04 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.35..@fluentui/react-button_v9.3.36)

### Patches

- Bump @fluentui/react-aria to v9.3.35 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.5 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-tabster to v9.12.7 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)
- Bump @fluentui/react-utilities to v9.13.2 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)

## [9.3.35](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.35)

Tue, 05 Sep 2023 13:29:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.34..@fluentui/react-button_v9.3.35)

### Patches

- chore: migrate package to use JSX importSource ([PR #28959](https://github.com/microsoft/fluentui/pull/28959) by <EMAIL>)
- bumps @swc/helpers version to 0.5.1 ([PR #28989](https://github.com/microsoft/fluentui/pull/28989) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.4 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-aria to v9.3.34 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.4 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.3 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-tabster to v9.12.6 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-theme to v9.1.12 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-utilities to v9.13.1 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)

## [9.3.34](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.34)

Tue, 29 Aug 2023 12:57:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.33..@fluentui/react-button_v9.3.34)

### Patches

- fix: Small Button now uses token for its border radius ([PR #28589](https://github.com/microsoft/fluentui/pull/28589) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.33 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.3 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-tabster to v9.12.5 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)
- Bump @fluentui/react-utilities to v9.13.0 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)

## [9.3.33](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.33)

Thu, 24 Aug 2023 10:26:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.32..@fluentui/react-button_v9.3.33)

### Patches

- Bump @fluentui/react-aria to v9.3.32 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.2 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-tabster to v9.12.4 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)
- Bump @fluentui/react-utilities to v9.12.0 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)

## [9.3.32](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.32)

Wed, 23 Aug 2023 12:01:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.31..@fluentui/react-button_v9.3.32)

### Patches

- Bump @fluentui/react-aria to v9.3.31 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.1 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-tabster to v9.12.3 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)
- Bump @fluentui/react-utilities to v9.11.2 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)

## [9.3.31](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.31)

Wed, 16 Aug 2023 17:41:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.30..@fluentui/react-button_v9.3.31)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.0 ([PR #28885](https://github.com/microsoft/fluentui/pull/28885) by beachball)

## [9.3.30](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.30)

Wed, 16 Aug 2023 11:38:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.29..@fluentui/react-button_v9.3.30)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.16 ([PR #28791](https://github.com/microsoft/fluentui/pull/28791) by beachball)

## [9.3.29](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.29)

Fri, 11 Aug 2023 12:14:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.28..@fluentui/react-button_v9.3.29)

### Patches

- Bump @fluentui/react-aria to v9.3.30 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.15 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-tabster to v9.12.2 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)
- Bump @fluentui/react-utilities to v9.11.1 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)

## [9.3.28](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.28)

Wed, 09 Aug 2023 13:16:51 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.27..@fluentui/react-button_v9.3.28)

### Patches

- fix: Button icon no longer rendering off-center with falsy children. ([PR #28495](https://github.com/microsoft/fluentui/pull/28495) by <EMAIL>)
- chore(cxe-red): migrate to new slot API ([PR #28753](https://github.com/microsoft/fluentui/pull/28753) by <EMAIL>)
- fix: updated styles to icon fill on hover only for subtle and transparent variant of button ([PR #28616](https://github.com/microsoft/fluentui/pull/28616) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #28684](https://github.com/microsoft/fluentui/pull/28684) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.29 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.14 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.2 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-tabster to v9.12.1 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)
- Bump @fluentui/react-theme to v9.1.11 ([PR #28775](https://github.com/microsoft/fluentui/pull/28775) by beachball)

## [9.3.27](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.27)

Fri, 04 Aug 2023 08:52:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.26..@fluentui/react-button_v9.3.27)

### Patches

- Bump @fluentui/react-aria to v9.3.28 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.13 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-shared-contexts to v9.7.1 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-tabster to v9.12.0 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-theme to v9.1.10 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)
- Bump @fluentui/react-utilities to v9.11.0 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)

## [9.3.26](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.26)

Tue, 01 Aug 2023 10:17:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.25..@fluentui/react-button_v9.3.26)

### Patches

- Bump @fluentui/react-shared-contexts to v9.7.0 ([PR #28689](https://github.com/microsoft/fluentui/pull/28689) by beachball)
- Bump @fluentui/react-tabster to v9.11.1 ([PR #28689](https://github.com/microsoft/fluentui/pull/28689) by beachball)

## [9.3.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.25)

Tue, 25 Jul 2023 13:29:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.24..@fluentui/react-button_v9.3.25)

### Patches

- chore: Update react-icons version to pick up fowardref change. ([PR #28590](https://github.com/microsoft/fluentui/pull/28590) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.11.0 ([PR #28622](https://github.com/microsoft/fluentui/pull/28622) by beachball)

## [9.3.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.24)

Thu, 20 Jul 2023 18:27:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.23..@fluentui/react-button_v9.3.24)

### Patches

- fix: high contrast mode hover style icon fixes ([PR #28156](https://github.com/microsoft/fluentui/pull/28156) by <EMAIL>)
- fix: updated right border token in primary variant of split button ([PR #28555](https://github.com/microsoft/fluentui/pull/28555) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.27 ([PR #26826](https://github.com/microsoft/fluentui/pull/26826) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.12 ([PR #26826](https://github.com/microsoft/fluentui/pull/26826) by beachball)

## [9.3.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.23)

Tue, 11 Jul 2023 18:46:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.22..@fluentui/react-button_v9.3.23)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.11 ([PR #28491](https://github.com/microsoft/fluentui/pull/28491) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0 ([PR #28491](https://github.com/microsoft/fluentui/pull/28491) by beachball)

## [9.3.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.22)

Mon, 03 Jul 2023 13:34:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.21..@fluentui/react-button_v9.3.22)

### Patches

- Bump @fluentui/react-tabster to v9.10.0 ([PR #28394](https://github.com/microsoft/fluentui/pull/28394) by beachball)

## [9.3.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.21)

Mon, 03 Jul 2023 11:57:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.20..@fluentui/react-button_v9.3.21)

### Patches

- Bump @fluentui/react-aria to v9.3.26 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.10 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)
- Bump @fluentui/react-shared-contexts to v9.6.0 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)
- Bump @fluentui/react-tabster to v9.9.2 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)
- Bump @fluentui/react-utilities to v9.10.1 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)

## [9.3.20](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.20)

Wed, 28 Jun 2023 11:12:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.19..@fluentui/react-button_v9.3.20)

### Patches

- Bump @fluentui/react-aria to v9.3.25 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.9 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)
- Bump @fluentui/react-tabster to v9.9.1 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)
- Bump @fluentui/react-utilities to v9.10.0 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)

## [9.3.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.19)

Tue, 27 Jun 2023 11:21:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.18..@fluentui/react-button_v9.3.19)

### Patches

- Bump @fluentui/react-tabster to v9.9.0 ([PR #28291](https://github.com/microsoft/fluentui/pull/28291) by beachball)

## [9.3.18](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.18)

Mon, 26 Jun 2023 09:53:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.17..@fluentui/react-button_v9.3.18)

### Patches

- Bump @fluentui/react-aria to v9.3.24 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.8 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)
- Bump @fluentui/react-tabster to v9.8.1 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)
- Bump @fluentui/react-utilities to v9.9.4 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)

## [9.3.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.17)

Tue, 20 Jun 2023 12:38:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.16..@fluentui/react-button_v9.3.17)

### Patches

- chore: update @fluentui/react-icons to 2.0.203 ([PR #28203](https://github.com/microsoft/fluentui/pull/28203) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.23 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.7 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-shared-contexts to v9.5.1 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-tabster to v9.8.0 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-theme to v9.1.9 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-utilities to v9.9.3 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.23 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)

## [9.3.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.16)

Wed, 31 May 2023 06:46:09 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.15..@fluentui/react-button_v9.3.16)

### Patches

- chore: Update Griffel to v1.5.7. ([PR #27925](https://github.com/microsoft/fluentui/pull/27925) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.22 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.6 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)
- Bump @fluentui/react-tabster to v9.7.5 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)
- Bump @fluentui/react-utilities to v9.9.2 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.22 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)

## [9.3.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.15)

Thu, 25 May 2023 10:00:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.14..@fluentui/react-button_v9.3.15)

### Patches

- Bump @fluentui/react-aria to v9.3.21 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.5 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)
- Bump @fluentui/react-tabster to v9.7.4 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)
- Bump @fluentui/react-utilities to v9.9.1 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)

## [9.3.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.14)

Wed, 24 May 2023 20:45:24 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.13..@fluentui/react-button_v9.3.14)

### Patches

- Bump @fluentui/react-shared-contexts to v9.5.0 ([PR #27989](https://github.com/microsoft/fluentui/pull/27989) by beachball)
- Bump @fluentui/react-tabster to v9.7.3 ([PR #27989](https://github.com/microsoft/fluentui/pull/27989) by beachball)

## [9.3.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.13)

Thu, 18 May 2023 13:11:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.12..@fluentui/react-button_v9.3.13)

### Patches

- Bump @fluentui/react-tabster to v9.7.2 ([PR #27540](https://github.com/microsoft/fluentui/pull/27540) by beachball)

## [9.3.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.12)

Thu, 18 May 2023 00:39:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.11..@fluentui/react-button_v9.3.12)

### Patches

- Bump @fluentui/react-aria to v9.3.20 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.4 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)
- Bump @fluentui/react-tabster to v9.7.1 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)
- Bump @fluentui/react-utilities to v9.9.0 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)

## [9.3.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.11)

Fri, 12 May 2023 20:28:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.10..@fluentui/react-button_v9.3.11)

### Patches

- chore: move makeStyles() calls to .styles.ts files ([PR #27710](https://github.com/microsoft/fluentui/pull/27710) by <EMAIL>)
- Update to use single hook selector ([PR #27491](https://github.com/microsoft/fluentui/pull/27491) by <EMAIL>)
- chore: exclude .swcrc from being published ([PR #27740](https://github.com/microsoft/fluentui/pull/27740) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.3 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-aria to v9.3.19 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.3 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-shared-contexts to v9.4.0 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-tabster to v9.7.0 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-theme to v9.1.8 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-utilities to v9.8.1 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.21 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)

## [9.3.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.10)

Mon, 24 Apr 2023 08:12:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.9..@fluentui/react-button_v9.3.10)

### Patches

- chore: adopt custom JSX pragma ([PR #27605](https://github.com/microsoft/fluentui/pull/27605) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.0-alpha.2 ([commit](https://github.com/microsoft/fluentui/commit/505433ac64f144c9cca456097413d6af4582e5ee) by beachball)

## [9.3.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.9)

Mon, 17 Apr 2023 17:54:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.8..@fluentui/react-button_v9.3.9)

### Patches

- Bump @fluentui/react-aria to v9.3.18 ([PR #27564](https://github.com/microsoft/fluentui/pull/27564) by beachball)
- Bump @fluentui/react-tabster to v9.6.5 ([PR #27564](https://github.com/microsoft/fluentui/pull/27564) by beachball)
- Bump @fluentui/react-utilities to v9.8.0 ([PR #27564](https://github.com/microsoft/fluentui/pull/27564) by beachball)

## [9.3.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.8)

Wed, 12 Apr 2023 09:31:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.7..@fluentui/react-button_v9.3.8)

### Patches

- Bump @fluentui/react-aria to v9.3.17 ([PR #27274](https://github.com/microsoft/fluentui/pull/27274) by beachball)
- Bump @fluentui/react-tabster to v9.6.4 ([PR #27274](https://github.com/microsoft/fluentui/pull/27274) by beachball)
- Bump @fluentui/react-utilities to v9.7.4 ([PR #27274](https://github.com/microsoft/fluentui/pull/27274) by beachball)

## [9.3.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.7)

Tue, 04 Apr 2023 18:44:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.5..@fluentui/react-button_v9.3.7)

### Patches

- Bump @fluentui/react-aria to v9.3.16 ([PR #27434](https://github.com/microsoft/fluentui/pull/27434) by beachball)
- Bump @fluentui/react-tabster to v9.6.3 ([PR #27434](https://github.com/microsoft/fluentui/pull/27434) by beachball)
- Bump @fluentui/react-utilities to v9.7.3 ([PR #27434](https://github.com/microsoft/fluentui/pull/27434) by beachball)

## [9.3.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.5)

Tue, 21 Mar 2023 21:23:25 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.4..@fluentui/react-button_v9.3.5)

### Patches

- chore: Bumping version of @fluentui/react-icons to ^2.0.196. ([PR #27100](https://github.com/microsoft/fluentui/pull/27100) by <EMAIL>)
- chore: migrate to swc transpilation approach. ([PR #27250](https://github.com/microsoft/fluentui/pull/27250) by <EMAIL>)
- fix: add node field to package.json exports map. ([PR #27154](https://github.com/microsoft/fluentui/pull/27154) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.2 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-aria to v9.3.15 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-shared-contexts to v9.3.2 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-tabster to v9.6.1 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-theme to v9.1.7 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-utilities to v9.7.2 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.20 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)

## [9.3.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.4)

Thu, 16 Mar 2023 14:36:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.3..@fluentui/react-button_v9.3.4)

### Patches

- Bump @fluentui/react-aria to v9.3.14 ([PR #27229](https://github.com/microsoft/fluentui/pull/27229) by beachball)
- Bump @fluentui/react-tabster to v9.6.0 ([PR #27229](https://github.com/microsoft/fluentui/pull/27229) by beachball)
- Bump @fluentui/react-utilities to v9.7.1 ([PR #27229](https://github.com/microsoft/fluentui/pull/27229) by beachball)

## [9.3.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.3)

Wed, 15 Mar 2023 10:19:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.2..@fluentui/react-button_v9.3.3)

### Patches

- Bump @fluentui/react-shared-contexts to v9.3.1 ([PR #27213](https://github.com/microsoft/fluentui/pull/27213) by beachball)
- Bump @fluentui/react-tabster to v9.5.7 ([PR #27213](https://github.com/microsoft/fluentui/pull/27213) by beachball)
- Bump @fluentui/react-theme to v9.1.6 ([PR #27213](https://github.com/microsoft/fluentui/pull/27213) by beachball)

## [9.3.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.2)

Mon, 13 Mar 2023 08:58:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.1..@fluentui/react-button_v9.3.2)

### Patches

- Bump @fluentui/react-aria to v9.3.13 ([PR #27161](https://github.com/microsoft/fluentui/pull/27161) by beachball)
- Bump @fluentui/react-tabster to v9.5.6 ([PR #27161](https://github.com/microsoft/fluentui/pull/27161) by beachball)
- Bump @fluentui/react-utilities to v9.7.0 ([PR #27161](https://github.com/microsoft/fluentui/pull/27161) by beachball)

## [9.3.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.1)

Fri, 10 Mar 2023 07:14:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.3.0..@fluentui/react-button_v9.3.1)

### Patches

- Bump @fluentui/react-aria to v9.3.12 ([PR #27157](https://github.com/microsoft/fluentui/pull/27157) by beachball)
- Bump @fluentui/react-tabster to v9.5.5 ([PR #27157](https://github.com/microsoft/fluentui/pull/27157) by beachball)
- Bump @fluentui/react-utilities to v9.6.2 ([PR #27157](https://github.com/microsoft/fluentui/pull/27157) by beachball)

## [9.3.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.3.0)

Wed, 08 Mar 2023 17:42:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.2.5..@fluentui/react-button_v9.3.0)

### Minor changes

- feat: Added custom style hook call ([PR #26943](https://github.com/microsoft/fluentui/pull/26943) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.11 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)
- Bump @fluentui/react-shared-contexts to v9.3.0 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)
- Bump @fluentui/react-tabster to v9.5.4 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)
- Bump @fluentui/react-utilities to v9.6.1 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)

## [9.2.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.2.5)

Wed, 15 Feb 2023 11:44:52 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.2.4..@fluentui/react-button_v9.2.5)

### Patches

- Bump @fluentui/react-aria to v9.3.10 ([PR #26845](https://github.com/microsoft/fluentui/pull/26845) by beachball)
- Bump @fluentui/react-tabster to v9.5.3 ([PR #26845](https://github.com/microsoft/fluentui/pull/26845) by beachball)
- Bump @fluentui/react-utilities to v9.6.0 ([PR #26845](https://github.com/microsoft/fluentui/pull/26845) by beachball)

## [9.2.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.2.4)

Mon, 13 Feb 2023 23:43:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.2.3..@fluentui/react-button_v9.2.4)

### Patches

- Bump @fluentui/react-aria to v9.3.9 ([PR #26814](https://github.com/microsoft/fluentui/pull/26814) by beachball)
- Bump @fluentui/react-tabster to v9.5.2 ([PR #26814](https://github.com/microsoft/fluentui/pull/26814) by beachball)
- Bump @fluentui/react-utilities to v9.5.3 ([PR #26814](https://github.com/microsoft/fluentui/pull/26814) by beachball)

## [9.2.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.2.3)

Fri, 10 Feb 2023 08:50:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.2.2..@fluentui/react-button_v9.2.3)

### Patches

- Bump @fluentui/react-aria to v9.3.8 ([commit](https://github.com/microsoft/fluentui/commit/cc62f050f8231e8f21a2cf7dddf33003e0ba3931) by beachball)
- Bump @fluentui/react-tabster to v9.5.1 ([commit](https://github.com/microsoft/fluentui/commit/cc62f050f8231e8f21a2cf7dddf33003e0ba3931) by beachball)
- Bump @fluentui/react-utilities to v9.5.2 ([commit](https://github.com/microsoft/fluentui/commit/cc62f050f8231e8f21a2cf7dddf33003e0ba3931) by beachball)

## [9.2.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.2.2)

Tue, 07 Feb 2023 14:13:09 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.2.1..@fluentui/react-button_v9.2.2)

### Patches

- fix: add forced-colors primary button variant ([PR #26623](https://github.com/microsoft/fluentui/pull/26623) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.5.0 ([PR #26732](https://github.com/microsoft/fluentui/pull/26732) by beachball)

## [9.2.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.2.1)

Tue, 31 Jan 2023 19:53:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.2.0..@fluentui/react-button_v9.2.1)

### Patches

- fix: Fixing button padding + minHeight to get correct sizes from design spec. ([PR #26522](https://github.com/microsoft/fluentui/pull/26522) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.7 ([PR #26557](https://github.com/microsoft/fluentui/pull/26557) by beachball)
- Bump @fluentui/react-tabster to v9.4.2 ([PR #26557](https://github.com/microsoft/fluentui/pull/26557) by beachball)
- Bump @fluentui/react-utilities to v9.5.1 ([PR #26557](https://github.com/microsoft/fluentui/pull/26557) by beachball)

## [9.2.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.2.0)

Thu, 26 Jan 2023 13:30:52 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.16..@fluentui/react-button_v9.2.0)

### Minor changes

- feature: creates ButtonContext ([PR #26478](https://github.com/microsoft/fluentui/pull/26478) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.6 ([PR #26496](https://github.com/microsoft/fluentui/pull/26496) by beachball)
- Bump @fluentui/react-tabster to v9.4.1 ([PR #26496](https://github.com/microsoft/fluentui/pull/26496) by beachball)
- Bump @fluentui/react-utilities to v9.5.0 ([PR #26496](https://github.com/microsoft/fluentui/pull/26496) by beachball)

### Patches

- fix: high contrast mode focus styles are applied ([PR #26116](https://github.com/microsoft/fluentui/pull/26116) by <EMAIL>)

## [9.1.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.16)

Wed, 18 Jan 2023 16:32:57 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.15..@fluentui/react-button_v9.1.16)

### Patches

- Bump @fluentui/react-tabster to v9.4.0 ([PR #26377](https://github.com/microsoft/fluentui/pull/26377) by beachball)

## [9.1.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.15)

Mon, 16 Jan 2023 08:38:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.14..@fluentui/react-button_v9.1.15)

### Patches

- Bump @fluentui/react-tabster to v9.3.7 ([commit](https://github.com/microsoft/fluentui/commit/a870d8360e47f3ea03358c4e75e89e08a74845d7) by beachball)

## [9.1.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.14)

Mon, 09 Jan 2023 14:35:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.13..@fluentui/react-button_v9.1.14)

### Patches

- Bump @fluentui/react-aria to v9.3.5 ([PR #26255](https://github.com/microsoft/fluentui/pull/26255) by beachball)
- Bump @fluentui/react-tabster to v9.3.6 ([PR #26255](https://github.com/microsoft/fluentui/pull/26255) by beachball)
- Bump @fluentui/react-utilities to v9.4.0 ([PR #26255](https://github.com/microsoft/fluentui/pull/26255) by beachball)

## [9.1.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.13)

Wed, 04 Jan 2023 01:40:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.12..@fluentui/react-button_v9.1.13)

### Patches

- chore: Update Griffel to latest version ([PR #26045](https://github.com/microsoft/fluentui/pull/26045) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.4 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)
- Bump @fluentui/react-tabster to v9.3.5 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)
- Bump @fluentui/react-utilities to v9.3.1 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.19 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)

## [9.1.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.12)

Wed, 21 Dec 2022 10:20:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.11..@fluentui/react-button_v9.1.12)

### Patches

- Bump @fluentui/react-tabster to v9.3.4 ([commit](https://github.com/microsoft/fluentui/commit/66bf89f634cad4a275e957d7a2214c7e73ff8c2e) by beachball)
- Bump @fluentui/react-theme to v9.1.5 ([commit](https://github.com/microsoft/fluentui/commit/66bf89f634cad4a275e957d7a2214c7e73ff8c2e) by beachball)

## [9.1.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.11)

Tue, 20 Dec 2022 14:59:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.10..@fluentui/react-button_v9.1.11)

### Patches

- Bump @fluentui/react-aria to v9.3.3 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)
- Bump @fluentui/react-tabster to v9.3.3 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)
- Bump @fluentui/react-theme to v9.1.4 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)
- Bump @fluentui/react-utilities to v9.3.0 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)

## [9.1.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.10)

Mon, 05 Dec 2022 18:29:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.9..@fluentui/react-button_v9.1.10)

### Patches

- chore: refactor to use makeResetStyles ([PR #25216](https://github.com/microsoft/fluentui/pull/25216) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.3.2 ([PR #25798](https://github.com/microsoft/fluentui/pull/25798) by beachball)
- Bump @fluentui/react-theme to v9.1.3 ([PR #25798](https://github.com/microsoft/fluentui/pull/25798) by beachball)

## [9.1.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.9)

Thu, 17 Nov 2022 23:05:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.8..@fluentui/react-button_v9.1.9)

### Patches

- Bump @fluentui/react-aria to v9.3.2 ([PR #25683](https://github.com/microsoft/fluentui/pull/25683) by beachball)
- Bump @fluentui/react-tabster to v9.3.1 ([PR #25683](https://github.com/microsoft/fluentui/pull/25683) by beachball)
- Bump @fluentui/react-utilities to v9.2.2 ([PR #25683](https://github.com/microsoft/fluentui/pull/25683) by beachball)

## [9.1.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.8)

Fri, 11 Nov 2022 14:57:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.7..@fluentui/react-button_v9.1.8)

### Patches

- fix: create valid export maps ([PR #25558](https://github.com/microsoft/fluentui/pull/25558) by <EMAIL>)
- chore: Replacing use of hard-coded constants with curve and duration tokens from theme. ([PR #25522](https://github.com/microsoft/fluentui/pull/25522) by <EMAIL>)
- fix: Showing correct behavior for icons inside of Buttons on hover, pressed and toggle states. ([PR #25479](https://github.com/microsoft/fluentui/pull/25479) by <EMAIL>)
- fix: Adding expanded styles for MenuButtons and making various other styling fixes for Button components. ([PR #25521](https://github.com/microsoft/fluentui/pull/25521) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.1 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-aria to v9.3.1 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-tabster to v9.3.0 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-theme to v9.1.2 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-utilities to v9.2.1 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.18 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)

## [9.1.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.7)

Wed, 02 Nov 2022 11:57:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.6..@fluentui/react-button_v9.1.7)

### Patches

- chore: Update Griffel to latest version ([PR #25412](https://github.com/microsoft/fluentui/pull/25412) by <EMAIL>)
- Bump @fluentui/react-aria to v9.3.0 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)
- Bump @fluentui/react-tabster to v9.2.1 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)
- Bump @fluentui/react-utilities to v9.2.0 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.17 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)

## [9.1.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.6)

Tue, 25 Oct 2022 00:35:29 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.5..@fluentui/react-button_v9.1.6)

### Patches

- chore: Migrate to new package structure. ([PR #25336](https://github.com/microsoft/fluentui/pull/25336) by <EMAIL>)

## [9.1.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.5)

Thu, 20 Oct 2022 08:39:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.4..@fluentui/react-button_v9.1.5)

### Patches

- chore: Update Griffel to latest version ([PR #25212](https://github.com/microsoft/fluentui/pull/25212) by <EMAIL>)
- fix: Fixing the styles of Buttons rendered as anchor tags. ([PR #25250](https://github.com/microsoft/fluentui/pull/25250) by <EMAIL>)
- chore: Bump peer deps to support React 18 ([PR #24972](https://github.com/microsoft/fluentui/pull/24972) by <EMAIL>)
- Bump @fluentui/react-aria to v9.2.3 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-tabster to v9.2.0 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-theme to v9.1.1 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-utilities to v9.1.2 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.16 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)

## [9.1.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.4)

Thu, 13 Oct 2022 11:02:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.3..@fluentui/react-button_v9.1.4)

### Patches

- chore: Update Griffel to latest version ([PR #25075](https://github.com/microsoft/fluentui/pull/25075) by <EMAIL>)
- feat: Adding subtle transition between states on Button components. ([PR #25106](https://github.com/microsoft/fluentui/pull/25106) by <EMAIL>)
- Bump @fluentui/react-aria to v9.2.2 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)
- Bump @fluentui/react-tabster to v9.1.3 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)
- Bump @fluentui/react-utilities to v9.1.1 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.15 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)

## [9.1.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.3)

Mon, 03 Oct 2022 22:24:42 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.2..@fluentui/react-button_v9.1.3)

### Patches

- Bump @fluentui/react-aria to v9.2.1 ([PR #25055](https://github.com/microsoft/fluentui/pull/25055) by beachball)
- Bump @fluentui/react-tabster to v9.1.2 ([PR #25055](https://github.com/microsoft/fluentui/pull/25055) by beachball)

## [9.1.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.2)

Tue, 20 Sep 2022 20:55:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.1..@fluentui/react-button_v9.1.2)

### Patches

- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.14 ([PR #24869](https://github.com/microsoft/fluentui/pull/24869) by beachball)

## [9.1.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.1)

Thu, 15 Sep 2022 09:49:56 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.1.0..@fluentui/react-button_v9.1.1)

### Patches

- fix: Adding 'aria-expanded=false' to collapsed MenuButtons. ([PR #24782](https://github.com/microsoft/fluentui/pull/24782) by <EMAIL>)
- chore: Remove max-width style from Button components. ([PR #24647](https://github.com/microsoft/fluentui/pull/24647) by <EMAIL>)
- chore: Cleaning up tokens in Button components so they better adhere to the design spec. ([PR #24732](https://github.com/microsoft/fluentui/pull/24732) by <EMAIL>)
- fix: Wrapping long text in Button components. ([PR #24682](https://github.com/microsoft/fluentui/pull/24682) by <EMAIL>)
- refactor: Replace useMergedEventCallbacks utility with mergeCallbacks ([PR #24152](https://github.com/microsoft/fluentui/pull/24152) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #24221](https://github.com/microsoft/fluentui/pull/24221) by <EMAIL>)
- Bump @fluentui/react-aria to v9.2.0 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-tabster to v9.1.1 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-theme to v9.1.0 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-utilities to v9.1.0 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.13 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)

## [9.1.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.1.0)

Wed, 03 Aug 2022 16:03:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.3..@fluentui/react-button_v9.1.0)

### Minor changes

- chore: Updates Button to use useARIAButton over useARIAButtonShorthand ([PR #24032](https://github.com/microsoft/fluentui/pull/24032) by <EMAIL>)
- Bump @fluentui/react-aria to v9.1.0 ([PR #24131](https://github.com/microsoft/fluentui/pull/24131) by beachball)
- Bump @fluentui/react-tabster to v9.1.0 ([PR #24131](https://github.com/microsoft/fluentui/pull/24131) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.12 ([PR #24131](https://github.com/microsoft/fluentui/pull/24131) by beachball)

### Patches

- chore: Bump Griffel dependencies ([PR #24114](https://github.com/microsoft/fluentui/pull/24114) by <EMAIL>)

## [9.0.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.3)

Thu, 14 Jul 2022 21:21:09 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.2..@fluentui/react-button_v9.0.3)

### Patches

- fix: Fixing bad version bump of @fluentui/react-utilities. ([PR #23920](https://github.com/microsoft/fluentui/pull/23920) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.2 ([PR #23918](https://github.com/microsoft/fluentui/pull/23918) by beachball)
- Bump @fluentui/react-tabster to v9.0.3 ([PR #23918](https://github.com/microsoft/fluentui/pull/23918) by beachball)
- Bump @fluentui/react-utilities to v9.0.2 ([PR #23918](https://github.com/microsoft/fluentui/pull/23918) by beachball)

## [9.0.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.2)

Thu, 14 Jul 2022 17:06:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.1..@fluentui/react-button_v9.0.2)

### Patches

- chore: Update @fluentui/react-icons dependency to v2.0.175 ([PR #23812](https://github.com/microsoft/fluentui/pull/23812) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.1 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)
- Bump @fluentui/react-tabster to v9.0.2 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)
- Bump @fluentui/react-utilities to v9.0.1-0 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.11 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)

## [9.0.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.1)

Tue, 28 Jun 2022 17:39:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0..@fluentui/react-button_v9.0.1)

### Patches

- fix: Use caret dependency range for Griffel ([PR #23754](https://github.com/microsoft/fluentui/pull/23754) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.0.1 ([PR #23754](https://github.com/microsoft/fluentui/pull/23754) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.10 ([PR #23754](https://github.com/microsoft/fluentui/pull/23754) by beachball)

## [9.0.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0)

Tue, 28 Jun 2022 15:13:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.14..@fluentui/react-button_v9.0.0)

### Patches

- feat: Initial 9.0.0 release ([PR #23733](https://github.com/microsoft/fluentui/pull/23733) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-aria to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-tabster to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-theme to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-utilities to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.9 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)

### Changes

- Bump Griffel dependencies ([PR #23688](https://github.com/microsoft/fluentui/pull/23688) by <EMAIL>)
- README and migration guide cleanup. ([PR #23395](https://github.com/microsoft/fluentui/pull/23395) by <EMAIL>)
- Button: Adding missing forced color adjust overrides. ([PR #23387](https://github.com/microsoft/fluentui/pull/23387) by <EMAIL>)
- Update 9.0.0-rc dependencies to use caret range ([PR #23732](https://github.com/microsoft/fluentui/pull/23732) by <EMAIL>)
- Button: Fixing hover styles in High Contrast mode. ([PR #23384](https://github.com/microsoft/fluentui/pull/23384) by <EMAIL>)
- chore: Update @fluentui/react-icons to latest version ([PR #23459](https://github.com/microsoft/fluentui/pull/23459) by <EMAIL>)

## [9.0.0-rc.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.14)

Thu, 23 Jun 2022 14:25:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.13..@fluentui/react-button_v9.0.0-rc.14)

### Changes

- Bump @fluentui/react-tabster to v9.0.0-rc.14 ([PR #23608](https://github.com/microsoft/fluentui/pull/23608) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.10 ([PR #23608](https://github.com/microsoft/fluentui/pull/23608) by beachball)

## [9.0.0-rc.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.13)

Tue, 31 May 2022 21:28:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.12..@fluentui/react-button_v9.0.0-rc.13)

### Changes

- chore: Update Griffel to latest version ([PR #23275](https://github.com/microsoft/fluentui/pull/23275) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.10 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.13 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.10 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.8 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)

## [9.0.0-rc.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.12)

Thu, 26 May 2022 21:01:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.11..@fluentui/react-button_v9.0.0-rc.12)

### Changes

- ToggleButton: Differentiating hover and checked styles. ([PR #23209](https://github.com/microsoft/fluentui/pull/23209) by <EMAIL>)
- ToggleButton: Apply thicker border width to checked outline toggle buttons. ([PR #23207](https://github.com/microsoft/fluentui/pull/23207) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.0.0-rc.12 ([PR #23267](https://github.com/microsoft/fluentui/pull/23267) by beachball)

## [9.0.0-rc.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.11)

Mon, 23 May 2022 18:56:41 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.10..@fluentui/react-button_v9.0.0-rc.11)

### Changes

- Removing <componentName>ClassName exports. ([PR #23092](https://github.com/microsoft/fluentui/pull/23092) by <EMAIL>)
- Removing deprecated block prop from all button components. ([PR #23092](https://github.com/microsoft/fluentui/pull/23092) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.0.0-rc.11 ([PR #23146](https://github.com/microsoft/fluentui/pull/23146) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.9 ([PR #23146](https://github.com/microsoft/fluentui/pull/23146) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.7 ([PR #23146](https://github.com/microsoft/fluentui/pull/23146) by beachball)

## [9.0.0-rc.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.10)

Mon, 23 May 2022 12:14:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.9..@fluentui/react-button_v9.0.0-rc.10)

### Changes

- Using spacing tokens for button components. ([PR #23076](https://github.com/microsoft/fluentui/pull/23076) by <EMAIL>)
- Removing flex gap from button components and fixing spacing to better align with design spec. ([PR #23091](https://github.com/microsoft/fluentui/pull/23091) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #22894](https://github.com/microsoft/fluentui/pull/22894) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #23029](https://github.com/microsoft/fluentui/pull/23029) by <EMAIL>)
- Removing Common types from all button components. ([PR #22904](https://github.com/microsoft/fluentui/pull/22904) by <EMAIL>)
- Removing ToggleButtonCommons type in @fluentui/react-button. ([PR #23067](https://github.com/microsoft/fluentui/pull/23067) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.9 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.10 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.8 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.9 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.6 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)

## [9.0.0-rc.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.9)

Thu, 05 May 2022 18:26:29 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.8..@fluentui/react-button_v9.0.0-rc.9)

### Changes

- feat: ship rolluped only dts ([PR #22823](https://github.com/microsoft/fluentui/pull/22823) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-rc.6 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-aria to v9.0.0-rc.8 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.9 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.7 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.8 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.5 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)

## [9.0.0-rc.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.8)

Wed, 04 May 2022 13:26:38 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.7..@fluentui/react-button_v9.0.0-rc.8)

### Changes

- Bump @fluentui/keyboard-keys to v9.0.0-rc.5 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-aria to v9.0.0-rc.7 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.8 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.6 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.7 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)

## [9.0.0-rc.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.7)

Mon, 25 Apr 2022 09:32:16 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.6..@fluentui/react-button_v9.0.0-rc.7)

### Changes

- Adding useToggleState hook to scaffold toggleable behavior for individual consumption. ([PR #22279](https://github.com/microsoft/fluentui/pull/22279) by <EMAIL>)
- fix(Button): Text color in HC mode for hover, active and checked states. ([PR #22566](https://github.com/microsoft/fluentui/pull/22566) by <EMAIL>)
- Button: Stop applying active styles when mouse moves away while it is being held and improving styles in High Contrast mode. ([PR #22574](https://github.com/microsoft/fluentui/pull/22574) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.0.0-rc.7 ([PR #22601](https://github.com/microsoft/fluentui/pull/22601) by beachball)

## [9.0.0-rc.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.6)

Tue, 19 Apr 2022 19:17:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.5..@fluentui/react-button_v9.0.0-rc.6)

### Changes

- Disabling lint rule for deprecated [component]ClassName exports. ([PR #22131](https://github.com/microsoft/fluentui/pull/22131) by <EMAIL>)
- Button: Deprecating block prop. ([PR #22148](https://github.com/microsoft/fluentui/pull/22148) by <EMAIL>)
- Button components: Making root a non-nullable slot. ([PR #22147](https://github.com/microsoft/fluentui/pull/22147) by <EMAIL>)
- CompoundButton: Fixing bug where component was iconOnly even when secondaryContent was being provided. ([PR #22107](https://github.com/microsoft/fluentui/pull/22107) by <EMAIL>)
- Removing star exports. ([PR #22111](https://github.com/microsoft/fluentui/pull/22111) by <EMAIL>)
- Add deprecation messages to react-button ([PR #21960](https://github.com/microsoft/fluentui/pull/21960) by <EMAIL>)
- chore: Update Griffel to latest version ([PR #21976](https://github.com/microsoft/fluentui/pull/21976) by <EMAIL>)
- update react-icons version to ^2.0.166-rc.3 from ^2.0.159-beta.10 ([PR #22512](https://github.com/microsoft/fluentui/pull/22512) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.6 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.6 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.5 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.6 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.4 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)

## [9.0.0-rc.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.5)

Fri, 04 Mar 2022 05:17:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.4..@fluentui/react-button_v9.0.0-rc.5)

### Changes

- Adding explicit export maps on all consumer packages for FUIR 8 and 9. ([PR #21508](https://github.com/microsoft/fluentui/pull/21508) by <EMAIL>)
- Button: Exporting classNames of individual slots. ([PR #20977](https://github.com/microsoft/fluentui/pull/20977) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-rc.4 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)
- Bump @fluentui/react-aria to v9.0.0-rc.5 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.5 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)
- Bump @fluentui/react-theme to v9.0.0-rc.4 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.5 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)

## [9.0.0-rc.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.4)

Tue, 01 Mar 2022 02:17:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.3..@fluentui/react-button_v9.0.0-rc.4)

### Changes

- fix: Add react-theme as dependency ([PR #21825](https://github.com/microsoft/fluentui/pull/21825) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-rc.4 ([PR #21884](https://github.com/microsoft/fluentui/pull/21884) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.4 ([PR #21884](https://github.com/microsoft/fluentui/pull/21884) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.4 ([PR #21884](https://github.com/microsoft/fluentui/pull/21884) by beachball)

## [9.0.0-rc.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.3)

Fri, 18 Feb 2022 13:35:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-rc.1..@fluentui/react-button_v9.0.0-rc.3)

### Changes

- SplitButton: Fixing issue where menuIcon prop was not being respected. ([PR #21683](https://github.com/microsoft/fluentui/pull/21683) by <EMAIL>)
- Button: Updating stories to fix a11y issues. ([PR #21679](https://github.com/microsoft/fluentui/pull/21679) by <EMAIL>)
- SplitButton: Fixing screen reader access issues. ([PR #21703](https://github.com/microsoft/fluentui/pull/21703) by <EMAIL>)
- fix: Source maps contain original source code ([PR #21690](https://github.com/microsoft/fluentui/pull/21690) by <EMAIL>)
- Button: Adding tests and fixing issues in SplitButton and ToggleButton. ([PR #21719](https://github.com/microsoft/fluentui/pull/21719) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-aria to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)

## [9.0.0-rc.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-rc.1)

Thu, 10 Feb 2022 08:51:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-beta.5..@fluentui/react-button_v9.0.0-rc.1)

### Changes

- Using ComponentSlotProps instead of ObjectShorthandProps. ([PR #20890](https://github.com/microsoft/fluentui/pull/20890) by <EMAIL>)
- Button: Changing border radius of small button to borderRadiusMedium from borderRadiusSmall. ([PR #21494](https://github.com/microsoft/fluentui/pull/21494) by <EMAIL>)
- Replace make-styles packages with griffel equivalents. ([PR #21436](https://github.com/microsoft/fluentui/pull/21436) by <EMAIL>)
- Update react-icons usage to resizable icons ([PR #21074](https://github.com/microsoft/fluentui/pull/21074) by <EMAIL>)
- BREAKING: Rename component hooks add the suffix \_unstable, as their API has not been finalized yet ([PR #21365](https://github.com/microsoft/fluentui/pull/21365) by <EMAIL>)
- Bump Fluent UI packages to 9.0.0-rc ([PR #21623](https://github.com/microsoft/fluentui/pull/21623) by <EMAIL>)
- (fix): Update react-icons dependency to latest version ([PR #20943](https://github.com/microsoft/fluentui/pull/20943) by <EMAIL>)
- Replacing use of functions in makeStyles with direct use of tokens. ([PR #21039](https://github.com/microsoft/fluentui/pull/21039) by <EMAIL>)
- CompoundButton: Updating styles to match latest design spec. ([PR #21523](https://github.com/microsoft/fluentui/pull/21523) by <EMAIL>)
- Remove component's shorthandProps array ([PR #21134](https://github.com/microsoft/fluentui/pull/21134) by <EMAIL>)
- remove export of commons types ([PR #21660](https://github.com/microsoft/fluentui/pull/21660) by <EMAIL>)
- Remove fontSize from default menuIcon, as it is already set via css ([PR #21221](https://github.com/microsoft/fluentui/pull/21221) by <EMAIL>)
- ToggleButton: Stopping aria-pressed from changing when ToggleButton is disabledFocusable. ([PR #21492](https://github.com/microsoft/fluentui/pull/21492) by <EMAIL>)
- update styles to not use CSS shorthands ([PR #20815](https://github.com/microsoft/fluentui/pull/20815) by <EMAIL>)
- Refactor component Slot typings ([PR #21518](https://github.com/microsoft/fluentui/pull/21518) by <EMAIL>)
- Updating use of tokens.fontWeight now that we don't need to use casting. ([PR #21217](https://github.com/microsoft/fluentui/pull/21217) by <EMAIL>)
- MenuButton: Updating types so it properly supports ref. ([PR #21515](https://github.com/microsoft/fluentui/pull/21515) by <EMAIL>)
- update @fluentui/react-icons package ([PR #21498](https://github.com/microsoft/fluentui/pull/21498) by <EMAIL>)
- Updating based on changes to composition types. ([PR #20891](https://github.com/microsoft/fluentui/pull/20891) by <EMAIL>)
- Updating packages based on changes to focusIndicator functions to remove functions from makeStyles in @fluentui/react-tabster. ([PR #21035](https://github.com/microsoft/fluentui/pull/21035) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-aria to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-conformance-griffel to v9.0.0-beta.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)

## [9.0.0-beta.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-beta.5)

Thu, 25 Nov 2021 08:34:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-beta.4..@fluentui/react-button_v9.0.0-beta.5)

### Changes

- MenuButton: Removing tight coupling with Menu by replicating type locally. ([PR #20635](https://github.com/microsoft/fluentui/pull/20635) by <EMAIL>)
- update react-icons dependency ([PR #20563](https://github.com/microsoft/fluentui/pull/20563) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-beta.5 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)

## [9.0.0-beta.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-beta.4)

Fri, 12 Nov 2021 13:25:16 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-beta.3..@fluentui/react-button_v9.0.0-beta.4)

### Changes

- export static classes for components ([PR #20454](https://github.com/microsoft/fluentui/pull/20454) by <EMAIL>)
- Updated beta and RC components to ES2019 ([PR #20405](https://github.com/microsoft/fluentui/pull/20405) by <EMAIL>)
- react-button: Removing use of enzyme in the converged package. ([PR #20342](https://github.com/microsoft/fluentui/pull/20342) by <EMAIL>)
- Fixing incomplete comment. ([PR #20273](https://github.com/microsoft/fluentui/pull/20273) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-beta.4 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-menu to v9.0.0-beta.4 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)

## [9.0.0-beta.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-beta.3)

Wed, 27 Oct 2021 12:14:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-beta.2..@fluentui/react-button_v9.0.0-beta.3)

### Changes

- Removed duplicate 'Button' from story names ([PR #20266](https://github.com/microsoft/fluentui/pull/20266) by <EMAIL>)
- added styling of documentation ([PR #20193](https://github.com/microsoft/fluentui/pull/20193) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-beta.3 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-menu to v9.0.0-beta.3 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)

## [9.0.0-beta.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-beta.2)

Tue, 12 Oct 2021 19:45:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-beta.1..@fluentui/react-button_v9.0.0-beta.2)

### Changes

- Bump @fluentui/react-tabster to v9.0.0-beta.2 ([PR #20132](https://github.com/microsoft/fluentui/pull/20132) by beachball)
- Bump @fluentui/react-menu to v9.0.0-beta.2 ([PR #20132](https://github.com/microsoft/fluentui/pull/20132) by beachball)

## [9.0.0-beta.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-beta.1)

Wed, 06 Oct 2021 10:37:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.101..@fluentui/react-button_v9.0.0-beta.1)

### Changes

- Bump all v9 components to beta prerelease tag ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-aria to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-menu to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)

## [9.0.0-alpha.101](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.101)

Tue, 05 Oct 2021 12:47:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.100..@fluentui/react-button_v9.0.0-alpha.101)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.45 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.78 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.73 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.60 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.53 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.22 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.92 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)

## [9.0.0-alpha.100](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.100)

Tue, 05 Oct 2021 09:28:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.99..@fluentui/react-button_v9.0.0-alpha.100)

### Changes

- Adds ForwardRefComponent to react-button components declaration ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-alpha.44 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.77 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.72 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.56 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.59 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.52 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.21 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.91 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)

## [9.0.0-alpha.99](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.99)

Fri, 01 Oct 2021 14:13:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.97..@fluentui/react-button_v9.0.0-alpha.99)

### Changes

- Bump v9 prerelease versions to rerelease ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-alpha.5 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-aria to v9.0.0-alpha.43 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.76 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.71 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.55 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.58 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.51 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.20 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.90 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)

## [9.0.0-alpha.97](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.97)

Fri, 01 Oct 2021 12:30:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.96..@fluentui/react-button_v9.0.0-alpha.97)

### Changes

- Updates button to simplify prop merging ([PR #18814](https://github.com/microsoft/fluentui/pull/18814) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-alpha.41 ([PR #18814](https://github.com/microsoft/fluentui/pull/18814) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.88 ([PR #18814](https://github.com/microsoft/fluentui/pull/18814) by beachball)

## [9.0.0-alpha.96](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.96)

Fri, 01 Oct 2021 09:44:56 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.95..@fluentui/react-button_v9.0.0-alpha.96)

### Changes

- Use the renamed createCustomFocusIndicatorStyle helper for focus outline style ([PR #19990](https://github.com/microsoft/fluentui/pull/19990) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.0.0-alpha.69 ([PR #19990](https://github.com/microsoft/fluentui/pull/19990) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.87 ([PR #19990](https://github.com/microsoft/fluentui/pull/19990) by beachball)

## [9.0.0-alpha.95](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.95)

Thu, 30 Sep 2021 09:18:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.94..@fluentui/react-button_v9.0.0-alpha.95)

### Changes

- Bump @fluentui/react-menu to v9.0.0-alpha.86 ([PR #19802](https://github.com/microsoft/fluentui/pull/19802) by beachball)

## [9.0.0-alpha.94](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.94)

Wed, 29 Sep 2021 08:06:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.93..@fluentui/react-button_v9.0.0-alpha.94)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.74 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.68 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.56 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.49 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.18 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.85 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)

## [9.0.0-alpha.93](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.93)

Mon, 27 Sep 2021 08:06:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.92..@fluentui/react-button_v9.0.0-alpha.93)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.73 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.67 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.53 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.55 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.48 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.17 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.84 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)

## [9.0.0-alpha.92](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.92)

Fri, 24 Sep 2021 09:17:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.91..@fluentui/react-button_v9.0.0-alpha.92)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.72 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.66 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.52 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.54 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.47 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.16 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.83 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)

## [9.0.0-alpha.91](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.91)

Thu, 23 Sep 2021 08:21:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.90..@fluentui/react-button_v9.0.0-alpha.91)

### Changes

- Updated for consistency to have shape and appearance properties ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by <EMAIL>)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.71 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.65 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.51 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.53 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.46 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.15 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.82 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)

## [9.0.0-alpha.90](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.90)

Wed, 22 Sep 2021 10:10:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.89..@fluentui/react-button_v9.0.0-alpha.90)

### Changes

- update react-icons dependency ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by <EMAIL>)
- SplitButton: Adding missing verticalAlign=middle style. ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by <EMAIL>)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.70 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.64 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.50 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.52 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.45 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.14 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.81 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)

## [9.0.0-alpha.89](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.89)

Tue, 21 Sep 2021 07:42:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.88..@fluentui/react-button_v9.0.0-alpha.89)

### Changes

- Updating to types over interfaces ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by <EMAIL>)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.69 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/react-tabster to v9.0.0-alpha.63 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-alpha.51 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.44 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/react-conformance-make-styles to v9.0.0-alpha.13 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/react-menu to v9.0.0-alpha.80 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)

## [9.0.0-alpha.88](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.88)

Mon, 20 Sep 2021 07:36:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.87..@fluentui/react-button_v9.0.0-alpha.88)

### Changes

- SplitButton: Re-introducing SplitButton using the latest version of makeStyles. ([PR #19844](https://github.com/microsoft/fluentui/pull/19844) by <EMAIL>)
- Bump @fluentui/react-button to v9.0.0-alpha.88 ([PR #19844](https://github.com/microsoft/fluentui/pull/19844) by <EMAIL>)

## [9.0.0-alpha.87](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.87)

Fri, 17 Sep 2021 07:35:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.86..@fluentui/react-button_v9.0.0-alpha.87)

### Changes

- Modifying MenuButton to adhere to MenuTrigger changes. ([PR #19840](https://github.com/microsoft/fluentui/pull/19840) by <EMAIL>)
- Updating API comments to be more descriptive. ([PR #19840](https://github.com/microsoft/fluentui/pull/19840) by <EMAIL>)
- Bump @fluentui/react-button to v9.0.0-alpha.87 ([PR #19840](https://github.com/microsoft/fluentui/pull/19840) by <EMAIL>)

## [9.0.0-alpha.86](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.86)

Thu, 16 Sep 2021 07:38:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.85..@fluentui/react-button_v9.0.0-alpha.86)

### Changes

- Bump @fluentui/react-button to v9.0.0-alpha.86 ([PR #19815](https://github.com/microsoft/fluentui/pull/19815) by <EMAIL>)

## [9.0.0-alpha.85](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.85)

Tue, 14 Sep 2021 20:09:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.84..@fluentui/react-button_v9.0.0-alpha.85)

### Changes

- Bump @fluentui/react-button to v9.0.0-alpha.85 ([PR #19155](https://github.com/microsoft/fluentui/pull/19155) by <EMAIL>)

## [9.0.0-alpha.84](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.84)

Fri, 10 Sep 2021 16:31:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.83..@fluentui/react-button_v9.0.0-alpha.84)

### Changes

- chore(v9): Move all internal v9 dependencies from caret to fixed version ([PR #19748](https://github.com/microsoft/fluentui/pull/19748) by <EMAIL>)
- Bump @fluentui/react-button to v9.0.0-alpha.84 ([PR #19748](https://github.com/microsoft/fluentui/pull/19748) by <EMAIL>)

## [9.0.0-alpha.83](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.83)

Fri, 10 Sep 2021 07:39:51 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.82..@fluentui/react-button_v9.0.0-alpha.83)

### Changes

- Bump @fluentui/react-menu to v9.0.0-alpha.74 ([PR #19642](https://github.com/microsoft/fluentui/pull/19642) by <EMAIL>)

## [9.0.0-alpha.82](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.82)

Wed, 08 Sep 2021 07:34:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.81..@fluentui/react-button_v9.0.0-alpha.82)

### Changes

- Bump @fluentui/react-menu to v9.0.0-alpha.73 ([PR #19661](https://github.com/microsoft/fluentui/pull/19661) by <EMAIL>)

## [9.0.0-alpha.81](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.81)

Mon, 06 Sep 2021 07:34:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.80..@fluentui/react-button_v9.0.0-alpha.81)

### Changes

- refactor(Button): use `event.key` for keyboard event ([PR #19638](https://github.com/microsoft/fluentui/pull/19638) by <EMAIL>)

## [9.0.0-alpha.80](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.80)

Fri, 03 Sep 2021 00:13:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.79..@fluentui/react-button_v9.0.0-alpha.80)

### Changes

- MenuButton: Cleaning up implementation and adhering to MenuTrigger contract. ([PR #19612](https://github.com/microsoft/fluentui/pull/19612) by <EMAIL>)

## [9.0.0-alpha.79](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.79)

Thu, 02 Sep 2021 07:36:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.78..@fluentui/react-button_v9.0.0-alpha.79)

### Patches

- Bump @fluentui/react-conformance to v0.4.5 ([PR #19590](https://github.com/microsoft/fluentui/pull/19590) by <EMAIL>)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.43 ([PR #19065](https://github.com/microsoft/fluentui/pull/19065) by <EMAIL>)

## [9.0.0-alpha.78](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.78)

Wed, 01 Sep 2021 07:39:56 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.77..@fluentui/react-button_v9.0.0-alpha.78)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.43 ([PR #19483](https://github.com/microsoft/fluentui/pull/19483) by <EMAIL>)

## [9.0.0-alpha.77](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.77)

Tue, 31 Aug 2021 07:37:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.76..@fluentui/react-button_v9.0.0-alpha.77)

### Changes

- Button: Adding type=button to all button components, removing @docCategory tags from API and fixing some documentation in the comments. ([PR #19564](https://github.com/microsoft/fluentui/pull/19564) by <EMAIL>)

## [9.0.0-alpha.76](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.76)

Mon, 30 Aug 2021 07:35:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.75..@fluentui/react-button_v9.0.0-alpha.76)

### Changes

- Updating TypeScript type-only imports/exports to use import/export type syntax. ([PR #19485](https://github.com/microsoft/fluentui/pull/19485) by <EMAIL>)

## [9.0.0-alpha.75](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.75)

Fri, 27 Aug 2021 07:33:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.74..@fluentui/react-button_v9.0.0-alpha.75)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.40 ([PR #19462](https://github.com/microsoft/fluentui/pull/19462) by <EMAIL>)

## [9.0.0-alpha.74](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.74)

Thu, 26 Aug 2021 07:35:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.73..@fluentui/react-button_v9.0.0-alpha.74)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.39 ([PR #19486](https://github.com/microsoft/fluentui/pull/19486) by <EMAIL>)

## [9.0.0-alpha.73](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.73)

Fri, 20 Aug 2021 07:37:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.72..@fluentui/react-button_v9.0.0-alpha.73)

### Changes

- Update .npmignore ([PR #19441](https://github.com/microsoft/fluentui/pull/19441) by <EMAIL>)

## [9.0.0-alpha.72](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.72)

Thu, 19 Aug 2021 07:41:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.71..@fluentui/react-button_v9.0.0-alpha.72)

### Changes

- fix default styling for button on focus ([PR #19440](https://github.com/microsoft/fluentui/pull/19440) by <EMAIL>)

## [9.0.0-alpha.71](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.71)

Wed, 18 Aug 2021 07:33:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.70..@fluentui/react-button_v9.0.0-alpha.71)

### Changes

- Update hc focus styling for vNext button ([PR #19408](https://github.com/microsoft/fluentui/pull/19408) by <EMAIL>)

## [9.0.0-alpha.70](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.70)

Fri, 13 Aug 2021 07:36:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.69..@fluentui/react-button_v9.0.0-alpha.70)

### Changes

- Button: Adding font family styles. ([PR #19353](https://github.com/microsoft/fluentui/pull/19353) by <EMAIL>)

## [9.0.0-alpha.69](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.69)

Wed, 11 Aug 2021 07:34:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.68..@fluentui/react-button_v9.0.0-alpha.69)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.35 ([PR #19256](https://github.com/microsoft/fluentui/pull/19256) by <EMAIL>)

## [9.0.0-alpha.68](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.68)

Fri, 06 Aug 2021 07:35:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.67..@fluentui/react-button_v9.0.0-alpha.68)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.34 ([PR #19281](https://github.com/microsoft/fluentui/pull/19281) by <EMAIL>)

## [9.0.0-alpha.67](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.67)

Tue, 03 Aug 2021 07:39:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.66..@fluentui/react-button_v9.0.0-alpha.67)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.3 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)
- Bump @fluentui/a11y-testing to v0.1.0 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)
- Bump @fluentui/keyboard-key to v0.3.4 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.4 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)

## [9.0.0-alpha.66](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.66)

Mon, 02 Aug 2021 07:36:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.65..@fluentui/react-button_v9.0.0-alpha.66)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.36 ([PR #19204](https://github.com/microsoft/fluentui/pull/19204) by <EMAIL>)

## [9.0.0-alpha.65](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.65)

Mon, 26 Jul 2021 07:37:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.64..@fluentui/react-button_v9.0.0-alpha.65)

### Changes

- Bump @fluentui/react-tabster to v9.0.0-alpha.46 ([PR #18968](https://github.com/microsoft/fluentui/pull/18968) by <EMAIL>)

## [9.0.0-alpha.64](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.64)

Fri, 23 Jul 2021 07:38:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.63..@fluentui/react-button_v9.0.0-alpha.64)

### Changes

- Fix button jump effect on border disparities ([PR #19035](https://github.com/microsoft/fluentui/pull/19035) by <EMAIL>)
- Update theme tokens ([PR #19041](https://github.com/microsoft/fluentui/pull/19041) by <EMAIL>)
- Button: Handling children correctly and not as a slot. ([PR #19085](https://github.com/microsoft/fluentui/pull/19085) by <EMAIL>)

## [9.0.0-alpha.63](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.63)

Thu, 22 Jul 2021 07:36:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.62..@fluentui/react-button_v9.0.0-alpha.63)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.29 ([PR #19038](https://github.com/microsoft/fluentui/pull/19038) by <EMAIL>)

## [9.0.0-alpha.62](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.62)

Tue, 20 Jul 2021 22:23:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.61..@fluentui/react-button_v9.0.0-alpha.62)

### Changes

- Button: Migrate to new dx ([PR #18607](https://github.com/microsoft/fluentui/pull/18607) by <EMAIL>)

## [9.0.0-alpha.61](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.61)

Fri, 16 Jul 2021 22:53:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.60..@fluentui/react-button_v9.0.0-alpha.61)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.26 ([PR #18973](https://github.com/microsoft/fluentui/pull/18973) by <EMAIL>)

## [9.0.0-alpha.60](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.60)

Thu, 15 Jul 2021 07:36:18 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.59..@fluentui/react-button_v9.0.0-alpha.60)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.33 ([PR #18861](https://github.com/microsoft/fluentui/pull/18861) by <EMAIL>)

## [9.0.0-alpha.59](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.59)

Tue, 13 Jul 2021 22:32:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.58..@fluentui/react-button_v9.0.0-alpha.59)

### Patches

- Bump @fluentui/react-conformance to v0.4.3 ([PR #18925](https://github.com/microsoft/fluentui/pull/18925) by <EMAIL>)

## [9.0.0-alpha.58](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.58)

Tue, 13 Jul 2021 07:35:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.57..@fluentui/react-button_v9.0.0-alpha.58)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.24 ([PR #18560](https://github.com/microsoft/fluentui/pull/18560) by <EMAIL>)

## [9.0.0-alpha.57](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.57)

Mon, 12 Jul 2021 07:33:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.56..@fluentui/react-button_v9.0.0-alpha.57)

### Changes

- Button: Adding back block, circular, disabledFocusable and outline props and making some styling fixes. ([PR #18563](https://github.com/microsoft/fluentui/pull/18563) by <EMAIL>)

## [9.0.0-alpha.56](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.56)

Fri, 09 Jul 2021 07:39:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.55..@fluentui/react-button_v9.0.0-alpha.56)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.2 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/a11y-testing to v0.1.0 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/keyboard-key to v0.3.3 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.2 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)

## [9.0.0-alpha.55](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.55)

Fri, 02 Jul 2021 23:15:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.54..@fluentui/react-button_v9.0.0-alpha.55)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.22 ([PR #18816](https://github.com/microsoft/fluentui/pull/18816) by <EMAIL>)

## [9.0.0-alpha.54](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.54)

Fri, 02 Jul 2021 07:37:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.53..@fluentui/react-button_v9.0.0-alpha.54)

### Changes

- Rename typings and getSlots to have the Compat Suffix ([PR #18796](https://github.com/microsoft/fluentui/pull/18796) by <EMAIL>)

## [9.0.0-alpha.53](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.53)

Thu, 01 Jul 2021 07:35:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.52..@fluentui/react-button_v9.0.0-alpha.53)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.20 ([PR #18680](https://github.com/microsoft/fluentui/pull/18680) by <EMAIL>)

## [9.0.0-alpha.52](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.52)

Wed, 30 Jun 2021 07:38:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.51..@fluentui/react-button_v9.0.0-alpha.52)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.19 ([PR #18695](https://github.com/microsoft/fluentui/pull/18695) by <EMAIL>)

## [9.0.0-alpha.51](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.51)

Tue, 29 Jun 2021 07:33:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.50..@fluentui/react-button_v9.0.0-alpha.51)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.18 ([PR #18169](https://github.com/microsoft/fluentui/pull/18169) by <EMAIL>)

## [9.0.0-alpha.50](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.50)

Wed, 23 Jun 2021 07:31:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.49..@fluentui/react-button_v9.0.0-alpha.50)

### Changes

- react-button: Removing last remnants of legacy patterns. ([PR #18519](https://github.com/microsoft/fluentui/pull/18519) by <EMAIL>)

## [9.0.0-alpha.49](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.49)

Tue, 22 Jun 2021 07:35:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.48..@fluentui/react-button_v9.0.0-alpha.49)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.17 ([PR #18397](https://github.com/microsoft/fluentui/pull/18397) by <EMAIL>)

## [9.0.0-alpha.48](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.48)

Tue, 15 Jun 2021 07:40:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.47..@fluentui/react-button_v9.0.0-alpha.48)

### Changes

- Temporary typing fix for children prop ([PR #18521](https://github.com/microsoft/fluentui/pull/18521) by <EMAIL>)

## [9.0.0-alpha.47](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.47)

Fri, 11 Jun 2021 07:34:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.46..@fluentui/react-button_v9.0.0-alpha.47)

### Changes

- CompoundButton: Removing legacy patterns from converged component. ([PR #18497](https://github.com/microsoft/fluentui/pull/18497) by <EMAIL>)
- ToggleButton: Removing legacy patterns from converged component. ([PR #18506](https://github.com/microsoft/fluentui/pull/18506) by <EMAIL>)
- MenuButton: Removing legacy patterns from converged component. ([PR #18498](https://github.com/microsoft/fluentui/pull/18498) by <EMAIL>)

## [9.0.0-alpha.46](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.46)

Thu, 10 Jun 2021 07:32:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.45..@fluentui/react-button_v9.0.0-alpha.46)

### Changes

- Button: Removing legacy patterns from converged component. ([PR #18468](https://github.com/microsoft/fluentui/pull/18468) by <EMAIL>)

## [9.0.0-alpha.45](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.45)

Wed, 09 Jun 2021 07:33:38 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.44..@fluentui/react-button_v9.0.0-alpha.45)

### Changes

- Adds focus indicator style to react-button ([PR #18400](https://github.com/microsoft/fluentui/pull/18400) by <EMAIL>)

## [9.0.0-alpha.44](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.44)

Mon, 07 Jun 2021 07:38:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.43..@fluentui/react-button_v9.0.0-alpha.44)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.1 ([PR #18437](https://github.com/microsoft/fluentui/pull/18437) by <EMAIL>)
- Bump @fluentui/a11y-testing to v0.1.0 ([PR #18437](https://github.com/microsoft/fluentui/pull/18437) by <EMAIL>)
- Bump @fluentui/keyboard-key to v0.3.2 ([PR #18437](https://github.com/microsoft/fluentui/pull/18437) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.1 ([PR #18437](https://github.com/microsoft/fluentui/pull/18437) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #18437](https://github.com/microsoft/fluentui/pull/18437) by <EMAIL>)

## [9.0.0-alpha.43](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.43)

Fri, 04 Jun 2021 07:37:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.42..@fluentui/react-button_v9.0.0-alpha.43)

### Changes

- MenuButton: Re-introducing MenuButton using the latest version of makeStyles. ([PR #18168](https://github.com/microsoft/fluentui/pull/18168) by <EMAIL>)

## [9.0.0-alpha.42](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.42)

Thu, 03 Jun 2021 07:36:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.41..@fluentui/react-button_v9.0.0-alpha.42)

### Changes

- Use updated theme tokens in component styles ([PR #18321](https://github.com/microsoft/fluentui/pull/18321) by <EMAIL>)

## [9.0.0-alpha.41](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.41)

Wed, 02 Jun 2021 07:37:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.40..@fluentui/react-button_v9.0.0-alpha.41)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.12 ([PR #18404](https://github.com/microsoft/fluentui/pull/18404) by <EMAIL>)

## [9.0.0-alpha.40](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.40)

Wed, 26 May 2021 07:35:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.39..@fluentui/react-button_v9.0.0-alpha.40)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.25 ([PR #18323](https://github.com/microsoft/fluentui/pull/18323) by <EMAIL>)

## [9.0.0-alpha.39](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.39)

Fri, 21 May 2021 07:34:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.38..@fluentui/react-button_v9.0.0-alpha.39)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.24 ([PR #18259](https://github.com/microsoft/fluentui/pull/18259) by <EMAIL>)

## [9.0.0-alpha.38](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.38)

Thu, 20 May 2021 07:41:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.37..@fluentui/react-button_v9.0.0-alpha.38)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.0 ([PR #18024](https://github.com/microsoft/fluentui/pull/18024) by <EMAIL>)
- Bump @fluentui/a11y-testing to v0.1.0 ([PR #18024](https://github.com/microsoft/fluentui/pull/18024) by <EMAIL>)
- Bump @fluentui/keyboard-key to v0.3.1 ([PR #18024](https://github.com/microsoft/fluentui/pull/18024) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.0 ([PR #17577](https://github.com/microsoft/fluentui/pull/17577) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #18024](https://github.com/microsoft/fluentui/pull/18024) by <EMAIL>)

## [9.0.0-alpha.37](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.37)

Wed, 19 May 2021 07:34:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.36..@fluentui/react-button_v9.0.0-alpha.37)

### Patches

- Bump @fluentui/react-conformance to v0.3.1 ([PR #18194](https://github.com/microsoft/fluentui/pull/18194) by <EMAIL>)

### Changes

- chore: add more Babel plugins ([PR #18037](https://github.com/microsoft/fluentui/pull/18037) by <EMAIL>)

## [9.0.0-alpha.36](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.36)

Tue, 18 May 2021 07:34:38 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.35..@fluentui/react-button_v9.0.0-alpha.36)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.8 ([PR #18171](https://github.com/microsoft/fluentui/pull/18171) by <EMAIL>)

## [9.0.0-alpha.35](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.35)

Thu, 13 May 2021 07:36:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.34..@fluentui/react-button_v9.0.0-alpha.35)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.7 ([PR #18039](https://github.com/microsoft/fluentui/pull/18039) by <EMAIL>)

## [9.0.0-alpha.34](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.34)

Wed, 12 May 2021 07:36:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.33..@fluentui/react-button_v9.0.0-alpha.34)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.6 ([PR #18097](https://github.com/microsoft/fluentui/pull/18097) by <EMAIL>)

## [9.0.0-alpha.33](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.33)

Mon, 10 May 2021 07:36:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.32..@fluentui/react-button_v9.0.0-alpha.33)

### Changes

- Bump @fluentui/babel-make-styles to v9.0.0-alpha.5 ([PR #18095](https://github.com/microsoft/fluentui/pull/18095) by <EMAIL>)

## [9.0.0-alpha.32](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.32)

Wed, 05 May 2021 07:36:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.31..@fluentui/react-button_v9.0.0-alpha.32)

### Changes

- transform styles with Babel plugin ([PR #16534](https://github.com/microsoft/fluentui/pull/16534) by <EMAIL>)

## [9.0.0-alpha.31](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.31)

Mon, 03 May 2021 07:45:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.30..@fluentui/react-button_v9.0.0-alpha.31)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.21 ([PR #18005](https://github.com/microsoft/fluentui/pull/18005) by <EMAIL>)

## [9.0.0-alpha.30](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.30)

Fri, 30 Apr 2021 07:42:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.29..@fluentui/react-button_v9.0.0-alpha.30)

### Patches

- Bump @fluentui/eslint-plugin to v1.2.0 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)
- Bump @fluentui/a11y-testing to v0.1.0 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)
- Bump @fluentui/keyboard-key to v0.3.0 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.3.0 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)

### Changes

- Upgrade typescript to 4.1.5 ([PR #17932](https://github.com/microsoft/fluentui/pull/17932) by <EMAIL>)

## [9.0.0-alpha.29](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.29)

Tue, 27 Apr 2021 07:34:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.28..@fluentui/react-button_v9.0.0-alpha.29)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.5 ([PR #17922](https://github.com/microsoft/fluentui/pull/17922) by <EMAIL>)

## [9.0.0-alpha.28](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.28)

Mon, 26 Apr 2021 07:34:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.27..@fluentui/react-button_v9.0.0-alpha.28)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.4 ([PR #17936](https://github.com/microsoft/fluentui/pull/17936) by <EMAIL>)

## [9.0.0-alpha.27](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.27)

Fri, 23 Apr 2021 07:37:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.26..@fluentui/react-button_v9.0.0-alpha.27)

### Patches

- Bump @fluentui/eslint-plugin to v1.1.1 ([PR #17894](https://github.com/microsoft/fluentui/pull/17894) by <EMAIL>)
- Bump @fluentui/a11y-testing to v0.1.0 ([PR #17894](https://github.com/microsoft/fluentui/pull/17894) by <EMAIL>)
- Bump @fluentui/keyboard-key to v0.2.17 ([PR #17894](https://github.com/microsoft/fluentui/pull/17894) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.2.6 ([PR #17894](https://github.com/microsoft/fluentui/pull/17894) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #17894](https://github.com/microsoft/fluentui/pull/17894) by <EMAIL>)

## [9.0.0-alpha.26](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.26)

Thu, 22 Apr 2021 07:33:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.25..@fluentui/react-button_v9.0.0-alpha.26)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.2 ([PR #17826](https://github.com/microsoft/fluentui/pull/17826) by <EMAIL>)

## [9.0.0-alpha.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.25)

Wed, 21 Apr 2021 07:31:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.24..@fluentui/react-button_v9.0.0-alpha.25)

### Changes

- Rename ax() to mergeClasses() ([PR #17875](https://github.com/microsoft/fluentui/pull/17875) by <EMAIL>)

## [9.0.0-alpha.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.24)

Tue, 20 Apr 2021 07:31:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.23..@fluentui/react-button_v9.0.0-alpha.24)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.23 ([PR #17855](https://github.com/microsoft/fluentui/pull/17855) by <EMAIL>)

## [9.0.0-alpha.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.23)

Fri, 16 Apr 2021 18:08:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.22..@fluentui/react-button_v9.0.0-alpha.23)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.22 ([PR #17840](https://github.com/microsoft/fluentui/pull/17840) by <EMAIL>)

## [9.0.0-alpha.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.22)

Wed, 14 Apr 2021 07:34:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.21..@fluentui/react-button_v9.0.0-alpha.22)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.21 ([PR #17707](https://github.com/microsoft/fluentui/pull/17707) by <EMAIL>)

## [9.0.0-alpha.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.21)

Fri, 09 Apr 2021 07:31:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.20..@fluentui/react-button_v9.0.0-alpha.21)

### Changes

- Button: Re-introducing transparent variant using latest version of makeStyles. ([PR #17736](https://github.com/microsoft/fluentui/pull/17736) by <EMAIL>)

## [9.0.0-alpha.20](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.20)

Thu, 08 Apr 2021 07:33:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.19..@fluentui/react-button_v9.0.0-alpha.20)

### Changes

- Button: Re-introducing subtle variant using latest version of makeStyles. ([PR #17676](https://github.com/microsoft/fluentui/pull/17676) by <EMAIL>)

## [9.0.0-alpha.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.19)

Thu, 01 Apr 2021 20:13:37 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.18..@fluentui/react-button_v9.0.0-alpha.19)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.18 ([PR #17651](https://github.com/microsoft/fluentui/pull/17651) by <EMAIL>)

## [9.0.0-alpha.18](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.18)

Wed, 31 Mar 2021 00:53:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.17..@fluentui/react-button_v9.0.0-alpha.18)

### Patches

- Bump @fluentui/eslint-plugin to v1.1.0 ([PR #17568](https://github.com/microsoft/fluentui/pull/17568) by <EMAIL>)
- Bump @fluentui/a11y-testing to v0.1.0 ([PR #17568](https://github.com/microsoft/fluentui/pull/17568) by <EMAIL>)
- Bump @fluentui/keyboard-key to v0.2.16 ([PR #17568](https://github.com/microsoft/fluentui/pull/17568) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.2.5 ([PR #17568](https://github.com/microsoft/fluentui/pull/17568) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #17568](https://github.com/microsoft/fluentui/pull/17568) by <EMAIL>)

### Changes

- mergeProps was updated to improve type checking; use compat layer until type errors can be fixed ([PR #17508](https://github.com/microsoft/fluentui/pull/17508) by <EMAIL>)

## [9.0.0-alpha.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.17)

Tue, 30 Mar 2021 07:34:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.16..@fluentui/react-button_v9.0.0-alpha.17)

### Patches

- Bump @fluentui/keyboard-key to v0.2.15 ([PR #17584](https://github.com/microsoft/fluentui/pull/17584) by <EMAIL>)

### Changes

- chore: restore "sideEffects" to enable treeshaking ([PR #17584](https://github.com/microsoft/fluentui/pull/17584) by <EMAIL>)

## [9.0.0-alpha.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.16)

Fri, 26 Mar 2021 07:32:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.15..@fluentui/react-button_v9.0.0-alpha.16)

### Changes

- ToggleButton: Re-introducing ToggleButton using the latest version of makeStyles. ([PR #17566](https://github.com/microsoft/fluentui/pull/17566) by <EMAIL>)
- Button: Beefing up accessibility tests and cleaning up state management. ([PR #17155](https://github.com/microsoft/fluentui/pull/17155) by <EMAIL>)

## [9.0.0-alpha.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.15)

Thu, 25 Mar 2021 07:33:24 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.14..@fluentui/react-button_v9.0.0-alpha.15)

### Changes

- Button: Adding SPEC.md and MIGRATION.md and updating README.md ([PR #17192](https://github.com/microsoft/fluentui/pull/17192) by <EMAIL>)
- CompoundButton: Re-introducing CompoundButton using latest version of makeStyles. ([PR #17534](https://github.com/microsoft/fluentui/pull/17534) by <EMAIL>)

## [9.0.0-alpha.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.14)

Tue, 23 Mar 2021 07:31:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.13..@fluentui/react-button_v9.0.0-alpha.14)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.10 ([PR #17339](https://github.com/microsoft/fluentui/pull/17339) by <EMAIL>)

## [9.0.0-alpha.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.13)

Thu, 18 Mar 2021 20:15:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.12..@fluentui/react-button_v9.0.0-alpha.13)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.9 ([PR #17387](https://github.com/microsoft/fluentui/pull/17387) by <EMAIL>)

## [9.0.0-alpha.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.12)

Wed, 17 Mar 2021 07:35:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.11..@fluentui/react-button_v9.0.0-alpha.12)

### Changes

- Button: Updates styles from makeStylesCompat to makeStyles. ([PR #17415](https://github.com/microsoft/fluentui/pull/17415) by <EMAIL>)

## [9.0.0-alpha.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.11)

Tue, 16 Mar 2021 07:32:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.10..@fluentui/react-button_v9.0.0-alpha.11)

### Patches

- Bump @fluentui/react-conformance to v0.2.4 ([PR #17418](https://github.com/microsoft/fluentui/pull/17418) by <EMAIL>)

## [9.0.0-alpha.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.10)

Mon, 15 Mar 2021 07:36:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.9..@fluentui/react-button_v9.0.0-alpha.10)

### Changes

- Remove set-version references ([PR #17381](https://github.com/microsoft/fluentui/pull/17381) by <EMAIL>)

## [9.0.0-alpha.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.9)

Fri, 12 Mar 2021 20:04:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.8..@fluentui/react-button_v9.0.0-alpha.9)

### Patches

- Bump @fluentui/react-conformance to v0.2.3 ([PR #17161](https://github.com/microsoft/fluentui/pull/17161) by <EMAIL>)

### Changes

- Change makeStyles to makeStylesCompat ([PR #17354](https://github.com/microsoft/fluentui/pull/17354) by <EMAIL>)

## [9.0.0-alpha.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.8)

Wed, 10 Mar 2021 07:34:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.7..@fluentui/react-button_v9.0.0-alpha.8)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.7 ([PR #17293](https://github.com/microsoft/fluentui/pull/17293) by <EMAIL>)

## [9.0.0-alpha.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.7)

Fri, 05 Mar 2021 20:30:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.6..@fluentui/react-button_v9.0.0-alpha.7)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.6 ([PR #17273](https://github.com/microsoft/fluentui/pull/17273) by <EMAIL>)

## [9.0.0-alpha.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.6)

Wed, 03 Mar 2021 00:10:09 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.5..@fluentui/react-button_v9.0.0-alpha.6)

### Patches

- Bump @fluentui/common-styles to v1.0.2 ([PR #17246](https://github.com/microsoft/fluentui/pull/17246) by <EMAIL>)

### Changes

- Remove set-version dependency from converged components ([PR #17211](https://github.com/microsoft/fluentui/pull/17211) by <EMAIL>)

## [9.0.0-alpha.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.5)

Tue, 02 Mar 2021 07:24:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.4..@fluentui/react-button_v9.0.0-alpha.5)

### Changes

- remove unused dependencies ([PR #17197](https://github.com/microsoft/fluentui/pull/17197) by <EMAIL>)

## [9.0.0-alpha.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.4)

Fri, 26 Feb 2021 01:16:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.3..@fluentui/react-button_v9.0.0-alpha.4)

### Patches

- Bump @fluentui/common-styles to v1.0.1 ([PR #17169](https://github.com/microsoft/fluentui/pull/17169) by <EMAIL>)
- Bump @fluentui/eslint-plugin to v1.0.1 ([PR #17169](https://github.com/microsoft/fluentui/pull/17169) by <EMAIL>)
- Bump @fluentui/a11y-testing to v0.1.0 ([PR #17169](https://github.com/microsoft/fluentui/pull/17169) by <EMAIL>)
- Bump @fluentui/keyboard-key to v0.2.14 ([PR #17169](https://github.com/microsoft/fluentui/pull/17169) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.2.2 ([PR #17169](https://github.com/microsoft/fluentui/pull/17169) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #17169](https://github.com/microsoft/fluentui/pull/17169) by <EMAIL>)
- Bump @fluentui/react-icons-mdl2 to v1.0.1 ([PR #17169](https://github.com/microsoft/fluentui/pull/17169) by <EMAIL>)
- Bump @fluentui/set-version to v8.0.1 ([PR #17169](https://github.com/microsoft/fluentui/pull/17169) by <EMAIL>)

### Changes

- Update references to major-bumped packages ([PR #17169](https://github.com/microsoft/fluentui/pull/17169) by <EMAIL>)

## [9.0.0-alpha.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.3)

Thu, 25 Feb 2021 20:16:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.2..@fluentui/react-button_v9.0.0-alpha.3)

### Changes

- Trim react-button down to converged styles and api ([PR #17060](https://github.com/microsoft/fluentui/pull/17060) by <EMAIL>)

## [9.0.0-alpha.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.2)

Thu, 25 Feb 2021 01:15:27 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v9.0.0-alpha.1..@fluentui/react-button_v9.0.0-alpha.2)

### Patches

- Bump @fluentui/react-conformance to v0.2.1 ([PR #17118](https://github.com/microsoft/fluentui/pull/17118) by <EMAIL>)

## [9.0.0-alpha.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v9.0.0-alpha.1)

Wed, 24 Feb 2021 00:05:29 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.35..@fluentui/react-button_v9.0.0-alpha.1)

### Changes

- bump version to v9 ([PR #17093](https://github.com/microsoft/fluentui/pull/17093) by <EMAIL>)

## [1.0.0-beta.35](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.35)

Mon, 22 Feb 2021 12:26:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.34..@fluentui/react-button_v1.0.0-beta.35)

### Changes

- Breaks dependency on react-hooks for convergence ([PR #17091](https://github.com/microsoft/fluentui/pull/17091) by <EMAIL>)

## [1.0.0-beta.34](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.34)

Thu, 18 Feb 2021 19:38:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.33..@fluentui/react-button_v1.0.0-beta.34)

### Patches

- Bump @fluentui/react-utilities to v0.3.1 ([PR #17048](https://github.com/microsoft/fluentui/pull/17048) by <EMAIL>)

### Changes

- Allow React 17 in peerDependencies ([PR #17048](https://github.com/microsoft/fluentui/pull/17048) by <EMAIL>)
- Button: Renaming ghost prop to subtle. ([PR #17058](https://github.com/microsoft/fluentui/pull/17058) by <EMAIL>)

## [1.0.0-beta.33](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.33)

Thu, 18 Feb 2021 12:27:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.32..@fluentui/react-button_v1.0.0-beta.33)

### Changes

- use @fluentui/react-utilities instead of @fluentui/react-utils ([PR #16970](https://github.com/microsoft/fluentui/pull/16970) by <EMAIL>)
- Cleaning up state function. ([PR #16943](https://github.com/microsoft/fluentui/pull/16943) by <EMAIL>)
- SplitButton: Adding the ability to open the menu while focusing on the primary action button by pressing Alt+Down. ([PR #17021](https://github.com/microsoft/fluentui/pull/17021) by <EMAIL>)

## [1.0.0-beta.32](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.32)

Mon, 15 Feb 2021 12:22:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.31..@fluentui/react-button_v1.0.0-beta.32)

### Changes

- remove unused temporary types ([PR #16964](https://github.com/microsoft/fluentui/pull/16964) by <EMAIL>)

## [1.0.0-beta.31](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.31)

Fri, 12 Feb 2021 12:26:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.30..@fluentui/react-button_v1.0.0-beta.31)

### Patches

- Bump @fluentui/react-utils to v0.3.1 ([PR #16945](https://github.com/microsoft/fluentui/pull/16945) by <EMAIL>)

### Changes

- Updated typings ([PR #16902](https://github.com/microsoft/fluentui/pull/16902) by <EMAIL>)
- Remove theming logic/interface which are unrelated to v8 release from @fluentui/theme package.. ([PR #16935](https://github.com/microsoft/fluentui/pull/16935) by <EMAIL>)

## [1.0.0-beta.30](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.30)

Thu, 11 Feb 2021 00:58:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.29..@fluentui/react-button_v1.0.0-beta.30)

### Changes

- Bump @fluentui/react-icons-mdl2 to v1.0.0-beta.13 ([PR #16911](https://github.com/microsoft/fluentui/pull/16911) by <EMAIL>)

## [1.0.0-beta.29](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.29)

Tue, 09 Feb 2021 00:56:52 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.28..@fluentui/react-button_v1.0.0-beta.29)

### Patches

- Bump @fluentui/react-utils to v0.3.0 ([PR #16865](https://github.com/microsoft/fluentui/pull/16865) by <EMAIL>)

### Changes

- MenuButton: Adding persistMenu prop to allow for constant rendering of the Menu. ([PR #16742](https://github.com/microsoft/fluentui/pull/16742) by <EMAIL>)

## [1.0.0-beta.28](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.28)

Mon, 08 Feb 2021 12:23:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.27..@fluentui/react-button_v1.0.0-beta.28)

### Changes

- Bump @fluentui/react-theme-provider to v1.0.0-beta.20 ([PR #16844](https://github.com/microsoft/fluentui/pull/16844) by <EMAIL>)

## [1.0.0-beta.27](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.27)

Wed, 03 Feb 2021 05:51:40 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.26..@fluentui/react-button_v1.0.0-beta.27)

### Changes

- SplitButton: Using menuButtonRef prop correctly when handling state. ([PR #16740](https://github.com/microsoft/fluentui/pull/16740) by <EMAIL>)

## [1.0.0-beta.26](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.26)

Tue, 02 Feb 2021 12:21:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.25..@fluentui/react-button_v1.0.0-beta.26)

### Changes

- adding accessibility related tests into react-button package ([PR #16711](https://github.com/microsoft/fluentui/pull/16711) by <EMAIL>)
- use a compat version of ThemeProvider ([PR #16729](https://github.com/microsoft/fluentui/pull/16729) by <EMAIL>)

## [1.0.0-beta.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.25)

Mon, 01 Feb 2021 12:23:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.24..@fluentui/react-button_v1.0.0-beta.25)

### Changes

- Extract compose/next to react-utils ([PR #16690](https://github.com/microsoft/fluentui/pull/16690) by <EMAIL>)

## [1.0.0-beta.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.24)

Thu, 28 Jan 2021 12:25:56 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.23..@fluentui/react-button_v1.0.0-beta.24)

### Changes

- Updating to webpack 5, latest typings, latest loaders and plugins. ([PR #16447](https://github.com/microsoft/fluentui/pull/16447) by <EMAIL>)
- ToggleButton: Replacing use of pressed semanticColors with checked semanticColors for checked states. ([PR #16597](https://github.com/microsoft/fluentui/pull/16597) by <EMAIL>)

## [1.0.0-beta.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.23)

Sat, 23 Jan 2021 05:20:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.22..@fluentui/react-button_v1.0.0-beta.23)

### Changes

- Button: Adding missing css variables for opacity in Button and borderColor in checked state of ToggleButton. ([PR #16554](https://github.com/microsoft/fluentui/pull/16554) by <EMAIL>)

## [1.0.0-beta.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.22)

Fri, 22 Jan 2021 12:24:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.21..@fluentui/react-button_v1.0.0-beta.22)

### Changes

- Button: Re-exporting MenuContext from react-button to avoid fragility in package versions usage. ([PR #16581](https://github.com/microsoft/fluentui/pull/16581) by <EMAIL>)

## [1.0.0-beta.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.21)

Thu, 21 Jan 2021 12:36:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.19..@fluentui/react-button_v1.0.0-beta.21)

### Changes

- Button: Enable pointer-events so that tooltip can be attached to disabledFocusable Button. ([PR #16508](https://github.com/microsoft/fluentui/pull/16508) by <EMAIL>)
- Updating dev dependencies. ([PR #16548](https://github.com/microsoft/fluentui/pull/16548) by <EMAIL>)

## [1.0.0-beta.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.19)

Thu, 14 Jan 2021 12:31:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.17..@fluentui/react-button_v1.0.0-beta.19)

### Changes

- Button: Adding disabledFocusable prop to allow for scenarios where Button has disabled styling but is focusable. ([PR #16418](https://github.com/microsoft/fluentui/pull/16418) by <EMAIL>)

## [1.0.0-beta.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.17)

Fri, 11 Dec 2020 12:28:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.14..@fluentui/react-button_v1.0.0-beta.17)

### Changes

- Update react-button readme to not say it's officially releasing with v8 ([PR #16198](https://github.com/microsoft/fluentui/pull/16198) by <EMAIL>)

## [1.0.0-beta.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.14)

Tue, 01 Dec 2020 12:38:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.13..@fluentui/react-button_v1.0.0-beta.14)

### Changes

- Moving comment to correct place. ([PR #16035](https://github.com/microsoft/fluentui/pull/16035) by <EMAIL>)

## [1.0.0-beta.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.13)

Mon, 23 Nov 2020 12:31:40 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.12..@fluentui/react-button_v1.0.0-beta.13)

### Changes

- Update variants to not use css variables for global tokens so button is themed without ThemeProvider ([PR #15976](https://github.com/microsoft/fluentui/pull/15976) by <EMAIL>)

## [1.0.0-beta.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.12)

Wed, 18 Nov 2020 12:30:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.11..@fluentui/react-button_v1.0.0-beta.12)

### Changes

- Updating button hooks to return state only; render function should be imported separately. ([PR #15961](https://github.com/microsoft/fluentui/pull/15961) by <EMAIL>)

## [1.0.0-beta.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.11)

Tue, 17 Nov 2020 12:36:24 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.9..@fluentui/react-button_v1.0.0-beta.11)

### Changes

- Updating arguments of style hook. ([PR #15952](https://github.com/microsoft/fluentui/pull/15952) by <EMAIL>)
- Updating classes hooks derived from Button to actually derive from Button. ([PR #15955](https://github.com/microsoft/fluentui/pull/15955) by <EMAIL>)

## [1.0.0-beta.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.9)

Fri, 06 Nov 2020 12:34:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.8..@fluentui/react-button_v1.0.0-beta.9)

### Changes

- Moving MenuContext outside of @fluentui/react-button and into @fluentui/react-shared-contexts. ([PR #15826](https://github.com/microsoft/fluentui/pull/15826) by <EMAIL>)

## [1.0.0-beta.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.8)

Thu, 05 Nov 2020 12:29:43 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.7..@fluentui/react-button_v1.0.0-beta.8)

### Changes

- Button: Removing unused scss files. ([PR #15835](https://github.com/microsoft/fluentui/pull/15835) by <EMAIL>)

## [1.0.0-beta.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.7)

Wed, 04 Nov 2020 12:34:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.4..@fluentui/react-button_v1.0.0-beta.7)

### Changes

- Button: Extrapolating expanded functionality in MenuButton to be provided via Context. ([PR #15780](https://github.com/microsoft/fluentui/pull/15780) by <EMAIL>)

## [1.0.0-beta.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.4)

Wed, 28 Oct 2020 12:32:24 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.1..@fluentui/react-button_v1.0.0-beta.4)

### Changes

- Updating use of makeVariantClasses and fixing typing bugs. ([PR #15518](https://github.com/microsoft/fluentui/pull/15518) by <EMAIL>)

## [1.0.0-beta.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.1)

Fri, 23 Oct 2020 23:25:40 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v1.0.0-beta.0..@fluentui/react-button_v1.0.0-beta.1)

### Changes

- Major bump react-compose package since it dependes on packages with new major. ([PR #15679](https://github.com/microsoft/fluentui/pull/15679) by <EMAIL>)

## [1.0.0-beta.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v1.0.0-beta.0)

Fri, 23 Oct 2020 03:26:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.14.3..@fluentui/react-button_v1.0.0-beta.0)

### Changes

- Updating styling to use variants. ([PR #15337](https://github.com/microsoft/fluentui/pull/15337) by <EMAIL>)
- Button: Adding high contrast styling. ([PR #15328](https://github.com/microsoft/fluentui/pull/15328) by <EMAIL>)
- Don't publish demo app; remove unused example-app-base dev dependency ([PR #15414](https://github.com/microsoft/fluentui/pull/15414) by <EMAIL>)
- Button: Adding transparent variant. ([PR #15463](https://github.com/microsoft/fluentui/pull/15463) by <EMAIL>)
- Button: Renaming `fluid` prop to `block` so that it is inline with the most used terminology in the web. ([PR #15513](https://github.com/microsoft/fluentui/pull/15513) by <EMAIL>)
- Rename @uifabric/styling to @fluentui/style-utilities ([PR #15567](https://github.com/microsoft/fluentui/pull/15567) by <EMAIL>)
- Adds docCategory Button to all type files so that they are picked up by api-docs. ([PR #15576](https://github.com/microsoft/fluentui/pull/15576) by <EMAIL>)
- Rename @fluentui/react-icons to @fluentui/react-icons-mdl2 ([PR #15599](https://github.com/microsoft/fluentui/pull/15599) by <EMAIL>)
- undefined ([PR #15585](https://github.com/microsoft/fluentui/pull/15585) by <EMAIL>)
- Rename @uifabric/set-version to @fluentui/set-version ([PR #15616](https://github.com/microsoft/fluentui/pull/15616) by <EMAIL>)
- Rename @uifabric/react-hooks to @fluentui/react-hooks & @uifabric/utilities to @fluentui/utilities ([PR #15629](https://github.com/microsoft/fluentui/pull/15629) by <EMAIL>)

## [0.14.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.14.3)

Sun, 27 Sep 2020 04:20:52 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.14.2..@fluentui/react-button_v0.14.3)

### Patches

- MenuButton: Removing office-ui-fabric-react dependency by having ContextualMenu as a JSX slot replacement instead of the default. ([PR #15257](https://github.com/microsoft/fluentui/pull/15257) by <EMAIL>)

## [0.14.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.14.2)

Fri, 25 Sep 2020 12:25:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.14.0..@fluentui/react-button_v0.14.2)

### Patches

- Add migration guide to readme ([PR #15128](https://github.com/microsoft/fluentui/pull/15128) by <EMAIL>)
- Moving examples to @fluentui/examples package. ([PR #15132](https://github.com/microsoft/fluentui/pull/15132) by <EMAIL>)

## [0.14.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.14.0)

Wed, 23 Sep 2020 12:27:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.13.1..@fluentui/react-button_v0.14.0)

### Minor changes

- Moving SizeValue const from react-button to theme package. ([PR #14206](https://github.com/microsoft/fluentui/pull/14206) by <EMAIL>)

### Patches

- set theme package version to be 1.0.0. ([PR #15174](https://github.com/microsoft/fluentui/pull/15174) by <EMAIL>)

## [0.13.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.13.1)

Thu, 17 Sep 2020 15:52:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.13.0..@fluentui/react-button_v0.13.1)

### Patches

- Fix button styles to match v7 buttons. ([PR #14972](https://github.com/microsoft/fluentui/pull/14972) by <EMAIL>)

## [0.13.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.13.0)

Thu, 17 Sep 2020 12:25:04 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.12.6..@fluentui/react-button_v0.13.0)

### Minor changes

- Updating color token references to use `color` prefix, and `accent` tokens have been renamed to `brand`. ([PR #15070](https://github.com/microsoft/fluentui/pull/15070) by <EMAIL>)

## [0.12.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.12.5)

Tue, 15 Sep 2020 12:26:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.12.4..@fluentui/react-button_v0.12.5)

### Patches

- Updated styling to use makeStyles. ([PR #14889](https://github.com/microsoft/fluentui/pull/14889) by <EMAIL>)

## [0.12.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.12.0)

Wed, 09 Sep 2020 12:24:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.11.6..@fluentui/react-button_v0.12.0)

### Minor changes

- CompoundButton: Adding initial component to react-button package. ([PR #14855](https://github.com/microsoft/fluentui/pull/14855) by <EMAIL>)

### Patches

- Fix rendering as anchor when href is passed. ([PR #14941](https://github.com/microsoft/fluentui/pull/14941) by <EMAIL>)

## [0.11.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.11.6)

Mon, 07 Sep 2020 12:26:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.11.5..@fluentui/react-button_v0.11.6)

### Patches

- Updated styling to use makeStyles. ([PR #14889](https://github.com/microsoft/fluentui/pull/14889) by <EMAIL>)

## [0.11.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.11.5)

Fri, 04 Sep 2020 12:28:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.11.0..@fluentui/react-button_v0.11.5)

### Patches

- Adding legacy startup for internal testing on IE11. ([PR #14567](https://github.com/microsoft/fluentui/pull/14567) by <EMAIL>)

## [0.11.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.11.0)

Fri, 28 Aug 2020 12:29:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.10.3..@fluentui/react-button_v0.11.0)

### Minor changes

- Button: Adding ghost prop for stealth styling. ([PR #14743](https://github.com/microsoft/fluentui/pull/14743) by <EMAIL>)

## [0.10.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.10.3)

Tue, 25 Aug 2020 12:36:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.10.1..@fluentui/react-button_v0.10.3)

### Patches

- Use new theme package. ([PR #14650](https://github.com/microsoft/fluentui/pull/14650) by <EMAIL>)

## [0.10.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.10.1)

Thu, 20 Aug 2020 12:37:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.10.0..@fluentui/react-button_v0.10.1)

### Patches

- Fix SplitButton styles ([PR #14612](https://github.com/microsoft/fluentui/pull/14612) by <EMAIL>)
- Remove references to React global (add explicit imports) ([PR #14613](https://github.com/microsoft/fluentui/pull/14613) by <EMAIL>)

## [0.10.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.10.0)

Wed, 19 Aug 2020 12:44:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.9.4..@fluentui/react-button_v0.10.0)

### Minor changes

- Change default theme to fluent; minor styles fixes; add Teams/Fluent themes; Convert v7 theme to tokens. ([PR #14500](https://github.com/microsoft/fluentui/pull/14500) by <EMAIL>)

## [0.9.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.9.4)

Tue, 18 Aug 2020 07:58:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.9.0..@fluentui/react-button_v0.9.4)

### Patches

- fixing publish ([PR #14566](https://github.com/microsoft/fluentui/pull/14566) by <EMAIL>)

## [0.9.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.9.0)

Tue, 11 Aug 2020 05:47:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.8.0..@fluentui/react-button_v0.9.0)

### Minor changes

- SplitButton: Initial implementation. ([PR #14018](https://github.com/microsoft/fluentui/pull/14018) by <EMAIL>)
- Adding opacity control for button tokens. ([PR #14455](https://github.com/microsoft/fluentui/pull/14455) by <EMAIL>)

## [0.8.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.8.0)

Mon, 10 Aug 2020 06:19:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.7.14..@fluentui/react-button_v0.8.0)

### Minor changes

- Button: removing compose usage in favor of hooks and factories. ([PR #14274](https://github.com/microsoft/fluentui/pull/14274) by <EMAIL>)

## [0.7.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.7.9)

Mon, 27 Jul 2020 12:42:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.7.3..@fluentui/react-button_v0.7.9)

### Patches

- Update react-button to use common-styles ([PR #14189](https://github.com/microsoft/fluentui/pull/14189) by <EMAIL>)

## [0.7.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.7.3)

Fri, 17 Jul 2020 12:38:41 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.7.1..@fluentui/react-button_v0.7.3)

### Patches

- fix: use a proper ref in useButton hook ([PR #14063](https://github.com/microsoft/fluentui/pull/14063) by <EMAIL>)

## [0.7.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.7.0)

Thu, 09 Jul 2020 21:59:04 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.6.2..@fluentui/react-button_v0.7.0)

### Minor changes

- MenuButton: Creating component using compose. ([PR #13812](https://github.com/microsoft/fluentui/pull/13812) by <EMAIL>)

## [0.6.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.6.2)

Thu, 02 Jul 2020 12:42:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.6.0..@fluentui/react-button_v0.6.2)

### Patches

- Adding bundle task to react-button package. ([PR #13873](https://github.com/microsoft/fluentui/pull/13873) by <EMAIL>)

## [0.6.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.6.0)

Tue, 30 Jun 2020 12:33:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.5.1..@fluentui/react-button_v0.6.0)

### Minor changes

- Button: Adding componentRef, focus method, styling, and tests. ([PR #13718](https://github.com/microsoft/fluentui/pull/13718) by <EMAIL>)

## [0.5.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.5.1)

Fri, 26 Jun 2020 12:36:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.5.0..@fluentui/react-button_v0.5.1)

### Patches

- Button: Adding stories of a Button themed like Fluent using tokens. ([PR #13769](https://github.com/microsoft/fluentui/pull/13769) by <EMAIL>)
- Button: Using correct focus borders from utilities. ([PR #13790](https://github.com/microsoft/fluentui/pull/13790) by <EMAIL>)

## [0.5.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.5.0)

Thu, 25 Jun 2020 12:50:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.4.0..@fluentui/react-button_v0.5.0)

### Minor changes

- Button: updates to styles, adjusting width tokens. ([PR #13753](https://github.com/microsoft/fluentui/pull/13753) by <EMAIL>)

### Patches

- Update typings related to mergeProps typing ([PR #13571](https://github.com/microsoft/fluentui/pull/13571) by <EMAIL>)

## [0.4.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.4.0)

Tue, 23 Jun 2020 12:41:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.3.7..@fluentui/react-button_v0.4.0)

### Minor changes

- Button: Mixing style coming from slotProps. ([PR #13723](https://github.com/microsoft/fluentui/pull/13723) by <EMAIL>)

## [0.3.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.3.7)

Mon, 22 Jun 2020 12:42:16 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.3.6..@fluentui/react-button_v0.3.7)

### Patches

- Button: Adding pointer-events: none to :after focus rectangle. ([PR #13696](https://github.com/microsoft/fluentui/pull/13696) by <EMAIL>)

## [0.3.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.3.6)

Fri, 19 Jun 2020 12:38:09 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.3.5..@fluentui/react-button_v0.3.6)

### Patches

- react-button: Adding api-extractor configuration. ([PR #13654](https://github.com/microsoft/fluentui/pull/13654) by <EMAIL>)

## [0.3.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.3.5)

Thu, 18 Jun 2020 12:37:09 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.3.3..@fluentui/react-button_v0.3.5)

### Patches

- Button: Replacing height with min-height so Button can adapt in flex scenarios. ([PR #13655](https://github.com/microsoft/fluentui/pull/13655) by <EMAIL>)

## [0.3.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.3.3)

Tue, 16 Jun 2020 17:35:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.3.0..@fluentui/react-button_v0.3.3)

### Patches

- The css output was being unintentionally botched by the postcss-clean minifier. Moving to clean-css directly without the postcss wrapping. ([PR #13612](https://github.com/microsoft/fluentui/pull/13612) by <EMAIL>)

## [0.3.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.3.0)

Fri, 12 Jun 2020 18:29:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.2.0..@fluentui/react-button_v0.3.0)

### Minor changes

- Adding inline tokens support to Button, fixing focus rects. ([PR #13416](https://github.com/microsoft/fluentui/pull/13416) by <EMAIL>)

### Patches

- ToggleButton: Updating to new css variables. ([PR #13569](https://github.com/microsoft/fluentui/pull/13569) by <EMAIL>)
- Updating typings to conform with react-compose changes. ([PR #13521](https://github.com/microsoft/fluentui/pull/13521) by <EMAIL>)

## [0.2.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.2.0)

Mon, 08 Jun 2020 12:34:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.1.13..@fluentui/react-button_v0.2.0)

### Minor changes

- Cleaning up typings. ([PR #13474](https://github.com/microsoft/fluentui/pull/13474) by <EMAIL>)

## [0.1.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.1.13)

Fri, 05 Jun 2020 05:09:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.1.12..@fluentui/react-button_v0.1.13)

### Patches

- Updating createClassResolver usage. ([PR #13448](https://github.com/microsoft/fluentui/pull/13448) by <EMAIL>)
- Adjusting styling to adhere to conventions. ([PR #13472](https://github.com/microsoft/fluentui/pull/13472) by <EMAIL>)

## [0.1.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.1.12)

Tue, 02 Jun 2020 12:36:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.1.11..@fluentui/react-button_v0.1.12)

### Patches

- Using new react-compose mergeProps. ([PR #13360](https://github.com/microsoft/fluentui/pull/13360) by <EMAIL>)

## [0.1.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.1.11)

Mon, 01 Jun 2020 12:32:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.1.4..@fluentui/react-button_v0.1.11)

### Patches

- Explicitly importing react in tempTypes and fixing bug in mergeProps. ([PR #13196](https://github.com/microsoft/fluentui/pull/13196) by <EMAIL>)

## [0.1.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.1.4)

Wed, 20 May 2020 12:31:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-button_v0.1.1..@fluentui/react-button_v0.1.4)

### Patches

- feat(compose): expose \_\_self slot ([PR #13115](https://github.com/microsoft/fluentui/pull/13115) by <EMAIL>)

## [0.1.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-button_v0.1.1)

Fri, 15 May 2020 12:42:20 GMT

### Patches

- Initial package scaffold. ([PR #13096](https://github.com/microsoft/fluentui/pull/13096) by <EMAIL>)
