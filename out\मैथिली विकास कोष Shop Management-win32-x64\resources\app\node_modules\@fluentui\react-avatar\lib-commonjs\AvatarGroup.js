"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AvatarGroup: function() {
        return _index.AvatarGroup;
    },
    avatarGroupClassNames: function() {
        return _index.avatarGroupClassNames;
    },
    defaultAvatarGroupSize: function() {
        return _index.defaultAvatarGroupSize;
    },
    renderAvatarGroup_unstable: function() {
        return _index.renderAvatarGroup_unstable;
    },
    useAvatarGroupContextValues: function() {
        return _index.useAvatarGroupContextValues;
    },
    useAvatarGroupStyles_unstable: function() {
        return _index.useAvatarGroupStyles_unstable;
    },
    useAvatarGroup_unstable: function() {
        return _index.useAvatarGroup_unstable;
    }
});
const _index = require("./components/AvatarGroup/index");
