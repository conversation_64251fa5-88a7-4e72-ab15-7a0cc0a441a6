{"version": 3, "names": ["__styles", "mergeClasses", "tokens", "typographyStyles", "useSizeStyles", "useFluent_unstable", "useFluent", "avatarGroupItemClassNames", "root", "avatar", "overflowLabel", "avatarGroupItemDividerWidthVar", "useRootStyles", "base", "Bt984gj", "mc9l5x", "Bnnss6s", "qhf8xq", "overflowItem", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "nonOverflowItem", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "d", "p", "useAvatarStyles", "pie", "useOverflowLabelStyles", "Frg6f3", "sj55zd", "Bahqtrf", "Be2twd7", "Bhrd7zp", "Bg96gwp", "useStackStyles", "thick", "E5pizo", "thicker", "thickest", "xxs", "jhia2w", "xs", "s", "l", "useSpreadStyles", "mNudge", "m", "xl", "usePieStyles", "slices", "B3gf25r", "Be2twx7", "Bvaow4n", "Gpecfs", "bhabj1", "B7rc6i7", "Bwrfys5", "Bwuzm9m", "fflka", "do7bja", "Be8zqhl", "Bij0kh0", "Bwexnyt", "Bhe5x6o", "B3kv7bh", "rtlSlices", "uiicq7", "useAvatarGroupItemStyles_unstable", "state", "isOverflowItem", "layout", "size", "dir", "avatar<PERSON><PERSON><PERSON>", "overflowLabelStyles", "pieStyles", "rootStyles", "sizeStyles", "groupChildClassName", "useGroupChildClassName", "rootClasses", "push", "className", "stackStyles", "spreadStyles", "layoutClasses"], "sources": ["useAvatarGroupItemStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses } from '@griffel/react';\nimport { tokens, typographyStyles } from '@fluentui/react-theme';\nimport { useSizeStyles } from '../../Avatar';\nimport { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nexport const avatarGroupItemClassNames = {\n    root: 'fui-AvatarGroupItem',\n    avatar: 'fui-AvatarGroupItem__avatar',\n    overflowLabel: 'fui-AvatarGroupItem__overflowLabel'\n};\nconst avatarGroupItemDividerWidthVar = '--fuiAvatarGroupItem__divider--width';\n/**\n * Styles for the root slot\n */ const useRootStyles = makeStyles({\n    base: {\n        alignItems: 'center',\n        display: 'inline-flex',\n        flexShrink: 0,\n        position: 'relative'\n    },\n    overflowItem: {\n        padding: `${tokens.spacingVerticalXS} ${tokens.spacingHorizontalXS}`\n    },\n    nonOverflowItem: {\n        borderRadius: tokens.borderRadiusCircular\n    }\n});\n/**\n * Styles for the avatar slot\n */ const useAvatarStyles = makeStyles({\n    nonOverflowItem: {\n        position: 'absolute'\n    },\n    pie: {\n        borderRadius: '0'\n    }\n});\n/**\n * Styles for the label slot\n */ const useOverflowLabelStyles = makeStyles({\n    base: {\n        marginLeft: tokens.spacingHorizontalS,\n        color: tokens.colorNeutralForeground1,\n        ...typographyStyles.body1\n    }\n});\n/**\n * Styles for the stack layout\n */ const useStackStyles = makeStyles({\n    thick: {\n        boxShadow: `0 0 0 ${tokens.strokeWidthThick} ${tokens.colorNeutralBackground2}`\n    },\n    thicker: {\n        boxShadow: `0 0 0 ${tokens.strokeWidthThicker} ${tokens.colorNeutralBackground2}`\n    },\n    thickest: {\n        boxShadow: `0 0 0 ${tokens.strokeWidthThickest} ${tokens.colorNeutralBackground2}`\n    },\n    xxs: {\n        '&:not(:first-child)': {\n            marginLeft: `calc(-1 * ${tokens.spacingHorizontalXXS})`\n        }\n    },\n    xs: {\n        '&:not(:first-child)': {\n            marginLeft: `calc(-1 * ${tokens.spacingHorizontalXS})`\n        }\n    },\n    s: {\n        '&:not(:first-child)': {\n            marginLeft: `calc(-1 * ${tokens.spacingHorizontalS})`\n        }\n    },\n    l: {\n        '&:not(:first-child)': {\n            marginLeft: `calc(-1 * ${tokens.spacingHorizontalL})`\n        }\n    }\n});\n/**\n * Styles for the spread layout\n */ const useSpreadStyles = makeStyles({\n    s: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalS\n        }\n    },\n    mNudge: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalMNudge\n        }\n    },\n    m: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalM\n        }\n    },\n    l: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalL\n        }\n    },\n    xl: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalXL\n        }\n    }\n});\n/**\n * Styles for the pie layout\n */ const usePieStyles = makeStyles({\n    base: {\n        position: 'absolute'\n    },\n    slices: {\n        // Two slices\n        // 1st of 2 items\n        '&:nth-of-type(1):nth-last-of-type(2)': {\n            clipPath: `inset(0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)) 0 25%)`,\n            left: '-25%'\n        },\n        // 2nd of 2 items\n        '&:nth-of-type(2):nth-last-of-type(1)': {\n            clipPath: `inset(0 25% 0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)))`,\n            left: '25%'\n        },\n        // Three slices\n        // 1st of 3 items\n        '&:nth-of-type(1):nth-last-of-type(3)': {\n            clipPath: `inset(0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)) 0 25%)`,\n            left: '-25%'\n        },\n        // 2nd of 3 items\n        '&:nth-of-type(2):nth-last-of-type(2)': {\n            // Since the two AvatarGroupItems on the right are scaled by 0.5, the divider width should not be halved.\n            clipPath: `inset(0 0 var(${avatarGroupItemDividerWidthVar}) var(${avatarGroupItemDividerWidthVar}))`,\n            left: '50%',\n            transform: 'scale(0.5)',\n            transformOrigin: '0 0'\n        },\n        // 3rd of 3 items\n        '&:nth-of-type(3):nth-last-of-type(1)': {\n            clipPath: `inset(var(${avatarGroupItemDividerWidthVar}) 0 0 var(${avatarGroupItemDividerWidthVar}))`,\n            left: '50%',\n            top: '50%',\n            transform: 'scale(0.5)',\n            transformOrigin: '0 0'\n        }\n    },\n    rtlSlices: {\n        // Two slices\n        // 1st of 2 items\n        '&:nth-of-type(1):nth-last-of-type(2)': {\n            clipPath: `inset(0 25% 0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)))`\n        },\n        // 2nd of 2 items\n        '&:nth-of-type(2):nth-last-of-type(1)': {\n            clipPath: `inset(0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)) 0 25%)`\n        },\n        // Three slices\n        // 1st of 3 items\n        '&:nth-of-type(1):nth-last-of-type(3)': {\n            clipPath: `inset(0 25% 0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)))`\n        },\n        // 2nd of 3 items\n        '&:nth-of-type(2):nth-last-of-type(2)': {\n            clipPath: `inset(0 var(${avatarGroupItemDividerWidthVar}) var(${avatarGroupItemDividerWidthVar}) 0)`,\n            left: '0'\n        },\n        // 3rd of 3 items\n        '&:nth-of-type(3):nth-last-of-type(1)': {\n            clipPath: `inset(var(${avatarGroupItemDividerWidthVar}) var(${avatarGroupItemDividerWidthVar}) 0 0)`,\n            left: '0'\n        }\n    },\n    thick: {\n        [avatarGroupItemDividerWidthVar]: tokens.strokeWidthThick\n    },\n    thicker: {\n        [avatarGroupItemDividerWidthVar]: tokens.strokeWidthThicker\n    },\n    thickest: {\n        [avatarGroupItemDividerWidthVar]: tokens.strokeWidthThickest\n    }\n});\n/**\n * Apply styling to the AvatarGroupItem slots based on the state\n */ export const useAvatarGroupItemStyles_unstable = (state)=>{\n    'use no memo';\n    const { isOverflowItem, layout, size } = state;\n    const { dir } = useFluent();\n    const avatarStyles = useAvatarStyles();\n    const overflowLabelStyles = useOverflowLabelStyles();\n    const pieStyles = usePieStyles();\n    const rootStyles = useRootStyles();\n    const sizeStyles = useSizeStyles();\n    const groupChildClassName = useGroupChildClassName(layout, size);\n    const rootClasses = [\n        rootStyles.base\n    ];\n    if (!isOverflowItem) {\n        rootClasses.push(rootStyles.nonOverflowItem);\n        rootClasses.push(groupChildClassName);\n        rootClasses.push(sizeStyles[size]);\n        if (layout === 'pie') {\n            rootClasses.push(pieStyles.base);\n            if (size < 56) {\n                rootClasses.push(pieStyles.thick);\n            } else if (size < 72) {\n                rootClasses.push(pieStyles.thicker);\n            } else {\n                rootClasses.push(pieStyles.thickest);\n            }\n            rootClasses.push(pieStyles.slices);\n            if (dir === 'rtl') {\n                rootClasses.push(pieStyles.rtlSlices);\n            }\n        }\n    } else {\n        rootClasses.push(rootStyles.overflowItem);\n    }\n    state.root.className = mergeClasses(avatarGroupItemClassNames.root, ...rootClasses, state.root.className);\n    state.avatar.className = mergeClasses(avatarGroupItemClassNames.avatar, !isOverflowItem && avatarStyles.nonOverflowItem, layout === 'pie' && avatarStyles.pie, state.avatar.className);\n    if (state.overflowLabel) {\n        state.overflowLabel.className = mergeClasses(avatarGroupItemClassNames.overflowLabel, overflowLabelStyles.base, state.overflowLabel.className);\n    }\n    return state;\n};\n/**\n * Hook for getting the className for the children of AvatarGroup. This hook will provide the spacing and outlines\n * needed for each layout.\n */ export const useGroupChildClassName = (layout, size)=>{\n    const stackStyles = useStackStyles();\n    const spreadStyles = useSpreadStyles();\n    const layoutClasses = [];\n    if (size) {\n        if (layout === 'stack') {\n            if (size < 56) {\n                layoutClasses.push(stackStyles.thick);\n            } else if (size < 72) {\n                layoutClasses.push(stackStyles.thicker);\n            } else {\n                layoutClasses.push(stackStyles.thickest);\n            }\n            if (size < 24) {\n                layoutClasses.push(stackStyles.xxs);\n            } else if (size < 48) {\n                layoutClasses.push(stackStyles.xs);\n            } else if (size < 96) {\n                layoutClasses.push(stackStyles.s);\n            } else {\n                layoutClasses.push(stackStyles.l);\n            }\n        } else if (layout === 'spread') {\n            if (size < 20) {\n                layoutClasses.push(spreadStyles.s);\n            } else if (size < 32) {\n                layoutClasses.push(spreadStyles.mNudge);\n            } else if (size < 64) {\n                layoutClasses.push(spreadStyles.l);\n            } else {\n                layoutClasses.push(spreadStyles.xl);\n            }\n        }\n    }\n    return mergeClasses(...layoutClasses);\n};\n"], "mappings": "AAAA,SAAAA,QAAA,EAAqBC,YAAY,QAAQ,gBAAgB;AACzD,SAASC,MAAM,EAAEC,gBAAgB,QAAQ,uBAAuB;AAChE,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,kBAAkB,IAAIC,SAAS,QAAQ,iCAAiC;AACjF,OAAO,MAAMC,yBAAyB,GAAG;EACrCC,IAAI,EAAE,qBAAqB;EAC3BC,MAAM,EAAE,6BAA6B;EACrCC,aAAa,EAAE;AACnB,CAAC;AACD,MAAMC,8BAA8B,GAAG,sCAAsC;AAC7E;AACA;AACA;AAAI,MAAMC,aAAa,gBAAGZ,QAAA;EAAAa,IAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAC,YAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;EAAAC,eAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;AAAA;EAAAC,CAAA;IAAAC,CAAA;EAAA;IAAAA,CAAA;EAAA;AAAA,CAazB,CAAC;AACF;AACA;AACA;AAAI,MAAMC,eAAe,gBAAGhC,QAAA;EAAAwB,eAAA;IAAAP,MAAA;EAAA;EAAAgB,GAAA;IAAAR,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;AAAA;EAAAC,CAAA;IAAAC,CAAA;EAAA;AAAA,CAO3B,CAAC;AACF;AACA;AACA;AAAI,MAAMG,sBAAsB,gBAAGlC,QAAA;EAAAa,IAAA;IAAAsB,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;EAAA;AAAA;EAAAV,CAAA;AAAA,CAMlC,CAAC;AACF;AACA;AACA;AAAI,MAAMW,cAAc,gBAAGzC,QAAA;EAAA0C,KAAA;IAAAC,MAAA;EAAA;EAAAC,OAAA;IAAAD,MAAA;EAAA;EAAAE,QAAA;IAAAF,MAAA;EAAA;EAAAG,GAAA;IAAAC,MAAA;EAAA;EAAAC,EAAA;IAAAD,MAAA;EAAA;EAAAE,CAAA;IAAAF,MAAA;EAAA;EAAAG,CAAA;IAAAH,MAAA;EAAA;AAAA;EAAAjB,CAAA;AAAA,CA8B1B,CAAC;AACF;AACA;AACA;AAAI,MAAMqB,eAAe,gBAAGnD,QAAA;EAAAiD,CAAA;IAAAF,MAAA;EAAA;EAAAK,MAAA;IAAAL,MAAA;EAAA;EAAAM,CAAA;IAAAN,MAAA;EAAA;EAAAG,CAAA;IAAAH,MAAA;EAAA;EAAAO,EAAA;IAAAP,MAAA;EAAA;AAAA;EAAAjB,CAAA;AAAA,CA0B3B,CAAC;AACF;AACA;AACA;AAAI,MAAMyB,YAAY,gBAAGvD,QAAA;EAAAa,IAAA;IAAAI,MAAA;EAAA;EAAAuC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,KAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;EAAA;EAAAC,SAAA;IAAAf,OAAA;IAAAE,OAAA;IAAAE,MAAA;IAAAE,OAAA;IAAAC,OAAA;IAAAG,OAAA;IAAAC,OAAA;EAAA;EAAA1B,KAAA;IAAA+B,MAAA;EAAA;EAAA7B,OAAA;IAAA6B,MAAA;EAAA;EAAA5B,QAAA;IAAA4B,MAAA;EAAA;AAAA;EAAA3C,CAAA;AAAA,CA0ExB,CAAC;AACF;AACA;AACA;AAAI,OAAO,MAAM4C,iCAAiC,GAAIC,KAAK,IAAG;EAC1D,aAAa;;EACb,MAAM;IAAEC,cAAc;IAAEC,MAAM;IAAEC;EAAK,CAAC,GAAGH,KAAK;EAC9C,MAAM;IAAEI;EAAI,CAAC,GAAGzE,SAAS,CAAC,CAAC;EAC3B,MAAM0E,YAAY,GAAGhD,eAAe,CAAC,CAAC;EACtC,MAAMiD,mBAAmB,GAAG/C,sBAAsB,CAAC,CAAC;EACpD,MAAMgD,SAAS,GAAG3B,YAAY,CAAC,CAAC;EAChC,MAAM4B,UAAU,GAAGvE,aAAa,CAAC,CAAC;EAClC,MAAMwE,UAAU,GAAGhF,aAAa,CAAC,CAAC;EAClC,MAAMiF,mBAAmB,GAAGC,sBAAsB,CAACT,MAAM,EAAEC,IAAI,CAAC;EAChE,MAAMS,WAAW,GAAG,CAChBJ,UAAU,CAACtE,IAAI,CAClB;EACD,IAAI,CAAC+D,cAAc,EAAE;IACjBW,WAAW,CAACC,IAAI,CAACL,UAAU,CAAC3D,eAAe,CAAC;IAC5C+D,WAAW,CAACC,IAAI,CAACH,mBAAmB,CAAC;IACrCE,WAAW,CAACC,IAAI,CAACJ,UAAU,CAACN,IAAI,CAAC,CAAC;IAClC,IAAID,MAAM,KAAK,KAAK,EAAE;MAClBU,WAAW,CAACC,IAAI,CAACN,SAAS,CAACrE,IAAI,CAAC;MAChC,IAAIiE,IAAI,GAAG,EAAE,EAAE;QACXS,WAAW,CAACC,IAAI,CAACN,SAAS,CAACxC,KAAK,CAAC;MACrC,CAAC,MAAM,IAAIoC,IAAI,GAAG,EAAE,EAAE;QAClBS,WAAW,CAACC,IAAI,CAACN,SAAS,CAACtC,OAAO,CAAC;MACvC,CAAC,MAAM;QACH2C,WAAW,CAACC,IAAI,CAACN,SAAS,CAACrC,QAAQ,CAAC;MACxC;MACA0C,WAAW,CAACC,IAAI,CAACN,SAAS,CAAC1B,MAAM,CAAC;MAClC,IAAIuB,GAAG,KAAK,KAAK,EAAE;QACfQ,WAAW,CAACC,IAAI,CAACN,SAAS,CAACV,SAAS,CAAC;MACzC;IACJ;EACJ,CAAC,MAAM;IACHe,WAAW,CAACC,IAAI,CAACL,UAAU,CAACjE,YAAY,CAAC;EAC7C;EACAyD,KAAK,CAACnE,IAAI,CAACiF,SAAS,GAAGxF,YAAY,CAACM,yBAAyB,CAACC,IAAI,EAAE,GAAG+E,WAAW,EAAEZ,KAAK,CAACnE,IAAI,CAACiF,SAAS,CAAC;EACzGd,KAAK,CAAClE,MAAM,CAACgF,SAAS,GAAGxF,YAAY,CAACM,yBAAyB,CAACE,MAAM,EAAE,CAACmE,cAAc,IAAII,YAAY,CAACxD,eAAe,EAAEqD,MAAM,KAAK,KAAK,IAAIG,YAAY,CAAC/C,GAAG,EAAE0C,KAAK,CAAClE,MAAM,CAACgF,SAAS,CAAC;EACtL,IAAId,KAAK,CAACjE,aAAa,EAAE;IACrBiE,KAAK,CAACjE,aAAa,CAAC+E,SAAS,GAAGxF,YAAY,CAACM,yBAAyB,CAACG,aAAa,EAAEuE,mBAAmB,CAACpE,IAAI,EAAE8D,KAAK,CAACjE,aAAa,CAAC+E,SAAS,CAAC;EAClJ;EACA,OAAOd,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AAAI,OAAO,MAAMW,sBAAsB,GAAGA,CAACT,MAAM,EAAEC,IAAI,KAAG;EACtD,MAAMY,WAAW,GAAGjD,cAAc,CAAC,CAAC;EACpC,MAAMkD,YAAY,GAAGxC,eAAe,CAAC,CAAC;EACtC,MAAMyC,aAAa,GAAG,EAAE;EACxB,IAAId,IAAI,EAAE;IACN,IAAID,MAAM,KAAK,OAAO,EAAE;MACpB,IAAIC,IAAI,GAAG,EAAE,EAAE;QACXc,aAAa,CAACJ,IAAI,CAACE,WAAW,CAAChD,KAAK,CAAC;MACzC,CAAC,MAAM,IAAIoC,IAAI,GAAG,EAAE,EAAE;QAClBc,aAAa,CAACJ,IAAI,CAACE,WAAW,CAAC9C,OAAO,CAAC;MAC3C,CAAC,MAAM;QACHgD,aAAa,CAACJ,IAAI,CAACE,WAAW,CAAC7C,QAAQ,CAAC;MAC5C;MACA,IAAIiC,IAAI,GAAG,EAAE,EAAE;QACXc,aAAa,CAACJ,IAAI,CAACE,WAAW,CAAC5C,GAAG,CAAC;MACvC,CAAC,MAAM,IAAIgC,IAAI,GAAG,EAAE,EAAE;QAClBc,aAAa,CAACJ,IAAI,CAACE,WAAW,CAAC1C,EAAE,CAAC;MACtC,CAAC,MAAM,IAAI8B,IAAI,GAAG,EAAE,EAAE;QAClBc,aAAa,CAACJ,IAAI,CAACE,WAAW,CAACzC,CAAC,CAAC;MACrC,CAAC,MAAM;QACH2C,aAAa,CAACJ,IAAI,CAACE,WAAW,CAACxC,CAAC,CAAC;MACrC;IACJ,CAAC,MAAM,IAAI2B,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAIC,IAAI,GAAG,EAAE,EAAE;QACXc,aAAa,CAACJ,IAAI,CAACG,YAAY,CAAC1C,CAAC,CAAC;MACtC,CAAC,MAAM,IAAI6B,IAAI,GAAG,EAAE,EAAE;QAClBc,aAAa,CAACJ,IAAI,CAACG,YAAY,CAACvC,MAAM,CAAC;MAC3C,CAAC,MAAM,IAAI0B,IAAI,GAAG,EAAE,EAAE;QAClBc,aAAa,CAACJ,IAAI,CAACG,YAAY,CAACzC,CAAC,CAAC;MACtC,CAAC,MAAM;QACH0C,aAAa,CAACJ,IAAI,CAACG,YAAY,CAACrC,EAAE,CAAC;MACvC;IACJ;EACJ;EACA,OAAOrD,YAAY,CAAC,GAAG2F,aAAa,CAAC;AACzC,CAAC", "ignoreList": []}