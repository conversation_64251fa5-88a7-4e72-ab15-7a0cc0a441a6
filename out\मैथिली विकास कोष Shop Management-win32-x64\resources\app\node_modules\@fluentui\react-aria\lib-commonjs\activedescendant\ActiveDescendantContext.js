"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    ActiveDescendantContextProvider: function() {
        return ActiveDescendantContextProvider;
    },
    useActiveDescendantContext: function() {
        return useActiveDescendantContext;
    },
    useHasParentActiveDescendantContext: function() {
        return useHasParentActiveDescendantContext;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const noop = ()=>undefined;
const activeDescendantContextDefaultValue = {
    controller: {
        active: noop,
        blur: noop,
        find: noop,
        first: noop,
        focus: noop,
        focusLastActive: noop,
        scrollActiveIntoView: noop,
        last: noop,
        next: noop,
        prev: noop,
        showAttributes: noop,
        hideAttributes: noop,
        showFocusVisibleAttributes: noop,
        hideFocusVisibleAttributes: noop
    }
};
const ActiveDescendantContext = _react.createContext(undefined);
const ActiveDescendantContextProvider = ActiveDescendantContext.Provider;
const useActiveDescendantContext = ()=>{
    var _React_useContext;
    return (_React_useContext = _react.useContext(ActiveDescendantContext)) !== null && _React_useContext !== void 0 ? _React_useContext : activeDescendantContextDefaultValue;
};
const useHasParentActiveDescendantContext = ()=>!!_react.useContext(ActiveDescendantContext);
