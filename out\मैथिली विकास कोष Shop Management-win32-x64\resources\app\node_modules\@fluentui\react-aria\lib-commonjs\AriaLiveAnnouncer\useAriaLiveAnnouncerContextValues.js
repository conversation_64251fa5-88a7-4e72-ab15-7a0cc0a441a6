"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useAriaLiveAnnouncerContextValues_unstable", {
    enumerable: true,
    get: function() {
        return useAriaLiveAnnouncerContextValues_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
function useAriaLiveAnnouncerContextValues_unstable(state) {
    const { announce } = state;
    return _react.useMemo(()=>({
            announce: {
                announce
            }
        }), [
        announce
    ]);
}
