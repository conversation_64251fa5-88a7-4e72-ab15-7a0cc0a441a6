"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    avatarGroupClassNames: function() {
        return avatarGroupClassNames;
    },
    useAvatarGroupStyles_unstable: function() {
        return useAvatarGroupStyles_unstable;
    }
});
const _react = require("@griffel/react");
const _useAvatarStylesstyles = require("../Avatar/useAvatarStyles.styles");
const avatarGroupClassNames = {
    root: 'fui-AvatarGroup'
};
/**
 * Styles for the root slot.
 */ const useStyles = /*#__PURE__*/ (0, _react.__styles)({
    base: {
        mc9l5x: "ftuwxu6",
        qhf8xq: "f10pi13n"
    },
    pie: {
        Bgl5zvf: "f1uz6ud1",
        De3pzq: "f1ganh6p",
        Bsw6fvg: "fe2ae1k"
    }
}, {
    d: [
        ".ftuwxu6{display:inline-flex;}",
        ".f10pi13n{position:relative;}",
        ".f1uz6ud1{clip-path:circle(50%);}",
        ".f1ganh6p{background-color:var(--colorTransparentStroke);}"
    ],
    m: [
        [
            "@media (forced-colors: active){.fe2ae1k{background-color:CanvasText;}}",
            {
                m: "(forced-colors: active)"
            }
        ]
    ]
});
const useAvatarGroupStyles_unstable = (state)=>{
    'use no memo';
    const { layout, size } = state;
    const styles = useStyles();
    const sizeStyles = (0, _useAvatarStylesstyles.useSizeStyles)();
    state.root.className = (0, _react.mergeClasses)(avatarGroupClassNames.root, styles.base, layout === 'pie' && sizeStyles[size], layout === 'pie' && styles.pie, state.root.className);
    return state;
};
