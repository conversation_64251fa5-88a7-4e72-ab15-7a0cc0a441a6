"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useCarouselNav_unstable", {
    enumerable: true,
    get: function() {
        return useCarouselNav_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _reacttabster = require("@fluentui/react-tabster");
const _reactutilities = require("@fluentui/react-utilities");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _CarouselContext = require("../CarouselContext");
const useCarouselNav_unstable = (props, ref)=>{
    const { appearance } = props;
    const focusableGroupAttr = (0, _reacttabster.useArrowNavigationGroup)({
        circular: false,
        axis: 'horizontal',
        memorizeCurrent: false,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        unstable_hasDefault: true
    });
    // Users can choose controlled or uncontrolled, if uncontrolled, the default is initialized by carousel context
    const [totalSlides, setTotalSlides] = (0, _reactutilities.useControllableState)({
        state: props.totalSlides,
        initialState: 0
    });
    const subscribeForValues = (0, _CarouselContext.useCarouselContext_unstable)((ctx)=>ctx.subscribeForValues);
    (0, _reactutilities.useIsomorphicLayoutEffect)(()=>{
        return subscribeForValues((data)=>{
            setTotalSlides(data.navItemsCount);
        });
    }, [
        subscribeForValues,
        setTotalSlides
    ]);
    return {
        totalSlides,
        appearance,
        renderNavButton: props.children,
        components: {
            root: 'div'
        },
        root: _reactutilities.slot.always((0, _reactutilities.getIntrinsicElementProps)('div', {
            ref,
            role: 'tablist',
            ...props,
            ...focusableGroupAttr,
            children: null
        }), {
            elementType: 'div'
        })
    };
};
