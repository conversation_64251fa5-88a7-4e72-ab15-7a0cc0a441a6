{"version": 3, "sources": ["Alert.types.ts"], "sourcesContent": ["import { Avatar } from '@fluentui/react-avatar';\nimport { <PERSON><PERSON> } from '@fluentui/react-button';\n\nimport type { ComponentProps, ComponentState, Slot } from '@fluentui/react-utilities';\n\n/**\n * @deprecated please use the Toast or MessageBar component\n */\nexport type AlertSlots = {\n  /**\n   * The root slot is the top level container for the alert component\n   */\n  root: NonNullable<Slot<'div'>>;\n  /**\n   * The icon slot renders the icon determined by the `icon` or `intent` prop\n   */\n  icon?: Slot<'span'>;\n  /**\n   * The action slot renders a button that prompts the user to take action on the alert\n   */\n  action?: Slot<typeof Button>;\n  /**\n   * The avatar slot renders an avatar before the contents of the alert\n   */\n  avatar?: Slot<typeof Avatar>;\n};\n\n/**\n * Alert Props\n * @deprecated please use the Toast or MessageBar component\n */\n// eslint-disable-next-line deprecation/deprecation\nexport type AlertProps = ComponentProps<AlertSlots> & {\n  /**\n   * The intent prop, if present, determines the icon to be rendered in the icon slot. The icon prop\n   * overrides the intent prop\n   */\n  intent?: 'info' | 'success' | 'error' | 'warning';\n  /**\n   * The appearance of the Alert.\n   * @default 'primary'\n   */\n  appearance?: 'primary' | 'inverted';\n};\n\n/**\n * State used in rendering Alert\n * @deprecated please use the Toast or MessageBar component\n */\n// eslint-disable-next-line deprecation/deprecation\nexport type AlertState = ComponentState<AlertSlots> &\n  // eslint-disable-next-line deprecation/deprecation\n  Pick<AlertProps, 'intent'> &\n  // eslint-disable-next-line deprecation/deprecation\n  Required<Pick<AlertProps, 'appearance'>>;\n"], "names": [], "mappings": "AAAA,WAsD2C"}