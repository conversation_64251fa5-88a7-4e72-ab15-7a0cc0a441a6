# Change Log - @fluentui/react-aria

This log was last generated on Fri, 27 Jun 2025 13:36:33 GMT and should not be manually modified.

<!-- Start content -->

## [9.15.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.15.3)

Fri, 27 Jun 2025 13:36:33 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.15.2..@fluentui/react-aria_v9.15.3)

### Patches

- Bump @fluentui/react-tabster to v9.25.3 ([PR #34734](https://github.com/microsoft/fluentui/pull/34734) by beachball)

## [9.15.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.15.2)

Thu, 26 Jun 2025 14:11:55 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.15.1..@fluentui/react-aria_v9.15.2)

### Patch<PERSON>

- <PERSON>ump @fluentui/react-jsx-runtime to v9.1.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-tabster to v9.25.2 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)
- Bump @fluentui/react-utilities to v9.22.0 ([PR #34529](https://github.com/microsoft/fluentui/pull/34529) by beachball)

## [9.15.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.15.1)

Wed, 18 Jun 2025 17:34:00 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.15.0..@fluentui/react-aria_v9.15.1)

### Patches

- Bump @fluentui/react-shared-contexts to v9.24.0 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.1.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-tabster to v9.25.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)
- Bump @fluentui/react-utilities to v9.21.1 ([PR #34675](https://github.com/microsoft/fluentui/pull/34675) by beachball)

## [9.15.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.15.0)

Thu, 12 Jun 2025 09:43:29 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.14.8..@fluentui/react-aria_v9.15.0)

### Minor changes

- Bump @fluentui/react-jsx-runtime to v9.1.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-tabster to v9.25.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)
- Bump @fluentui/react-utilities to v9.21.0 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by beachball)

### Patches

- chore: adjust types to support react 18 ([PR #34456](https://github.com/microsoft/fluentui/pull/34456) by <EMAIL>)

## [9.14.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.14.8)

Fri, 06 Jun 2025 13:15:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.14.7..@fluentui/react-aria_v9.14.8)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.55 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-tabster to v9.24.8 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)
- Bump @fluentui/react-utilities to v9.20.0 ([PR #34572](https://github.com/microsoft/fluentui/pull/34572) by beachball)

## [9.14.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.14.7)

Wed, 14 May 2025 18:49:15 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.14.6..@fluentui/react-aria_v9.14.7)

### Patches

- fix: update fluent ariaNotify implementation to match new API ([PR #34311](https://github.com/microsoft/fluentui/pull/34311) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.24.7 ([PR #34438](https://github.com/microsoft/fluentui/pull/34438) by beachball)

## [9.14.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.14.6)

Thu, 24 Apr 2025 09:59:45 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.14.5..@fluentui/react-aria_v9.14.6)

### Patches

- Bump @fluentui/react-tabster to v9.24.6 ([PR #34315](https://github.com/microsoft/fluentui/pull/34315) by beachball)

## [9.14.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.14.5)

Wed, 16 Apr 2025 19:42:18 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.14.4..@fluentui/react-aria_v9.14.5)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.54 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-tabster to v9.24.5 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)
- Bump @fluentui/react-utilities to v9.19.0 ([PR #34166](https://github.com/microsoft/fluentui/pull/34166) by beachball)

## [9.14.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.14.4)

Tue, 01 Apr 2025 15:08:02 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.14.3..@fluentui/react-aria_v9.14.4)

### Patches

- Bump @fluentui/react-tabster to v9.24.4 ([PR #33909](https://github.com/microsoft/fluentui/pull/33909) by beachball)

## [9.14.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.14.3)

Thu, 27 Mar 2025 21:12:51 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.14.2..@fluentui/react-aria_v9.14.3)

### Patches

- Bump @fluentui/react-shared-contexts to v9.23.1 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.53 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-tabster to v9.24.3 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)
- Bump @fluentui/react-utilities to v9.18.23 ([PR #34034](https://github.com/microsoft/fluentui/pull/34034) by beachball)

## [9.14.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.14.2)

Wed, 19 Mar 2025 15:40:43 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.14.1..@fluentui/react-aria_v9.14.2)

### Patches

- Bump @fluentui/react-shared-contexts to v9.23.0 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.52 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-tabster to v9.24.2 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)
- Bump @fluentui/react-utilities to v9.18.22 ([PR #34032](https://github.com/microsoft/fluentui/pull/34032) by beachball)

## [9.14.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.14.1)

Tue, 11 Mar 2025 18:58:54 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.14.0..@fluentui/react-aria_v9.14.1)

### Patches

- Bump @fluentui/react-shared-contexts to v9.22.0 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.51 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-tabster to v9.24.1 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)
- Bump @fluentui/react-utilities to v9.18.21 ([PR #33927](https://github.com/microsoft/fluentui/pull/33927) by beachball)

## [9.14.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.14.0)

Fri, 21 Feb 2025 14:34:03 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.14..@fluentui/react-aria_v9.14.0)

### Minor changes

- Bump @fluentui/react-tabster to v9.24.0 ([PR #33876](https://github.com/microsoft/fluentui/pull/33876) by beachball)

### Patches

- fix: useAnnounce live regions do not get aria-hidden from tabster ([PR #33855](https://github.com/microsoft/fluentui/pull/33855) by <EMAIL>)

## [9.13.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.14)

Wed, 22 Jan 2025 14:00:14 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.13..@fluentui/react-aria_v9.13.14)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.50 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-tabster to v9.23.3 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)
- Bump @fluentui/react-utilities to v9.18.20 ([PR #33631](https://github.com/microsoft/fluentui/pull/33631) by beachball)

## [9.13.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.13)

Wed, 08 Jan 2025 18:33:36 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.12..@fluentui/react-aria_v9.13.13)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.49 ([PR #33550](https://github.com/microsoft/fluentui/pull/33550) by beachball)

## [9.13.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.12)

Mon, 16 Dec 2024 16:26:49 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.11..@fluentui/react-aria_v9.13.12)

### Patches

- Bump @fluentui/react-shared-contexts to v9.21.2 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.48 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-tabster to v9.23.2 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)
- Bump @fluentui/react-utilities to v9.18.19 ([PR #33468](https://github.com/microsoft/fluentui/pull/33468) by beachball)

## [9.13.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.11)

Mon, 09 Dec 2024 17:38:08 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.10..@fluentui/react-aria_v9.13.11)

### Patches

- chore: remove usage of "export *" ([PR #33384](https://github.com/microsoft/fluentui/pull/33384) by <EMAIL>)

## [9.13.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.10)

Fri, 06 Dec 2024 12:53:46 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.9..@fluentui/react-aria_v9.13.10)

### Patches

- Bump @fluentui/react-shared-contexts to v9.21.1 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.47 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-tabster to v9.23.1 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)
- Bump @fluentui/react-utilities to v9.18.18 ([PR #33372](https://github.com/microsoft/fluentui/pull/33372) by beachball)

## [9.13.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.9)

Mon, 11 Nov 2024 10:01:04 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.8..@fluentui/react-aria_v9.13.9)

### Patches

- chore: replace npm-scripts and just-scrtips with nx inferred tasks ([PR #33074](https://github.com/microsoft/fluentui/pull/33074) by <EMAIL>)
- fix: correct expectations for Button rendered as a link with an href ([PR #33050](https://github.com/microsoft/fluentui/pull/33050) by <EMAIL>)
- style: resolve exposed jsx pragma lint issues within monorepo ([PR #32975](https://github.com/microsoft/fluentui/pull/32975) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.8 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-shared-contexts to v9.21.0 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.46 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-tabster to v9.23.0 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)
- Bump @fluentui/react-utilities to v9.18.17 ([PR #33238](https://github.com/microsoft/fluentui/pull/33238) by beachball)

## [9.13.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.8)

Tue, 15 Oct 2024 17:17:53 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.7..@fluentui/react-aria_v9.13.8)

### Patches

- Bump @fluentui/react-shared-contexts to v9.20.2 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.45 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-tabster to v9.22.9 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)
- Bump @fluentui/react-utilities to v9.18.16 ([PR #32999](https://github.com/microsoft/fluentui/pull/32999) by beachball)

## [9.13.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.7)

Tue, 08 Oct 2024 22:05:59 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.6..@fluentui/react-aria_v9.13.7)

### Patches

- Bump @fluentui/react-tabster to v9.22.8 ([PR #33007](https://github.com/microsoft/fluentui/pull/33007) by beachball)

## [9.13.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.6)

Mon, 23 Sep 2024 12:40:12 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.5..@fluentui/react-aria_v9.13.6)

### Patches

- Bump @fluentui/react-shared-contexts to v9.20.1 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.44 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-tabster to v9.22.7 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)
- Bump @fluentui/react-utilities to v9.18.15 ([PR #32840](https://github.com/microsoft/fluentui/pull/32840) by beachball)

## [9.13.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.5)

Tue, 10 Sep 2024 10:19:12 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.4..@fluentui/react-aria_v9.13.5)

### Patches

- Bump @fluentui/react-jsx-runtime to v9.0.43 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-tabster to v9.22.6 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)
- Bump @fluentui/react-utilities to v9.18.14 ([PR #32494](https://github.com/microsoft/fluentui/pull/32494) by beachball)

## [9.13.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.4)

Thu, 15 Aug 2024 13:49:46 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.3..@fluentui/react-aria_v9.13.4)

### Patches

- Bump @fluentui/react-tabster to v9.22.5 ([PR #32313](https://github.com/microsoft/fluentui/pull/32313) by beachball)

## [9.13.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.3)

Thu, 15 Aug 2024 08:22:07 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.2..@fluentui/react-aria_v9.13.3)

### Patches

- Bump @fluentui/react-tabster to v9.22.4 ([PR #31885](https://github.com/microsoft/fluentui/pull/31885) by beachball)

## [9.13.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.2)

Tue, 23 Jul 2024 20:13:14 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.1..@fluentui/react-aria_v9.13.2)

### Patches

- Bump @fluentui/react-shared-contexts to v9.20.0 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.42 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-tabster to v9.22.3 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)
- Bump @fluentui/react-utilities to v9.18.13 ([PR #32067](https://github.com/microsoft/fluentui/pull/32067) by beachball)

## [9.13.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.1)

Mon, 15 Jul 2024 17:25:51 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.13.0..@fluentui/react-aria_v9.13.1)

### Patches

- fix: revert incorectly set npm versions in all packages ([PR #31937](https://github.com/microsoft/fluentui/pull/31937) by <EMAIL>)
- Bump @fluentui/react-shared-contexts to v9.19.1 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.41 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-tabster to v9.22.2 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)
- Bump @fluentui/react-utilities to v9.18.12 ([PR #31998](https://github.com/microsoft/fluentui/pull/31998) by beachball)

## [9.13.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.13.0)

Mon, 01 Jul 2024 20:30:41 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.12.1..@fluentui/react-aria_v9.13.0)

### Minor changes

- feat: add options to manually show/hide focus visibility on active descendants, and scroll the active item into view ([PR #31415](https://github.com/microsoft/fluentui/pull/31415) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.40 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-tabster to v9.22.1 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)
- Bump @fluentui/react-utilities to v9.18.11 ([PR #31861](https://github.com/microsoft/fluentui/pull/31861) by beachball)

## [9.12.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.12.1)

Mon, 17 Jun 2024 07:34:17 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.12.0..@fluentui/react-aria_v9.12.1)

### Patches

- Bump @fluentui/react-tabster to v9.22.0 ([commit](https://github.com/microsoft/fluentui/commit/9ae683c22f2e65d94422a571ad5d3f97d0a77234) by beachball)

## [9.12.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.12.0)

Thu, 06 Jun 2024 15:26:29 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.11.4..@fluentui/react-aria_v9.12.0)

### Minor changes

- add optional `window` argument to `scrollIntoView`. ([PR #30967](https://github.com/microsoft/fluentui/pull/30967) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.39 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-tabster to v9.21.5 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)
- Bump @fluentui/react-utilities to v9.18.10 ([PR #31586](https://github.com/microsoft/fluentui/pull/31586) by beachball)

### Patches

- chore: remove optional "win" argument ([PR #31470](https://github.com/microsoft/fluentui/pull/31470) by <EMAIL>)

## [9.11.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.11.4)

Thu, 23 May 2024 08:02:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.11.3..@fluentui/react-aria_v9.11.4)

### Patches

- fix: add cleanup for timeout in announce ([PR #31264](https://github.com/microsoft/fluentui/pull/31264) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.21.4 ([commit](https://github.com/microsoft/fluentui/commit/03599d609e8310b08c57d1f871cffbf717d79207) by beachball)

## [9.11.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.11.3)

Mon, 20 May 2024 12:45:09 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.11.2..@fluentui/react-aria_v9.11.3)

### Patches

- Bump @fluentui/react-shared-contexts to v9.19.0 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.38 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-tabster to v9.21.3 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)
- Bump @fluentui/react-utilities to v9.18.9 ([PR #26682](https://github.com/microsoft/fluentui/pull/26682) by beachball)

## [9.11.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.11.2)

Thu, 09 May 2024 19:35:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.11.1..@fluentui/react-aria_v9.11.2)

### Patches

- Bump @fluentui/react-tabster to v9.21.2 ([PR #31321](https://github.com/microsoft/fluentui/pull/31321) by beachball)

## [9.11.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.11.1)

Mon, 06 May 2024 12:55:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.11.0..@fluentui/react-aria_v9.11.1)

### Patches

- feat: use ariaNotify in AriaLiveAnnouncer when available ([PR #31251](https://github.com/microsoft/fluentui/pull/31251) by <EMAIL>)
- Bump @fluentui/react-shared-contexts to v9.18.0 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.37 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-tabster to v9.21.1 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)
- Bump @fluentui/react-utilities to v9.18.8 ([PR #31271](https://github.com/microsoft/fluentui/pull/31271) by beachball)

## [9.11.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.11.0)

Thu, 02 May 2024 11:36:29 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.10.5..@fluentui/react-aria_v9.11.0)

### Minor changes

- Adding function to focus last active descendant if it exists ([PR #31140](https://github.com/microsoft/fluentui/pull/31140) by <EMAIL>)
- ActiveDescendantChangeEvent for tracking active item ([PR #31149](https://github.com/microsoft/fluentui/pull/31149) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.21.0 ([PR #31231](https://github.com/microsoft/fluentui/pull/31231) by beachball)

### Patches

- Updating ActiveDescendent scrollIntoView logic to work with ancester scroll containers and scroll margin styles ([PR #31158](https://github.com/microsoft/fluentui/pull/31158) by <EMAIL>)

## [9.10.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.10.5)

Tue, 23 Apr 2024 08:17:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.10.4..@fluentui/react-aria_v9.10.5)

### Patches

- Bump @fluentui/react-shared-contexts to v9.17.0 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.36 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-tabster to v9.20.1 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)
- Bump @fluentui/react-utilities to v9.18.7 ([PR #31113](https://github.com/microsoft/fluentui/pull/31113) by beachball)

## [9.10.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.10.4)

Wed, 17 Apr 2024 21:53:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.10.3..@fluentui/react-aria_v9.10.4)

### Patches

- fix: End key should move activeOption ([PR #30864](https://github.com/microsoft/fluentui/pull/30864) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.20.0 ([PR #31100](https://github.com/microsoft/fluentui/pull/31100) by beachball)

## [9.10.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.10.3)

Tue, 02 Apr 2024 09:48:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.10.2..@fluentui/react-aria_v9.10.3)

### Patches

- Bump @fluentui/react-shared-contexts to v9.16.0 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.35 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-tabster to v9.19.6 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)
- Bump @fluentui/react-utilities to v9.18.6 ([PR #30644](https://github.com/microsoft/fluentui/pull/30644) by beachball)

## [9.10.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.10.2)

Mon, 18 Mar 2024 19:50:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.10.1..@fluentui/react-aria_v9.10.2)

### Patches

- Bump @fluentui/react-shared-contexts to v9.15.2 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.34 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-tabster to v9.19.5 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)
- Bump @fluentui/react-utilities to v9.18.5 ([PR #30600](https://github.com/microsoft/fluentui/pull/30600) by beachball)

## [9.10.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.10.1)

Fri, 15 Mar 2024 21:43:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.10.0..@fluentui/react-aria_v9.10.1)

### Patches

- Bump @fluentui/react-shared-contexts to v9.15.1 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.33 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-tabster to v9.19.4 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)
- Bump @fluentui/react-utilities to v9.18.4 ([PR #30740](https://github.com/microsoft/fluentui/pull/30740) by beachball)

## [9.10.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.10.0)

Thu, 07 Mar 2024 19:33:21 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.9.1..@fluentui/react-aria_v9.10.0)

### Minor changes

- feat: active descendants imperative handle to toggle attribute ([PR #30716](https://github.com/microsoft/fluentui/pull/30716) by <EMAIL>)
- Bump @fluentui/react-shared-contexts to v9.15.0 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.32 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-tabster to v9.19.3 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)
- Bump @fluentui/react-utilities to v9.18.3 ([PR #30687](https://github.com/microsoft/fluentui/pull/30687) by beachball)

## [9.9.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.9.1)

Wed, 28 Feb 2024 02:34:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.9.0..@fluentui/react-aria_v9.9.1)

### Patches

- Bump @fluentui/react-shared-contexts to v9.14.1 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-jsx-runtime to v9.0.31 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-tabster to v9.19.2 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)
- Bump @fluentui/react-utilities to v9.18.2 ([PR #30639](https://github.com/microsoft/fluentui/pull/30639) by beachball)

## [9.9.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.9.0)

Tue, 20 Feb 2024 14:22:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.8.2..@fluentui/react-aria_v9.9.0)

### Minor changes

- feat: adds a context for active descendant ([PR #30528](https://github.com/microsoft/fluentui/pull/30528) by <EMAIL>)
- Bump @fluentui/react-jsx-runtime to v9.0.30 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-tabster to v9.19.1 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)
- Bump @fluentui/react-utilities to v9.18.1 ([PR #30543](https://github.com/microsoft/fluentui/pull/30543) by beachball)

## [9.8.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.8.2)

Tue, 06 Feb 2024 17:55:18 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.8.1..@fluentui/react-aria_v9.8.2)

### Patches

- chore: adds @fluentui/react-jsx-runtime as dependency ([PR #30485](https://github.com/microsoft/fluentui/pull/30485) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.19.0 ([PR #30392](https://github.com/microsoft/fluentui/pull/30392) by beachball)

## [9.8.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.8.1)

Tue, 30 Jan 2024 23:16:54 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.8.0..@fluentui/react-aria_v9.8.1)

### Patches

- Bump @fluentui/react-tabster to v9.18.0 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)
- Bump @fluentui/react-utilities to v9.18.0 ([PR #29983](https://github.com/microsoft/fluentui/pull/29983) by beachball)

## [9.8.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.8.0)

Tue, 23 Jan 2024 15:10:57 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.7.3..@fluentui/react-aria_v9.8.0)

### Minor changes

- feat: add AriaLiveAnnouncer component ([PR #30345](https://github.com/microsoft/fluentui/pull/30345) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.17.4 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)
- Bump @fluentui/react-utilities to v9.17.0 ([PR #30359](https://github.com/microsoft/fluentui/pull/30359) by beachball)

### Patches

- chore: import UnionToIntersection from react-utilities instead of redeclaring it locally ([PR #30317](https://github.com/microsoft/fluentui/pull/30317) by <EMAIL>)

## [9.7.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.7.3)

Thu, 18 Jan 2024 14:25:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.7.2..@fluentui/react-aria_v9.7.3)

### Patches

- Bump @fluentui/react-shared-contexts to v9.14.0 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-tabster to v9.17.3 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)
- Bump @fluentui/react-utilities to v9.16.1 ([PR #30046](https://github.com/microsoft/fluentui/pull/30046) by beachball)

## [9.7.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.7.2)

Wed, 17 Jan 2024 16:18:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.7.1..@fluentui/react-aria_v9.7.2)

### Patches

- Bump @fluentui/react-tabster to v9.17.2 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)
- Bump @fluentui/react-utilities to v9.16.0 ([PR #30339](https://github.com/microsoft/fluentui/pull/30339) by beachball)

## [9.7.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.7.1)

Tue, 16 Jan 2024 13:14:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.7.0..@fluentui/react-aria_v9.7.1)

### Patches

- fix: correct version of @types/react-dom peer dep that matches for 16.x ([PR #30259](https://github.com/microsoft/fluentui/pull/30259) by <EMAIL>)
- Bump @fluentui/react-tabster to v9.17.1 ([PR #30299](https://github.com/microsoft/fluentui/pull/30299) by beachball)

## [9.7.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.7.0)

Thu, 11 Jan 2024 09:04:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.6.2..@fluentui/react-aria_v9.7.0)

### Minor changes

- feat: active descedant improvements ([PR #30253](https://github.com/microsoft/fluentui/pull/30253) by <EMAIL>)

## [9.6.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.6.2)

Mon, 08 Jan 2024 16:24:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.6.1..@fluentui/react-aria_v9.6.2)

### Patches

- Bump @fluentui/react-utilities to v9.15.6 ([PR #30179](https://github.com/microsoft/fluentui/pull/30179) by beachball)

## [9.6.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.6.1)

Wed, 03 Jan 2024 09:26:42 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.6.0..@fluentui/react-aria_v9.6.1)

### Patches

- chore: deprecate old slot API methods and types ([PR #29646](https://github.com/microsoft/fluentui/pull/29646) by <EMAIL>)
- Bump @fluentui/react-utilities to v9.15.5 ([PR #30163](https://github.com/microsoft/fluentui/pull/30163) by beachball)

## [9.6.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.6.0)

Mon, 18 Dec 2023 14:40:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.5.0..@fluentui/react-aria_v9.6.0)

### Minor changes

- feat: deprecates useARIAButtonShorthand in favor of useARIAButtonProps ([PR #29735](https://github.com/microsoft/fluentui/pull/29735) by <EMAIL>)
- Bump @fluentui/react-shared-contexts to v9.13.2 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)
- Bump @fluentui/react-utilities to v9.15.4 ([PR #30103](https://github.com/microsoft/fluentui/pull/30103) by beachball)

## [9.5.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.5.0)

Thu, 14 Dec 2023 09:58:42 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.4.0..@fluentui/react-aria_v9.5.0)

### Minor changes

- feat(useActiveDescendant): Changing active descendant scrolls into view ([PR #29992](https://github.com/microsoft/fluentui/pull/29992) by <EMAIL>)
- Bump @fluentui/react-shared-contexts to v9.13.1 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)
- Bump @fluentui/react-utilities to v9.15.3 ([PR #30056](https://github.com/microsoft/fluentui/pull/30056) by beachball)

## [9.4.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.4.0)

Thu, 30 Nov 2023 13:42:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.44..@fluentui/react-aria_v9.4.0)

### Minor changes

- feat: Implement aria-activedescendant utility ([PR #29904](https://github.com/microsoft/fluentui/pull/29904) by <EMAIL>)

## [9.3.44](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.44)

Thu, 09 Nov 2023 17:29:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.43..@fluentui/react-aria_v9.3.44)

### Patches

- chore: use package.json#files setup instead of npmignore for all v9 libraries ([PR #29734](https://github.com/microsoft/fluentui/pull/29734) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.7 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)
- Bump @fluentui/react-utilities to v9.15.2 ([PR #29800](https://github.com/microsoft/fluentui/pull/29800) by beachball)

## [9.3.43](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.43)

Wed, 18 Oct 2023 17:54:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.42..@fluentui/react-aria_v9.3.43)

### Patches

- Bump @fluentui/react-utilities to v9.15.1 ([PR #29560](https://github.com/microsoft/fluentui/pull/29560) by beachball)

## [9.3.42](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.42)

Wed, 11 Oct 2023 13:54:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.41..@fluentui/react-aria_v9.3.42)

### Patches

- Bump @fluentui/react-utilities to v9.15.0 ([PR #29262](https://github.com/microsoft/fluentui/pull/29262) by beachball)

## [9.3.41](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.41)

Mon, 09 Oct 2023 20:45:44 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.40..@fluentui/react-aria_v9.3.41)

### Patches

- Bump @fluentui/react-utilities to v9.14.2 ([PR #29364](https://github.com/microsoft/fluentui/pull/29364) by beachball)

## [9.3.40](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.40)

Thu, 05 Oct 2023 15:25:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.39..@fluentui/react-aria_v9.3.40)

### Patches

- Bump @fluentui/react-utilities to v9.14.1 ([PR #29412](https://github.com/microsoft/fluentui/pull/29412) by beachball)

## [9.3.39](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.39)

Wed, 04 Oct 2023 08:45:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.38..@fluentui/react-aria_v9.3.39)

### Patches

- Bump @fluentui/react-utilities to v9.14.0 ([commit](https://github.com/microsoft/fluentui/commit/67b6cc6534e684ed32704dc6c0faee632bb840dc) by beachball)

## [9.3.38](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.38)

Tue, 26 Sep 2023 17:49:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.37..@fluentui/react-aria_v9.3.38)

### Patches

- chore: trigger manual version bump after broken release ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.6 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)
- Bump @fluentui/react-utilities to v9.13.5 ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by beachball)

## [9.3.37](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.37)

Tue, 26 Sep 2023 15:32:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.36..@fluentui/react-aria_v9.3.37)

### Patches

- fix: bump swc core to mitigate transpilation memory leaks ([PR #29253](https://github.com/microsoft/fluentui/pull/29253) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.5 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)
- Bump @fluentui/react-utilities to v9.13.4 ([PR #29300](https://github.com/microsoft/fluentui/pull/29300) by beachball)

## [9.3.36](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.36)

Wed, 06 Sep 2023 13:31:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.35..@fluentui/react-aria_v9.3.36)

### Patches

- Bump @fluentui/react-utilities to v9.13.3 ([PR #29080](https://github.com/microsoft/fluentui/pull/29080) by beachball)

## [9.3.35](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.35)

Tue, 05 Sep 2023 15:39:04 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.34..@fluentui/react-aria_v9.3.35)

### Patches

- Bump @fluentui/react-utilities to v9.13.2 ([PR #29055](https://github.com/microsoft/fluentui/pull/29055) by beachball)

## [9.3.34](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.34)

Tue, 05 Sep 2023 13:29:15 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.33..@fluentui/react-aria_v9.3.34)

### Patches

- bumps @swc/helpers version to 0.5.1 ([PR #28989](https://github.com/microsoft/fluentui/pull/28989) by <EMAIL>)
- bumps react peer dependencies to v16.14.0 ([PR #28959](https://github.com/microsoft/fluentui/pull/28959) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.4 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)
- Bump @fluentui/react-utilities to v9.13.1 ([PR #29056](https://github.com/microsoft/fluentui/pull/29056) by beachball)

## [9.3.33](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.33)

Tue, 29 Aug 2023 12:57:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.32..@fluentui/react-aria_v9.3.33)

### Patches

- Bump @fluentui/react-utilities to v9.13.0 ([PR #29005](https://github.com/microsoft/fluentui/pull/29005) by beachball)

## [9.3.32](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.32)

Thu, 24 Aug 2023 10:26:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.31..@fluentui/react-aria_v9.3.32)

### Patches

- Bump @fluentui/react-utilities to v9.12.0 ([PR #28973](https://github.com/microsoft/fluentui/pull/28973) by beachball)

## [9.3.31](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.31)

Wed, 23 Aug 2023 12:01:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.30..@fluentui/react-aria_v9.3.31)

### Patches

- Bump @fluentui/react-utilities to v9.11.2 ([PR #28957](https://github.com/microsoft/fluentui/pull/28957) by beachball)

## [9.3.30](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.30)

Fri, 11 Aug 2023 12:14:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.29..@fluentui/react-aria_v9.3.30)

### Patches

- Bump @fluentui/react-utilities to v9.11.1 ([PR #28808](https://github.com/microsoft/fluentui/pull/28808) by beachball)

## [9.3.29](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.29)

Wed, 09 Aug 2023 13:16:49 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.28..@fluentui/react-aria_v9.3.29)

### Patches

- chore(teams-prg): migrate to new slot API ([PR #28751](https://github.com/microsoft/fluentui/pull/28751) by <EMAIL>)

## [9.3.28](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.28)

Fri, 04 Aug 2023 08:52:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.26..@fluentui/react-aria_v9.3.28)

### Patches

- Bump @fluentui/react-utilities to v9.11.0 ([commit](https://github.com/microsoft/fluentui/commit/0bf7d9438c1d0ff90cd2b28bc4cceb4f807afbca) by beachball)

## [9.3.26](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.26)

Mon, 03 Jul 2023 11:57:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.25..@fluentui/react-aria_v9.3.26)

### Patches

- Bump @fluentui/react-utilities to v9.10.1 ([PR #28412](https://github.com/microsoft/fluentui/pull/28412) by beachball)

## [9.3.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.25)

Wed, 28 Jun 2023 11:12:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.24..@fluentui/react-aria_v9.3.25)

### Patches

- Bump @fluentui/react-utilities to v9.10.0 ([PR #28320](https://github.com/microsoft/fluentui/pull/28320) by beachball)

## [9.3.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.24)

Mon, 26 Jun 2023 09:53:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.23..@fluentui/react-aria_v9.3.24)

### Patches

- Bump @fluentui/react-utilities to v9.9.4 ([PR #28296](https://github.com/microsoft/fluentui/pull/28296) by beachball)

## [9.3.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.23)

Tue, 20 Jun 2023 12:39:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.22..@fluentui/react-aria_v9.3.23)

### Patches

- Bump @fluentui/react-utilities to v9.9.3 ([PR #28229](https://github.com/microsoft/fluentui/pull/28229) by beachball)

## [9.3.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.22)

Wed, 31 May 2023 06:46:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.21..@fluentui/react-aria_v9.3.22)

### Patches

- Bump @fluentui/react-utilities to v9.9.2 ([PR #28054](https://github.com/microsoft/fluentui/pull/28054) by beachball)

## [9.3.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.21)

Thu, 25 May 2023 10:00:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.20..@fluentui/react-aria_v9.3.21)

### Patches

- Bump @fluentui/react-utilities to v9.9.1 ([PR #27988](https://github.com/microsoft/fluentui/pull/27988) by beachball)

## [9.3.20](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.20)

Thu, 18 May 2023 00:39:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.19..@fluentui/react-aria_v9.3.20)

### Patches

- Bump @fluentui/react-utilities to v9.9.0 ([PR #27834](https://github.com/microsoft/fluentui/pull/27834) by beachball)

## [9.3.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.19)

Fri, 12 May 2023 20:28:12 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.18..@fluentui/react-aria_v9.3.19)

### Patches

- chore: exclude .swcrc from being published ([PR #27740](https://github.com/microsoft/fluentui/pull/27740) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.3 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)
- Bump @fluentui/react-utilities to v9.8.1 ([PR #27827](https://github.com/microsoft/fluentui/pull/27827) by beachball)

## [9.3.18](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.18)

Mon, 17 Apr 2023 17:54:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.17..@fluentui/react-aria_v9.3.18)

### Patches

- Bump @fluentui/react-utilities to v9.8.0 ([PR #27564](https://github.com/microsoft/fluentui/pull/27564) by beachball)

## [9.3.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.17)

Wed, 12 Apr 2023 09:31:45 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.16..@fluentui/react-aria_v9.3.17)

### Patches

- Bump @fluentui/react-utilities to v9.7.4 ([PR #27274](https://github.com/microsoft/fluentui/pull/27274) by beachball)

## [9.3.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.16)

Tue, 04 Apr 2023 18:44:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.15..@fluentui/react-aria_v9.3.16)

### Patches

- Bump @fluentui/react-utilities to v9.7.3 ([PR #27434](https://github.com/microsoft/fluentui/pull/27434) by beachball)

## [9.3.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.15)

Tue, 21 Mar 2023 21:23:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.14..@fluentui/react-aria_v9.3.15)

### Patches

- chore: migrate to swc transpilation approach. ([PR #27250](https://github.com/microsoft/fluentui/pull/27250) by <EMAIL>)
- fix: add node field to package.json exports map. ([PR #27154](https://github.com/microsoft/fluentui/pull/27154) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.2 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)
- Bump @fluentui/react-utilities to v9.7.2 ([PR #27271](https://github.com/microsoft/fluentui/pull/27271) by beachball)

## [9.3.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.14)

Thu, 16 Mar 2023 14:36:59 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.13..@fluentui/react-aria_v9.3.14)

### Patches

- Bump @fluentui/react-utilities to v9.7.1 ([PR #27229](https://github.com/microsoft/fluentui/pull/27229) by beachball)

## [9.3.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.13)

Mon, 13 Mar 2023 08:58:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.12..@fluentui/react-aria_v9.3.13)

### Patches

- Bump @fluentui/react-utilities to v9.7.0 ([PR #27161](https://github.com/microsoft/fluentui/pull/27161) by beachball)

## [9.3.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.12)

Fri, 10 Mar 2023 07:14:01 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.11..@fluentui/react-aria_v9.3.12)

### Patches

- Bump @fluentui/react-utilities to v9.6.2 ([PR #27157](https://github.com/microsoft/fluentui/pull/27157) by beachball)

## [9.3.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.11)

Wed, 08 Mar 2023 17:42:25 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.10..@fluentui/react-aria_v9.3.11)

### Patches

- Bump @fluentui/react-utilities to v9.6.1 ([PR #27127](https://github.com/microsoft/fluentui/pull/27127) by beachball)

## [9.3.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.10)

Wed, 15 Feb 2023 11:44:52 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.9..@fluentui/react-aria_v9.3.10)

### Patches

- Bump @fluentui/react-utilities to v9.6.0 ([PR #26845](https://github.com/microsoft/fluentui/pull/26845) by beachball)

## [9.3.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.9)

Mon, 13 Feb 2023 23:43:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.8..@fluentui/react-aria_v9.3.9)

### Patches

- Bump @fluentui/react-utilities to v9.5.3 ([PR #26814](https://github.com/microsoft/fluentui/pull/26814) by beachball)

## [9.3.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.8)

Fri, 10 Feb 2023 08:50:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.7..@fluentui/react-aria_v9.3.8)

### Patches

- Bump @fluentui/react-utilities to v9.5.2 ([commit](https://github.com/microsoft/fluentui/commit/cc62f050f8231e8f21a2cf7dddf33003e0ba3931) by beachball)

## [9.3.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.7)

Tue, 31 Jan 2023 19:53:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.6..@fluentui/react-aria_v9.3.7)

### Patches

- Bump @fluentui/react-utilities to v9.5.1 ([PR #26557](https://github.com/microsoft/fluentui/pull/26557) by beachball)

## [9.3.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.6)

Thu, 26 Jan 2023 13:31:03 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.5..@fluentui/react-aria_v9.3.6)

### Patches

- Bump @fluentui/react-utilities to v9.5.0 ([PR #26496](https://github.com/microsoft/fluentui/pull/26496) by beachball)

## [9.3.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.5)

Mon, 09 Jan 2023 14:35:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.4..@fluentui/react-aria_v9.3.5)

### Patches

- Bump @fluentui/react-utilities to v9.4.0 ([PR #26255](https://github.com/microsoft/fluentui/pull/26255) by beachball)

## [9.3.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.4)

Wed, 04 Jan 2023 01:40:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.3..@fluentui/react-aria_v9.3.4)

### Patches

- Bump @fluentui/react-utilities to v9.3.1 ([PR #26114](https://github.com/microsoft/fluentui/pull/26114) by beachball)

## [9.3.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.3)

Tue, 20 Dec 2022 14:59:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.2..@fluentui/react-aria_v9.3.3)

### Patches

- Bump @fluentui/react-utilities to v9.3.0 ([PR #26047](https://github.com/microsoft/fluentui/pull/26047) by beachball)

## [9.3.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.2)

Thu, 17 Nov 2022 23:05:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.1..@fluentui/react-aria_v9.3.2)

### Patches

- Bump @fluentui/react-utilities to v9.2.2 ([PR #25683](https://github.com/microsoft/fluentui/pull/25683) by beachball)

## [9.3.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.1)

Fri, 11 Nov 2022 14:57:51 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.3.0..@fluentui/react-aria_v9.3.1)

### Patches

- fix: create valid export maps ([PR #25558](https://github.com/microsoft/fluentui/pull/25558) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.1 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)
- Bump @fluentui/react-utilities to v9.2.1 ([PR #25615](https://github.com/microsoft/fluentui/pull/25615) by beachball)

## [9.3.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.3.0)

Wed, 02 Nov 2022 11:57:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.2.3..@fluentui/react-aria_v9.3.0)

### Minor changes

- exposes internal typings: ARIAButtonAlteredProps and ARIAButtonElement ([PR #25403](https://github.com/microsoft/fluentui/pull/25403) by <EMAIL>)
- Bump @fluentui/react-utilities to v9.2.0 ([PR #25456](https://github.com/microsoft/fluentui/pull/25456) by beachball)

## [9.2.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.2.3)

Thu, 20 Oct 2022 08:39:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.2.2..@fluentui/react-aria_v9.2.3)

### Patches

- chore: Bump peer deps to support React 18 ([PR #24972](https://github.com/microsoft/fluentui/pull/24972) by <EMAIL>)
- Bump @fluentui/react-utilities to v9.1.2 ([PR #25265](https://github.com/microsoft/fluentui/pull/25265) by beachball)

## [9.2.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.2.2)

Thu, 13 Oct 2022 11:03:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.2.1..@fluentui/react-aria_v9.2.2)

### Patches

- Bump @fluentui/react-utilities to v9.1.1 ([PR #25181](https://github.com/microsoft/fluentui/pull/25181) by beachball)

## [9.2.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.2.1)

Mon, 03 Oct 2022 22:24:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.2.0..@fluentui/react-aria_v9.2.1)

### Patches

- chore: user defined tabIndex should prevail aria-button defined ([PR #24962](https://github.com/microsoft/fluentui/pull/24962) by <EMAIL>)

## [9.2.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.2.0)

Thu, 15 Sep 2022 09:49:48 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.1.0..@fluentui/react-aria_v9.2.0)

### Minor changes

- feat: add helper types to assist DOM element handling ([PR #24722](https://github.com/microsoft/fluentui/pull/24722) by <EMAIL>)
- Bump @fluentui/react-utilities to v9.1.0 ([PR #24808](https://github.com/microsoft/fluentui/pull/24808) by beachball)

### Patches

- docs(react-aria): re-generate api.md ([PR #23369](https://github.com/microsoft/fluentui/pull/23369) by <EMAIL>)
- chore(react-aria): improve internal typings ([PR #24742](https://github.com/microsoft/fluentui/pull/24742) by <EMAIL>)
- feat: upgrade typings on useARIAButtonProps to be more specific ([PR #24177](https://github.com/microsoft/fluentui/pull/24177) by <EMAIL>)

## [9.1.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.1.0)

Wed, 03 Aug 2022 16:03:37 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.2..@fluentui/react-aria_v9.1.0)

### Minor changes

- chore: Splits useARIAButton into useARIAButtonProps and useARIAButtonShorthand. ([PR #24032](https://github.com/microsoft/fluentui/pull/24032) by <EMAIL>)

### Patches

- feat: treats aria-disabled as disabled state on useARIAButtonProps ([PR #24197](https://github.com/microsoft/fluentui/pull/24197) by <EMAIL>)

## [9.0.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.2)

Thu, 14 Jul 2022 21:21:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.1..@fluentui/react-aria_v9.0.2)

### Patches

- fix: Fixing bad version bump of @fluentui/react-utilities. ([PR #23920](https://github.com/microsoft/fluentui/pull/23920) by <EMAIL>)
- Bump @fluentui/react-utilities to v9.0.2 ([PR #23918](https://github.com/microsoft/fluentui/pull/23918) by beachball)

## [9.0.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.1)

Thu, 14 Jul 2022 17:06:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0..@fluentui/react-aria_v9.0.1)

### Patches

- Bump @fluentui/react-utilities to v9.0.1-0 ([PR #23897](https://github.com/microsoft/fluentui/pull/23897) by beachball)

## [9.0.0](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0)

Tue, 28 Jun 2022 15:14:13 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-rc.10..@fluentui/react-aria_v9.0.0)

### Patches

- feat: Initial 9.0.0 release ([PR #23733](https://github.com/microsoft/fluentui/pull/23733) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)
- Bump @fluentui/react-utilities to v9.0.0 ([commit](https://github.com/microsoft/fluentui/commit/ba6c5d651559b91c815429c9a9357c4d5a390f3e) by beachball)

### Changes

- Update 9.0.0-rc dependencies to use caret range ([PR #23732](https://github.com/microsoft/fluentui/pull/23732) by <EMAIL>)
- chore: Mark internal APIs with @internal ([PR #23689](https://github.com/microsoft/fluentui/pull/23689) by <EMAIL>)

## [9.0.0-rc.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-rc.10)

Tue, 31 May 2022 21:28:50 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-rc.9..@fluentui/react-aria_v9.0.0-rc.10)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-rc.10 ([PR #23325](https://github.com/microsoft/fluentui/pull/23325) by beachball)

## [9.0.0-rc.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-rc.9)

Mon, 23 May 2022 12:14:24 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-rc.8..@fluentui/react-aria_v9.0.0-rc.9)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-rc.9 ([PR #23030](https://github.com/microsoft/fluentui/pull/23030) by beachball)

## [9.0.0-rc.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-rc.8)

Thu, 05 May 2022 18:26:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-rc.7..@fluentui/react-aria_v9.0.0-rc.8)

### Changes

- Removing star exports. ([PR #22798](https://github.com/microsoft/fluentui/pull/22798) by <EMAIL>)
- feat: ship rolluped only dts ([PR #22823](https://github.com/microsoft/fluentui/pull/22823) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-rc.6 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.8 ([PR #22857](https://github.com/microsoft/fluentui/pull/22857) by beachball)

## [9.0.0-rc.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-rc.7)

Wed, 04 May 2022 13:26:37 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-rc.6..@fluentui/react-aria_v9.0.0-rc.7)

### Changes

- Bump @fluentui/keyboard-keys to v9.0.0-rc.5 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.7 ([PR #22786](https://github.com/microsoft/fluentui/pull/22786) by beachball)

## [9.0.0-rc.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-rc.6)

Tue, 19 Apr 2022 19:17:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-rc.5..@fluentui/react-aria_v9.0.0-rc.6)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-rc.6 ([PR #21995](https://github.com/microsoft/fluentui/pull/21995) by beachball)

## [9.0.0-rc.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-rc.5)

Fri, 04 Mar 2022 05:17:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-rc.4..@fluentui/react-aria_v9.0.0-rc.5)

### Changes

- Adding explicit export maps on all consumer packages for FUIR 8 and 9. ([PR #21508](https://github.com/microsoft/fluentui/pull/21508) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-rc.4 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.5 ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by beachball)

## [9.0.0-rc.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-rc.4)

Tue, 01 Mar 2022 02:17:40 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-rc.3..@fluentui/react-aria_v9.0.0-rc.4)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-rc.4 ([PR #21884](https://github.com/microsoft/fluentui/pull/21884) by beachball)

## [9.0.0-rc.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-rc.3)

Fri, 18 Feb 2022 13:35:38 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-rc.1..@fluentui/react-aria_v9.0.0-rc.3)

### Changes

- fix: Source maps contain original source code ([PR #21690](https://github.com/microsoft/fluentui/pull/21690) by <EMAIL>)
- Fixing issue in useAriaButton where passing disabledFocusable=false caused aria-disabled to be set to false and appear in DOM instead of just be undefined for that element. ([PR #21703](https://github.com/microsoft/fluentui/pull/21703) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.3 ([PR #21800](https://github.com/microsoft/fluentui/pull/21800) by beachball)

## [9.0.0-rc.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-rc.1)

Thu, 10 Feb 2022 08:52:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-beta.4..@fluentui/react-aria_v9.0.0-rc.1)

### Changes

- remove Griffel packages ([PR #21440](https://github.com/microsoft/fluentui/pull/21440) by <EMAIL>)
- Bump Fluent UI packages to 9.0.0-rc ([PR #21623](https://github.com/microsoft/fluentui/pull/21623) by <EMAIL>)
- Updating based on changes to composition types. ([PR #20891](https://github.com/microsoft/fluentui/pull/20891) by <EMAIL>)
- Refactor component Slot typings ([PR #21518](https://github.com/microsoft/fluentui/pull/21518) by <EMAIL>)
- Remove component's shorthandProps array ([PR #21134](https://github.com/microsoft/fluentui/pull/21134) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-rc.1 ([commit](https://github.com/microsoft/fluentui/commit/e6c855f6d9019d6c73668d15fc9bc3a13291a6c8) by beachball)

## [9.0.0-beta.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-beta.4)

Thu, 25 Nov 2021 08:34:10 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-beta.3..@fluentui/react-aria_v9.0.0-beta.4)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)
- Bump @fluentui/babel-make-styles to v9.0.0-beta.4 ([PR #20762](https://github.com/microsoft/fluentui/pull/20762) by beachball)

## [9.0.0-beta.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-beta.3)

Fri, 12 Nov 2021 13:25:13 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-beta.2..@fluentui/react-aria_v9.0.0-beta.3)

### Changes

- Fixing events being fired when button is disabledFocusable ([PR #20342](https://github.com/microsoft/fluentui/pull/20342) by <EMAIL>)
- Updated beta and RC components to ES2019 ([PR #20405](https://github.com/microsoft/fluentui/pull/20405) by <EMAIL>)
- Bump @fluentui/react-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.3 ([PR #20583](https://github.com/microsoft/fluentui/pull/20583) by beachball)

## [9.0.0-beta.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-beta.2)

Wed, 27 Oct 2021 12:14:13 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-beta.1..@fluentui/react-aria_v9.0.0-beta.2)

### Changes

- added styling of documentation ([PR #20193](https://github.com/microsoft/fluentui/pull/20193) by <EMAIL>)
- Bump @fluentui/react-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.2 ([PR #20353](https://github.com/microsoft/fluentui/pull/20353) by beachball)

## [9.0.0-beta.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-beta.1)

Wed, 06 Oct 2021 10:37:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.45..@fluentui/react-aria_v9.0.0-beta.1)

### Changes

- Bump all v9 components to beta prerelease tag ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-beta.1 ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by beachball)

## [9.0.0-alpha.45](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.45)

Tue, 05 Oct 2021 12:47:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.44..@fluentui/react-aria_v9.0.0-alpha.45)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.78 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.53 ([PR #20108](https://github.com/microsoft/fluentui/pull/20108) by beachball)

## [9.0.0-alpha.44](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.44)

Tue, 05 Oct 2021 09:28:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.43..@fluentui/react-aria_v9.0.0-alpha.44)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.77 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.56 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.52 ([PR #20081](https://github.com/microsoft/fluentui/pull/20081) by beachball)

## [9.0.0-alpha.43](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.43)

Fri, 01 Oct 2021 14:13:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.41..@fluentui/react-aria_v9.0.0-alpha.43)

### Changes

- Bump v9 prerelease versions to rerelease ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by <EMAIL>)
- Bump @fluentui/keyboard-keys to v9.0.0-alpha.5 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-make-styles to v9.0.0-alpha.76 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.55 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.51 ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by beachball)

## [9.0.0-alpha.41](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.41)

Fri, 01 Oct 2021 12:30:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.40..@fluentui/react-aria_v9.0.0-alpha.41)

### Changes

- Removes disabled attribute when not required ([PR #18814](https://github.com/microsoft/fluentui/pull/18814) by <EMAIL>)

## [9.0.0-alpha.40](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.40)

Wed, 29 Sep 2021 08:06:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.39..@fluentui/react-aria_v9.0.0-alpha.40)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.74 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.49 ([PR #19660](https://github.com/microsoft/fluentui/pull/19660) by beachball)

## [9.0.0-alpha.39](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.39)

Mon, 27 Sep 2021 08:06:00 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.38..@fluentui/react-aria_v9.0.0-alpha.39)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.73 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.53 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.48 ([PR #19981](https://github.com/microsoft/fluentui/pull/19981) by beachball)

## [9.0.0-alpha.38](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.38)

Fri, 24 Sep 2021 09:17:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.37..@fluentui/react-aria_v9.0.0-alpha.38)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.72 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.52 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.47 ([PR #19950](https://github.com/microsoft/fluentui/pull/19950) by beachball)

## [9.0.0-alpha.37](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.37)

Thu, 23 Sep 2021 08:21:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.36..@fluentui/react-aria_v9.0.0-alpha.37)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.71 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.51 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.46 ([commit](https://github.com/microsoft/fluentui/commit/95682da34c48813f7658032ae490d21d2f363b90) by beachball)

## [9.0.0-alpha.36](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.36)

Wed, 22 Sep 2021 10:10:07 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.35..@fluentui/react-aria_v9.0.0-alpha.36)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.70 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/react-utilities to v9.0.0-alpha.50 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.45 ([commit](https://github.com/microsoft/fluentui/commit/bc3f1ec72fc7784a558b0dd6598ee0662f4649c1) by beachball)

## [9.0.0-alpha.35](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.35)

Tue, 21 Sep 2021 07:42:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.34..@fluentui/react-aria_v9.0.0-alpha.35)

### Changes

- Bump @fluentui/react-make-styles to v9.0.0-alpha.69 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)
- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.44 ([PR #19865](https://github.com/microsoft/fluentui/pull/19865) by beachball)

## [9.0.0-alpha.34](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.34)

Mon, 20 Sep 2021 07:36:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.33..@fluentui/react-aria_v9.0.0-alpha.34)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.34 ([PR #19844](https://github.com/microsoft/fluentui/pull/19844) by <EMAIL>)

## [9.0.0-alpha.33](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.33)

Fri, 17 Sep 2021 07:35:26 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.32..@fluentui/react-aria_v9.0.0-alpha.33)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.33 ([PR #19840](https://github.com/microsoft/fluentui/pull/19840) by <EMAIL>)

## [9.0.0-alpha.32](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.32)

Thu, 16 Sep 2021 07:38:39 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.31..@fluentui/react-aria_v9.0.0-alpha.32)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.32 ([PR #19815](https://github.com/microsoft/fluentui/pull/19815) by <EMAIL>)

## [9.0.0-alpha.31](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.31)

Tue, 14 Sep 2021 20:09:02 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.30..@fluentui/react-aria_v9.0.0-alpha.31)

### Changes

- Bump @fluentui/react-aria to v9.0.0-alpha.31 ([PR #19155](https://github.com/microsoft/fluentui/pull/19155) by <EMAIL>)

## [9.0.0-alpha.30](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.30)

Tue, 14 Sep 2021 07:38:18 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.29..@fluentui/react-aria_v9.0.0-alpha.30)

### Changes

- Generalize mergeARIADisabled ([PR #19605](https://github.com/microsoft/fluentui/pull/19605) by <EMAIL>)

## [9.0.0-alpha.29](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.29)

Fri, 10 Sep 2021 16:31:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.28..@fluentui/react-aria_v9.0.0-alpha.29)

### Changes

- chore(v9): Move all internal v9 dependencies from caret to fixed version ([PR #19748](https://github.com/microsoft/fluentui/pull/19748) by <EMAIL>)
- Bump @fluentui/react-aria to v9.0.0-alpha.29 ([PR #19748](https://github.com/microsoft/fluentui/pull/19748) by <EMAIL>)

## [9.0.0-alpha.28](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.28)

Fri, 10 Sep 2021 07:39:51 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.27..@fluentui/react-aria_v9.0.0-alpha.28)

### Changes

- Refactor ObjectShorthandProps into IntrinsicShorthandProps ([PR #19642](https://github.com/microsoft/fluentui/pull/19642) by <EMAIL>)

## [9.0.0-alpha.27](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.27)

Mon, 06 Sep 2021 07:34:53 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.26..@fluentui/react-aria_v9.0.0-alpha.27)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.44 ([PR #19640](https://github.com/microsoft/fluentui/pull/19640) by <EMAIL>)

## [9.0.0-alpha.26](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.26)

Thu, 02 Sep 2021 07:36:46 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.25..@fluentui/react-aria_v9.0.0-alpha.26)

### Patches

- Bump @fluentui/react-conformance to v0.4.5 ([PR #19590](https://github.com/microsoft/fluentui/pull/19590) by <EMAIL>)

## [9.0.0-alpha.25](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.25)

Wed, 01 Sep 2021 07:39:56 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.24..@fluentui/react-aria_v9.0.0-alpha.25)

### Changes

- Updates react-aria to use root as slot ([PR #19483](https://github.com/microsoft/fluentui/pull/19483) by <EMAIL>)

## [9.0.0-alpha.24](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.24)

Tue, 31 Aug 2021 07:37:47 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.23..@fluentui/react-aria_v9.0.0-alpha.24)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.42 ([PR #19556](https://github.com/microsoft/fluentui/pull/19556) by <EMAIL>)

## [9.0.0-alpha.23](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.23)

Mon, 30 Aug 2021 07:35:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.22..@fluentui/react-aria_v9.0.0-alpha.23)

### Changes

- Updating TypeScript type-only imports/exports to use import/export type syntax. ([PR #19485](https://github.com/microsoft/fluentui/pull/19485) by <EMAIL>)

## [9.0.0-alpha.22](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.22)

Fri, 20 Aug 2021 07:37:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.21..@fluentui/react-aria_v9.0.0-alpha.22)

### Changes

- Update .npmignore ([PR #19441](https://github.com/microsoft/fluentui/pull/19441) by <EMAIL>)

## [9.0.0-alpha.21](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.21)

Thu, 19 Aug 2021 07:41:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.20..@fluentui/react-aria_v9.0.0-alpha.21)

### Changes

- Updates react-aria on slot null rendering ([PR #19273](https://github.com/microsoft/fluentui/pull/19273) by <EMAIL>)

## [9.0.0-alpha.20](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.20)

Fri, 13 Aug 2021 07:36:34 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.19..@fluentui/react-aria_v9.0.0-alpha.20)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.32 ([PR #19341](https://github.com/microsoft/fluentui/pull/19341) by <EMAIL>)

## [9.0.0-alpha.19](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.19)

Fri, 06 Aug 2021 07:35:14 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.18..@fluentui/react-aria_v9.0.0-alpha.19)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.31 ([PR #19281](https://github.com/microsoft/fluentui/pull/19281) by <EMAIL>)

## [9.0.0-alpha.18](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.18)

Tue, 03 Aug 2021 07:39:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.17..@fluentui/react-aria_v9.0.0-alpha.18)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.3 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.4 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.30 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)

## [9.0.0-alpha.17](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.17)

Mon, 02 Aug 2021 07:36:20 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.16..@fluentui/react-aria_v9.0.0-alpha.17)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.36 ([PR #19204](https://github.com/microsoft/fluentui/pull/19204) by <EMAIL>)

## [9.0.0-alpha.16](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.16)

Mon, 26 Jul 2021 07:37:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.15..@fluentui/react-aria_v9.0.0-alpha.16)

### Changes

- add Babel plugins to build pipeline ([PR #18968](https://github.com/microsoft/fluentui/pull/18968) by <EMAIL>)

## [9.0.0-alpha.15](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.15)

Fri, 23 Jul 2021 07:38:19 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.14..@fluentui/react-aria_v9.0.0-alpha.15)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.27 ([PR #19041](https://github.com/microsoft/fluentui/pull/19041) by <EMAIL>)

## [9.0.0-alpha.14](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.14)

Thu, 22 Jul 2021 07:36:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.13..@fluentui/react-aria_v9.0.0-alpha.14)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.34 ([PR #19023](https://github.com/microsoft/fluentui/pull/19023) by <EMAIL>)

## [9.0.0-alpha.13](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.13)

Tue, 20 Jul 2021 22:23:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.12..@fluentui/react-aria_v9.0.0-alpha.13)

### Changes

- chore(react-aria): Replace keyboard-key with keyboard-keys ([PR #19016](https://github.com/microsoft/fluentui/pull/19016) by <EMAIL>)

## [9.0.0-alpha.12](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.12)

Thu, 15 Jul 2021 07:36:18 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.11..@fluentui/react-aria_v9.0.0-alpha.12)

### Changes

- Fix react-aria due to changes on slots typings ([PR #18861](https://github.com/microsoft/fluentui/pull/18861) by <EMAIL>)

## [9.0.0-alpha.11](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.11)

Tue, 13 Jul 2021 22:32:58 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.10..@fluentui/react-aria_v9.0.0-alpha.11)

### Patches

- Bump @fluentui/react-conformance to v0.4.3 ([PR #18925](https://github.com/microsoft/fluentui/pull/18925) by <EMAIL>)

## [9.0.0-alpha.10](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.10)

Tue, 13 Jul 2021 07:35:36 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.9..@fluentui/react-aria_v9.0.0-alpha.10)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.24 ([PR #18560](https://github.com/microsoft/fluentui/pull/18560) by <EMAIL>)

## [9.0.0-alpha.9](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.9)

Mon, 12 Jul 2021 07:33:23 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.8..@fluentui/react-aria_v9.0.0-alpha.9)

### Changes

- Update prop merging mechanism ([PR #18813](https://github.com/microsoft/fluentui/pull/18813) by <EMAIL>)
- Removes forcing children on useARIAButton ([PR #18879](https://github.com/microsoft/fluentui/pull/18879) by <EMAIL>)

## [9.0.0-alpha.8](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.8)

Fri, 09 Jul 2021 07:39:31 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.7..@fluentui/react-aria_v9.0.0-alpha.8)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.2 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/keyboard-key to v0.3.3 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/react-conformance to v0.4.2 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.23 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)

## [9.0.0-alpha.7](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.7)

Fri, 02 Jul 2021 23:15:55 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.6..@fluentui/react-aria_v9.0.0-alpha.7)

### Changes

- Bump @fluentui/react-utilities to v9.0.0-alpha.31 ([PR #18721](https://github.com/microsoft/fluentui/pull/18721) by <EMAIL>)

## [9.0.0-alpha.6](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.6)

Fri, 02 Jul 2021 07:37:06 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.5..@fluentui/react-aria_v9.0.0-alpha.6)

### Changes

- Rename typings and getSlots to have the Compat Suffix ([PR #18796](https://github.com/microsoft/fluentui/pull/18796) by <EMAIL>)

## [9.0.0-alpha.5](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.5)

Thu, 01 Jul 2021 07:35:05 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.4..@fluentui/react-aria_v9.0.0-alpha.5)

### Changes

- Fixing bug in start script of converged packages. ([PR #18768](https://github.com/microsoft/fluentui/pull/18768) by <EMAIL>)

## [9.0.0-alpha.4](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.4)

Wed, 30 Jun 2021 07:38:35 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.3..@fluentui/react-aria_v9.0.0-alpha.4)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.20 ([PR #18695](https://github.com/microsoft/fluentui/pull/18695) by <EMAIL>)

## [9.0.0-alpha.3](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.3)

Tue, 29 Jun 2021 07:33:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.2..@fluentui/react-aria_v9.0.0-alpha.3)

### Changes

- Bump @fluentui/jest-serializer-make-styles to v9.0.0-alpha.19 ([PR #18169](https://github.com/microsoft/fluentui/pull/18169) by <EMAIL>)

## [9.0.0-alpha.2](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.2)

Mon, 21 Jun 2021 07:34:33 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/react-aria_v9.0.0-alpha.1..@fluentui/react-aria_v9.0.0-alpha.2)

### Changes

- Fix useARIAButton children problem ([PR #18597](https://github.com/microsoft/fluentui/pull/18597) by <EMAIL>)

## [9.0.0-alpha.1](https://github.com/microsoft/fluentui/tree/@fluentui/react-aria_v9.0.0-alpha.1)

Thu, 17 Jun 2021 07:34:11 GMT

### Changes

- Adds @fluentui/keyboard-key due to legacy ([PR #18585](https://github.com/microsoft/fluentui/pull/18585) by <EMAIL>)
- Creates react-aria package ([PR #18550](https://github.com/microsoft/fluentui/pull/18550) by <EMAIL>)
