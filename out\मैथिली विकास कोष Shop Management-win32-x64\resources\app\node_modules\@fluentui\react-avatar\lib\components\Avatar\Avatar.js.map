{"version": 3, "sources": ["../src/components/Avatar/Avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAvatar_unstable } from './renderAvatar';\nimport { useAvatar_unstable } from './useAvatar';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAvatarStyles_unstable } from './useAvatarStyles.styles';\nimport type { AvatarProps } from './Avatar.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\nexport const Avatar: ForwardRefComponent<AvatarProps> = React.forwardRef((props, ref) => {\n  const state = useAvatar_unstable(props, ref);\n\n  useAvatarStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAvatarStyles_unstable')(state);\n\n  return renderAvatar_unstable(state);\n});\n\nAvatar.displayName = 'Avatar';\n"], "names": ["React", "renderAvatar_unstable", "useAvatar_unstable", "useCustomStyleHook_unstable", "useAvatarStyles_unstable", "Avatar", "forwardRef", "props", "ref", "state", "displayName"], "rangeMappings": ";;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,kBAAkB,QAAQ,cAAc;AACjD,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,wBAAwB,QAAQ,2BAA2B;AAIpE,OAAO,MAAMC,uBAA2CL,MAAMM,UAAU,CAAC,CAACC,OAAOC;IAC/E,MAAMC,QAAQP,mBAAmBK,OAAOC;IAExCJ,yBAAyBK;IAEzBN,4BAA4B,4BAA4BM;IAExD,OAAOR,sBAAsBQ;AAC/B,GAAG;AAEHJ,OAAOK,WAAW,GAAG"}