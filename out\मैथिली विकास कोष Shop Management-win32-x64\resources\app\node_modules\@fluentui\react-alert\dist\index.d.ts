/// <reference types="react" />

import { Avatar } from '@fluentui/react-avatar';
import { Button } from '@fluentui/react-button';
import type { ComponentProps } from '@fluentui/react-utilities';
import type { ComponentState } from '@fluentui/react-utilities';
import type { ForwardRefComponent } from '@fluentui/react-utilities';
import * as React_2 from 'react';
import type { Slot } from '@fluentui/react-utilities';
import type { SlotClassNames } from '@fluentui/react-utilities';

/**
 * @deprecated please use the Toast or MessageBar component
 * An Alert component displays a brief, important message to attract a user's attention
 *  without interrupting their current task.
 */
export declare const Alert: ForwardRefComponent<AlertProps>;

export declare const alertClassNames: SlotClassNames<AlertSlots>;

/**
 * Alert Props
 * @deprecated please use the Toast or MessageBar component
 */
export declare type AlertProps = ComponentProps<AlertSlots> & {
    /**
     * The intent prop, if present, determines the icon to be rendered in the icon slot. The icon prop
     * overrides the intent prop
     */
    intent?: 'info' | 'success' | 'error' | 'warning';
    /**
     * The appearance of the Alert.
     * @default 'primary'
     */
    appearance?: 'primary' | 'inverted';
};

/**
 * @deprecated please use the Toast or MessageBar component
 */
export declare type AlertSlots = {
    /**
     * The root slot is the top level container for the alert component
     */
    root: NonNullable<Slot<'div'>>;
    /**
     * The icon slot renders the icon determined by the `icon` or `intent` prop
     */
    icon?: Slot<'span'>;
    /**
     * The action slot renders a button that prompts the user to take action on the alert
     */
    action?: Slot<typeof Button>;
    /**
     * The avatar slot renders an avatar before the contents of the alert
     */
    avatar?: Slot<typeof Avatar>;
};

/**
 * State used in rendering Alert
 * @deprecated please use the Toast or MessageBar component
 */
export declare type AlertState = ComponentState<AlertSlots> & Pick<AlertProps, 'intent'> & Required<Pick<AlertProps, 'appearance'>>;

/**
 * @deprecated please use the Toast or MessageBar component
 */
export declare const renderAlert_unstable: (state: AlertState) => JSX.Element;

/**
 * @deprecated please use the Toast or MessageBar component
 * Create the state required to render Alert.
 *
 * The returned state can be modified with hooks such as useAlertStyles_unstable,
 * before being passed to renderAlert_unstable.
 *
 * @param props - props from this instance of Alert
 * @param ref - reference to root HTMLElement of Alert
 */
export declare const useAlert_unstable: (props: AlertProps, ref: React_2.Ref<HTMLElement>) => AlertState;

/**
 * @deprecated please use the Toast or MessageBar component
 * Apply styling to the Alert slots based on the state
 */
export declare const useAlertStyles_unstable: (state: AlertState) => AlertState;

export { }
