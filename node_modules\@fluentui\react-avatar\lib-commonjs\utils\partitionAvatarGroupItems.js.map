{"version": 3, "sources": ["../src/utils/partitionAvatarGroupItems.ts"], "sourcesContent": ["export type PartitionAvatarGroupItemsOptions<T> = {\n  items: readonly T[];\n  layout?: 'spread' | 'stack' | 'pie';\n  maxInlineItems?: number;\n};\n\nexport type PartitionAvatarGroupItems<T> = {\n  inlineItems: readonly T[];\n  overflowItems?: readonly T[];\n};\n\n/**\n * Get the inline items and overflowing items based on the array of AvatarGroupItems needed for AvatarGroup.\n *\n * @param options - Configure the partition options\n *\n * @returns Two arrays split into inline items and overflow items based on maxInlineItems.\n */\nexport const partitionAvatarGroupItems = <T>(\n  options: PartitionAvatarGroupItemsOptions<T>,\n): PartitionAvatarGroupItems<T> => {\n  const { items } = options;\n  const isPie = options.layout === 'pie';\n\n  if (isPie) {\n    return {\n      inlineItems: items.slice(0, 3),\n      overflowItems: items.length > 0 ? items : undefined,\n    };\n  }\n\n  const maxInlineItems = options.maxInlineItems ?? 5;\n  const inlineCount = -(maxInlineItems - (items.length > maxInlineItems ? 1 : 0));\n  const overflowItems = items.slice(0, inlineCount);\n\n  return {\n    inlineItems: items.slice(inlineCount),\n    overflowItems: overflowItems.length > 0 ? overflowItems : undefined,\n  };\n};\n"], "names": ["partitionAvatarGroupItems", "options", "items", "is<PERSON><PERSON>", "layout", "inlineItems", "slice", "overflowItems", "length", "undefined", "maxInlineItems", "inlineCount"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAWA;;;;;;CAMC;;;;+BACYA;;;eAAAA;;;AAAN,MAAMA,4BAA4B,CACvCC;IAEA,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,MAAME,QAAQF,QAAQG,MAAM,KAAK;IAEjC,IAAID,OAAO;QACT,OAAO;YACLE,aAAaH,MAAMI,KAAK,CAAC,GAAG;YAC5BC,eAAeL,MAAMM,MAAM,GAAG,IAAIN,QAAQO;QAC5C;IACF;QAEuBR;IAAvB,MAAMS,iBAAiBT,CAAAA,0BAAAA,QAAQS,cAAc,AAAdA,MAAc,QAAtBT,4BAAAA,KAAAA,IAAAA,0BAA0B;IACjD,MAAMU,cAAc,CAAED,CAAAA,iBAAkBR,CAAAA,MAAMM,MAAM,GAAGE,iBAAiB,IAAI,CAAA,CAAA;IAC5E,MAAMH,gBAAgBL,MAAMI,KAAK,CAAC,GAAGK;IAErC,OAAO;QACLN,aAAaH,MAAMI,KAAK,CAACK;QACzBJ,eAAeA,cAAcC,MAAM,GAAG,IAAID,gBAAgBE;IAC5D;AACF"}