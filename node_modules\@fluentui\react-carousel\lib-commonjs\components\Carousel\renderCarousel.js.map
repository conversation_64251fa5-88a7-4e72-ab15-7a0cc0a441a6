{"version": 3, "sources": ["../src/components/Carousel/renderCarousel.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { CarouselState, CarouselSlots } from './Carousel.types';\nimport { CarouselProvider } from '../CarouselContext';\nimport type { CarouselContextValues } from '../CarouselContext.types';\n\n/**\n * Render the final JSX of Carousel\n */\nexport const renderCarousel_unstable = (state: CarouselState, contextValues: CarouselContextValues) => {\n  assertSlots<CarouselSlots>(state);\n\n  return (\n    <CarouselProvider value={contextValues.carousel}>\n      <state.root />\n    </CarouselProvider>\n  );\n};\n"], "names": ["renderCarousel_unstable", "state", "contextValues", "assertSlots", "_jsx", "CarouselProvider", "value", "carousel", "root"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAWaA;;;eAAAA;;;4BAVb;gCAE4B;iCAEK;AAM1B,MAAMA,0BAA0B,CAACC,OAAsBC;IAC5DC,IAAAA,2BAAAA,EAA2BF;IAE3B,OAAA,WAAA,GACEG,IAAAA,eAAA,EAACC,iCAAAA,EAAAA;QAAiBC,OAAOJ,cAAcK,QAAQ;kBAC7C,WAAA,GAAAH,IAAAA,eAAA,EAACH,MAAMO,IAAI,EAAA,CAAA;;AAGjB"}