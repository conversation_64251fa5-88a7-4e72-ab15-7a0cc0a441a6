"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useOptionWalker", {
    enumerable: true,
    get: function() {
        return useOptionWalker;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _reactsharedcontexts = require("@fluentui/react-shared-contexts");
const _reactutilities = require("@fluentui/react-utilities");
function useOptionWalker(options) {
    const { matchOption } = options;
    const { targetDocument } = (0, _reactsharedcontexts.useFluent_unstable)();
    const treeWalkerRef = _react.useRef(null);
    const listboxRef = _react.useRef(null);
    const optionFilter = _react.useCallback((node)=>{
        if ((0, _reactutilities.isHTMLElement)(node) && matchOption(node)) {
            return NodeFilter.FILTER_ACCEPT;
        }
        return NodeFilter.FILTER_SKIP;
    }, [
        matchOption
    ]);
    const setListbox = _react.useCallback((el)=>{
        if (el && targetDocument) {
            listboxRef.current = el;
            treeWalkerRef.current = targetDocument.createTreeWalker(el, NodeFilter.SHOW_ELEMENT, optionFilter);
        } else {
            listboxRef.current = null;
        }
    }, [
        targetDocument,
        optionFilter
    ]);
    const optionWalker = _react.useMemo(()=>({
            first: ()=>{
                if (!treeWalkerRef.current || !listboxRef.current) {
                    return null;
                }
                treeWalkerRef.current.currentNode = listboxRef.current;
                return treeWalkerRef.current.firstChild();
            },
            last: ()=>{
                if (!treeWalkerRef.current || !listboxRef.current) {
                    return null;
                }
                treeWalkerRef.current.currentNode = listboxRef.current;
                return treeWalkerRef.current.lastChild();
            },
            next: ()=>{
                if (!treeWalkerRef.current) {
                    return null;
                }
                return treeWalkerRef.current.nextNode();
            },
            prev: ()=>{
                if (!treeWalkerRef.current) {
                    return null;
                }
                return treeWalkerRef.current.previousNode();
            },
            find: (predicate, startFrom)=>{
                if (!treeWalkerRef.current || !listboxRef.current) {
                    return null;
                }
                const start = startFrom ? targetDocument === null || targetDocument === void 0 ? void 0 : targetDocument.getElementById(startFrom) : null;
                treeWalkerRef.current.currentNode = start !== null && start !== void 0 ? start : listboxRef.current;
                let cur = treeWalkerRef.current.currentNode;
                while(cur && !predicate(cur.id)){
                    cur = treeWalkerRef.current.nextNode();
                }
                return cur;
            },
            setCurrent: (el)=>{
                if (!treeWalkerRef.current) {
                    return;
                }
                treeWalkerRef.current.currentNode = el;
            }
        }), [
        targetDocument
    ]);
    return {
        optionWalker,
        listboxCallbackRef: setListbox
    };
}
