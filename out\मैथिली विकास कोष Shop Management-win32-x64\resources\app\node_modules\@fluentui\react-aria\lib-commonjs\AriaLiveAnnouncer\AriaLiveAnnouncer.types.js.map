{"version": 3, "sources": ["../src/AriaLiveAnnouncer/AriaLiveAnnouncer.types.ts"], "sourcesContent": ["import type { AnnounceContextValue } from '@fluentui/react-shared-contexts';\nimport * as React from 'react';\n\nexport type AriaLiveAnnounceFn = AnnounceContextValue['announce'];\n\nexport type AriaLiveMessage = {\n  message: string;\n\n  createdAt: number;\n\n  priority: number;\n  batchId?: string;\n};\n\nexport type AriaLiveAnnouncerProps = {\n  children?: React.ReactNode;\n};\n\nexport type AriaLiveAnnouncerState = {\n  announce: AriaLiveAnnounceFn;\n  children?: React.ReactNode;\n};\n\nexport type AriaLiveAnnouncerContextValues = {\n  announce: { announce: AriaLiveAnnounceFn };\n};\n"], "names": [], "rangeMappings": ";;;;;", "mappings": ";;;;;iEACuB"}