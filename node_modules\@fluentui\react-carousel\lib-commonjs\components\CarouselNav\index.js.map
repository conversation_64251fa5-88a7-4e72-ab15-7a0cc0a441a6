{"version": 3, "sources": ["../src/components/CarouselNav/index.ts"], "sourcesContent": ["export { CarouselNav } from './CarouselNav';\nexport type {\n  CarouselNavContextValue,\n  CarouselNavProps,\n  CarouselNavSlots,\n  CarouselNavState,\n  NavButtonRenderFunction,\n} from './CarouselNav.types';\nexport { renderCarouselNav_unstable } from './renderCarouselNav';\nexport { useCarouselNav_unstable } from './useCarouselNav';\nexport { carouselNavClassNames, useCarouselNavStyles_unstable } from './useCarouselNavStyles.styles';\n"], "names": ["CarouselNav", "carouselNavClassNames", "renderCarouselNav_unstable", "useCarouselNavStyles_unstable", "useCarouselNav_unstable"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAASA,WAAW;eAAXA,wBAAW;;IAUXC,qBAAqB;eAArBA,iDAAqB;;IAFrBC,0BAA0B;eAA1BA,6CAA0B;;IAEHC,6BAA6B;eAA7BA,yDAA6B;;IADpDC,uBAAuB;eAAvBA,uCAAuB;;;6BATJ;mCAQe;gCACH;4CAC6B"}