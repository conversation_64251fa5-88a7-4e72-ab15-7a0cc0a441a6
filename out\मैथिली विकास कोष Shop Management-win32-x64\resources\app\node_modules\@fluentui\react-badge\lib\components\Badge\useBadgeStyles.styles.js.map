{"version": 3, "names": ["shorthands", "__resetStyles", "__styles", "mergeClasses", "tokens", "typographyStyles", "badgeClassNames", "root", "icon", "textPadding", "spacingHorizontalXXS", "useRootClassName", "useRootStyles", "fontSmallToTiny", "Bahqtrf", "Be2twd7", "Bhrd7zp", "Bg96gwp", "tiny", "a9b677", "Bqenvij", "Bf4jedk", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "small", "medium", "large", "square", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "rounded", "roundedSmallToTiny", "circular", "borderGhost", "ap17g6", "filled", "De3pzq", "sj55zd", "ghost", "outline", "g2u3we", "h3c5rm", "B9xav0g", "zhjwy3", "tint", "d", "p", "useIconRootClassName", "useIconStyles", "beforeText", "t21cq0", "afterText", "Frg6f3", "beforeTextXL", "afterTextXL", "useBadgeStyles_unstable", "state", "rootClassName", "rootStyles", "smallToTiny", "size", "className", "shape", "appearance", "color", "iconRootClassName", "iconStyles", "iconPositionClass", "children", "iconPosition"], "sources": ["useBadgeStyles.styles.js"], "sourcesContent": ["import { shorthands, makeResetStyles, makeStyles, mergeClasses } from '@griffel/react';\nimport { tokens, typographyStyles } from '@fluentui/react-theme';\nexport const badgeClassNames = {\n    root: 'fui-Badge',\n    icon: 'fui-Badge__icon'\n};\n// The text content of the badge has additional horizontal padding, but there is no `text` slot to add that padding to.\n// Instead, add extra padding to the root, and a negative margin on the icon to \"remove\" the extra padding on the icon.\nconst textPadding = tokens.spacingHorizontalXXS;\nconst useRootClassName = makeResetStyles({\n    display: 'inline-flex',\n    boxSizing: 'border-box',\n    alignItems: 'center',\n    justifyContent: 'center',\n    position: 'relative',\n    ...typographyStyles.caption1Strong,\n    height: '20px',\n    minWidth: '20px',\n    padding: `0 calc(${tokens.spacingHorizontalXS} + ${textPadding})`,\n    borderRadius: tokens.borderRadiusCircular,\n    // Use a transparent stroke (rather than no border) so the border is visible in high contrast\n    borderColor: tokens.colorTransparentStroke,\n    '::after': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        bottom: 0,\n        right: 0,\n        borderStyle: 'solid',\n        borderColor: 'inherit',\n        borderWidth: tokens.strokeWidthThin,\n        borderRadius: 'inherit'\n    }\n});\nconst useRootStyles = makeStyles({\n    fontSmallToTiny: {\n        ...typographyStyles.caption2Strong\n    },\n    // size\n    tiny: {\n        width: '6px',\n        height: '6px',\n        fontSize: '4px',\n        lineHeight: '4px',\n        minWidth: 'unset',\n        padding: 'unset'\n    },\n    'extra-small': {\n        width: '10px',\n        height: '10px',\n        fontSize: '6px',\n        lineHeight: '6px',\n        minWidth: 'unset',\n        padding: 'unset'\n    },\n    small: {\n        minWidth: '16px',\n        height: '16px',\n        padding: `0 calc(${tokens.spacingHorizontalXXS} + ${textPadding})`\n    },\n    medium: {\n    },\n    large: {\n        minWidth: '24px',\n        height: '24px',\n        padding: `0 calc(${tokens.spacingHorizontalXS} + ${textPadding})`\n    },\n    'extra-large': {\n        minWidth: '32px',\n        height: '32px',\n        padding: `0 calc(${tokens.spacingHorizontalSNudge} + ${textPadding})`\n    },\n    // shape\n    square: {\n        borderRadius: tokens.borderRadiusNone\n    },\n    rounded: {\n        borderRadius: tokens.borderRadiusMedium\n    },\n    roundedSmallToTiny: {\n        borderRadius: tokens.borderRadiusSmall\n    },\n    circular: {\n    },\n    // hide the boder when appearance is \"ghost\"\n    borderGhost: {\n        // The border is applied in an ::after pseudo-element because it should not affect layout.\n        // The padding and size of the badge should be the same regardless of whether or not it has a border.\n        '::after': {\n            display: 'none'\n        }\n    },\n    // appearance: filled\n    filled: {\n    },\n    'filled-brand': {\n        backgroundColor: tokens.colorBrandBackground,\n        color: tokens.colorNeutralForegroundOnBrand\n    },\n    'filled-danger': {\n        backgroundColor: tokens.colorPaletteRedBackground3,\n        color: tokens.colorNeutralForegroundOnBrand\n    },\n    'filled-important': {\n        backgroundColor: tokens.colorNeutralForeground1,\n        color: tokens.colorNeutralBackground1\n    },\n    'filled-informative': {\n        backgroundColor: tokens.colorNeutralBackground5,\n        color: tokens.colorNeutralForeground3\n    },\n    'filled-severe': {\n        backgroundColor: tokens.colorPaletteDarkOrangeBackground3,\n        color: tokens.colorNeutralForegroundOnBrand\n    },\n    'filled-subtle': {\n        backgroundColor: tokens.colorNeutralBackground1,\n        color: tokens.colorNeutralForeground1\n    },\n    'filled-success': {\n        backgroundColor: tokens.colorPaletteGreenBackground3,\n        color: tokens.colorNeutralForegroundOnBrand\n    },\n    'filled-warning': {\n        backgroundColor: tokens.colorPaletteYellowBackground3,\n        color: tokens.colorNeutralForeground1Static\n    },\n    // appearance: ghost\n    ghost: {\n    },\n    'ghost-brand': {\n        color: tokens.colorBrandForeground1\n    },\n    'ghost-danger': {\n        color: tokens.colorPaletteRedForeground3\n    },\n    'ghost-important': {\n        color: tokens.colorNeutralForeground1\n    },\n    'ghost-informative': {\n        color: tokens.colorNeutralForeground3\n    },\n    'ghost-severe': {\n        color: tokens.colorPaletteDarkOrangeForeground3\n    },\n    'ghost-subtle': {\n        color: tokens.colorNeutralForegroundStaticInverted\n    },\n    'ghost-success': {\n        color: tokens.colorPaletteGreenForeground3\n    },\n    'ghost-warning': {\n        color: tokens.colorPaletteYellowForeground2\n    },\n    // appearance: outline\n    outline: {\n        ...shorthands.borderColor('currentColor')\n    },\n    'outline-brand': {\n        color: tokens.colorBrandForeground1\n    },\n    'outline-danger': {\n        color: tokens.colorPaletteRedForeground3,\n        ...shorthands.borderColor(tokens.colorPaletteRedBorder2)\n    },\n    'outline-important': {\n        color: tokens.colorNeutralForeground3,\n        ...shorthands.borderColor(tokens.colorNeutralStrokeAccessible)\n    },\n    'outline-informative': {\n        color: tokens.colorNeutralForeground3,\n        ...shorthands.borderColor(tokens.colorNeutralStroke2)\n    },\n    'outline-severe': {\n        color: tokens.colorPaletteDarkOrangeForeground3\n    },\n    'outline-subtle': {\n        color: tokens.colorNeutralForegroundStaticInverted\n    },\n    'outline-success': {\n        color: tokens.colorPaletteGreenForeground3,\n        ...shorthands.borderColor(tokens.colorPaletteGreenBorder2)\n    },\n    'outline-warning': {\n        color: tokens.colorPaletteYellowForeground2\n    },\n    // appearance: tint\n    tint: {\n    },\n    'tint-brand': {\n        backgroundColor: tokens.colorBrandBackground2,\n        color: tokens.colorBrandForeground2,\n        ...shorthands.borderColor(tokens.colorBrandStroke2)\n    },\n    'tint-danger': {\n        backgroundColor: tokens.colorPaletteRedBackground1,\n        color: tokens.colorPaletteRedForeground1,\n        ...shorthands.borderColor(tokens.colorPaletteRedBorder1)\n    },\n    'tint-important': {\n        backgroundColor: tokens.colorNeutralForeground3,\n        color: tokens.colorNeutralBackground1,\n        ...shorthands.borderColor(tokens.colorTransparentStroke)\n    },\n    'tint-informative': {\n        backgroundColor: tokens.colorNeutralBackground4,\n        color: tokens.colorNeutralForeground3,\n        ...shorthands.borderColor(tokens.colorNeutralStroke2)\n    },\n    'tint-severe': {\n        backgroundColor: tokens.colorPaletteDarkOrangeBackground1,\n        color: tokens.colorPaletteDarkOrangeForeground1,\n        ...shorthands.borderColor(tokens.colorPaletteDarkOrangeBorder1)\n    },\n    'tint-subtle': {\n        backgroundColor: tokens.colorNeutralBackground1,\n        color: tokens.colorNeutralForeground3,\n        ...shorthands.borderColor(tokens.colorNeutralStroke2)\n    },\n    'tint-success': {\n        backgroundColor: tokens.colorPaletteGreenBackground1,\n        color: tokens.colorPaletteGreenForeground1,\n        ...shorthands.borderColor(tokens.colorPaletteGreenBorder1)\n    },\n    'tint-warning': {\n        backgroundColor: tokens.colorPaletteYellowBackground1,\n        color: tokens.colorPaletteYellowForeground1,\n        ...shorthands.borderColor(tokens.colorPaletteYellowBorder1)\n    }\n});\nconst useIconRootClassName = makeResetStyles({\n    display: 'flex',\n    lineHeight: '1',\n    margin: `0 calc(-1 * ${textPadding})`,\n    fontSize: '12px'\n});\nconst useIconStyles = makeStyles({\n    beforeText: {\n        marginRight: `calc(${tokens.spacingHorizontalXXS} + ${textPadding})`\n    },\n    afterText: {\n        marginLeft: `calc(${tokens.spacingHorizontalXXS} + ${textPadding})`\n    },\n    beforeTextXL: {\n        marginRight: `calc(${tokens.spacingHorizontalXS} + ${textPadding})`\n    },\n    afterTextXL: {\n        marginLeft: `calc(${tokens.spacingHorizontalXS} + ${textPadding})`\n    },\n    // size\n    tiny: {\n        fontSize: '6px'\n    },\n    'extra-small': {\n        fontSize: '10px'\n    },\n    small: {\n        fontSize: '12px'\n    },\n    medium: {\n    },\n    large: {\n        fontSize: '16px'\n    },\n    'extra-large': {\n        fontSize: '20px'\n    }\n});\n/**\n * Applies style classnames to slots\n */ export const useBadgeStyles_unstable = (state)=>{\n    'use no memo';\n    const rootClassName = useRootClassName();\n    const rootStyles = useRootStyles();\n    const smallToTiny = state.size === 'small' || state.size === 'extra-small' || state.size === 'tiny';\n    state.root.className = mergeClasses(badgeClassNames.root, rootClassName, smallToTiny && rootStyles.fontSmallToTiny, rootStyles[state.size], rootStyles[state.shape], state.shape === 'rounded' && smallToTiny && rootStyles.roundedSmallToTiny, state.appearance === 'ghost' && rootStyles.borderGhost, rootStyles[state.appearance], rootStyles[`${state.appearance}-${state.color}`], state.root.className);\n    const iconRootClassName = useIconRootClassName();\n    const iconStyles = useIconStyles();\n    if (state.icon) {\n        let iconPositionClass;\n        if (state.root.children) {\n            if (state.size === 'extra-large') {\n                iconPositionClass = state.iconPosition === 'after' ? iconStyles.afterTextXL : iconStyles.beforeTextXL;\n            } else {\n                iconPositionClass = state.iconPosition === 'after' ? iconStyles.afterText : iconStyles.beforeText;\n            }\n        }\n        state.icon.className = mergeClasses(badgeClassNames.icon, iconRootClassName, iconPositionClass, iconStyles[state.size], state.icon.className);\n    }\n    return state;\n};\n"], "mappings": "AAAA,SAASA,UAAU,EAAAC,aAAA,EAAAC,QAAA,EAA+BC,YAAY,QAAQ,gBAAgB;AACtF,SAASC,MAAM,EAAEC,gBAAgB,QAAQ,uBAAuB;AAChE,OAAO,MAAMC,eAAe,GAAG;EAC3BC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;AACV,CAAC;AACD;AACA;AACA,MAAMC,WAAW,GAAGL,MAAM,CAACM,oBAAoB;AAC/C,MAAMC,gBAAgB,gBAAGV,aAAA,wvCAyBxB,CAAC;AACF,MAAMW,aAAa,gBAAGV,QAAA;EAAAW,eAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;EAAA;EAAAC,IAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAL,OAAA;IAAAE,OAAA;IAAAI,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAP,MAAA;IAAAC,OAAA;IAAAL,OAAA;IAAAE,OAAA;IAAAI,OAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;EAAAC,KAAA;IAAAN,OAAA;IAAAD,OAAA;IAAAE,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;EAAAE,MAAA;EAAAC,KAAA;IAAAR,OAAA;IAAAD,OAAA;IAAAE,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;EAAA;IAAAL,OAAA;IAAAD,OAAA;IAAAE,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;EAAA;EAAAI,MAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAC,OAAA;IAAAL,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAE,kBAAA;IAAAN,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAAG,QAAA;EAAAC,WAAA;IAAAC,MAAA;EAAA;EAAAC,MAAA;EAAA;IAAAC,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAA;IAAAD,MAAA;IAAAC,MAAA;EAAA;EAAAC,KAAA;EAAA;IAAAD,MAAA;EAAA;EAAA;IAAAA,MAAA;EAAA;EAAA;IAAAA,MAAA;EAAA;EAAA;IAAAA,MAAA;EAAA;EAAA;IAAAA,MAAA;EAAA;EAAA;IAAAA,MAAA;EAAA;EAAA;IAAAA,MAAA;EAAA;EAAA;IAAAA,MAAA;EAAA;EAAAE,OAAA;IAAAC,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAN,MAAA;EAAA;EAAA;IAAAA,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAN,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAN,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAN,MAAA;EAAA;EAAA;IAAAA,MAAA;EAAA;EAAA;IAAAA,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAN,MAAA;EAAA;EAAAO,IAAA;EAAA;IAAAR,MAAA;IAAAC,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAP,MAAA;IAAAC,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAP,MAAA;IAAAC,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAP,MAAA;IAAAC,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAP,MAAA;IAAAC,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAP,MAAA;IAAAC,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAP,MAAA;IAAAC,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;EAAA;IAAAP,MAAA;IAAAC,MAAA;IAAAG,MAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,MAAA;EAAA;AAAA;EAAAE,CAAA;IAAAC,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;IAAAA,CAAA;EAAA;AAAA,CAmMrB,CAAC;AACF,MAAMC,oBAAoB,gBAAGpD,aAAA,0HAK5B,CAAC;AACF,MAAMqD,aAAa,gBAAGpD,QAAA;EAAAqD,UAAA;IAAAC,MAAA;EAAA;EAAAC,SAAA;IAAAC,MAAA;EAAA;EAAAC,YAAA;IAAAH,MAAA;EAAA;EAAAI,WAAA;IAAAF,MAAA;EAAA;EAAAxC,IAAA;IAAAH,OAAA;EAAA;EAAA;IAAAA,OAAA;EAAA;EAAAY,KAAA;IAAAZ,OAAA;EAAA;EAAAa,MAAA;EAAAC,KAAA;IAAAd,OAAA;EAAA;EAAA;IAAAA,OAAA;EAAA;AAAA;EAAAoC,CAAA;AAAA,CA+BrB,CAAC;AACF;AACA;AACA;AAAI,OAAO,MAAMU,uBAAuB,GAAIC,KAAK,IAAG;EAChD,aAAa;;EACb,MAAMC,aAAa,GAAGpD,gBAAgB,CAAC,CAAC;EACxC,MAAMqD,UAAU,GAAGpD,aAAa,CAAC,CAAC;EAClC,MAAMqD,WAAW,GAAGH,KAAK,CAACI,IAAI,KAAK,OAAO,IAAIJ,KAAK,CAACI,IAAI,KAAK,aAAa,IAAIJ,KAAK,CAACI,IAAI,KAAK,MAAM;EACnGJ,KAAK,CAACvD,IAAI,CAAC4D,SAAS,GAAGhE,YAAY,CAACG,eAAe,CAACC,IAAI,EAAEwD,aAAa,EAAEE,WAAW,IAAID,UAAU,CAACnD,eAAe,EAAEmD,UAAU,CAACF,KAAK,CAACI,IAAI,CAAC,EAAEF,UAAU,CAACF,KAAK,CAACM,KAAK,CAAC,EAAEN,KAAK,CAACM,KAAK,KAAK,SAAS,IAAIH,WAAW,IAAID,UAAU,CAAC3B,kBAAkB,EAAEyB,KAAK,CAACO,UAAU,KAAK,OAAO,IAAIL,UAAU,CAACzB,WAAW,EAAEyB,UAAU,CAACF,KAAK,CAACO,UAAU,CAAC,EAAEL,UAAU,CAAC,GAAGF,KAAK,CAACO,UAAU,IAAIP,KAAK,CAACQ,KAAK,EAAE,CAAC,EAAER,KAAK,CAACvD,IAAI,CAAC4D,SAAS,CAAC;EAC7Y,MAAMI,iBAAiB,GAAGlB,oBAAoB,CAAC,CAAC;EAChD,MAAMmB,UAAU,GAAGlB,aAAa,CAAC,CAAC;EAClC,IAAIQ,KAAK,CAACtD,IAAI,EAAE;IACZ,IAAIiE,iBAAiB;IACrB,IAAIX,KAAK,CAACvD,IAAI,CAACmE,QAAQ,EAAE;MACrB,IAAIZ,KAAK,CAACI,IAAI,KAAK,aAAa,EAAE;QAC9BO,iBAAiB,GAAGX,KAAK,CAACa,YAAY,KAAK,OAAO,GAAGH,UAAU,CAACZ,WAAW,GAAGY,UAAU,CAACb,YAAY;MACzG,CAAC,MAAM;QACHc,iBAAiB,GAAGX,KAAK,CAACa,YAAY,KAAK,OAAO,GAAGH,UAAU,CAACf,SAAS,GAAGe,UAAU,CAACjB,UAAU;MACrG;IACJ;IACAO,KAAK,CAACtD,IAAI,CAAC2D,SAAS,GAAGhE,YAAY,CAACG,eAAe,CAACE,IAAI,EAAE+D,iBAAiB,EAAEE,iBAAiB,EAAED,UAAU,CAACV,KAAK,CAACI,IAAI,CAAC,EAAEJ,KAAK,CAACtD,IAAI,CAAC2D,SAAS,CAAC;EACjJ;EACA,OAAOL,KAAK;AAChB,CAAC", "ignoreList": []}