{"version": 3, "sources": ["../src/activedescendant/useOptionWalker.ts"], "sourcesContent": ["import * as React from 'react';\nimport { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nimport { isHTMLElement } from '@fluentui/react-utilities';\n\ninterface UseOptionWalkerOptions {\n  matchOption: (el: HTMLElement) => boolean;\n}\n\nexport function useOptionWalker<TListboxElement extends HTMLElement>(options: UseOptionWalkerOptions) {\n  const { matchOption } = options;\n  const { targetDocument } = useFluent();\n  const treeWalkerRef = React.useRef<TreeWalker | null>(null);\n  const listboxRef = React.useRef<TListboxElement | null>(null);\n\n  const optionFilter = React.useCallback(\n    (node: Node) => {\n      if (isHTMLElement(node) && matchOption(node)) {\n        return NodeFilter.FILTER_ACCEPT;\n      }\n\n      return NodeFilter.FILTER_SKIP;\n    },\n    [matchOption],\n  );\n\n  const setListbox = React.useCallback(\n    (el: TListboxElement) => {\n      if (el && targetDocument) {\n        listboxRef.current = el;\n        treeWalkerRef.current = targetDocument.createTreeWalker(el, NodeFilter.SHOW_ELEMENT, optionFilter);\n      } else {\n        listboxRef.current = null;\n      }\n    },\n    [targetDocument, optionFilter],\n  );\n\n  const optionWalker = React.useMemo(\n    () => ({\n      first: () => {\n        if (!treeWalkerRef.current || !listboxRef.current) {\n          return null;\n        }\n\n        treeWalkerRef.current.currentNode = listboxRef.current;\n        return treeWalkerRef.current.firstChild() as HTMLElement | null;\n      },\n      last: () => {\n        if (!treeWalkerRef.current || !listboxRef.current) {\n          return null;\n        }\n\n        treeWalkerRef.current.currentNode = listboxRef.current;\n        return treeWalkerRef.current.lastChild() as HTMLElement | null;\n      },\n      next: () => {\n        if (!treeWalkerRef.current) {\n          return null;\n        }\n\n        return treeWalkerRef.current.nextNode() as HTMLElement | null;\n      },\n      prev: () => {\n        if (!treeWalkerRef.current) {\n          return null;\n        }\n\n        return treeWalkerRef.current.previousNode() as HTMLElement | null;\n      },\n      find: (predicate: (id: string) => boolean, startFrom?: string) => {\n        if (!treeWalkerRef.current || !listboxRef.current) {\n          return null;\n        }\n\n        const start = startFrom ? targetDocument?.getElementById(startFrom) : null;\n        treeWalkerRef.current.currentNode = start ?? listboxRef.current;\n        let cur: HTMLElement | null = treeWalkerRef.current.currentNode as HTMLElement;\n        while (cur && !predicate(cur.id)) {\n          cur = treeWalkerRef.current.nextNode() as HTMLElement | null;\n        }\n\n        return cur;\n      },\n      setCurrent: (el: HTMLElement) => {\n        if (!treeWalkerRef.current) {\n          return;\n        }\n\n        treeWalkerRef.current.currentNode = el;\n      },\n    }),\n    [targetDocument],\n  );\n\n  return {\n    optionWalker,\n    listboxCallbackRef: setListbox,\n  };\n}\n"], "names": ["useOptionWalker", "options", "matchOption", "targetDocument", "useFluent", "treeWalkerRef", "React", "useRef", "listboxRef", "optionFilter", "useCallback", "node", "isHTMLElement", "Node<PERSON><PERSON><PERSON>", "FILTER_ACCEPT", "FILTER_SKIP", "setListbox", "el", "current", "createTreeWalker", "SHOW_ELEMENT", "<PERSON><PERSON><PERSON><PERSON>", "useMemo", "first", "currentNode", "<PERSON><PERSON><PERSON><PERSON>", "last", "<PERSON><PERSON><PERSON><PERSON>", "next", "nextNode", "prev", "previousNode", "find", "predicate", "startFrom", "start", "getElementById", "cur", "id", "setCurrent", "listboxCallbackRef"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAQgBA;;;eAAAA;;;;iEARO;qCACyB;gCAClB;AAMvB,SAASA,gBAAqDC,OAA+B;IAClG,MAAM,EAAEC,WAAW,EAAE,GAAGD;IACxB,MAAM,EAAEE,cAAc,EAAE,GAAGC,IAAAA,uCAAS;IACpC,MAAMC,gBAAgBC,OAAMC,MAAM,CAAoB;IACtD,MAAMC,aAAaF,OAAMC,MAAM,CAAyB;IAExD,MAAME,eAAeH,OAAMI,WAAW,CACpC,CAACC;QACC,IAAIC,IAAAA,6BAAa,EAACD,SAAST,YAAYS,OAAO;YAC5C,OAAOE,WAAWC,aAAa;QACjC;QAEA,OAAOD,WAAWE,WAAW;IAC/B,GACA;QAACb;KAAY;IAGf,MAAMc,aAAaV,OAAMI,WAAW,CAClC,CAACO;QACC,IAAIA,MAAMd,gBAAgB;YACxBK,WAAWU,OAAO,GAAGD;YACrBZ,cAAca,OAAO,GAAGf,eAAegB,gBAAgB,CAACF,IAAIJ,WAAWO,YAAY,EAAEX;QACvF,OAAO;YACLD,WAAWU,OAAO,GAAG;QACvB;IACF,GACA;QAACf;QAAgBM;KAAa;IAGhC,MAAMY,eAAef,OAAMgB,OAAO,CAChC,IAAO,CAAA;YACLC,OAAO;gBACL,IAAI,CAAClB,cAAca,OAAO,IAAI,CAACV,WAAWU,OAAO,EAAE;oBACjD,OAAO;gBACT;gBAEAb,cAAca,OAAO,CAACM,WAAW,GAAGhB,WAAWU,OAAO;gBACtD,OAAOb,cAAca,OAAO,CAACO,UAAU;YACzC;YACAC,MAAM;gBACJ,IAAI,CAACrB,cAAca,OAAO,IAAI,CAACV,WAAWU,OAAO,EAAE;oBACjD,OAAO;gBACT;gBAEAb,cAAca,OAAO,CAACM,WAAW,GAAGhB,WAAWU,OAAO;gBACtD,OAAOb,cAAca,OAAO,CAACS,SAAS;YACxC;YACAC,MAAM;gBACJ,IAAI,CAACvB,cAAca,OAAO,EAAE;oBAC1B,OAAO;gBACT;gBAEA,OAAOb,cAAca,OAAO,CAACW,QAAQ;YACvC;YACAC,MAAM;gBACJ,IAAI,CAACzB,cAAca,OAAO,EAAE;oBAC1B,OAAO;gBACT;gBAEA,OAAOb,cAAca,OAAO,CAACa,YAAY;YAC3C;YACAC,MAAM,CAACC,WAAoCC;gBACzC,IAAI,CAAC7B,cAAca,OAAO,IAAI,CAACV,WAAWU,OAAO,EAAE;oBACjD,OAAO;gBACT;gBAEA,MAAMiB,QAAQD,YAAY/B,2BAAAA,qCAAAA,eAAgBiC,cAAc,CAACF,aAAa;gBACtE7B,cAAca,OAAO,CAACM,WAAW,GAAGW,kBAAAA,mBAAAA,QAAS3B,WAAWU,OAAO;gBAC/D,IAAImB,MAA0BhC,cAAca,OAAO,CAACM,WAAW;gBAC/D,MAAOa,OAAO,CAACJ,UAAUI,IAAIC,EAAE,EAAG;oBAChCD,MAAMhC,cAAca,OAAO,CAACW,QAAQ;gBACtC;gBAEA,OAAOQ;YACT;YACAE,YAAY,CAACtB;gBACX,IAAI,CAACZ,cAAca,OAAO,EAAE;oBAC1B;gBACF;gBAEAb,cAAca,OAAO,CAACM,WAAW,GAAGP;YACtC;QACF,CAAA,GACA;QAACd;KAAe;IAGlB,OAAO;QACLkB;QACAmB,oBAAoBxB;IACtB;AACF"}