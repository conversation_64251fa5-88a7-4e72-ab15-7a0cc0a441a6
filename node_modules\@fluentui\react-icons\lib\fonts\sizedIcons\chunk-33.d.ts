/// <reference types="react" />
export declare const NumberCircle048Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle616Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle620Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle624Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle628Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle632Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle648Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle716Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle720Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle724Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle728Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle732Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle748Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle816Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle820Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle824Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle828Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle832Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle848Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle916Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle920Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle924Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle928Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle932Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberCircle948Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Server12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintHexagon12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintHexagon16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintHexagon20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintHexagon24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintHexagon28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintHexagon32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintHexagon48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TabDesktopMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TabDesktopMultipleAdd16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TargetAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TargetAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TargetDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TargetDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader1Lines16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader1Lines20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader1Lines24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader1LinesCaret16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader1LinesCaret20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader1LinesCaret24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader2Lines16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader2Lines20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader2Lines24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader2LinesCaret16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader2LinesCaret20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader2LinesCaret24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader3Lines16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader3Lines20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader3Lines24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader3LinesCaret16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader3LinesCaret20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader3LinesCaret24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownload28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownload32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowExpand16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowExportUp16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowImport16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowUpRightDashes16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Battery1016Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BeakerEmpty16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Book16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderNone16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BranchRequest16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardTaskList16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Cut16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderSearch16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderSearch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Hexagon28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Hexagon32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Hexagon48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PlugConnected16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PlugDisconnected16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProjectionScreenText20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShapeOrganic16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShapeOrganic20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShapeOrganic24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShapeOrganic28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShapeOrganic32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShapeOrganic48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TeardropBottomRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TeardropBottomRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TeardropBottomRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TeardropBottomRight28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TeardropBottomRight32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TeardropBottomRight48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextEditStyle16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextWholeWord16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Triangle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Triangle28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAsterisk16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownloadOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownloadOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownloadOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownloadOff28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownloadOff32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownloadOff48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderInside16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderInside20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderInside24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatLock16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatLock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatLock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatLock28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ErrorCircle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FullScreenMaximize28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FullScreenMaximize32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FullScreenMinimize28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FullScreenMinimize32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LinkPerson16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LinkPerson20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LinkPerson24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LinkPerson32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LinkPerson48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleChat16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleChat20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleChat24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSupport28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Shapes32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideTextEdit16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideTextEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideTextEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideTextEdit28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractParentheses16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractParentheses20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractParentheses24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractParentheses28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractParentheses32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractParentheses48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Warning48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlertOn16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownExclamation16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownExclamation20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowFit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowFitIn24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Book32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookDatabase16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookDatabase32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookToolbox16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingDesktop32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingGovernment16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingGovernmentSearch16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingGovernmentSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingGovernmentSearch24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingGovernmentSearch32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarRecord16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarRecord20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarRecord24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarRecord28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarRecord32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarRecord48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Clipboard28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardMathFormula16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardMathFormula20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardMathFormula24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardMathFormula28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardMathFormula32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardNumber12316Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardNumber12320Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardNumber12324Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardNumber12328Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardNumber12332Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Collections16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommunicationShield16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommunicationShield20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommunicationShield24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DialpadQuestionMark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DialpadQuestionMark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentBriefcase16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentBriefcase32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSearch32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Fingerprint16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Fingerprint32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderPerson24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderPerson28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderPerson32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderPerson48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HatGraduationAdd16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HatGraduationAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HatGraduationAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayerDiagonalAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Library32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LightbulbFilament32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LinkAdd16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LinkAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LockShield16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LockShield28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LockShield32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonVoice16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonWarning16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonWarning20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonWarning24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonWarning28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonWarning32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonWarning48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanTypeOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Screenshot16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScreenshotRecord16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScreenshotRecord20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScreenshotRecord24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideSearch16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideSearch32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleSubwayClock16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleSubwayClock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleSubwayClock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipOptimize16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipOptimize20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipOptimize24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipOptimize28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoPersonPulse16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoPersonPulse20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoPersonPulse24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoPersonPulse28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArchiveSettings32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowForward32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowReply32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowReplyAll32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Attach32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Autocorrect32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Broom32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarNote16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarNote20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarNote24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarNote32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckmarkUnderlineCircle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataBarVerticalAscending20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataBarVerticalAscending24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diversity16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Filter32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FolderMail32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlanceHorizontal32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlanceHorizontalSparkle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeArrowUp16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeArrowUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeArrowUp24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeError16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeError20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeError24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeProhibited16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeProhibited24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeSync16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeSync20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeSync24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeWarning16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeWarning20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeWarning24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Important32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayerDiagonal16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayerDiagonalPerson16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailMultiple32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailRead32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailUnread32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Mailbox16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Mailbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const OrganizationHorizontal16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const OrganizationHorizontal24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleList32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonAdd32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSquare16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSquare32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSquareCheckmark16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSquareCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSquareCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonSquareCheckmark32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PhoneFooterArrowDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PhoneFooterArrowDown24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PhoneHeaderArrowUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PhoneHeaderArrowUp24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Poll32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Question32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Screenshot28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScreenshotRecord28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Star32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextDensity32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextEditStyleCharacterA32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WrenchScrewdriver32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowClockwiseDashes16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowClockwiseDashes32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingSwap16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingSwap20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingSwap24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingSwap32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingSwap48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Certificate32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardBrush16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardBrush20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardBrush24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardBrush28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardBrush32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudBeaker16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudBeaker20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudBeaker24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudBeaker28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudBeaker32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudBeaker48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCube16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCube20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCube24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCube28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCube32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCube48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContractUpRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContractUpRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContractUpRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContractUpRight28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContractUpRight32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ContractUpRight48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentDataLock16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentDataLock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentDataLock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentDataLock32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlanceHorizontalSparkles20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutCellFour16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutCellFour20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutCellFour24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnFour16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnFour20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnFour24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnOneThirdLeft16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnOneThirdLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnOneThirdLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnOneThirdRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnOneThirdRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnOneThirdRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnOneThirdRightHint16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnOneThirdRightHint20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnOneThirdRightHint24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnThree16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnThree20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnThree24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnTwo16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnTwo20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnTwo24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnTwoSplitLeft16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnTwoSplitLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnTwoSplitLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnTwoSplitRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnTwoSplitRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutColumnTwoSplitRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowFour16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowFour20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowFour24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowThree16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowThree20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowThree24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowTwo16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowTwo20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowTwo24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowTwoSplitBottom16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowTwoSplitBottom20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowTwoSplitBottom24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowTwoSplitTop16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowTwoSplitTop20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayoutRowTwoSplitTop24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LocationTargetSquare16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LocationTargetSquare20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LocationTargetSquare24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LocationTargetSquare32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Resize16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Resize28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Resize32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Resize48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectAllOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectAllOn16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareAndroid16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareAndroid32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextArrowDownRightColumn16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextArrowDownRightColumn20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextArrowDownRightColumn24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextArrowDownRightColumn28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextArrowDownRightColumn32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextArrowDownRightColumn48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextEffectsSparkle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextEffectsSparkle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Whiteboard16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WhiteboardOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WhiteboardOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WhiteboardOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Flowchart16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Flowchart32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayerDiagonal24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayerDiagonalPerson24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PollOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PollOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PollOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PollOff32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscapeSparkle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscapeSync16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscapeSync20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscapeSync24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscapeSync28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscapeSyncOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscapeSyncOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscapeSyncOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscapeSyncOff28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Seat16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Seat20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Seat24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SeatAdd16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SeatAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SeatAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SpeakerBox16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SpeakerBox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SpeakerBox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextEditStyleCharacterGa32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowAd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WrenchSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WrenchSettings24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingLighthouse24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingLighthouse32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingLighthouse48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarLink24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarLink28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarVideo24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarVideo28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Cookies16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Cookies28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Cookies32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Cookies48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HardDrive28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HardDrive48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Laptop32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LaptopSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LaptopSettings24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LaptopSettings32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleAudience32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagAdd20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagAdd24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StreetSign20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StreetSign24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoLink24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoLink28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingLighthouse16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarSparkle16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarSparkle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarSparkle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarSparkle28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarSparkle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarSparkle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarTemplate20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarTemplate24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarTemplate32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Clipboard12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Clipboard48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Compose12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Compose32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Compose48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Globe28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Guest12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Guest32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Guest48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LaptopBriefcase20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LaptopBriefcase24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LaptopBriefcase32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayerDiagonalSparkle16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayerDiagonalSparkle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LayerDiagonalSparkle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaymentWireless16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaymentWireless20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaymentWireless24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaymentWireless28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaymentWireless32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PaymentWireless48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Status28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Status32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Status48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckmarkCircleWarning16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckmarkCircleWarning20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckmarkCircleWarning24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentArrowDown24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSignature16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSignature20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSignature24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSignature28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSignature32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSignature48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HomeGarage20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HomeGarage24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageSplit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageSplit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Laptop48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineFlowDiagonalUpRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineFlowDiagonalUpRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineFlowDiagonalUpRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineFlowDiagonalUpRight32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowClockwise16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowClockwise20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowClockwise24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonPasskey16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
