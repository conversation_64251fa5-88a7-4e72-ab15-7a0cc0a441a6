{"version": 3, "sources": ["useAvatarGroupItemStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses } from '@griffel/react';\nimport { tokens, typographyStyles } from '@fluentui/react-theme';\nimport { useSizeStyles } from '../../Avatar';\nimport { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nexport const avatarGroupItemClassNames = {\n    root: 'fui-AvatarGroupItem',\n    avatar: 'fui-AvatarGroupItem__avatar',\n    overflowLabel: 'fui-AvatarGroupItem__overflowLabel'\n};\nconst avatarGroupItemDividerWidthVar = '--fuiAvatarGroupItem__divider--width';\n/**\n * Styles for the root slot\n */ const useRootStyles = makeStyles({\n    base: {\n        alignItems: 'center',\n        display: 'inline-flex',\n        flexShrink: 0,\n        position: 'relative'\n    },\n    overflowItem: {\n        padding: `${tokens.spacingVerticalXS} ${tokens.spacingHorizontalXS}`\n    },\n    nonOverflowItem: {\n        borderRadius: tokens.borderRadiusCircular\n    }\n});\n/**\n * Styles for the avatar slot\n */ const useAvatarStyles = makeStyles({\n    nonOverflowItem: {\n        position: 'absolute'\n    },\n    pie: {\n        borderRadius: '0'\n    }\n});\n/**\n * Styles for the label slot\n */ const useOverflowLabelStyles = makeStyles({\n    base: {\n        marginLeft: tokens.spacingHorizontalS,\n        color: tokens.colorNeutralForeground1,\n        ...typographyStyles.body1\n    }\n});\n/**\n * Styles for the stack layout\n */ const useStackStyles = makeStyles({\n    thick: {\n        boxShadow: `0 0 0 ${tokens.strokeWidthThick} ${tokens.colorNeutralBackground2}`\n    },\n    thicker: {\n        boxShadow: `0 0 0 ${tokens.strokeWidthThicker} ${tokens.colorNeutralBackground2}`\n    },\n    thickest: {\n        boxShadow: `0 0 0 ${tokens.strokeWidthThickest} ${tokens.colorNeutralBackground2}`\n    },\n    xxs: {\n        '&:not(:first-child)': {\n            marginLeft: `calc(-1 * ${tokens.spacingHorizontalXXS})`\n        }\n    },\n    xs: {\n        '&:not(:first-child)': {\n            marginLeft: `calc(-1 * ${tokens.spacingHorizontalXS})`\n        }\n    },\n    s: {\n        '&:not(:first-child)': {\n            marginLeft: `calc(-1 * ${tokens.spacingHorizontalS})`\n        }\n    },\n    l: {\n        '&:not(:first-child)': {\n            marginLeft: `calc(-1 * ${tokens.spacingHorizontalL})`\n        }\n    }\n});\n/**\n * Styles for the spread layout\n */ const useSpreadStyles = makeStyles({\n    s: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalS\n        }\n    },\n    mNudge: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalMNudge\n        }\n    },\n    m: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalM\n        }\n    },\n    l: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalL\n        }\n    },\n    xl: {\n        '&:not(:first-child)': {\n            marginLeft: tokens.spacingHorizontalXL\n        }\n    }\n});\n/**\n * Styles for the pie layout\n */ const usePieStyles = makeStyles({\n    base: {\n        position: 'absolute'\n    },\n    slices: {\n        // Two slices\n        // 1st of 2 items\n        '&:nth-of-type(1):nth-last-of-type(2)': {\n            clipPath: `inset(0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)) 0 25%)`,\n            left: '-25%'\n        },\n        // 2nd of 2 items\n        '&:nth-of-type(2):nth-last-of-type(1)': {\n            clipPath: `inset(0 25% 0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)))`,\n            left: '25%'\n        },\n        // Three slices\n        // 1st of 3 items\n        '&:nth-of-type(1):nth-last-of-type(3)': {\n            clipPath: `inset(0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)) 0 25%)`,\n            left: '-25%'\n        },\n        // 2nd of 3 items\n        '&:nth-of-type(2):nth-last-of-type(2)': {\n            // Since the two AvatarGroupItems on the right are scaled by 0.5, the divider width should not be halved.\n            clipPath: `inset(0 0 var(${avatarGroupItemDividerWidthVar}) var(${avatarGroupItemDividerWidthVar}))`,\n            left: '50%',\n            transform: 'scale(0.5)',\n            transformOrigin: '0 0'\n        },\n        // 3rd of 3 items\n        '&:nth-of-type(3):nth-last-of-type(1)': {\n            clipPath: `inset(var(${avatarGroupItemDividerWidthVar}) 0 0 var(${avatarGroupItemDividerWidthVar}))`,\n            left: '50%',\n            top: '50%',\n            transform: 'scale(0.5)',\n            transformOrigin: '0 0'\n        }\n    },\n    rtlSlices: {\n        // Two slices\n        // 1st of 2 items\n        '&:nth-of-type(1):nth-last-of-type(2)': {\n            clipPath: `inset(0 25% 0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)))`\n        },\n        // 2nd of 2 items\n        '&:nth-of-type(2):nth-last-of-type(1)': {\n            clipPath: `inset(0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)) 0 25%)`\n        },\n        // Three slices\n        // 1st of 3 items\n        '&:nth-of-type(1):nth-last-of-type(3)': {\n            clipPath: `inset(0 25% 0 calc(25% + (var(${avatarGroupItemDividerWidthVar}) / 2)))`\n        },\n        // 2nd of 3 items\n        '&:nth-of-type(2):nth-last-of-type(2)': {\n            clipPath: `inset(0 var(${avatarGroupItemDividerWidthVar}) var(${avatarGroupItemDividerWidthVar}) 0)`,\n            left: '0'\n        },\n        // 3rd of 3 items\n        '&:nth-of-type(3):nth-last-of-type(1)': {\n            clipPath: `inset(var(${avatarGroupItemDividerWidthVar}) var(${avatarGroupItemDividerWidthVar}) 0 0)`,\n            left: '0'\n        }\n    },\n    thick: {\n        [avatarGroupItemDividerWidthVar]: tokens.strokeWidthThick\n    },\n    thicker: {\n        [avatarGroupItemDividerWidthVar]: tokens.strokeWidthThicker\n    },\n    thickest: {\n        [avatarGroupItemDividerWidthVar]: tokens.strokeWidthThickest\n    }\n});\n/**\n * Apply styling to the AvatarGroupItem slots based on the state\n */ export const useAvatarGroupItemStyles_unstable = (state)=>{\n    'use no memo';\n    const { isOverflowItem, layout, size } = state;\n    const { dir } = useFluent();\n    const avatarStyles = useAvatarStyles();\n    const overflowLabelStyles = useOverflowLabelStyles();\n    const pieStyles = usePieStyles();\n    const rootStyles = useRootStyles();\n    const sizeStyles = useSizeStyles();\n    const groupChildClassName = useGroupChildClassName(layout, size);\n    const rootClasses = [\n        rootStyles.base\n    ];\n    if (!isOverflowItem) {\n        rootClasses.push(rootStyles.nonOverflowItem);\n        rootClasses.push(groupChildClassName);\n        rootClasses.push(sizeStyles[size]);\n        if (layout === 'pie') {\n            rootClasses.push(pieStyles.base);\n            if (size < 56) {\n                rootClasses.push(pieStyles.thick);\n            } else if (size < 72) {\n                rootClasses.push(pieStyles.thicker);\n            } else {\n                rootClasses.push(pieStyles.thickest);\n            }\n            rootClasses.push(pieStyles.slices);\n            if (dir === 'rtl') {\n                rootClasses.push(pieStyles.rtlSlices);\n            }\n        }\n    } else {\n        rootClasses.push(rootStyles.overflowItem);\n    }\n    state.root.className = mergeClasses(avatarGroupItemClassNames.root, ...rootClasses, state.root.className);\n    state.avatar.className = mergeClasses(avatarGroupItemClassNames.avatar, !isOverflowItem && avatarStyles.nonOverflowItem, layout === 'pie' && avatarStyles.pie, state.avatar.className);\n    if (state.overflowLabel) {\n        state.overflowLabel.className = mergeClasses(avatarGroupItemClassNames.overflowLabel, overflowLabelStyles.base, state.overflowLabel.className);\n    }\n    return state;\n};\n/**\n * Hook for getting the className for the children of AvatarGroup. This hook will provide the spacing and outlines\n * needed for each layout.\n */ export const useGroupChildClassName = (layout, size)=>{\n    const stackStyles = useStackStyles();\n    const spreadStyles = useSpreadStyles();\n    const layoutClasses = [];\n    if (size) {\n        if (layout === 'stack') {\n            if (size < 56) {\n                layoutClasses.push(stackStyles.thick);\n            } else if (size < 72) {\n                layoutClasses.push(stackStyles.thicker);\n            } else {\n                layoutClasses.push(stackStyles.thickest);\n            }\n            if (size < 24) {\n                layoutClasses.push(stackStyles.xxs);\n            } else if (size < 48) {\n                layoutClasses.push(stackStyles.xs);\n            } else if (size < 96) {\n                layoutClasses.push(stackStyles.s);\n            } else {\n                layoutClasses.push(stackStyles.l);\n            }\n        } else if (layout === 'spread') {\n            if (size < 20) {\n                layoutClasses.push(spreadStyles.s);\n            } else if (size < 32) {\n                layoutClasses.push(spreadStyles.mNudge);\n            } else if (size < 64) {\n                layoutClasses.push(spreadStyles.l);\n            } else {\n                layoutClasses.push(spreadStyles.xl);\n            }\n        }\n    }\n    return mergeClasses(...layoutClasses);\n};\n"], "names": ["avatarGroupItemClassNames", "useAvatarGroupItemStyles_unstable", "useGroupChildClassName", "root", "avatar", "overflowLabel", "avatarGroupItemDividerWidthVar", "useRootStyles", "__styles", "base", "Bt984gj", "mc9l5x", "Bnnss6s", "qhf8xq", "overflowItem", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "nonOverflowItem", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "d", "p", "useAvatarStyles", "pie", "useOverflowLabelStyles", "Frg6f3", "sj55zd", "Bahqtrf", "Be2twd7", "Bhrd7zp", "Bg96gwp", "useStackStyles", "thick", "E5pizo", "thicker", "thickest", "xxs", "jhia2w", "xs", "s", "l", "useSpreadStyles", "mNudge", "m", "xl", "usePieStyles", "slices", "B3gf25r", "Be2twx7", "Bvaow4n", "Gpecfs", "bhabj1", "B7rc6i7", "Bwrfys5", "Bwuzm9m", "fflka", "do7bja", "Be8zqhl", "Bij0kh0", "Bwexnyt", "Bhe5x6o", "B3kv7bh", "rtlSlices", "uiicq7", "state", "isOverflowItem", "layout", "size", "dir", "useFluent", "avatar<PERSON><PERSON><PERSON>", "overflowLabelStyles", "pieStyles", "rootStyles", "sizeStyles", "useSizeStyles", "groupChildClassName", "rootClasses", "push", "className", "mergeClasses", "stackStyles", "spreadStyles", "layoutClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAIaA,yBAAyB;eAAzBA;;IAsLIC,iCAAiC;eAAjCA;;IA4CAC,sBAAsB;eAAtBA;;;uBAtOwB;wBAEX;qCACkB;AACzC,MAAMF,4BAA4B;IACrCG,MAAM;IACNC,QAAQ;IACRC,eAAe;AACnB;AACA,MAAMC,iCAAiC;AACvC;;CAEA,GAAI,MAAMC,gBAAa,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAC,MAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAAC,cAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAAC,iBAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;KAAA;AAAA;AAc1B;;CAEA,GAAI,MAAMC,kBAAe,WAAA,GAAGpB,IAAAA,eAAA,EAAA;IAAAY,iBAAA;QAAAP,QAAA;IAAA;IAAAgB,KAAA;QAAAR,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;KAAA;AAAA;AAQ5B;;CAEA,GAAI,MAAMG,yBAAsB,WAAA,GAAGtB,IAAAA,eAAA,EAAA;IAAAC,MAAA;QAAAsB,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;AAAA,GAAA;IAAAV,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AAOnC;;CAEA,GAAI,MAAMW,iBAAc,WAAA,GAAG7B,IAAAA,eAAA,EAAA;IAAA8B,OAAA;QAAAC,QAAA;IAAA;IAAAC,SAAA;QAAAD,QAAA;IAAA;IAAAE,UAAA;QAAAF,QAAA;IAAA;IAAAG,KAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAC,IAAA;QAAAD,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAE,GAAA;QAAAF,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAG,GAAA;QAAAH,QAAA;YAAA;YAAA;SAAA;IAAA;AAAA,GAAA;IAAAjB,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AA+B3B;;CAEA,GAAI,MAAMqB,kBAAe,WAAA,GAAGvC,IAAAA,eAAA,EAAA;IAAAqC,GAAA;QAAAF,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAK,QAAA;QAAAL,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAM,GAAA;QAAAN,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAG,GAAA;QAAAH,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAO,IAAA;QAAAP,QAAA;YAAA;YAAA;SAAA;IAAA;AAAA,GAAA;IAAAjB,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AA2B5B;;CAEA,GAAI,MAAMyB,eAAY,WAAA,GAAG3C,IAAAA,eAAA,EAAA;IAAAC,MAAA;QAAAI,QAAA;IAAA;IAAAuC,QAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,OAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAAC,WAAA;QAAAf,SAAA;QAAAE,SAAA;QAAAE,QAAA;QAAAE,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAG,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;IAAA;IAAA1B,OAAA;QAAA+B,QAAA;IAAA;IAAA7B,SAAA;QAAA6B,QAAA;IAAA;IAAA5B,UAAA;QAAA4B,QAAA;IAAA;AAAA,GAAA;IAAA3C,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AA6Ed,MAAMzB,oCAAqCqE,CAAAA;IAClD;IACA,MAAM,EAAEC,cAAc,EAAEC,MAAM,EAAEC,IAAAA,EAAM,GAAGH;IACzC,MAAM,EAAEI,GAAAA,EAAK,GAAGC,IAAAA,uCAAS;IACzB,MAAMC,eAAehD;IACrB,MAAMiD,sBAAsB/C;IAC5B,MAAMgD,YAAY3B;IAClB,MAAM4B,aAAaxE;IACnB,MAAMyE,aAAaC,IAAAA,qBAAa;IAChC,MAAMC,sBAAsBhF,uBAAuBsE,QAAQC;IAC3D,MAAMU,cAAc;QAChBJ,WAAWtE,IAAI;KAClB;IACD,IAAI,CAAC8D,gBAAgB;QACjBY,YAAYC,IAAI,CAACL,WAAW3D,eAAe;QAC3C+D,YAAYC,IAAI,CAACF;QACjBC,YAAYC,IAAI,CAACJ,UAAU,CAACP,KAAK;QACjC,IAAID,WAAW,OAAO;YAClBW,YAAYC,IAAI,CAACN,UAAUrE,IAAI;YAC/B,IAAIgE,OAAO,IAAI;gBACXU,YAAYC,IAAI,CAACN,UAAUxC,KAAK;YACpC,OAAO,IAAImC,OAAO,IAAI;gBAClBU,YAAYC,IAAI,CAACN,UAAUtC,OAAO;YACtC,OAAO;gBACH2C,YAAYC,IAAI,CAACN,UAAUrC,QAAQ;YACvC;YACA0C,YAAYC,IAAI,CAACN,UAAU1B,MAAM;YACjC,IAAIsB,QAAQ,OAAO;gBACfS,YAAYC,IAAI,CAACN,UAAUV,SAAS;YACxC;QACJ;IACJ,OAAO;QACHe,YAAYC,IAAI,CAACL,WAAWjE,YAAY;IAC5C;IACAwD,MAAMnE,IAAI,CAACkF,SAAS,GAAGC,IAAAA,mBAAY,EAACtF,0BAA0BG,IAAI,KAAKgF,aAAab,MAAMnE,IAAI,CAACkF,SAAS;IACxGf,MAAMlE,MAAM,CAACiF,SAAS,GAAGC,IAAAA,mBAAY,EAACtF,0BAA0BI,MAAM,EAAE,CAACmE,kBAAkBK,aAAaxD,eAAe,EAAEoD,WAAW,SAASI,aAAa/C,GAAG,EAAEyC,MAAMlE,MAAM,CAACiF,SAAS;IACrL,IAAIf,MAAMjE,aAAa,EAAE;QACrBiE,MAAMjE,aAAa,CAACgF,SAAS,GAAGC,IAAAA,mBAAY,EAACtF,0BAA0BK,aAAa,EAAEwE,oBAAoBpE,IAAI,EAAE6D,MAAMjE,aAAa,CAACgF,SAAS;IACjJ;IACA,OAAOf;AACX;AAIW,MAAMpE,yBAAyBA,CAACsE,QAAQC;IAC/C,MAAMc,cAAclD;IACpB,MAAMmD,eAAezC;IACrB,MAAM0C,gBAAgB,EAAE;IACxB,IAAIhB,MAAM;QACN,IAAID,WAAW,SAAS;YACpB,IAAIC,OAAO,IAAI;gBACXgB,cAAcL,IAAI,CAACG,YAAYjD,KAAK;YACxC,OAAO,IAAImC,OAAO,IAAI;gBAClBgB,cAAcL,IAAI,CAACG,YAAY/C,OAAO;YAC1C,OAAO;gBACHiD,cAAcL,IAAI,CAACG,YAAY9C,QAAQ;YAC3C;YACA,IAAIgC,OAAO,IAAI;gBACXgB,cAAcL,IAAI,CAACG,YAAY7C,GAAG;YACtC,OAAO,IAAI+B,OAAO,IAAI;gBAClBgB,cAAcL,IAAI,CAACG,YAAY3C,EAAE;YACrC,OAAO,IAAI6B,OAAO,IAAI;gBAClBgB,cAAcL,IAAI,CAACG,YAAY1C,CAAC;YACpC,OAAO;gBACH4C,cAAcL,IAAI,CAACG,YAAYzC,CAAC;YACpC;QACJ,OAAO,IAAI0B,WAAW,UAAU;YAC5B,IAAIC,OAAO,IAAI;gBACXgB,cAAcL,IAAI,CAACI,aAAa3C,CAAC;YACrC,OAAO,IAAI4B,OAAO,IAAI;gBAClBgB,cAAcL,IAAI,CAACI,aAAaxC,MAAM;YAC1C,OAAO,IAAIyB,OAAO,IAAI;gBAClBgB,cAAcL,IAAI,CAACI,aAAa1C,CAAC;YACrC,OAAO;gBACH2C,cAAcL,IAAI,CAACI,aAAatC,EAAE;YACtC;QACJ;IACJ;IACA,OAAOoC,IAAAA,mBAAY,KAAIG;AAC3B"}