"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const handlers_1 = require("./ipc/handlers");
// TODO: Re-enable when database is working
// import { databaseService } from './database/database';
// import { seedDataService } from './database/seedData';
// Keep a global reference of the window object
let mainWindow = null;
const isDevelopment = process.env.NODE_ENV === 'development';
function createWindow() {
    // Create the browser window
    mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, '../preload/preload.js'),
        },
        icon: path.join(__dirname, '../../assets/icons/app-icon.png'),
        show: false, // Don't show until ready-to-show
        titleBarStyle: 'default',
    });
    // Load the app
    if (isDevelopment) {
        mainWindow.loadURL('http://localhost:3000');
        // Open DevTools in development
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }
    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        if (mainWindow) {
            mainWindow.show();
            // Focus on window
            if (isDevelopment) {
                mainWindow.focus();
            }
        }
    });
    // Emitted when the window is closed
    mainWindow.on('closed', () => {
        // Dereference the window object
        mainWindow = null;
    });
    // Handle window controls on macOS
    mainWindow.on('close', (event) => {
        if (process.platform === 'darwin') {
            event.preventDefault();
            mainWindow === null || mainWindow === void 0 ? void 0 : mainWindow.hide();
        }
    });
}
// This method will be called when Electron has finished initialization
electron_1.app.whenReady().then(async () => {
    try {
        // TODO: Initialize database (temporarily disabled for React frontend testing)
        console.log('🔧 Skipping database initialization for now...');
        // await databaseService.initialize();
        // TODO: Check if data seeding is needed
        // const needsSeeding = await seedDataService.isDataSeedingNeeded();
        // if (needsSeeding) {
        //   console.log('🌱 Seeding initial data...');
        //   await seedDataService.seedAll();
        // }
        // Register IPC handlers
        (0, handlers_1.registerIpcHandlers)();
        createWindow();
    }
    catch (error) {
        console.error('❌ Failed to initialize application:', error);
        electron_1.app.quit();
    }
    // On macOS, re-create window when dock icon is clicked
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
    // Set application menu
    if (process.platform === 'darwin') {
        const template = [
            {
                label: electron_1.app.getName(),
                submenu: [
                    { role: 'about' },
                    { type: 'separator' },
                    { role: 'services' },
                    { type: 'separator' },
                    { role: 'hide' },
                    { role: 'hideothers' },
                    { role: 'unhide' },
                    { type: 'separator' },
                    { role: 'quit' }
                ]
            }
        ];
        electron_1.Menu.setApplicationMenu(electron_1.Menu.buildFromTemplate(template));
    }
    else {
        electron_1.Menu.setApplicationMenu(null);
    }
});
// Quit when all windows are closed
electron_1.app.on('window-all-closed', () => {
    // On macOS, keep app running even when all windows are closed
    if (process.platform !== 'darwin') {
        // TODO: Close database connection before quitting
        // databaseService.close();
        electron_1.app.quit();
    }
});
// Handle app quit
electron_1.app.on('before-quit', () => {
    // TODO: Close database connection
    // databaseService.close();
});
// Security: Prevent new window creation
electron_1.app.on('web-contents-created', (_event, contents) => {
    contents.setWindowOpenHandler(({ url }) => {
        // Prevent opening new windows
        console.log('Blocked attempt to open:', url);
        return { action: 'deny' };
    });
});
//# sourceMappingURL=main.js.map