{"version": 3, "sources": ["../src/AriaLiveAnnouncer/useAriaNotifyAnnounce.ts"], "sourcesContent": ["import { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nimport type { AnnounceOptions } from '@fluentui/react-shared-contexts';\nimport * as React from 'react';\n\nimport type { AriaLiveAnnounceFn } from './AriaLiveAnnouncer.types';\n\ntype AriaNotifyOptions = {\n  priority?: 'high' | 'normal';\n};\n\ntype DocumentWithAriaNotify = Document & {\n  ariaNotify: (message: string, options: AriaNotifyOptions) => void;\n};\n\n/* INTERNAL: implementation of the announcer using the ariaNotify API */\nexport const useAriaNotifyAnnounce_unstable = (): AriaLiveAnnounceFn => {\n  const { targetDocument } = useFluent();\n\n  const announce: AriaLiveAnnounceFn = React.useCallback(\n    (message: string, options: AnnounceOptions = {}) => {\n      if (!targetDocument) {\n        return;\n      }\n\n      const { alert = false, polite } = options;\n\n      // default priority to 0 if polite, 2 if alert, and 1 by default\n      // used to set both ariaNotify's priority and interrupt\n      const defaultPriority = polite ? 0 : alert ? 2 : 1;\n      const priority = options.priority ?? defaultPriority;\n\n      // map fluent announce options to ariaNotify options\n      const ariaNotifyOptions: AriaNotifyOptions = {\n        priority: priority > 1 ? 'high' : 'normal',\n      };\n\n      (targetDocument as DocumentWithAriaNotify).ariaNotify(message, ariaNotifyOptions);\n    },\n    [targetDocument],\n  );\n\n  return announce;\n};\n"], "names": ["useFluent_unstable", "useFluent", "React", "useAriaNotifyAnnounce_unstable", "targetDocument", "announce", "useCallback", "message", "options", "alert", "polite", "defaultPriority", "priority", "ariaNotifyOptions", "ariaNotify"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,SAASA,sBAAsBC,SAAS,QAAQ,kCAAkC;AAElF,YAAYC,WAAW,QAAQ;AAY/B,sEAAsE,GACtE,OAAO,MAAMC,iCAAiC;IAC5C,MAAM,EAAEC,cAAc,EAAE,GAAGH;IAE3B,MAAMI,WAA+BH,MAAMI,WAAW,CACpD,CAACC,SAAiBC,UAA2B,CAAC,CAAC;QAC7C,IAAI,CAACJ,gBAAgB;YACnB;QACF;QAEA,MAAM,EAAEK,QAAQ,KAAK,EAAEC,MAAM,EAAE,GAAGF;QAElC,gEAAgE;QAChE,uDAAuD;QACvD,MAAMG,kBAAkBD,SAAS,IAAID,QAAQ,IAAI;YAChCD;QAAjB,MAAMI,WAAWJ,CAAAA,oBAAAA,QAAQI,QAAQ,cAAhBJ,+BAAAA,oBAAoBG;QAErC,oDAAoD;QACpD,MAAME,oBAAuC;YAC3CD,UAAUA,WAAW,IAAI,SAAS;QACpC;QAECR,eAA0CU,UAAU,CAACP,SAASM;IACjE,GACA;QAACT;KAAe;IAGlB,OAAOC;AACT,EAAE"}