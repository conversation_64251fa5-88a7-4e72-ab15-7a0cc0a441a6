"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AccordionHeaderProvider: function() {
        return AccordionHeaderProvider;
    },
    useAccordionHeaderContext_unstable: function() {
        return useAccordionHeaderContext_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const AccordionHeaderContext = /*#__PURE__*/ _react.createContext(undefined);
const accordionHeaderContextDefaultValue = {
    open: false,
    disabled: false,
    size: 'medium',
    expandIconPosition: 'start'
};
const { Provider: AccordionHeaderProvider } = AccordionHeaderContext;
const useAccordionHeaderContext_unstable = ()=>{
    var _React_useContext;
    return (_React_useContext = _react.useContext(AccordionHeaderContext)) !== null && _React_useContext !== void 0 ? _React_useContext : accordionHeaderContextDefaultValue;
};
