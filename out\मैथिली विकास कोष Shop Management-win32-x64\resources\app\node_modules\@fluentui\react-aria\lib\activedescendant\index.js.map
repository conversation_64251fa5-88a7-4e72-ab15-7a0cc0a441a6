{"version": 3, "sources": ["../src/activedescendant/index.ts"], "sourcesContent": ["export type { ActiveDescendantContextValue } from './ActiveDescendantContext';\nexport {\n  ActiveDescendantContextProvider,\n  useActiveDescendantContext,\n  useHasParentActiveDescendantContext,\n} from './ActiveDescendantContext';\nexport type { ActiveDescendantChangeEvent } from './useActiveDescendant';\nexport { createActiveDescendantChangeEvent, useActiveDescendant } from './useActiveDescendant';\nexport { ACTIVEDESCENDANT_ATTRIBUTE, ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE } from './constants';\nexport type {\n  ActiveDescendantImperativeRef,\n  ActiveDescendantOptions,\n  FindOptions,\n  IteratorOptions,\n  UseActiveDescendantReturn,\n} from './types';\n"], "names": ["ActiveDescendantContextProvider", "useActiveDescendantContext", "useHasParentActiveDescendantContext", "createActiveDescendantChangeEvent", "useActiveDescendant", "ACTIVEDESCENDANT_ATTRIBUTE", "ACTIVEDESCENDANT_FOCUSVISIBLE_ATTRIBUTE"], "rangeMappings": ";;", "mappings": "AACA,SACEA,+BAA+B,EAC/BC,0BAA0B,EAC1BC,mCAAmC,QAC9B,4BAA4B;AAEnC,SAASC,iCAAiC,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC/F,SAASC,0BAA0B,EAAEC,uCAAuC,QAAQ,cAAc"}