"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useAvatarGroupItem_unstable", {
    enumerable: true,
    get: function() {
        return useAvatarGroupItem_unstable;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _Avatar = require("../Avatar/Avatar");
const _AvatarGroupContext = require("../../contexts/AvatarGroupContext");
const _useAvatarGroup = require("../AvatarGroup/useAvatarGroup");
const _reactutilities = require("@fluentui/react-utilities");
const _reactcontextselector = require("@fluentui/react-context-selector");
const useAvatarGroupItem_unstable = (props, ref)=>{
    const groupIsOverflow = (0, _AvatarGroupContext.useAvatarGroupContext_unstable)((ctx)=>ctx.isOverflow);
    const groupSize = (0, _AvatarGroupContext.useAvatarGroupContext_unstable)((ctx)=>ctx.size);
    const layout = (0, _AvatarGroupContext.useAvatarGroupContext_unstable)((ctx)=>ctx.layout);
    // Since the primary slot is not an intrinsic element, getPartitionedNativeProps cannot be used here.
    const { style, className, ...avatarSlotProps } = props;
    const size = groupSize !== null && groupSize !== void 0 ? groupSize : _useAvatarGroup.defaultAvatarGroupSize;
    const hasAvatarGroupContext = (0, _reactcontextselector.useHasParentContext)(_AvatarGroupContext.AvatarGroupContext);
    if (process.env.NODE_ENV !== 'production' && !hasAvatarGroupContext) {
        // eslint-disable-next-line no-console
        console.warn('AvatarGroupItem must only be used inside an AvatarGroup component.');
    }
    return {
        isOverflowItem: groupIsOverflow,
        layout,
        size,
        components: {
            root: groupIsOverflow ? 'li' : 'div',
            avatar: _Avatar.Avatar,
            overflowLabel: 'span'
        },
        root: _reactutilities.slot.always(props.root, {
            defaultProps: {
                style,
                className
            },
            elementType: groupIsOverflow ? 'li' : 'div'
        }),
        avatar: _reactutilities.slot.always(props.avatar, {
            defaultProps: {
                ref,
                size,
                color: 'colorful',
                ...avatarSlotProps
            },
            elementType: _Avatar.Avatar
        }),
        overflowLabel: _reactutilities.slot.always(props.overflowLabel, {
            defaultProps: {
                // Avatar already has its aria-label set to the name, this will prevent the name to be read twice.
                'aria-hidden': true,
                children: props.name
            },
            elementType: 'span'
        })
    };
};
