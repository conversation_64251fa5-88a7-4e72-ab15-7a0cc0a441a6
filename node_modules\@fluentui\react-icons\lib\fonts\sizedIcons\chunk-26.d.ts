/// <reference types="react" />
export declare const CloudArrowUp24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowUp28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowUp32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArrowUp48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCheckmark16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCheckmark28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCheckmark32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudCheckmark48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudDismiss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudDismiss28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudDismiss32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudDismiss48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GridDots20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GridDots24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GridDots28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const IosArrowLtr24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const IosArrowRtl24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelBottom20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelLeft16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelLeft28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelLeft48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelRight28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelRight48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelRightContract16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelRightContract20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelRightContract24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelRightExpand20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PuzzlePiece16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PuzzlePiece20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PuzzlePiece24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanTable24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanText24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanType20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanType24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanTypeOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldTask16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldTask20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldTask24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldTask28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShieldTask48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StackStar16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StackStar20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StackStar24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircleArrowBack16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircleArrowBack20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircleArrowForward16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircleArrowForward20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAddT24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextMore24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextT20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextT24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const UsbPlug20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const UsbPlug24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WifiLock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBidirectionalUpDown12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBidirectionalUpDown16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBidirectionalUpDown20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBidirectionalUpDown24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleDownUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleLeft12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleLeft16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleRight12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowEject20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowExportRtl16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowLeft12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowMoveInward20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowRedo16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowRight12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepIn12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInLeft12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInLeft16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInRight12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepOut12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowUp12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BezierCurveSquare12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderAll16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderAll20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Braces20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingLighthouse20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarAssistant16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CenterVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatDismiss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatWarning16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxIndeterminate16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxIndeterminate20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxIndeterminate24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleRight12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CircleOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardPulse24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockArrowDownload24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudFlow24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudSwap20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudSwap24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CodeCircle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultiple28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultipleCheckmark16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultipleCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultipleCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentMultipleCheckmark28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Cube12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DeveloperBoardSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentChevronDouble24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentCss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentJavascript24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentMultiplePercent20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentPercent20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentPercent24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSplitHint16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSplitHint20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSync16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentSync20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DoorArrowRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DoorArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DualScreen20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Glance20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlanceHorizontal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HighlightLink20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Keyboard12324Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LaptopDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LinkDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelBottomContract20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelBottomExpand20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelLeftExpand20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelSeparateWindow20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelTopContract20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelTopExpand20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PauseOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonCircle12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonInfo20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Phone12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Replay20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareDismiss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractCircle12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagCircle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextBulletListLtr16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextBulletListRtl16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader124Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader224Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextHeader324Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextPeriodAsterisk20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Timeline20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Triangle12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WifiSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Window16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Window24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowAdOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowArrowUp16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowArrowUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowConsole20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowHeaderHorizontalOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ZoomIn16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ZoomOut16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignEndHorizontal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignEndVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignSpaceAroundHorizontal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignSpaceAroundVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignSpaceBetweenHorizontal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignSpaceBetweenVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignSpaceEvenlyHorizontal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignSpaceEvenlyVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignSpaceFitVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignStartHorizontal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignStartVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignStretchHorizontal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlignStretchVertical20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCollapseAll20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCollapseAll24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowWrap20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowWrapOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CenterHorizontal20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentMultiplePercent24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTable16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTable20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTable24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlanceHorizontal12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PanelLeftExpand16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareShadow12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextBaseline20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleDown12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleDown16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleDown28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleDown32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleDown48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleLeft24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleLeft28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleLeft32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleLeft48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleRight28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleRight32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleRight48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleUp12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleUp28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleUp32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleUp48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingSettings24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Balloon12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarWorkWeek28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChannelSubtract16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChannelSubtract20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChannelSubtract24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChannelSubtract28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChannelSubtract48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardSettings24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArchive16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArchive20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudWords16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudWords20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudWords24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudWords28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudWords32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudWords48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ColumnArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ControlButton20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ControlButton24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CreditCardPerson20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CreditCardPerson24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CreditCardToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CubeLink20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DeleteLines20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Dialpad28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Dialpad32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Dialpad48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diversity20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diversity24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diversity28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diversity48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodCake12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeProhibited20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeSearch20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeVideo28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeVideo32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeVideo48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Guardian20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Guardian24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Guardian28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Guardian48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HatGraduation12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LockClosed32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyCalculator20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyCalculator24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneySettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Options48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PauseSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonLock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonNote20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RoadCone16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RoadCone20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RoadCone24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RoadCone28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RoadCone32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RoadCone48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUp16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUp20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUp24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUp28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUp48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Share48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Speaker032Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Speaker132Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Speaker232Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Sticker12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Toolbox12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarCollision16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarCollision20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarCollision24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarCollision48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Wallet20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Wallet24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Whiteboard48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Call48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClosedCaption32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopKeyboard16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopKeyboard20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopKeyboard24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopKeyboard28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Heart12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageAltText16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Mail12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoreCircle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberSymbol32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Poll16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscape12Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscape16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscape24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscape28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscape32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RectangleLandscape48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUpOff16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUpOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUpOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUpOff28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanThumbUpOff48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SendClock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPerson16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPerson20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPerson24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPerson28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonOverlay16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonOverlay20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonOverlay24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonOverlay28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonOverlayInside16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonOverlayInside20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonOverlayInside24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonOverlayInside28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideMicrophone32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Snooze20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Tag32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagDismiss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagLock16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagLock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagLock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagLock32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignCenter16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignLeft16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextAlignRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextChangeCase16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextClearFormatting16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextFontSize16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentDecreaseLtr16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentDecreaseLtr20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentDecreaseLtr24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentDecreaseRtl16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentDecreaseRtl20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentDecreaseRtl24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentIncreaseLtr16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentIncreaseLtr20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentIncreaseLtr24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentIncreaseRtl16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentIncreaseRtl20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextIndentIncreaseRtl24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextNumberListLtr16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextNumberListRtl16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextParagraphDirectionLeft16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextParagraphDirectionLeft20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextParagraphDirectionRight16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextParagraphDirectionRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextSubscript16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextSuperscript16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AppsListDetail20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AppsListDetail24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowMaximize32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowOutlineUpRight32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowUpRight32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxEdit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxEdit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxToolbox20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxToolbox24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatVideo20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatVideo24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Circle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DatabaseLink20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DatabaseLink24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentText20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentText24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextLink20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextLink24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeShield20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GlobeShield24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Line32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineDashes32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberSymbol28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NumberSymbol48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pentagon32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PlayCircle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProjectionScreen16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProjectionScreen20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProjectionScreen24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProjectionScreen28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProjectionScreenDismiss16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProjectionScreenDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProjectionScreenDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ProjectionScreenDismiss28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SaveArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SaveArrowRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Shortpick20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Shortpick24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Sparkle16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Sparkle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Sparkle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Sparkle28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Sparkle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SubtractSquare24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextCaseLowercase16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextCaseLowercase20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextCaseLowercase24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextCaseTitle16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextCaseTitle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextCaseTitle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextCaseUppercase16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextCaseUppercase20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextCaseUppercase24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Triangle32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoPersonSparkle16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoPersonSparkle20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoPersonSparkle24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoPersonSparkle28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoPersonSparkle48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Accessibility48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArchiveMultiple16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArchiveMultiple20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArchiveMultiple24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowReset32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowReset48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Box20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Box24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClearFormatting16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClearFormatting20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardClock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardClock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArchive24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArchive28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArchive32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CloudArchive48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableArrowRight20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableArrowRight24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableCheckmark20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableCheckmark24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextClock20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextClock24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlashSettings20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlashSettings24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Games16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Games20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Games28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Games32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Games48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const HandDraw28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Lasso28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyDismiss20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyDismiss24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyOff20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyOff24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Note28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Note48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonLightbulb20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonLightbulb24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PictureInPictureEnter16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PictureInPictureEnter20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PictureInPictureEnter24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PictureInPictureExit16Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PictureInPictureExit20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PictureInPictureExit24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PlugConnected20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Sanitize20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Sanitize24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Settings32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Settings48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Shapes28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Shapes48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPercent20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPercent24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagTag20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagTag24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StackArrowForward20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StackArrowForward24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TableLightning20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TableLightning24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TableLink20Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TableLink24Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextT28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextT48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ThumbLike28Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ThumbLike48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoOff32Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoOff48Regular: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
