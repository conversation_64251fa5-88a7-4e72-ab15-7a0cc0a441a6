{"version": 3, "sources": ["../src/components/CarouselNavImageButton/renderCarouselNavImageButton.tsx"], "sourcesContent": ["/** @jsxRuntime automatic */\n/** @jsxImportSource @fluentui/react-jsx-runtime */\n\nimport { assertSlots } from '@fluentui/react-utilities';\nimport type { CarouselNavImageButtonState, CarouselNavImageButtonSlots } from './CarouselNavImageButton.types';\n\n/**\n * Render the final JSX of CarouselNavImageButton\n */\nexport const renderCarouselNavImageButton_unstable = (state: CarouselNavImageButtonState) => {\n  assertSlots<CarouselNavImageButtonSlots>(state);\n\n  return (\n    <state.root>\n      <state.image />\n    </state.root>\n  );\n};\n"], "names": ["renderCarouselNavImageButton_unstable", "state", "assertSlots", "_jsx", "root", "image"], "rangeMappings": ";;;;;;;;;;;;;;;;;", "mappings": ";;;;+BASaA;;;eAAAA;;;4BARb;gCAE4B;AAMrB,MAAMA,wCAAwC,CAACC;IACpDC,IAAAA,2BAAAA,EAAyCD;IAEzC,OAAA,WAAA,GACEE,IAAAA,eAAA,EAACF,MAAMG,IAAI,EAAA;kBACT,WAAA,GAAAD,IAAAA,eAAA,EAACF,MAAMI,KAAK,EAAA,CAAA;;AAGlB"}