/**
 * Database entity interfaces for Maithili Vikas Kosh Shop Management System
 */
export interface BaseEntity {
    id: number;
    createdAt: string;
    updatedAt: string;
}
export interface User extends BaseEntity {
    username: string;
    email: string;
    passwordHash: string;
    salt: string;
    role: 'admin' | 'staff' | 'viewer';
    fullName: string;
    phone?: string;
    isActive: boolean;
    lastLoginAt?: string;
}
export interface Category extends BaseEntity {
    name: string;
    nameHindi: string;
    description?: string;
    descriptionHindi?: string;
    parentId?: number;
    sortOrder: number;
    isActive: boolean;
}
export interface Artist extends BaseEntity {
    name: string;
    nameHindi: string;
    email?: string;
    phone?: string;
    address?: string;
    addressHindi?: string;
    specialization: string;
    specializationHindi: string;
    commissionRate: number;
    bankAccount?: string;
    panNumber?: string;
    aadharNumber?: string;
    isActive: boolean;
    notes?: string;
}
export interface Product extends BaseEntity {
    name: string;
    nameHindi: string;
    description?: string;
    descriptionHindi?: string;
    categoryId: number;
    artistId: number;
    sku: string;
    barcode?: string;
    costPrice: number;
    sellingPrice: number;
    mrp: number;
    weight?: number;
    dimensions?: string;
    materials?: string;
    materialsHindi?: string;
    colors?: string;
    isActive: boolean;
    tags?: string;
    images?: string;
}
export interface Inventory extends BaseEntity {
    productId: number;
    currentStock: number;
    reservedStock: number;
    availableStock: number;
    minStockLevel: number;
    maxStockLevel: number;
    reorderPoint: number;
    lastStockUpdate: string;
    location?: string;
}
export interface InventoryMovement extends BaseEntity {
    productId: number;
    movementType: 'in' | 'out' | 'adjustment' | 'transfer';
    quantity: number;
    previousStock: number;
    newStock: number;
    reason: string;
    referenceType?: 'order' | 'purchase' | 'adjustment' | 'return';
    referenceId?: number;
    userId: number;
    notes?: string;
}
export interface Customer extends BaseEntity {
    customerCode: string;
    name: string;
    nameHindi?: string;
    email?: string;
    phone: string;
    alternatePhone?: string;
    address: string;
    addressHindi?: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
    gstNumber?: string;
    customerType: 'individual' | 'business' | 'reseller';
    loyaltyPoints: number;
    totalPurchases: number;
    lastPurchaseDate?: string;
    isActive: boolean;
    notes?: string;
    preferences?: string;
}
export interface Order extends BaseEntity {
    orderNumber: string;
    customerId: number;
    orderDate: string;
    expectedDeliveryDate?: string;
    actualDeliveryDate?: string;
    status: 'draft' | 'confirmed' | 'processing' | 'packed' | 'shipped' | 'delivered' | 'cancelled' | 'returned';
    subtotal: number;
    taxAmount: number;
    discountAmount: number;
    shippingAmount: number;
    totalAmount: number;
    paymentStatus: 'pending' | 'partial' | 'paid' | 'refunded';
    paymentMethod?: 'cash' | 'card' | 'upi' | 'bank_transfer' | 'cheque';
    shippingAddress: string;
    billingAddress: string;
    notes?: string;
    userId: number;
}
export interface OrderItem extends BaseEntity {
    orderId: number;
    productId: number;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    discountAmount: number;
    taxAmount: number;
    notes?: string;
}
export interface Payment extends BaseEntity {
    orderId: number;
    paymentDate: string;
    amount: number;
    paymentMethod: 'cash' | 'card' | 'upi' | 'bank_transfer' | 'cheque';
    paymentStatus: 'pending' | 'completed' | 'failed' | 'cancelled';
    transactionId?: string;
    referenceNumber?: string;
    bankName?: string;
    chequeNumber?: string;
    chequeDate?: string;
    notes?: string;
    userId: number;
}
export interface Supplier extends BaseEntity {
    name: string;
    contactPerson: string;
    email?: string;
    phone: string;
    address: string;
    city: string;
    state: string;
    pincode: string;
    gstNumber?: string;
    panNumber?: string;
    bankAccount?: string;
    paymentTerms?: string;
    isActive: boolean;
    notes?: string;
}
export interface DatabaseConfig {
    version: number;
    lastMigration: string;
    encryptionEnabled: boolean;
    backupEnabled: boolean;
    lastBackup?: string;
}
export interface QueryResult<T> {
    success: boolean;
    data?: T;
    error?: string | undefined;
    rowsAffected?: number;
}
export interface PaginatedResult<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface SearchFilters {
    query?: string;
    category?: number;
    artist?: number;
    priceMin?: number;
    priceMax?: number;
    isActive?: boolean;
    dateFrom?: string;
    dateTo?: string;
}
export interface SortOptions {
    field: string;
    direction: 'asc' | 'desc';
}
//# sourceMappingURL=database.d.ts.map