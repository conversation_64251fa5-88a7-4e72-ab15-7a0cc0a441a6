/// <reference types="react" />
export declare const SelectObjectSkewDismiss20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectObjectSkewDismiss24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectObjectSkewEdit20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SelectObjectSkewEdit24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagDismiss20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagDismiss24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPause20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPause24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagError16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagError20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagError24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagMultiple24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagQuestionMark20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagQuestionMark32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextColumnOneWideLightning20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextColumnOneWideLightning24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextFontInfo16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextFontInfo20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextFontInfo24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrayItemAdd20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrayItemAdd24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrayItemRemove20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrayItemRemove24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckBag20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckBag24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckmarkStarburst20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckmarkStarburst24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AccessTime20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AccessibilityCheckmark20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AddSquare20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Album20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlbumAdd20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AlertOn20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AppGeneric20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AppRecent20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AppTitle20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitDown20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitHeight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitHeightDotted20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitUp20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitWidth20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowAutofitWidthDotted20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBounce20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleDownRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowCircleUpLeft20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowExpand20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowFit20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowMinimizeVertical20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSortDownLines24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSquareDown20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepBack20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInLeft20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepOut20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepOver20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTurnBidirectionalDownRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTurnRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowsBidirectional20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AttachText20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AutoFitHeight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const AutoFitWidth20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Autocorrect20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Badge20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BatteryCheckmark20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BatteryWarning20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BluetoothConnected20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BluetoothDisabled20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BluetoothSearching20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkMultiple16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkMultiple28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkMultiple32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkMultiple48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BranchForkHint20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BranchForkHint24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BranchForkLink20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BranchForkLink24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarLtr48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarRtl48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataUsageToolbox20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DataUsageToolbox24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopCursor16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopCursor20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopCursor24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopCursor28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopFlow20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopFlow24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopSignal20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopSignal24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSadSlight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSadSlight24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSmileSlight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSmileSlight24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodApple20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodApple24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailEdit20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailEdit24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonArrowLeft16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonEdit20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PersonEdit24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash12Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScanDash48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagOff20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagOff24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextBoxSettings20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextBoxSettings24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckCube20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleTruckCube24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowForwardDownLightning20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowForwardDownLightning24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowForwardDownPerson20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowForwardDownPerson24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepIn28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInLeft24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInLeft28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInRight24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepInRight28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepOut24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowStepOut28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingText20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingText24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingWrench20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowTrendingWrench24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Battery1020Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Beach32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Beach48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookClock20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookCoins20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookCompass20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookDatabase20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookExclamationMark20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookGlobe20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookInformation20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookLetter20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookPulse20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookQuestionMark20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookQuestionMarkRtl20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookSearch20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookStar20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookTheta20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookmarkOff20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderBottom20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderBottomDouble20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderBottomThick20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderLeft20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderNone20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderOutside20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderOutsideThick20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderTop20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderTopBottom20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderTopBottomDouble20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BorderTopBottomThick20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Branch20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessHigh48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BrightnessLow48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingGovernment20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BuildingMultiple20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatDismiss20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatDismiss24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CubeTree20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CubeTree24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawImage20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawImage24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawShape20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawText20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerAdd20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerAdd24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerArrowDownload20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerArrowDownload24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerDismiss20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerDismiss24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerPlay20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerPlay24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerSubtract20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DrawerSubtract24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Emoji28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Emoji32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Emoji48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EmojiSparkle48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipHorizontal48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FlipVertical48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodGrains20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const FoodGrains24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardArrowRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardArrowRight24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Heart32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Heart48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Link12Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Link32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Luggage28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Luggage32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Luggage48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyHand20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MoneyHand24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Next28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Next32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Next48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleList28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Previous28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Previous32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Previous48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const QuestionCircle32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TextParagraph16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarCollision28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VehicleCarCollision32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipMultiple16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipMultiple20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const VideoClipMultiple24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Wallet28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Wallet32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Wallet48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WeatherSunny28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WrenchScrewdriver20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WrenchScrewdriver24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBetweenDown20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowBetweenDown24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowRedo28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncCheckmark20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncCheckmark24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncDismiss20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowSyncDismiss24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalculatorArrowClockwise20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalculatorArrowClockwise24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarArrowDown20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarArrowRight24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarError20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarMail20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CalendarWeekNumbers20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CallAdd20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CameraOff20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CellularOff20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleDown32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleUp32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Circle48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockPause20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockPause24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Diamond48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Door28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DoorArrowRight28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImmersiveReader28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const KeyReset20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const KeyReset24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LightbulbFilament48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Line20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Line24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Line48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineDashes20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineDashes24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const LineDashes48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailArrowUp16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const NotepadPerson16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Oval48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Pentagon48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleAdd28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleList24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleQueue20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleQueue24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PeopleSettings28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Print28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Rhombus48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScreenSearch20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ScreenSearch24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SettingsChat20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SettingsChat24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonP16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonP20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonP24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShareScreenPersonP28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideAdd48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideArrowRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SlideArrowRight24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SoundWaveCircle24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Star48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarAdd28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarDismiss16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarDismiss20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarDismiss24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarDismiss28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarEdit20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const StarSettings20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Triangle48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Trophy28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Trophy32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Trophy48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TrophyOff48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Window28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Window32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Window48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowApps48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const WindowWrench48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownLeft32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowDownLeft48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowMaximize48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowMaximizeVertical48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowOutlineUpRight48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowUpLeft48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ArrowUpRight48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookArrowClockwise20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BookArrowClockwise24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxArrowLeft20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxArrowLeft24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxArrowUp20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxArrowUp24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxCheckmark20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxCheckmark24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultiple20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultiple24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleArrowLeft20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleArrowLeft24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleArrowRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleArrowRight24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleCheckmark20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleCheckmark24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleSearch20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxMultipleSearch24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxSearch20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoxSearch24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CellularWarning20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChartMultiple20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChartMultiple24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxArrowRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxWarning20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockToolbox20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockToolbox24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CubeMultiple20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CubeMultiple24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopToolbox20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DesktopToolbox24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentBulletListMultiple20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentBulletListMultiple24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueue20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueue24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueueAdd20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueueAdd24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueueMultiple20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentQueueMultiple24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableCube20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableCube24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableTruck20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTableTruck24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextToolbox20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const DocumentTextToolbox24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EqualCircle20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const EqualCircle24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardMoney20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardMoney24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardMultiple20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const GiftCardMultiple24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageMultiple32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ImageMultiple48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const MailAlert28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const PuzzlePieceShield20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Ribbon12Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff12Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const RibbonOff32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Share28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagArrowLeft20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagArrowLeft24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPlay20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ShoppingBagPlay24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Square28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHint48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintApps20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintApps24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const SquareHintSparkles48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Tablet12Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Tablet16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Tablet32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Tablet48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagReset20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagReset24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagSearch20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TagSearch24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TapDouble32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TapDouble48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TapSingle32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const TapSingle48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Timer12Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Timer28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Timer32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Timer48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoardHeart16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoardHeart20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BoardHeart24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Braces24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BreakoutRoom24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const BreakoutRoom28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChatWarning20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxPerson16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CheckboxPerson20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleDown12Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleDown16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleDown20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleDown28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleDown48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleLeft12Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleLeft16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleLeft20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleLeft24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleLeft28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleLeft32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleLeft48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleRight16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleRight20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleRight28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleRight32Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleRight48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleUp12Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleUp16Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleUp20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleUp24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleUp28Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ChevronCircleUp48Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CircleEdit20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CircleLine20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CircleOff20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const Class20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardDataBar20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardDataBar24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardImage20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClipboardMore20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ClockDismiss20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentDismiss20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentError24Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const CommentNote20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
export declare const ConvertRange20Filled: import("react").FC<import("..").FluentIconsProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>> & {
    codepoint: string;
};
