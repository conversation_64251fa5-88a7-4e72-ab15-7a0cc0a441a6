{"version": 3, "sources": ["../src/AriaLiveAnnouncer/AriaLiveAnnouncer.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { AriaLiveAnnouncerProps } from './AriaLiveAnnouncer.types';\nimport { renderAriaLiveAnnouncer_unstable } from './renderAriaLiveAnnouncer';\nimport { useAriaLiveAnnouncer_unstable } from './useAriaLiveAnnouncer';\nimport { useAriaLiveAnnouncerContextValues_unstable } from './useAriaLiveAnnouncerContextValues';\n\n/**\n * A sample implementation of a component that manages aria live announcements.\n */\nexport const AriaLiveAnnouncer: React.FC<AriaLiveAnnouncerProps> = props => {\n  const state = useAriaLiveAnnouncer_unstable(props);\n  const contextValues = useAriaLiveAnnouncerContextValues_unstable(state);\n\n  return renderAriaLiveAnnouncer_unstable(state, contextValues);\n};\n\nAriaLiveAnnouncer.displayName = 'AriaLiveAnnouncer';\n"], "names": ["AriaLiveAnnouncer", "props", "state", "useAriaLiveAnnouncer_unstable", "contextValues", "useAriaLiveAnnouncerContextValues_unstable", "renderAriaLiveAnnouncer_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAUaA;;;eAAAA;;;;iEAVU;yCAG0B;sCACH;mDACa;AAKpD,MAAMA,oBAAsDC,CAAAA;IACjE,MAAMC,QAAQC,IAAAA,mDAA6B,EAACF;IAC5C,MAAMG,gBAAgBC,IAAAA,6EAA0C,EAACH;IAEjE,OAAOI,IAAAA,yDAAgC,EAACJ,OAAOE;AACjD;AAEAJ,kBAAkBO,WAAW,GAAG"}