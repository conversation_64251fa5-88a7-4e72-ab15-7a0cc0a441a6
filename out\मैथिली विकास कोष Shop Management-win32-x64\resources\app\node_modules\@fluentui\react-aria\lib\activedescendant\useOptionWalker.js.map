{"version": 3, "sources": ["../src/activedescendant/useOptionWalker.ts"], "sourcesContent": ["import * as React from 'react';\nimport { useFluent_unstable as useFluent } from '@fluentui/react-shared-contexts';\nimport { isHTMLElement } from '@fluentui/react-utilities';\n\ninterface UseOptionWalkerOptions {\n  matchOption: (el: HTMLElement) => boolean;\n}\n\nexport function useOptionWalker<TListboxElement extends HTMLElement>(options: UseOptionWalkerOptions) {\n  const { matchOption } = options;\n  const { targetDocument } = useFluent();\n  const treeWalkerRef = React.useRef<TreeWalker | null>(null);\n  const listboxRef = React.useRef<TListboxElement | null>(null);\n\n  const optionFilter = React.useCallback(\n    (node: Node) => {\n      if (isHTMLElement(node) && matchOption(node)) {\n        return NodeFilter.FILTER_ACCEPT;\n      }\n\n      return NodeFilter.FILTER_SKIP;\n    },\n    [matchOption],\n  );\n\n  const setListbox = React.useCallback(\n    (el: TListboxElement) => {\n      if (el && targetDocument) {\n        listboxRef.current = el;\n        treeWalkerRef.current = targetDocument.createTreeWalker(el, NodeFilter.SHOW_ELEMENT, optionFilter);\n      } else {\n        listboxRef.current = null;\n      }\n    },\n    [targetDocument, optionFilter],\n  );\n\n  const optionWalker = React.useMemo(\n    () => ({\n      first: () => {\n        if (!treeWalkerRef.current || !listboxRef.current) {\n          return null;\n        }\n\n        treeWalkerRef.current.currentNode = listboxRef.current;\n        return treeWalkerRef.current.firstChild() as HTMLElement | null;\n      },\n      last: () => {\n        if (!treeWalkerRef.current || !listboxRef.current) {\n          return null;\n        }\n\n        treeWalkerRef.current.currentNode = listboxRef.current;\n        return treeWalkerRef.current.lastChild() as HTMLElement | null;\n      },\n      next: () => {\n        if (!treeWalkerRef.current) {\n          return null;\n        }\n\n        return treeWalkerRef.current.nextNode() as HTMLElement | null;\n      },\n      prev: () => {\n        if (!treeWalkerRef.current) {\n          return null;\n        }\n\n        return treeWalkerRef.current.previousNode() as HTMLElement | null;\n      },\n      find: (predicate: (id: string) => boolean, startFrom?: string) => {\n        if (!treeWalkerRef.current || !listboxRef.current) {\n          return null;\n        }\n\n        const start = startFrom ? targetDocument?.getElementById(startFrom) : null;\n        treeWalkerRef.current.currentNode = start ?? listboxRef.current;\n        let cur: HTMLElement | null = treeWalkerRef.current.currentNode as HTMLElement;\n        while (cur && !predicate(cur.id)) {\n          cur = treeWalkerRef.current.nextNode() as HTMLElement | null;\n        }\n\n        return cur;\n      },\n      setCurrent: (el: HTMLElement) => {\n        if (!treeWalkerRef.current) {\n          return;\n        }\n\n        treeWalkerRef.current.currentNode = el;\n      },\n    }),\n    [targetDocument],\n  );\n\n  return {\n    optionWalker,\n    listboxCallbackRef: setListbox,\n  };\n}\n"], "names": ["React", "useFluent_unstable", "useFluent", "isHTMLElement", "useOptionWalker", "options", "matchOption", "targetDocument", "treeWalkerRef", "useRef", "listboxRef", "optionFilter", "useCallback", "node", "Node<PERSON><PERSON><PERSON>", "FILTER_ACCEPT", "FILTER_SKIP", "setListbox", "el", "current", "createTreeWalker", "SHOW_ELEMENT", "<PERSON><PERSON><PERSON><PERSON>", "useMemo", "first", "currentNode", "<PERSON><PERSON><PERSON><PERSON>", "last", "<PERSON><PERSON><PERSON><PERSON>", "next", "nextNode", "prev", "previousNode", "find", "predicate", "startFrom", "start", "getElementById", "cur", "id", "setCurrent", "listboxCallbackRef"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,sBAAsBC,SAAS,QAAQ,kCAAkC;AAClF,SAASC,aAAa,QAAQ,4BAA4B;AAM1D,OAAO,SAASC,gBAAqDC,OAA+B;IAClG,MAAM,EAAEC,WAAW,EAAE,GAAGD;IACxB,MAAM,EAAEE,cAAc,EAAE,GAAGL;IAC3B,MAAMM,gBAAgBR,MAAMS,MAAM,CAAoB;IACtD,MAAMC,aAAaV,MAAMS,MAAM,CAAyB;IAExD,MAAME,eAAeX,MAAMY,WAAW,CACpC,CAACC;QACC,IAAIV,cAAcU,SAASP,YAAYO,OAAO;YAC5C,OAAOC,WAAWC,aAAa;QACjC;QAEA,OAAOD,WAAWE,WAAW;IAC/B,GACA;QAACV;KAAY;IAGf,MAAMW,aAAajB,MAAMY,WAAW,CAClC,CAACM;QACC,IAAIA,MAAMX,gBAAgB;YACxBG,WAAWS,OAAO,GAAGD;YACrBV,cAAcW,OAAO,GAAGZ,eAAea,gBAAgB,CAACF,IAAIJ,WAAWO,YAAY,EAAEV;QACvF,OAAO;YACLD,WAAWS,OAAO,GAAG;QACvB;IACF,GACA;QAACZ;QAAgBI;KAAa;IAGhC,MAAMW,eAAetB,MAAMuB,OAAO,CAChC,IAAO,CAAA;YACLC,OAAO;gBACL,IAAI,CAAChB,cAAcW,OAAO,IAAI,CAACT,WAAWS,OAAO,EAAE;oBACjD,OAAO;gBACT;gBAEAX,cAAcW,OAAO,CAACM,WAAW,GAAGf,WAAWS,OAAO;gBACtD,OAAOX,cAAcW,OAAO,CAACO,UAAU;YACzC;YACAC,MAAM;gBACJ,IAAI,CAACnB,cAAcW,OAAO,IAAI,CAACT,WAAWS,OAAO,EAAE;oBACjD,OAAO;gBACT;gBAEAX,cAAcW,OAAO,CAACM,WAAW,GAAGf,WAAWS,OAAO;gBACtD,OAAOX,cAAcW,OAAO,CAACS,SAAS;YACxC;YACAC,MAAM;gBACJ,IAAI,CAACrB,cAAcW,OAAO,EAAE;oBAC1B,OAAO;gBACT;gBAEA,OAAOX,cAAcW,OAAO,CAACW,QAAQ;YACvC;YACAC,MAAM;gBACJ,IAAI,CAACvB,cAAcW,OAAO,EAAE;oBAC1B,OAAO;gBACT;gBAEA,OAAOX,cAAcW,OAAO,CAACa,YAAY;YAC3C;YACAC,MAAM,CAACC,WAAoCC;gBACzC,IAAI,CAAC3B,cAAcW,OAAO,IAAI,CAACT,WAAWS,OAAO,EAAE;oBACjD,OAAO;gBACT;gBAEA,MAAMiB,QAAQD,YAAY5B,2BAAAA,qCAAAA,eAAgB8B,cAAc,CAACF,aAAa;gBACtE3B,cAAcW,OAAO,CAACM,WAAW,GAAGW,kBAAAA,mBAAAA,QAAS1B,WAAWS,OAAO;gBAC/D,IAAImB,MAA0B9B,cAAcW,OAAO,CAACM,WAAW;gBAC/D,MAAOa,OAAO,CAACJ,UAAUI,IAAIC,EAAE,EAAG;oBAChCD,MAAM9B,cAAcW,OAAO,CAACW,QAAQ;gBACtC;gBAEA,OAAOQ;YACT;YACAE,YAAY,CAACtB;gBACX,IAAI,CAACV,cAAcW,OAAO,EAAE;oBAC1B;gBACF;gBAEAX,cAAcW,OAAO,CAACM,WAAW,GAAGP;YACtC;QACF,CAAA,GACA;QAACX;KAAe;IAGlB,OAAO;QACLe;QACAmB,oBAAoBxB;IACtB;AACF"}