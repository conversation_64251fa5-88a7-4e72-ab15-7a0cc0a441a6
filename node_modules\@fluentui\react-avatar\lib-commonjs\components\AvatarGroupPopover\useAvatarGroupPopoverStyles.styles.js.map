{"version": 3, "sources": ["useAvatarGroupPopoverStyles.styles.js"], "sourcesContent": ["import { createCustomFocusIndicatorStyle } from '@fluentui/react-tabster';\nimport { makeStyles, mergeClasses, shorthands } from '@griffel/react';\nimport { tokens, typographyStyles } from '@fluentui/react-theme';\nimport { useGroupChildClassName } from '../AvatarGroupItem/useAvatarGroupItemStyles.styles';\nimport { useSizeStyles } from '../Avatar/useAvatarStyles.styles';\nexport const avatarGroupPopoverClassNames = {\n    root: 'fui-AvatarGroupPopover',\n    content: 'fui-AvatarGroupPopover__content',\n    popoverSurface: 'fui-AvatarGroupPopover__popoverSurface',\n    tooltip: 'fui-AvatarGroupPopover__tooltip',\n    triggerButton: 'fui-AvatarGroupPopover__triggerButton'\n};\n/**\n * Styles for the content slot.\n */ const useContentStyles = makeStyles({\n    base: {\n        listStyleType: 'none',\n        margin: '0',\n        padding: '0',\n        display: 'flex',\n        flexDirection: 'column'\n    }\n});\n/**\n * Styles for the popoverSurface slot.\n */ const usePopoverSurfaceStyles = makeStyles({\n    base: {\n        maxHeight: '220px',\n        minHeight: '80px',\n        overflow: 'hidden scroll',\n        padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalS}`,\n        width: '220px'\n    }\n});\n/**\n * Styles for the triggerButton slot.\n */ const useTriggerButtonStyles = makeStyles({\n    base: {\n        display: 'inline-flex',\n        position: 'relative',\n        flexShrink: 0,\n        justifyContent: 'center',\n        alignItems: 'center',\n        color: tokens.colorNeutralForeground1,\n        backgroundColor: tokens.colorNeutralBackground1,\n        ...shorthands.borderColor(tokens.colorNeutralStroke1),\n        borderRadius: tokens.borderRadiusCircular,\n        ...shorthands.borderStyle('solid'),\n        padding: '0',\n        // Match color to Avatar's outline color.\n        '@media (forced-colors: active)': {\n            ...shorthands.borderColor('CanvasText')\n        }\n    },\n    pie: {\n        backgroundColor: tokens.colorTransparentBackground,\n        ...shorthands.borderColor(tokens.colorTransparentStroke),\n        color: 'transparent'\n    },\n    focusIndicator: createCustomFocusIndicatorStyle({\n        border: `${tokens.strokeWidthThick} solid ${tokens.colorStrokeFocus2}`,\n        outlineStyle: 'none'\n    }),\n    states: {\n        '&:hover': {\n            color: tokens.colorNeutralForeground1Hover,\n            backgroundColor: tokens.colorNeutralBackground1Hover,\n            ...shorthands.borderColor(tokens.colorNeutralStroke1Hover)\n        },\n        '&:active': {\n            color: tokens.colorNeutralForeground1Pressed,\n            backgroundColor: tokens.colorNeutralBackground1Pressed,\n            ...shorthands.borderColor(tokens.colorNeutralStroke1Pressed)\n        }\n    },\n    selected: {\n        color: tokens.colorNeutralForeground1Selected,\n        backgroundColor: tokens.colorNeutralBackground1Selected,\n        ...shorthands.borderColor(tokens.colorNeutralStroke1Selected)\n    },\n    icon12: {\n        fontSize: '12px'\n    },\n    icon16: {\n        fontSize: '16px'\n    },\n    icon20: {\n        fontSize: '20px'\n    },\n    icon24: {\n        fontSize: '24px'\n    },\n    icon28: {\n        fontSize: '28px'\n    },\n    icon32: {\n        fontSize: '32px'\n    },\n    icon48: {\n        fontSize: '48px'\n    },\n    caption2Strong: {\n        ...typographyStyles.caption2Strong\n    },\n    caption1Strong: {\n        ...typographyStyles.caption1Strong\n    },\n    body1Strong: {\n        ...typographyStyles.body1Strong\n    },\n    subtitle2: {\n        ...typographyStyles.subtitle2\n    },\n    subtitle1: {\n        ...typographyStyles.subtitle1\n    },\n    title3: {\n        ...typographyStyles.title3\n    },\n    borderThin: {\n        ...shorthands.borderWidth(tokens.strokeWidthThin)\n    },\n    borderThick: {\n        ...shorthands.borderWidth(tokens.strokeWidthThick)\n    },\n    borderThicker: {\n        ...shorthands.borderWidth(tokens.strokeWidthThicker)\n    },\n    borderThickest: {\n        ...shorthands.borderWidth(tokens.strokeWidthThickest)\n    }\n});\n/**\n * Apply styling to the AvatarGroupPopover slots based on the state\n */ export const useAvatarGroupPopoverStyles_unstable = (state)=>{\n    'use no memo';\n    const { indicator, size, layout, popoverOpen } = state;\n    const sizeStyles = useSizeStyles();\n    const triggerButtonStyles = useTriggerButtonStyles();\n    const contentStyles = useContentStyles();\n    const popoverSurfaceStyles = usePopoverSurfaceStyles();\n    const groupChildClassName = useGroupChildClassName(layout, size);\n    const triggerButtonClasses = [];\n    if (size < 36) {\n        triggerButtonClasses.push(triggerButtonStyles.borderThin);\n    } else if (size < 56) {\n        triggerButtonClasses.push(triggerButtonStyles.borderThick);\n    } else if (size < 72) {\n        triggerButtonClasses.push(triggerButtonStyles.borderThicker);\n    } else {\n        triggerButtonClasses.push(triggerButtonStyles.borderThickest);\n    }\n    if (indicator === 'count') {\n        if (size <= 24) {\n            triggerButtonClasses.push(triggerButtonStyles.caption2Strong);\n        } else if (size <= 28) {\n            triggerButtonClasses.push(triggerButtonStyles.caption1Strong);\n        } else if (size <= 40) {\n            triggerButtonClasses.push(triggerButtonStyles.body1Strong);\n        } else if (size <= 56) {\n            triggerButtonClasses.push(triggerButtonStyles.subtitle2);\n        } else if (size <= 96) {\n            triggerButtonClasses.push(triggerButtonStyles.subtitle1);\n        } else {\n            triggerButtonClasses.push(triggerButtonStyles.title3);\n        }\n    } else {\n        if (size <= 16) {\n            triggerButtonClasses.push(triggerButtonStyles.icon12);\n        } else if (size <= 24) {\n            triggerButtonClasses.push(triggerButtonStyles.icon16);\n        } else if (size <= 40) {\n            triggerButtonClasses.push(triggerButtonStyles.icon20);\n        } else if (size <= 48) {\n            triggerButtonClasses.push(triggerButtonStyles.icon24);\n        } else if (size <= 56) {\n            triggerButtonClasses.push(triggerButtonStyles.icon28);\n        } else if (size <= 72) {\n            triggerButtonClasses.push(triggerButtonStyles.icon32);\n        } else {\n            triggerButtonClasses.push(triggerButtonStyles.icon48);\n        }\n    }\n    state.triggerButton.className = mergeClasses(avatarGroupPopoverClassNames.triggerButton, groupChildClassName, sizeStyles[size], triggerButtonStyles.base, layout === 'pie' && triggerButtonStyles.pie, triggerButtonStyles.focusIndicator, layout !== 'pie' && triggerButtonStyles.states, layout !== 'pie' && popoverOpen && triggerButtonStyles.selected, ...triggerButtonClasses, state.triggerButton.className);\n    state.content.className = mergeClasses(avatarGroupPopoverClassNames.content, contentStyles.base, state.content.className);\n    state.popoverSurface.className = mergeClasses(avatarGroupPopoverClassNames.popoverSurface, popoverSurfaceStyles.base, state.popoverSurface.className);\n    return state;\n};\n"], "names": ["avatarGroupPopoverClassNames", "useAvatarGroupPopoverStyles_unstable", "root", "content", "popoverSurface", "tooltip", "trigger<PERSON>utton", "useContentStyles", "__styles", "base", "dclx09", "j<PERSON><PERSON>", "Frg6f3", "t21cq0", "B6of3ja", "B74szlk", "Byoj8tv", "uwmqm3", "z189sj", "z8tnut", "B0ocmuz", "mc9l5x", "Beiy3e4", "d", "p", "usePopoverSurfaceStyles", "Bxyxcbc", "sshi5w", "B68tc82", "Bmxbyg5", "Bpg54ce", "a9b677", "useTriggerButtonStyles", "qhf8xq", "Bnnss6s", "Brf1p80", "Bt984gj", "sj55zd", "De3pzq", "g2u3we", "h3c5rm", "B9xav0g", "zhjwy3", "Beyfa6y", "Bbmb7ep", "Btl43ni", "B7oj6ja", "Dimara", "<PERSON><PERSON><PERSON><PERSON>", "vrafjx", "oivjwe", "wvpqe5", "Bjwas2f", "Bn1d65q", "Bxeuatn", "n51gp8", "pie", "focusIndicator", "Byu6kyc", "n8qw10", "Bbjhlyh", "i2cumq", "Bunx835", "Bdrgwmp", "mqozju", "lbo84a", "Bksnhdo", "Bci5o5g", "u5e7qz", "Bn40d3w", "B7b6zxw", "B8q5s1w", "B5gfjzb", "Bbcte9g", "Bqz3imu", "g9k6zt", "states", "Bi91k9c", "Jwef8y", "Bgoe8wy", "Bwzppfd", "oetu4i", "gg5e9n", "lj723h", "ecr2s2", "B6oc9vd", "ak43y8", "wmxk5l", "B50zh58", "selected", "icon12", "Be2twd7", "icon16", "icon20", "icon24", "icon28", "icon32", "icon48", "caption2Strong", "Bahqtrf", "Bhrd7zp", "Bg96gwp", "caption1Strong", "body1Strong", "subtitle2", "subtitle1", "title3", "borderThin", "B4j52fo", "Bekrc4i", "Bn0qgzm", "ibv6hh", "borderThick", "borderThicker", "borderThickest", "m", "h", "a", "state", "indicator", "size", "layout", "popoverOpen", "sizeStyles", "useSizeStyles", "triggerButtonStyles", "contentStyles", "popoverSurfaceStyles", "groupChildClassName", "useGroupChildClassName", "triggerButtonClasses", "push", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAKaA,4BAA4B;eAA5BA;;IAiIIC,oCAAoC;eAApCA;;;uBArIoC;gDAEd;uCACT;AACvB,MAAMD,+BAA+B;IACxCE,MAAM;IACNC,SAAS;IACTC,gBAAgB;IAChBC,SAAS;IACTC,eAAe;AACnB;AACA;;CAEA,GAAI,MAAMC,mBAAgB,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAC,MAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;KAAA;AAAA;AAS7B;;CAEA,GAAI,MAAMC,0BAAuB,WAAA,GAAGjB,IAAAA,eAAA,EAAA;IAAAC,MAAA;QAAAiB,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAd,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAW,QAAA;IAAA;AAAA,GAAA;IAAAR,GAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;KAAA;AAAA;AASpC;;CAEA,GAAI,MAAMQ,yBAAsB,WAAA,GAAGxB,IAAAA,eAAA,EAAA;IAAAC,MAAA;QAAAY,QAAA;QAAAY,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAnC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAgC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAC,KAAA;QAAAlB,QAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAL,QAAA;IAAA;IAAAoB,gBAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;IAAA;IAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,QAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;IAAA;IAAAC,UAAA;QAAApD,QAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAgD,QAAA;QAAAC,SAAA;IAAA;IAAAC,QAAA;QAAAD,SAAA;IAAA;IAAAE,QAAA;QAAAF,SAAA;IAAA;IAAAG,QAAA;QAAAH,SAAA;IAAA;IAAAI,QAAA;QAAAJ,SAAA;IAAA;IAAAK,QAAA;QAAAL,SAAA;IAAA;IAAAM,QAAA;QAAAN,SAAA;IAAA;IAAAO,gBAAA;QAAAC,SAAA;QAAAR,SAAA;QAAAS,SAAA;QAAAC,SAAA;IAAA;IAAAC,gBAAA;QAAAH,SAAA;QAAAR,SAAA;QAAAS,SAAA;QAAAC,SAAA;IAAA;IAAAE,aAAA;QAAAJ,SAAA;QAAAR,SAAA;QAAAS,SAAA;QAAAC,SAAA;IAAA;IAAAG,WAAA;QAAAL,SAAA;QAAAR,SAAA;QAAAS,SAAA;QAAAC,SAAA;IAAA;IAAAI,WAAA;QAAAN,SAAA;QAAAR,SAAA;QAAAS,SAAA;QAAAC,SAAA;IAAA;IAAAK,QAAA;QAAAP,SAAA;QAAAR,SAAA;QAAAS,SAAA;QAAAC,SAAA;IAAA;IAAAM,YAAA;QAAAC,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAC,aAAA;QAAAJ,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAE,eAAA;QAAAL,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAG,gBAAA;QAAAN,SAAA;QAAAC,SAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;AAAA,GAAA;IAAAxF,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAC,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA,CAAA;YAAA;SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAA2F,GAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;QAAA;YAAA;YAAA;gBAAAA,GAAA;YAAA;SAAA;KAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AAkGxB,MAAMpH,uCAAwCqH,CAAAA;IACrD;IACA,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,WAAAA,EAAa,GAAGJ;IACjD,MAAMK,aAAaC,IAAAA,oCAAa;IAChC,MAAMC,sBAAsB7F;IAC5B,MAAM8F,gBAAgBvH;IACtB,MAAMwH,uBAAuBtG;IAC7B,MAAMuG,sBAAsBC,IAAAA,sDAAsB,EAACR,QAAQD;IAC3D,MAAMU,uBAAuB,EAAE;IAC/B,IAAIV,OAAO,IAAI;QACXU,qBAAqBC,IAAI,CAACN,oBAAoBlB,UAAU;IAC5D,OAAO,IAAIa,OAAO,IAAI;QAClBU,qBAAqBC,IAAI,CAACN,oBAAoBb,WAAW;IAC7D,OAAO,IAAIQ,OAAO,IAAI;QAClBU,qBAAqBC,IAAI,CAACN,oBAAoBZ,aAAa;IAC/D,OAAO;QACHiB,qBAAqBC,IAAI,CAACN,oBAAoBX,cAAc;IAChE;IACA,IAAIK,cAAc,SAAS;QACvB,IAAIC,QAAQ,IAAI;YACZU,qBAAqBC,IAAI,CAACN,oBAAoB3B,cAAc;QAChE,OAAO,IAAIsB,QAAQ,IAAI;YACnBU,qBAAqBC,IAAI,CAACN,oBAAoBvB,cAAc;QAChE,OAAO,IAAIkB,QAAQ,IAAI;YACnBU,qBAAqBC,IAAI,CAACN,oBAAoBtB,WAAW;QAC7D,OAAO,IAAIiB,QAAQ,IAAI;YACnBU,qBAAqBC,IAAI,CAACN,oBAAoBrB,SAAS;QAC3D,OAAO,IAAIgB,QAAQ,IAAI;YACnBU,qBAAqBC,IAAI,CAACN,oBAAoBpB,SAAS;QAC3D,OAAO;YACHyB,qBAAqBC,IAAI,CAACN,oBAAoBnB,MAAM;QACxD;IACJ,OAAO;QACH,IAAIc,QAAQ,IAAI;YACZU,qBAAqBC,IAAI,CAACN,oBAAoBnC,MAAM;QACxD,OAAO,IAAI8B,QAAQ,IAAI;YACnBU,qBAAqBC,IAAI,CAACN,oBAAoBjC,MAAM;QACxD,OAAO,IAAI4B,QAAQ,IAAI;YACnBU,qBAAqBC,IAAI,CAACN,oBAAoBhC,MAAM;QACxD,OAAO,IAAI2B,QAAQ,IAAI;YACnBU,qBAAqBC,IAAI,CAACN,oBAAoB/B,MAAM;QACxD,OAAO,IAAI0B,QAAQ,IAAI;YACnBU,qBAAqBC,IAAI,CAACN,oBAAoB9B,MAAM;QACxD,OAAO,IAAIyB,QAAQ,IAAI;YACnBU,qBAAqBC,IAAI,CAACN,oBAAoB7B,MAAM;QACxD,OAAO;YACHkC,qBAAqBC,IAAI,CAACN,oBAAoB5B,MAAM;QACxD;IACJ;IACAqB,MAAMhH,aAAa,CAAC8H,SAAS,GAAGC,IAAAA,mBAAY,EAACrI,6BAA6BM,aAAa,EAAE0H,qBAAqBL,UAAU,CAACH,KAAK,EAAEK,oBAAoBpH,IAAI,EAAEgH,WAAW,SAASI,oBAAoBrE,GAAG,EAAEqE,oBAAoBpE,cAAc,EAAEgE,WAAW,SAASI,oBAAoBjD,MAAM,EAAE6C,WAAW,SAASC,eAAeG,oBAAoBpC,QAAQ,KAAKyC,sBAAsBZ,MAAMhH,aAAa,CAAC8H,SAAS;IAClZd,MAAMnH,OAAO,CAACiI,SAAS,GAAGC,IAAAA,mBAAY,EAACrI,6BAA6BG,OAAO,EAAE2H,cAAcrH,IAAI,EAAE6G,MAAMnH,OAAO,CAACiI,SAAS;IACxHd,MAAMlH,cAAc,CAACgI,SAAS,GAAGC,IAAAA,mBAAY,EAACrI,6BAA6BI,cAAc,EAAE2H,qBAAqBtH,IAAI,EAAE6G,MAAMlH,cAAc,CAACgI,SAAS;IACpJ,OAAOd;AACX"}