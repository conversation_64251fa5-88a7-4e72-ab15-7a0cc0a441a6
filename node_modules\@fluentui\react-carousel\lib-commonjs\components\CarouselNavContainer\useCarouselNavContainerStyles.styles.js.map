{"version": 3, "sources": ["useCarouselNavContainerStyles.styles.js"], "sourcesContent": ["import { makeStyles, mergeClasses } from '@griffel/react';\nimport { tokens } from '@fluentui/react-theme';\nexport const carouselNavContainerClassNames = {\n    root: 'fui-CarouselNavContainer',\n    next: 'fui-CarouselNavContainer__next',\n    prev: 'fui-CarouselNavContainer__prev',\n    autoplay: 'fui-CarouselNavContainer__autoplay',\n    /* Tooltip classNames are listed for type compatibility only (cannot assign root className to portal)\n   * Use 'content' slot to style Tooltip content instead\n   */ nextTooltip: 'fui-CarouselNavContainer__nextTooltip',\n    prevTooltip: 'fui-CarouselNavContainer__prevTooltip',\n    autoplayTooltip: 'fui-CarouselNavContainer__autoplayTooltip'\n};\n/**\n * Styles for the root slot\n */ const useStyles = makeStyles({\n    root: {\n        display: 'flex',\n        flexDirection: 'row',\n        justifyContent: 'center',\n        width: '100%',\n        pointerEvents: 'none'\n    },\n    next: {},\n    prev: {},\n    autoplay: {},\n    inline: {\n        marginTop: tokens.spacingVerticalM\n    },\n    overlay: {\n        position: 'absolute',\n        bottom: tokens.spacingVerticalM,\n        boxSizing: 'border-box'\n    },\n    overlayWide: {\n        bottom: tokens.spacingVerticalM\n    },\n    nextWide: {\n        marginLeft: 'auto'\n    },\n    prevWide: {\n        marginRight: 'auto'\n    },\n    nextOverlayWide: {\n        marginRight: tokens.spacingHorizontalM\n    },\n    prevOverlayWide: {\n        marginLeft: tokens.spacingHorizontalM\n    },\n    autoplayOverlayWide: {\n        marginLeft: tokens.spacingHorizontalM\n    },\n    expanded: {\n        width: '100%',\n        height: '100%',\n        alignItems: 'flex-end',\n        justifyContent: 'center',\n        bottom: 0,\n        '> div': {\n            position: 'relative',\n            bottom: tokens.spacingVerticalL,\n            marginBottom: 0\n        }\n    },\n    nextOverlayExpanded: {\n        position: 'absolute',\n        right: tokens.spacingHorizontalM,\n        top: '50%',\n        transform: 'translateY(-50%)'\n    },\n    prevOverlayExpanded: {\n        position: 'absolute',\n        left: tokens.spacingHorizontalM,\n        top: '50%',\n        transform: 'translateY(-50%)'\n    },\n    autoplayExpanded: {\n        position: 'absolute',\n        bottom: `-${tokens.spacingHorizontalXS}`,\n        left: tokens.spacingHorizontalM,\n        marginBottom: tokens.spacingVerticalM\n    }\n});\n/**\n * Apply styling to the CarouselNavContainer slots based on the state\n */ export const useCarouselNavContainerStyles_unstable = (state)=>{\n    'use no memo';\n    const { layout } = state;\n    const isOverlay = layout === 'overlay' || layout === 'overlay-wide' || layout === 'overlay-expanded';\n    const isWide = layout === 'inline-wide' || layout === 'overlay-wide';\n    const styles = useStyles();\n    state.root.className = mergeClasses(carouselNavContainerClassNames.root, styles.root, isOverlay ? styles.overlay : styles.inline, isOverlay && isWide && styles.overlayWide, layout === 'overlay-expanded' && styles.expanded, state.root.className);\n    if (state.next) {\n        state.next.className = mergeClasses(carouselNavContainerClassNames.next, styles.next, isWide && styles.nextWide, isWide && isOverlay && styles.nextOverlayWide, layout === 'overlay-expanded' && styles.nextOverlayExpanded, state.next.className);\n    }\n    if (state.prev) {\n        state.prev.className = mergeClasses(carouselNavContainerClassNames.prev, styles.prev, isWide && styles.prevWide, !state.autoplay && isWide && isOverlay && styles.prevOverlayWide, layout === 'overlay-expanded' && styles.prevOverlayExpanded, state.prev.className);\n    }\n    if (state.autoplay) {\n        state.autoplay.className = mergeClasses(carouselNavContainerClassNames.autoplay, styles.autoplay, layout === 'overlay-expanded' && styles.autoplayExpanded, isWide && isOverlay && styles.autoplayOverlayWide, state.autoplay.className);\n    }\n    return state;\n};\n"], "names": ["carouselNavContainerClassNames", "useCarouselNavContainerStyles_unstable", "root", "next", "prev", "autoplay", "nextTooltip", "prevTooltip", "autoplayTooltip", "useStyles", "__styles", "mc9l5x", "Beiy3e4", "Brf1p80", "a9b677", "Bkecrkj", "inline", "B6of3ja", "overlay", "qhf8xq", "B5kzvoi", "B7ck84d", "overlayWide", "nextWide", "Frg6f3", "prevWide", "t21cq0", "nextOverlayWide", "prevOverlayWide", "autoplayOverlayWide", "expanded", "Bqenvij", "Bt984gj", "c7y7m3", "yx0ijg", "v4amzz", "nextOverlayExpanded", "j35jbq", "Bhzewxz", "Bz10aip", "prevOverlayExpanded", "oyh7mz", "autoplayExpanded", "j<PERSON><PERSON>", "d", "state", "layout", "isOverlay", "isWide", "styles", "className", "mergeClasses"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;;;;;;;;IAEaA,8BAA8B;eAA9BA;;IAmFIC,sCAAsC;eAAtCA;;;uBArFwB;AAElC,MAAMD,iCAAiC;IAC1CE,MAAM;IACNC,MAAM;IACNC,MAAM;IACNC,UAAU;IACV;;EAEJ,GAAMC,aAAa;IACfC,aAAa;IACbC,iBAAiB;AACrB;AACA;;CAEA,GAAI,MAAMC,YAAS,WAAA,GAAGC,IAAAA,eAAA,EAAA;IAAAR,MAAA;QAAAS,QAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;IAAA;IAAAZ,MAAA,CAAA;IAAAC,MAAA,CAAA;IAAAC,UAAA,CAAA;IAAAW,QAAA;QAAAC,SAAA;IAAA;IAAAC,SAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAAC,aAAA;QAAAF,SAAA;IAAA;IAAAG,UAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAC,UAAA;QAAAC,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAC,iBAAA;QAAAD,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAE,iBAAA;QAAAJ,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAK,qBAAA;QAAAL,QAAA;YAAA;YAAA;SAAA;IAAA;IAAAM,UAAA;QAAAhB,QAAA;QAAAiB,SAAA;QAAAC,SAAA;QAAAnB,SAAA;QAAAO,SAAA;QAAAa,QAAA;QAAAC,QAAA;QAAAC,QAAA;IAAA;IAAAC,qBAAA;QAAAjB,QAAA;QAAAkB,QAAA;YAAA;YAAA;SAAA;QAAAC,SAAA;QAAAC,SAAA;IAAA;IAAAC,qBAAA;QAAArB,QAAA;QAAAsB,QAAA;YAAA;YAAA;SAAA;QAAAH,SAAA;QAAAC,SAAA;IAAA;IAAAG,kBAAA;QAAAvB,QAAA;QAAAC,SAAA;QAAAqB,QAAA;YAAA;YAAA;SAAA;QAAAE,QAAA;IAAA;AAAA,GAAA;IAAAC,GAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;KAAA;AAAA;AAsEX,MAAM3C,yCAA0C4C,CAAAA;IACvD;IACA,MAAM,EAAEC,MAAAA,EAAQ,GAAGD;IACnB,MAAME,YAAYD,WAAW,aAAaA,WAAW,kBAAkBA,WAAW;IAClF,MAAME,SAASF,WAAW,iBAAiBA,WAAW;IACtD,MAAMG,SAASxC;IACfoC,MAAM3C,IAAI,CAACgD,SAAS,GAAGC,IAAAA,mBAAY,EAACnD,+BAA+BE,IAAI,EAAE+C,OAAO/C,IAAI,EAAE6C,YAAYE,OAAO/B,OAAO,GAAG+B,OAAOjC,MAAM,EAAE+B,aAAaC,UAAUC,OAAO3B,WAAW,EAAEwB,WAAW,sBAAsBG,OAAOnB,QAAQ,EAAEe,MAAM3C,IAAI,CAACgD,SAAS;IACnP,IAAIL,MAAM1C,IAAI,EAAE;QACZ0C,MAAM1C,IAAI,CAAC+C,SAAS,GAAGC,IAAAA,mBAAY,EAACnD,+BAA+BG,IAAI,EAAE8C,OAAO9C,IAAI,EAAE6C,UAAUC,OAAO1B,QAAQ,EAAEyB,UAAUD,aAAaE,OAAOtB,eAAe,EAAEmB,WAAW,sBAAsBG,OAAOb,mBAAmB,EAAES,MAAM1C,IAAI,CAAC+C,SAAS;IACrP;IACA,IAAIL,MAAMzC,IAAI,EAAE;QACZyC,MAAMzC,IAAI,CAAC8C,SAAS,GAAGC,IAAAA,mBAAY,EAACnD,+BAA+BI,IAAI,EAAE6C,OAAO7C,IAAI,EAAE4C,UAAUC,OAAOxB,QAAQ,EAAE,CAACoB,MAAMxC,QAAQ,IAAI2C,UAAUD,aAAaE,OAAOrB,eAAe,EAAEkB,WAAW,sBAAsBG,OAAOT,mBAAmB,EAAEK,MAAMzC,IAAI,CAAC8C,SAAS;IACxQ;IACA,IAAIL,MAAMxC,QAAQ,EAAE;QAChBwC,MAAMxC,QAAQ,CAAC6C,SAAS,GAAGC,IAAAA,mBAAY,EAACnD,+BAA+BK,QAAQ,EAAE4C,OAAO5C,QAAQ,EAAEyC,WAAW,sBAAsBG,OAAOP,gBAAgB,EAAEM,UAAUD,aAAaE,OAAOpB,mBAAmB,EAAEgB,MAAMxC,QAAQ,CAAC6C,SAAS;IAC3O;IACA,OAAOL;AACX"}