{"version": 3, "sources": ["../src/components/AccordionItem/AccordionItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useAccordionItem_unstable } from './useAccordionItem';\nimport { useAccordionItemContextValues_unstable } from './useAccordionItemContextValues';\nimport { renderAccordionItem_unstable } from './renderAccordionItem';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAccordionItemStyles_unstable } from './useAccordionItemStyles.styles';\nimport type { AccordionItemProps } from './AccordionItem.types';\nimport type { ForwardRefComponent } from '@fluentui/react-utilities';\n\n/**\n * Define a styled AccordionItem, using the `useAccordionItem_unstable` and `useAccordionItemStyles_unstable` hooks.\n */\nexport const AccordionItem: ForwardRefComponent<AccordionItemProps> = React.forwardRef((props, ref) => {\n  const state = useAccordionItem_unstable(props, ref);\n  const contextValues = useAccordionItemContextValues_unstable(state);\n\n  useAccordionItemStyles_unstable(state);\n\n  useCustomStyleHook_unstable('useAccordionItemStyles_unstable')(state);\n\n  return renderAccordionItem_unstable(state, contextValues);\n});\n\nAccordionItem.displayName = 'AccordionItem';\n"], "names": ["AccordionItem", "React", "forwardRef", "props", "ref", "state", "useAccordionItem_unstable", "contextValues", "useAccordionItemContextValues_unstable", "useAccordionItemStyles_unstable", "useCustomStyleHook_unstable", "renderAccordionItem_unstable", "displayName"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAYaA;;;eAAAA;;;;iEAZU;kCACmB;+CACa;qCACV;qCACD;8CACI;AAOzC,MAAMA,gBAAAA,WAAAA,GAAyDC,OAAMC,UAAU,CAAC,CAACC,OAAOC;IAC7F,MAAMC,QAAQC,IAAAA,2CAAAA,EAA0BH,OAAOC;IAC/C,MAAMG,gBAAgBC,IAAAA,qEAAAA,EAAuCH;IAE7DI,IAAAA,6DAAAA,EAAgCJ;IAEhCK,IAAAA,gDAAAA,EAA4B,mCAAmCL;IAE/D,OAAOM,IAAAA,iDAAAA,EAA6BN,OAAOE;AAC7C;AAEAP,cAAcY,WAAW,GAAG"}